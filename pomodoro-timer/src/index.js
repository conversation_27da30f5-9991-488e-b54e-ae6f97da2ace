import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import AppWithAuth from './AppWithAuth';
import reportWebVitals, { enhancedWebVitals } from './reportWebVitals';

// Optimize desktop FID with efficient error handling
const optimizedErrorHandler = (event) => {
  if (event.message?.includes?.('youtube') || 
      event.message?.includes?.('cross-origin') ||
      event.message?.includes?.('Blocked a frame')) {
    event.preventDefault();
    return false;
  }
};

// Use passive listeners for better performance
window.addEventListener('error', optimizedErrorHandler, { passive: true });
window.addEventListener('unhandledrejection', (event) => {
  if (event.reason?.message?.includes?.('youtube') ||
      event.reason?.message?.includes?.('cross-origin')) {
    event.preventDefault();
  }
}, { passive: true });

// 主应用已在顶部导入

const root = ReactDOM.createRoot(document.getElementById('root'));

// 立即渲染减少LCP时间
root.render(<AppWithAuth />);

// Enhanced performance monitoring
if (process.env.NODE_ENV === 'production') {
  enhancedWebVitals();
} else {
  // Log web vitals in development
  reportWebVitals(console.log);
}
