// Utility functions for handling blog post tags

/**
 * Parse tags from various formats into a clean array
 * @param {string|Array} tags - Tags as string (comma-separated) or array
 * @returns {Array} Clean array of tags
 */
export const parseTags = (tags) => {
  if (!tags) return [];
  
  let tagsArray = [];
  
  if (Array.isArray(tags)) {
    // If tags is already an array, check if it contains comma-separated strings
    tagsArray = tags.flatMap(tag => 
      typeof tag === 'string' && tag.includes(',') 
        ? tag.split(',').map(t => t.trim())
        : [tag]
    );
  } else if (typeof tags === 'string') {
    // If tags is a string, split by comma
    tagsArray = tags.split(',').map(tag => tag.trim());
  }
  
  // Remove empty tags, normalize case, and remove duplicates
  const normalizedTags = tagsArray
    .filter(tag => tag && tag.length > 0)
    .map(tag => {
      // Normalize to title case for consistency
      return tag.toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
    });
  
  return [...new Set(normalizedTags)];
};

/**
 * Convert tag to URL-friendly slug
 * @param {string} tag - Tag name
 * @returns {string} URL-friendly slug
 */
export const tagToSlug = (tag) => {
  return encodeURIComponent(tag.toLowerCase().replace(/\s+/g, '-'));
};

/**
 * Convert slug back to display format
 * @param {string} slug - URL slug
 * @returns {string} Display format tag
 */
export const slugToTag = (slug) => {
  // Decode and convert slug back to title case format
  return decodeURIComponent(slug)
    .replace(/-/g, ' ')
    .toLowerCase()
    .replace(/\b\w/g, l => l.toUpperCase());
};

/**
 * Check if a post contains a specific tag (by slug)
 * @param {Object} post - Blog post object
 * @param {string} tagSlug - Tag slug to search for
 * @returns {boolean} Whether the post contains the tag
 */
export const postHasTag = (post, tagSlug) => {
  const tags = parseTags(post.tags);
  return tags.some(tag => tagToSlug(tag) === tagSlug);
};