/**
 * Utility functions for debugging social media sharing
 */

/**
 * Log all Open Graph and Twitter Card meta tags in the current page
 */
export const debugSocialMetaTags = () => {
  console.log('=== Social Media Meta Tags Debug ===');
  
  // Get all meta tags
  const metaTags = document.querySelectorAll('meta');
  
  const ogTags = [];
  const twitterTags = [];
  const otherTags = [];
  
  metaTags.forEach(tag => {
    const property = tag.getAttribute('property');
    const name = tag.getAttribute('name');
    const content = tag.getAttribute('content');
    
    if (property && property.startsWith('og:')) {
      ogTags.push({ property, content });
    } else if (name && name.startsWith('twitter:')) {
      twitterTags.push({ name, content });
    } else if (property && property.startsWith('article:')) {
      ogTags.push({ property, content });
    } else if (name && ['description', 'keywords', 'author'].includes(name)) {
      otherTags.push({ name, content });
    }
  });
  
  console.log('\n--- Open Graph Tags ---');
  ogTags.forEach(tag => {
    console.log(`${tag.property}: ${tag.content}`);
  });
  
  console.log('\n--- Twitter Card Tags ---');
  twitterTags.forEach(tag => {
    console.log(`${tag.name}: ${tag.content}`);
  });
  
  console.log('\n--- Other Important Tags ---');
  otherTags.forEach(tag => {
    console.log(`${tag.name}: ${tag.content}`);
  });
  
  // Get title
  const title = document.title;
  console.log(`\n--- Page Title ---`);
  console.log(`title: ${title}`);
  
  // Get canonical URL
  const canonical = document.querySelector('link[rel="canonical"]');
  if (canonical) {
    console.log(`\n--- Canonical URL ---`);
    console.log(`canonical: ${canonical.href}`);
  }
  
  console.log('\n=== End Debug ===');
};

/**
 * Validate social media meta tags
 */
export const validateSocialMetaTags = () => {
  const errors = [];
  const warnings = [];
  
  // Required Open Graph tags
  const requiredOGTags = ['og:title', 'og:description', 'og:image', 'og:url', 'og:type'];
  
  requiredOGTags.forEach(tagName => {
    const tag = document.querySelector(`meta[property="${tagName}"]`);
    if (!tag || !tag.content) {
      errors.push(`Missing required Open Graph tag: ${tagName}`);
    }
  });
  
  // Required Twitter Card tags
  const requiredTwitterTags = ['twitter:card', 'twitter:title', 'twitter:description', 'twitter:image'];
  
  requiredTwitterTags.forEach(tagName => {
    const tag = document.querySelector(`meta[name="${tagName}"]`);
    if (!tag || !tag.content) {
      errors.push(`Missing required Twitter Card tag: ${tagName}`);
    }
  });
  
  // Check image dimensions
  const ogImage = document.querySelector('meta[property="og:image"]');
  const ogImageWidth = document.querySelector('meta[property="og:image:width"]');
  const ogImageHeight = document.querySelector('meta[property="og:image:height"]');
  
  if (ogImage && ogImage.content) {
    if (!ogImageWidth || !ogImageHeight) {
      warnings.push('Missing image dimensions (og:image:width, og:image:height)');
    }
  }
  
  // Check description length
  const ogDescription = document.querySelector('meta[property="og:description"]');
  if (ogDescription && ogDescription.content.length > 160) {
    warnings.push(`Open Graph description is too long (${ogDescription.content.length} chars, recommended max 160)`);
  }
  
  const twitterDescription = document.querySelector('meta[name="twitter:description"]');
  if (twitterDescription && twitterDescription.content.length > 200) {
    warnings.push(`Twitter description is too long (${twitterDescription.content.length} chars, recommended max 200)`);
  }
  
  // Check title length
  const ogTitle = document.querySelector('meta[property="og:title"]');
  if (ogTitle && ogTitle.content.length > 60) {
    warnings.push(`Open Graph title is too long (${ogTitle.content.length} chars, recommended max 60)`);
  }
  
  return { errors, warnings };
};

/**
 * Generate social media preview URLs for testing
 */
export const generatePreviewUrls = (url) => {
  const encodedUrl = encodeURIComponent(url);
  
  return {
    facebook: `https://developers.facebook.com/tools/debug/?q=${encodedUrl}`,
    twitter: `https://cards-dev.twitter.com/validator?url=${encodedUrl}`,
    linkedin: `https://www.linkedin.com/post-inspector/inspect/${encodedUrl}`,
    whatsapp: `https://developers.facebook.com/tools/debug/?q=${encodedUrl}`, // WhatsApp uses Facebook's scraper
  };
};

/**
 * Test social media sharing for current page
 */
export const testSocialSharing = () => {
  const currentUrl = window.location.href;
  
  console.log('=== Social Media Sharing Test ===');
  console.log(`Current URL: ${currentUrl}`);
  
  // Debug meta tags
  debugSocialMetaTags();
  
  // Validate tags
  const validation = validateSocialMetaTags();
  
  if (validation.errors.length > 0) {
    console.log('\n--- ERRORS ---');
    validation.errors.forEach(error => console.error(error));
  }
  
  if (validation.warnings.length > 0) {
    console.log('\n--- WARNINGS ---');
    validation.warnings.forEach(warning => console.warn(warning));
  }
  
  // Generate preview URLs
  const previewUrls = generatePreviewUrls(currentUrl);
  console.log('\n--- Test URLs ---');
  console.log('Facebook Debugger:', previewUrls.facebook);
  console.log('Twitter Card Validator:', previewUrls.twitter);
  console.log('LinkedIn Post Inspector:', previewUrls.linkedin);
  
  console.log('\n=== End Test ===');
  
  return {
    validation,
    previewUrls,
    currentUrl
  };
};

/**
 * Add debug button to page (for development)
 */
export const addDebugButton = () => {
  if (process.env.NODE_ENV !== 'development') return;
  
  const button = document.createElement('button');
  button.textContent = 'Debug Social Tags';
  button.style.position = 'fixed';
  button.style.top = '10px';
  button.style.right = '10px';
  button.style.zIndex = '9999';
  button.style.padding = '10px';
  button.style.backgroundColor = '#007bff';
  button.style.color = 'white';
  button.style.border = 'none';
  button.style.borderRadius = '4px';
  button.style.cursor = 'pointer';
  
  button.onclick = testSocialSharing;
  
  document.body.appendChild(button);
};
