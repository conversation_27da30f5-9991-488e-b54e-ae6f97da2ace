// Structured Data (JSON-LD) utilities for SEO

export const createWebsiteStructuredData = () => ({
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "AI Pomo",
  "alternateName": "AI-Enhanced Pomodoro Timer",
  "url": "https://www.ai-pomo.com",
  "description": "AI Pomo combines the Pomodoro technique with AI-powered task management to help you focus, track progress, and achieve your goals efficiently.",
  "potentialAction": {
    "@type": "SearchAction",
    "target": {
      "@type": "EntryPoint",
      "urlTemplate": "https://www.ai-pomo.com/blog?search={search_term_string}"
    },
    "query-input": "required name=search_term_string"
  },
  "publisher": {
    "@type": "Organization",
    "name": "AI Pomo",
    "url": "https://www.ai-pomo.com",
    "logo": {
      "@type": "ImageObject",
      "url": "https://www.ai-pomo.com/logo512.png"
    }
  }
});

export const createSoftwareApplicationStructuredData = () => ({
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  "name": "AI Pomo",
  "description": "AI-Enhanced Pomodoro Timer and Task Management System",
  "url": "https://www.ai-pomo.com",
  "applicationCategory": "ProductivityApplication",
  "operatingSystem": "Web Browser",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "USD",
    "availability": "https://schema.org/InStock"
  },
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "4.8",
    "ratingCount": "150",
    "bestRating": "5",
    "worstRating": "1"
  },
  "author": {
    "@type": "Organization",
    "name": "AI Pomo Team"
  },
  "datePublished": "2024-01-01",
  "dateModified": new Date().toISOString().split('T')[0],
  "screenshot": "https://www.ai-pomo.com/screenshot.jpg",
  "featureList": [
    "AI-powered task generation",
    "Pomodoro timer with customizable intervals",
    "Project and milestone tracking",
    "Progress analytics and statistics",
    "Cross-platform synchronization"
  ]
});

export const createBlogPostStructuredData = (post) => ({
  "@context": "https://schema.org",
  "@type": "BlogPosting",
  "headline": post.title,
  "description": post.excerpt || post.seoDescription,
  "image": post.coverImage,
  "url": `https://www.ai-pomo.com/blog/${post.slug}`,
  "datePublished": post.createdAt,
  "dateModified": post.updatedAt || post.createdAt,
  "author": {
    "@type": "Person",
    "name": post.author?.name || "AI Pomo Team",
    "url": "https://www.ai-pomo.com"
  },
  "publisher": {
    "@type": "Organization",
    "name": "AI Pomo",
    "url": "https://www.ai-pomo.com",
    "logo": {
      "@type": "ImageObject",
      "url": "https://www.ai-pomo.com/logo512.png"
    }
  },
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": `https://www.ai-pomo.com/blog/${post.slug}`
  },
  "keywords": post.tags?.join(', ') || post.seoKeywords,
  "wordCount": post.content ? post.content.split(' ').length : 0,
  "timeRequired": `PT${post.readTime || 5}M`,
  "articleSection": post.category,
  "about": {
    "@type": "Thing",
    "name": "Productivity and Time Management"
  }
});

export const createBreadcrumbStructuredData = (breadcrumbs) => ({
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": breadcrumbs.map((crumb, index) => ({
    "@type": "ListItem",
    "position": index + 1,
    "name": crumb.name,
    "item": {
      "@type": "WebPage",
      "@id": crumb.url.startsWith('http') ? crumb.url : `https://www.ai-pomo.com${crumb.url}`
    }
  }))
});

export const createFAQStructuredData = (faqs) => ({
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": faqs.map(faq => ({
    "@type": "Question",
    "name": faq.question,
    "acceptedAnswer": {
      "@type": "Answer",
      "text": faq.answer
    }
  }))
});

export const createHowToStructuredData = (title, description, steps) => ({
  "@context": "https://schema.org",
  "@type": "HowTo",
  "name": title,
  "description": description,
  "step": steps.map((step, index) => ({
    "@type": "HowToStep",
    "position": index + 1,
    "name": step.name,
    "text": step.text,
    "image": step.image
  }))
});

export const createOrganizationStructuredData = () => ({
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "AI Pomo",
  "url": "https://www.ai-pomo.com",
  "logo": "https://www.ai-pomo.com/logo512.png",
  "description": "AI Pomo provides AI-enhanced productivity tools combining the Pomodoro technique with intelligent task management.",
  "foundingDate": "2024",
  "sameAs": [
    "https://twitter.com/aipomo",
    "https://github.com/aipomo"
  ],
  "contactPoint": {
    "@type": "ContactPoint",
    "contactType": "customer service",
    "email": "<EMAIL>"
  }
});
