/**
 * Utility functions for social media sharing
 */

/**
 * Generate social media share URLs
 * @param {Object} params - Share parameters
 * @param {string} params.title - Article title
 * @param {string} params.excerpt - Article excerpt
 * @param {string} params.url - Article URL
 * @param {Array} params.tags - Article tags
 * @returns {Object} Object containing share URLs for different platforms
 */
export const generateShareUrls = ({ title, excerpt, url, tags = [] }) => {
  const shareTitle = title;
  const shareText = excerpt;
  const shareUrl = url;
  const hashtags = tags.slice(0, 3).map(tag => tag.replace(/\s+/g, '')).join(',');

  return {
    twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}&hashtags=${encodeURIComponent(hashtags)}`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${encodeURIComponent(shareText)}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}&title=${encodeURIComponent(shareTitle)}&summary=${encodeURIComponent(shareText)}`,
    whatsapp: `https://wa.me/?text=${encodeURIComponent(`${shareTitle}\n\n${shareText}\n\n${shareUrl}`)}`
  };
};

/**
 * Copy text to clipboard with fallback for older browsers
 * @param {string} text - Text to copy
 * @returns {Promise<boolean>} Success status
 */
export const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (err) {
    // Fallback for older browsers
    try {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      const success = document.execCommand('copy');
      document.body.removeChild(textArea);
      return success;
    } catch (fallbackErr) {
      console.error('Failed to copy to clipboard:', fallbackErr);
      return false;
    }
  }
};

/**
 * Open social media share window
 * @param {string} url - Share URL
 * @param {string} platform - Platform name for window title
 */
export const openShareWindow = (url, platform = 'share') => {
  const windowFeatures = 'width=600,height=400,scrollbars=yes,resizable=yes';
  window.open(url, `${platform}-share`, windowFeatures);
};

/**
 * Validate share parameters
 * @param {Object} params - Share parameters
 * @returns {Object} Validation result
 */
export const validateShareParams = ({ title, excerpt, url, tags }) => {
  const errors = [];
  
  if (!title || typeof title !== 'string' || title.trim().length === 0) {
    errors.push('Title is required and must be a non-empty string');
  }
  
  if (!excerpt || typeof excerpt !== 'string' || excerpt.trim().length === 0) {
    errors.push('Excerpt is required and must be a non-empty string');
  }
  
  if (!url || typeof url !== 'string' || !isValidUrl(url)) {
    errors.push('URL is required and must be a valid URL');
  }
  
  if (tags && !Array.isArray(tags)) {
    errors.push('Tags must be an array');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Check if a string is a valid URL
 * @param {string} string - String to validate
 * @returns {boolean} Whether the string is a valid URL
 */
const isValidUrl = (string) => {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
};

/**
 * Format tags for social media (remove spaces, limit length)
 * @param {Array} tags - Array of tag strings
 * @param {number} maxTags - Maximum number of tags to include
 * @returns {Array} Formatted tags
 */
export const formatTagsForSocial = (tags = [], maxTags = 3) => {
  return tags
    .slice(0, maxTags)
    .map(tag => tag.replace(/\s+/g, '').toLowerCase())
    .filter(tag => tag.length > 0);
};

/**
 * Generate meta tags for social sharing
 * @param {Object} params - Share parameters
 * @returns {Object} Meta tags object
 */
export const generateSocialMetaTags = ({ title, excerpt, url, image, type = 'article' }) => {
  return {
    // Open Graph
    'og:title': title,
    'og:description': excerpt,
    'og:url': url,
    'og:type': type,
    'og:image': image,
    'og:site_name': 'AI Pomo',
    
    // Twitter Card
    'twitter:card': 'summary_large_image',
    'twitter:title': title,
    'twitter:description': excerpt,
    'twitter:image': image,
    'twitter:url': url,
    'twitter:site': '@aipomo',
    'twitter:creator': '@aipomo'
  };
};

/**
 * Test function to verify share URL generation
 * @param {Object} testData - Test data
 */
export const testShareUrls = (testData = {
  title: 'Test Article Title',
  excerpt: 'This is a test article excerpt for social sharing.',
  url: 'https://www.ai-pomo.com/blog/test-article',
  tags: ['productivity', 'pomodoro', 'time management']
}) => {
  console.log('Testing social share URL generation...');
  
  const validation = validateShareParams(testData);
  if (!validation.isValid) {
    console.error('Validation errors:', validation.errors);
    return;
  }
  
  const urls = generateShareUrls(testData);
  
  console.log('Generated URLs:');
  console.log('Twitter:', urls.twitter);
  console.log('Facebook:', urls.facebook);
  console.log('LinkedIn:', urls.linkedin);
  console.log('WhatsApp:', urls.whatsapp);
  
  const formattedTags = formatTagsForSocial(testData.tags);
  console.log('Formatted tags:', formattedTags);
  
  const metaTags = generateSocialMetaTags({
    ...testData,
    image: 'https://www.ai-pomo.com/og-image.jpg'
  });
  console.log('Meta tags:', metaTags);
  
  console.log('Social share URL generation test completed successfully!');
};
