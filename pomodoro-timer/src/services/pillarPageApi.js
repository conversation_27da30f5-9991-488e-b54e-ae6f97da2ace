import { api } from './apiService';

export const pillarPageApi = {
  // Get all published pillar pages
  getPillarPages: async () => {
    try {
      const response = await api.get('/pillar-pages');
      return response.data;
    } catch (error) {
      console.error('Error fetching pillar pages:', error);
      throw error;
    }
  },

  // Get all pillar pages for admin (including unpublished)
  getAllPillarPages: async () => {
    try {
      const response = await api.get('/pillar-pages/all');
      return response.data;
    } catch (error) {
      console.error('Error fetching all pillar pages:', error);
      throw error;
    }
  },

  // Get pillar page by slug (with optional cluster pages)
  getPillarPageBySlug: async (slug, includeClusterPages = true) => {
    try {
      const response = await api.get(`/pillar-pages/slug/${slug}?includeClusterPages=${includeClusterPages}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching pillar page by slug:', error);
      throw error;
    }
  },

  // Get pillar page by category (optimized)
  getPillarPageByCategory: async (category, includeClusterPages = true) => {
    try {
      const response = await api.get(`/pillar-pages/category/${category}?includeClusterPages=${includeClusterPages}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching pillar page by category:', error);
      throw error;
    }
  },

  // Get cluster pages for a pillar page (lazy loading)
  getPillarPageClusterPages: async (slug) => {
    try {
      const response = await api.get(`/pillar-pages/slug/${slug}/cluster-pages`);
      return response.data;
    } catch (error) {
      console.error('Error fetching pillar page cluster pages:', error);
      throw error;
    }
  },

  // Get pillar page by ID (for admin)
  getPillarPageById: async (id) => {
    try {
      const response = await api.get(`/pillar-pages/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching pillar page by ID:', error);
      throw error;
    }
  },

  // Create a new pillar page
  createPillarPage: async (pillarPageData) => {
    try {
      const response = await api.post('/pillar-pages', pillarPageData);
      return response.data;
    } catch (error) {
      console.error('Error creating pillar page:', error);
      throw error;
    }
  },

  // Update pillar page
  updatePillarPage: async (id, pillarPageData) => {
    try {
      const response = await api.put(`/pillar-pages/${id}`, pillarPageData);
      return response.data;
    } catch (error) {
      console.error('Error updating pillar page:', error);
      throw error;
    }
  },

  // Delete pillar page
  deletePillarPage: async (id) => {
    try {
      const response = await api.delete(`/pillar-pages/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting pillar page:', error);
      throw error;
    }
  },

  // Get available blog posts for cluster association
  getAvailableBlogPosts: async () => {
    try {
      const response = await api.get('/pillar-pages/blog-posts/available');
      return response.data;
    } catch (error) {
      console.error('Error fetching available blog posts:', error);
      throw error;
    }
  }
};
