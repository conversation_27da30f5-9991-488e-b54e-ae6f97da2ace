import { api } from './apiService';

// Use the existing api service which already has the correct base URL and auth setup

// The api service already handles auth tokens, so we don't need to add interceptors

export const blogImageApi = {
  // Get all blog images with filtering
  getBlogImages: async (params = {}) => {
    try {
      console.log('blogImageApi.getBlogImages called with params:', params);
      console.log('Making request to: /blog-images');
      const response = await api.get('/blog-images', { params });
      console.log('blogImageApi.getBlogImages response:', response);
      console.log('blogImageApi.getBlogImages response.data:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching blog images:', error);
      console.error('Error details:', error.response?.data);
      console.error('Error status:', error.response?.status);
      throw error;
    }
  },

  // Get image categories
  getImageCategories: async () => {
    try {
      console.log('blogImageApi.getImageCategories called');
      console.log('Making request to: /blog-images/categories');
      const response = await api.get('/blog-images/categories');
      console.log('blogImageApi.getImageCategories response:', response);
      console.log('blogImageApi.getImageCategories response.data:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching image categories:', error);
      console.error('Error details:', error.response?.data);
      console.error('Error status:', error.response?.status);
      throw error;
    }
  },

  // Get random image by category/tags
  getRandomImage: async (params = {}) => {
    try {
      const response = await api.get('/blog-images/random', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching random image:', error);
      throw error;
    }
  },

  // Mark image as used (increment usage count)
  markImageAsUsed: async (imageId) => {
    try {
      const response = await api.post(`/blog-images/${imageId}/use`);
      return response.data;
    } catch (error) {
      console.error('Error marking image as used:', error);
      throw error;
    }
  },

  // Admin functions
  createBlogImage: async (imageData) => {
    try {
      const response = await api.post('/blog-images', imageData);
      return response.data;
    } catch (error) {
      console.error('Error creating blog image:', error);
      throw error;
    }
  },

  updateBlogImage: async (imageId, imageData) => {
    try {
      const response = await api.put(`/blog-images/${imageId}`, imageData);
      return response.data;
    } catch (error) {
      console.error('Error updating blog image:', error);
      throw error;
    }
  },

  deleteBlogImage: async (imageId) => {
    try {
      const response = await api.delete(`/blog-images/${imageId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting blog image:', error);
      throw error;
    }
  },

  // Get image statistics (admin only)
  getImageStats: async () => {
    try {
      const response = await api.get('/blog-images/stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching image stats:', error);
      throw error;
    }
  },
};
