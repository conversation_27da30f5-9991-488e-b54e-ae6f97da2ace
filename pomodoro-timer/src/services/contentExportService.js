import { api } from './apiService';

/**
 * Export content as Markdown files
 * @param {string} type - 'all', 'blog', or 'pillar'
 * @returns {Promise<Blob>} ZIP file blob
 */
export const exportContent = async (type = 'all') => {
  try {
    const response = await api.get(`/content-export/export?type=${type}`, {
      responseType: 'blob'
    });
    
    return response.data;
  } catch (error) {
    console.error('Error exporting content:', error);
    throw error;
  }
};

/**
 * Import content from Markdown files
 * @param {File} file - ZIP file containing Markdown files
 * @param {string} mode - 'update' or 'replace'
 * @returns {Promise<Object>} Import results
 */
export const importContent = async (file, mode = 'update') => {
  try {
    console.log('API: Starting import with file:', file, 'mode:', mode); // Debug log

    const formData = new FormData();
    formData.append('importFile', file);
    formData.append('mode', mode);

    console.log('API: FormData created:', formData); // Debug log

    // Log FormData contents
    for (let [key, value] of formData.entries()) {
      console.log('FormData entry:', key, value);
    }

    // Create a custom config for this request to override the default transformRequest
    const config = {
      headers: {
        // Don't set Content-Type, let the browser set it with boundary
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      transformRequest: [(data) => data] // Don't transform FormData
    };

    const response = await api.post('/content-export/import', formData, config);

    console.log('API: Import response:', response.data); // Debug log
    return response.data;
  } catch (error) {
    console.error('API: Error importing content:', error);
    console.error('API: Error response:', error.response?.data);
    throw error;
  }
};

/**
 * Download a blob as a file
 * @param {Blob} blob - File blob
 * @param {string} filename - Filename for download
 */
export const downloadBlob = (blob, filename) => {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};
