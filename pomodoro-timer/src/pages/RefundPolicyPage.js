import React from 'react';
import styled from 'styled-components';
import SEOHead from '../components/SEO/SEOHead';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import { createWebsiteStructuredData } from '../utils/structuredData';

const RefundPolicyPage = () => {
  const structuredData = [createWebsiteStructuredData()];

  return (
    <>
      <SEOHead
        title="Refund Policy | AI Pomo - AI Pomodoro Timer Refund Terms"
        description="Read AI Pomo's Refund Policy to understand our refund terms, conditions, and process for our AI-powered Pomodoro timer and productivity platform."
        keywords="AI Pomo refund policy, refund terms, money back guarantee, subscription refund, digital product refund, Pomodoro app refund"
        url="https://www.ai-pomo.com/refund-policy"
        structuredData={structuredData}
      />

      <Container>
        <PageHeader />

        <ContentSection>
          <ContentContainer>
            <PageTitle>Refund Policy</PageTitle>
            <LastUpdated>Last updated: May 2025</LastUpdated>

            <Section>
              <SectionTitle>1. Overview</SectionTitle>
              <SectionText>
                At AI Pomo, developed by Shenzhen Juleplay Technology Co. Ltd, we strive to provide exceptional value through our AI-enhanced Pomodoro timer and
                productivity platform. This Refund Policy outlines the terms and conditions under which refunds
                may be requested and processed for our digital services.
              </SectionText>
              <SectionText>
                Due to the digital nature of our services and the immediate access provided upon purchase,
                all sales are generally considered final. However, we understand that exceptional circumstances
                may arise, and we are committed to fair and reasonable customer service.
              </SectionText>
            </Section>

            <Section>
              <SectionTitle>2. Subscription Plans and Refund Eligibility</SectionTitle>

              <SubSection>
                <SubSectionTitle>2.1 Free Plan</SubSectionTitle>
                <SectionText>
                  Our Free Plan provides access to basic features at no cost. Since no payment is required,
                  refunds are not applicable for the Free Plan.
                </SectionText>
              </SubSection>

              <SubSection>
                <SubSectionTitle>2.2 Premium Annual Subscription ($30/year)</SubSectionTitle>
                <SectionText>
                  Premium annual subscriptions may be eligible for refunds under the following conditions:
                </SectionText>
                <List>
                  <ListItem>Technical issues that prevent access to premium features for more than 7 consecutive days</ListItem>
                  <ListItem>Billing errors or unauthorized charges</ListItem>
                  <ListItem>Service unavailability due to our technical failures (not including scheduled maintenance)</ListItem>
                  <ListItem>Requests made within 7 days of initial purchase for first-time subscribers</ListItem>
                </List>
              </SubSection>

              <SubSection>
                <SubSectionTitle>2.3 Lifetime Plan ($60 one-time payment)</SubSectionTitle>
                <SectionText>
                  Lifetime plan purchases may be eligible for refunds under limited circumstances:
                </SectionText>
                <List>
                  <ListItem>Technical issues that prevent access to the service for more than 7 consecutive days</ListItem>
                  <ListItem>Billing errors or unauthorized charges</ListItem>
                  <ListItem>Requests made within 7 days of purchase for first-time customers</ListItem>
                </List>
              </SubSection>
            </Section>

            <Section>
              <SectionTitle>3. Refund Request Process</SectionTitle>

              <SubSection>
                <SubSectionTitle>3.1 How to Request a Refund</SubSectionTitle>
                <SectionText>To request a refund, please contact us with the following information:</SectionText>
                <List>
                  <ListItem>Your account email address</ListItem>
                  <ListItem>Order/transaction ID or payment reference</ListItem>
                  <ListItem>Detailed reason for the refund request</ListItem>
                  <ListItem>Supporting documentation (if applicable)</ListItem>
                </List>
              </SubSection>

              <SubSection>
                <SubSectionTitle>3.2 Contact Methods</SubSectionTitle>
                <ContactInfo>
                  <ContactItem><strong>Email:</strong> <EMAIL></ContactItem>
                  <ContactItem><strong>Contact Form:</strong> <a href="/contact">https://www.ai-pomo.com/contact</a></ContactItem>
                  <ContactItem><strong>Response Time:</strong> We aim to respond within 48 hours</ContactItem>
                </ContactInfo>
              </SubSection>

              <SubSection>
                <SubSectionTitle>3.3 Review Process</SubSectionTitle>
                <SectionText>
                  All refund requests will be reviewed on a case-by-case basis. We will:
                </SectionText>
                <List>
                  <ListItem>Acknowledge receipt of your request within 48 hours</ListItem>
                  <ListItem>Review your account usage and payment history</ListItem>
                  <ListItem>Investigate any technical issues reported</ListItem>
                  <ListItem>Provide a decision within 5-7 business days</ListItem>
                </List>
              </SubSection>
            </Section>

            <Section>
              <SectionTitle>4. Refund Processing</SectionTitle>

              <SubSection>
                <SubSectionTitle>4.1 Approved Refunds</SubSectionTitle>
                <SectionText>
                  If your refund request is approved, we will process the refund using the original payment method:
                </SectionText>
                <List>
                  <ListItem><strong>PayPal payments:</strong> 3-5 business days</ListItem>
                  <ListItem><strong>USDT (TRC20) payments:</strong> 1-2 business days to your provided wallet address</ListItem>
                  <ListItem><strong>Credit/Debit cards:</strong> 5-10 business days (depending on your bank)</ListItem>
                </List>
              </SubSection>

              <SubSection>
                <SubSectionTitle>4.2 Partial Refunds</SubSectionTitle>
                <SectionText>
                  In some cases, we may offer partial refunds based on:
                </SectionText>
                <List>
                  <ListItem>Usage duration (pro-rated refunds for annual subscriptions)</ListItem>
                  <ListItem>Extent of service disruption experienced</ListItem>
                  <ListItem>Specific circumstances of the request</ListItem>
                </List>
              </SubSection>
            </Section>

            <Section>
              <SectionTitle>5. Non-Refundable Circumstances</SectionTitle>
              <SectionText>
                Refunds will not be provided in the following situations:
              </SectionText>
              <List>
                <ListItem>Change of mind or buyer's remorse after using the service</ListItem>
                <ListItem>Failure to use the service or access premium features</ListItem>
                <ListItem>Violation of our Terms of Service resulting in account suspension</ListItem>
                <ListItem>Requests made after the applicable refund period has expired</ListItem>
                <ListItem>Technical issues caused by user's device, internet connection, or third-party software</ListItem>
                <ListItem>Scheduled maintenance or minor service interruptions</ListItem>
                <ListItem>Dissatisfaction with AI-generated content or recommendations</ListItem>
              </List>
            </Section>

            <Section>
              <SectionTitle>6. Account Access After Refund</SectionTitle>
              <SectionText>
                Upon processing a refund:
              </SectionText>
              <List>
                <ListItem>Premium features will be immediately revoked</ListItem>
                <ListItem>Your account will revert to the Free Plan</ListItem>
                <ListItem>All data and projects will be preserved</ListItem>
                <ListItem>You may re-subscribe at any time</ListItem>
              </List>
            </Section>

            <Section>
              <SectionTitle>7. Chargebacks and Disputes</SectionTitle>
              <SectionText>
                We encourage customers to contact us directly before initiating chargebacks or payment disputes.
                Chargebacks may result in:
              </SectionText>
              <List>
                <ListItem>Immediate suspension of account access</ListItem>
                <ListItem>Additional processing fees</ListItem>
                <ListItem>Requirement to resolve the dispute before account reactivation</ListItem>
              </List>
              <SectionText>
                We are committed to resolving issues fairly and promptly through direct communication.
              </SectionText>
            </Section>

            <Section>
              <SectionTitle>8. Changes to This Refund Policy</SectionTitle>
              <SectionText>
                We reserve the right to modify this Refund Policy at any time. Changes will be effective immediately
                upon posting on our website. We will notify users of material changes through:
              </SectionText>
              <List>
                <ListItem>Email notifications to registered users</ListItem>
                <ListItem>In-app notifications</ListItem>
                <ListItem>Website announcements</ListItem>
              </List>
            </Section>

            <Section>
              <SectionTitle>9. Contact Information</SectionTitle>
              <SectionText>
                For refund requests or questions about this policy, please contact us:
              </SectionText>
              <ContactInfo>
                <ContactItem><strong>Company:</strong> Shenzhen Juleplay Technology Co. Ltd</ContactItem>
                <ContactItem><strong>Support & Refunds:</strong> <EMAIL></ContactItem>
                <ContactItem><strong>Contact Form:</strong> <a href="/contact">https://www.ai-pomo.com/contact</a></ContactItem>
                <ContactItem><strong>Business Hours:</strong> Monday-Friday, 9 AM - 6 PM UTC</ContactItem>
              </ContactInfo>
            </Section>
          </ContentContainer>
        </ContentSection>

        <Footer />
      </Container>
    </>
  );
};

// Styled Components (reusing from other policy pages)
const Container = styled.div`
  min-height: 100vh;
  background-color: #ffffff;
`;

const ContentSection = styled.section`
  padding: 6rem 2rem 4rem;
  max-width: 800px;
  margin: 0 auto;
`;

const ContentContainer = styled.div`
  background: white;
  padding: 3rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
`;

const PageTitle = styled.h1`
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
`;

const LastUpdated = styled.p`
  font-size: 1rem;
  color: #4a5568;
  margin-bottom: 3rem;
  font-style: italic;
`;

const Section = styled.section`
  margin-bottom: 3rem;
`;

const SectionTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
`;

const SubSection = styled.div`
  margin-bottom: 2rem;
`;

const SubSectionTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.75rem;
`;

const SectionText = styled.p`
  font-size: 1rem;
  line-height: 1.6;
  color: #4a5568;
  margin-bottom: 1rem;
`;

const List = styled.ul`
  margin: 1rem 0;
  padding-left: 1.5rem;
`;

const ListItem = styled.li`
  font-size: 1rem;
  line-height: 1.6;
  color: #4a5568;
  margin-bottom: 0.5rem;

  strong {
    color: #2d3748;
    font-weight: 600;
  }
`;

const ContactInfo = styled.div`
  background: #f7fafc;
  padding: 1.5rem;
  border-radius: 8px;
  margin: 1rem 0;
`;

const ContactItem = styled.p`
  font-size: 1rem;
  color: #4a5568;
  margin-bottom: 0.5rem;

  strong {
    color: #2d3748;
    font-weight: 600;
  }

  a {
    color: #d95550;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
`;

export default RefundPolicyPage;
