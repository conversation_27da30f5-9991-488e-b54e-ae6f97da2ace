import React, { useState } from 'react';
import styled from 'styled-components';
import { FaEnvelope, FaPhone, FaMapMarkerAlt, FaQuestionCircle, FaRocket, FaUsers } from 'react-icons/fa';
import SEOHead from '../components/SEO/SEOHead';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import { createWebsiteStructuredData } from '../utils/structuredData';
import axios from 'axios';

const ContactPage = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    subject: '',
    message: '',
    inquiryType: 'general'
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  const structuredData = [createWebsiteStructuredData()];

  // Create axios instance for API calls
  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';
  const api = axios.create({
    baseURL: API_URL,
    headers: {
      'Content-Type': 'application/json'
    }
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus(null);

    try {
      // Send form data to backend API
      const response = await api.post('/contact', {
        name: formData.name,
        email: formData.email,
        message: `Subject: ${formData.subject}\n\nCompany: ${formData.company || 'Not provided'}\n\nMessage:\n${formData.message}`,
        type: formData.inquiryType
      });

      console.log('Contact form submission response:', response.data);

      if (response.data.success) {
        setSubmitStatus('success');
        setFormData({
          name: '',
          email: '',
          company: '',
          subject: '',
          message: '',
          inquiryType: 'general'
        });
      } else {
        setSubmitStatus('error');
      }
    } catch (error) {
      console.error('Error submitting contact form:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const inquiryTypes = [
    { value: 'general', label: 'General Inquiry', icon: <FaQuestionCircle /> },
    { value: 'sales', label: 'Sales & Pricing', icon: <FaRocket /> },
    { value: 'enterprise', label: 'Enterprise Solutions', icon: <FaUsers /> },
    { value: 'support', label: 'Technical Support', icon: <FaEnvelope /> }
  ];

  return (
    <>
      <SEOHead
        title="Contact AI Pomo | AI Pomodoro Timer Support & Enterprise Sales"
        description="Contact AI Pomo for AI Pomodoro timer support, enterprise productivity solutions, pricing questions, or technical help. Get expert assistance for your productivity needs."
        keywords="contact AI Pomo, AI Pomodoro timer support, enterprise productivity solutions, Pomodoro app help, productivity software support, AI timer customer service"
        url="https://www.ai-pomo.com/contact"
        structuredData={structuredData}
      />

      <Container>
        <PageHeader />

        <HeroSection>
          <HeroContent>
            <HeroTitle>Contact Our AI Productivity Experts</HeroTitle>
            <HeroSubtitle>
              Have questions about our AI Pomodoro timer? Need help implementing productivity solutions
              for your team? Our experts are here to help you maximize focus, manage time effectively,
              and transform your workflow with AI-enhanced productivity tools.
            </HeroSubtitle>
          </HeroContent>
        </HeroSection>

        <ContactSection>
          <ContactContainer>
            <ContactInfo>
              <InfoTitle>Let's Discuss Your Productivity Goals</InfoTitle>
              <InfoDescription>
                Whether you're a student looking to improve study habits with Pomodoro techniques,
                a professional seeking AI-powered project management, or an enterprise needing
                organization-wide productivity solutions, we're here to help you succeed.
              </InfoDescription>

              <ContactMethods>
                <ContactMethod>
                  <MethodIcon><FaEnvelope /></MethodIcon>
                  <MethodContent>
                    <MethodTitle>Email Us</MethodTitle>
                    <MethodText><EMAIL></MethodText>
                    <MethodSubtext>We typically respond within 24 hours</MethodSubtext>
                  </MethodContent>
                </ContactMethod>
              </ContactMethods>

              <InquiryTypes>
                <InquiryTitle>What can we help you with?</InquiryTitle>
                <InquiryGrid>
                  {inquiryTypes.map((type) => (
                    <InquiryType key={type.value}>
                      <InquiryIcon>{type.icon}</InquiryIcon>
                      <InquiryLabel>{type.label}</InquiryLabel>
                    </InquiryType>
                  ))}
                </InquiryGrid>
              </InquiryTypes>
            </ContactInfo>

            <ContactForm onSubmit={handleSubmit}>
              <FormTitle>Send us a Message</FormTitle>

              <FormRow>
                <FormGroup>
                  <Label htmlFor="name">Full Name *</Label>
                  <Input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup>
                  <Label htmlFor="company">Company</Label>
                  <Input
                    type="text"
                    id="company"
                    name="company"
                    value={formData.company}
                    onChange={handleInputChange}
                  />
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="inquiryType">Inquiry Type</Label>
                  <Select
                    id="inquiryType"
                    name="inquiryType"
                    value={formData.inquiryType}
                    onChange={handleInputChange}
                  >
                    {inquiryTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </Select>
                </FormGroup>
              </FormRow>

              <FormGroup>
                <Label htmlFor="subject">Subject *</Label>
                <Input
                  type="text"
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  required
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="message">Message *</Label>
                <Textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  rows={6}
                  required
                />
              </FormGroup>

              {submitStatus === 'success' && (
                <SuccessMessage>
                  Your message has been received! We'll get back to you within 24 hours.
                </SuccessMessage>
              )}

              {submitStatus === 'error' && (
                <ErrorMessage>
                  Sorry, there was an error sending your message. Please try again or email us directly.
                </ErrorMessage>
              )}

              <SubmitButton type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Sending...' : 'Send Message'}
              </SubmitButton>
            </ContactForm>
          </ContactContainer>
        </ContactSection>

        <FAQSection>
          <FAQTitle>Frequently Asked Questions About AI Pomo</FAQTitle>
          <FAQGrid>
            <FAQItem>
              <FAQQuestion>How quickly can I start using the AI Pomodoro timer?</FAQQuestion>
              <FAQAnswer>You can start using AI Pomo's AI Pomodoro timer immediately after signing up. Our AI begins learning your productivity patterns from day one, and you'll see focus improvements within your first week of using structured Pomodoro sessions.</FAQAnswer>
            </FAQItem>
            <FAQItem>
              <FAQQuestion>What's included in the free AI Pomodoro app?</FAQQuestion>
              <FAQAnswer>The free plan includes up to 3 active projects, AI-enhanced Pomodoro timer functionality, task management, and limited AI-powered project generation. Perfect for students and individual users getting started with Pomodoro techniques.</FAQAnswer>
            </FAQItem>
            <FAQItem>
              <FAQQuestion>How does AI-assisted project planning work?</FAQQuestion>
              <FAQAnswer>Simply describe your project goals, and our AI will break it down into actionable tasks with estimated Pomodoro counts, deadlines, and milestones. It learns from your work patterns to provide increasingly accurate time estimates and project structures.</FAQAnswer>
            </FAQItem>
            <FAQItem>
              <FAQQuestion>Can I use AI Pomo offline?</FAQQuestion>
              <FAQAnswer>AI Pomo works best with an internet connection for AI features and data sync. However, basic timer functionality works offline, and your data will sync when you reconnect.</FAQAnswer>
            </FAQItem>
            <FAQItem>
              <FAQQuestion>Do you offer enterprise AI productivity solutions?</FAQQuestion>
              <FAQAnswer>Yes, we offer custom enterprise productivity solutions with unlimited projects, team Pomodoro coordination, advanced analytics, priority support, and volume discounts for organizations implementing AI-powered time management.</FAQAnswer>
            </FAQItem>
            <FAQItem>
              <FAQQuestion>How much does the AI Pomodoro timer cost?</FAQQuestion>
              <FAQAnswer>AI Pomo offers a free plan with basic features, Premium plans ($30/year or $60 lifetime) with unlimited projects and AI features, plus custom enterprise pricing for organizations. All plans include our core AI Pomodoro timer.</FAQAnswer>
            </FAQItem>
            <FAQItem>
              <FAQQuestion>Can I integrate AI Pomo with my existing tools?</FAQQuestion>
              <FAQAnswer>AI Pomo integrates with popular productivity tools and supports data export. We're continuously adding new integrations based on user feedback and requests.</FAQAnswer>
            </FAQItem>
            <FAQItem>
              <FAQQuestion>Is my data secure and private?</FAQQuestion>
              <FAQAnswer>Absolutely. We use enterprise-grade security, encrypt all data in transit and at rest, and never share your personal productivity data with third parties. Your privacy is our top priority.</FAQAnswer>
            </FAQItem>
            <FAQItem>
              <FAQQuestion>What payment methods do you accept?</FAQQuestion>
              <FAQAnswer>We accept PayPal for convenient online payments and USDT (TRC20) cryptocurrency for users who prefer crypto payments. Both methods are secure and processed instantly.</FAQAnswer>
            </FAQItem>
          </FAQGrid>
        </FAQSection>

        <Footer />
      </Container>
    </>
  );
};

// Styled Components
const Container = styled.div`
  min-height: 100vh;
  background-color: #ffffff;
`;

const HeroSection = styled.section`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-image: url('/images/content-banner-blue-no-text.svg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  color: white;
  padding: 10rem 2rem 6rem;
  text-align: center;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1;
  }

  > * {
    position: relative;
    z-index: 2;
  }
`;

const HeroContent = styled.div`
  max-width: 600px;
  margin: 0 auto;
`;

const HeroTitle = styled.h1`
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const HeroSubtitle = styled.p`
  font-size: 1.25rem;
  line-height: 1.6;
  opacity: 0.9;
`;

const ContactSection = styled.section`
  padding: 6rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const ContactContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;

  @media (max-width: 968px) {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
`;

const ContactInfo = styled.div``;

const InfoTitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #2d3748;
`;

const InfoDescription = styled.p`
  font-size: 1.1rem;
  line-height: 1.6;
  color: #4a5568;
  margin-bottom: 3rem;
`;

const ContactMethods = styled.div`
  margin-bottom: 3rem;
`;

const ContactMethod = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 2rem;
`;

const MethodIcon = styled.div`
  font-size: 1.5rem;
  color: #d95550;
  margin-top: 0.25rem;
`;

const MethodContent = styled.div``;

const MethodTitle = styled.h3`
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.25rem;
`;

const MethodText = styled.p`
  font-size: 1rem;
  color: #4a5568;
  margin-bottom: 0.25rem;
`;

const MethodSubtext = styled.p`
  font-size: 0.9rem;
  color: #718096;
`;

const InquiryTypes = styled.div``;

const InquiryTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1.5rem;
`;

const InquiryGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
`;

const InquiryType = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #f7fafc;
  border-radius: 8px;
`;

const InquiryIcon = styled.div`
  font-size: 1.25rem;
  color: #d95550;
`;

const InquiryLabel = styled.span`
  font-size: 0.9rem;
  color: #4a5568;
  font-weight: 500;
`;

const ContactForm = styled.form`
  background: #f7fafc;
  padding: 2.5rem;
  border-radius: 12px;
`;

const FormTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 2rem;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;

  @media (max-width: 640px) {
    grid-template-columns: 1fr;
  }
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`;

const Label = styled.label`
  display: block;
  font-size: 0.9rem;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 0.5rem;
`;

const Input = styled.input`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #d95550;
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 1rem;
  background: white;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #d95550;
  }
`;

const Textarea = styled.textarea`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 1rem;
  resize: vertical;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #d95550;
  }
`;

const SubmitButton = styled.button`
  background-color: #d95550;
  color: white;
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover:not(:disabled) {
    background-color: #c04540;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const SuccessMessage = styled.div`
  background: #f0fff4;
  color: #22543d;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  border: 1px solid #9ae6b4;
`;

const ErrorMessage = styled.div`
  background: #fed7d7;
  color: #742a2a;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  border: 1px solid #feb2b2;
`;

const FAQSection = styled.section`
  background: #f7fafc;
  padding: 4rem 2rem;
`;

const FAQTitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  color: #2d3748;
`;

const FAQGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
`;

const FAQItem = styled.div`
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
`;

const FAQQuestion = styled.h3`
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.75rem;
`;

const FAQAnswer = styled.p`
  font-size: 1rem;
  color: #4a5568;
  line-height: 1.5;
`;

export default ContactPage;
