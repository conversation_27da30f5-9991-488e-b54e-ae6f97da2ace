import React from 'react';
import { FaCode, FaBug, FaRocket } from 'react-icons/fa';
import ScenarioTimerPage from '../components/Timer/ScenarioTimerPage';

const DeveloperCodingTimerPage = () => {
  const developerConfig = {
    scenario: 'Developer Coding',
    title: 'Developer Coding Timer - Programming Productivity Timer | AI Pomo',
    description: 'Specialized timer for developers and programmers. Optimize coding sessions, debugging, code reviews, and maintain flow state for maximum programming productivity.',
    keywords: 'coding timer, programming timer, developer productivity, debugging timer, code review timer, software development',
    url: 'https://www.ai-pomo.com/developer-coding-timer',
    heroTitle: 'Developer Coding Timer',
    heroDescription: 'Enter the flow state and maximize your coding productivity. Designed for developers to optimize programming sessions, debugging, and technical work.',
    defaultMinutes: 90,
    gradientColors: ['#10b981', '#059669'],
    backgroundColor: 'linear-gradient(135deg, #ecfdf5, #d1fae5)',
    
    scenarios: [
      {
        emoji: '💻',
        title: 'Deep Coding',
        description: 'Complex feature development and algorithm implementation',
        recommendedTime: 'Recommended 90-120 minutes'
      },
      {
        emoji: '🐛',
        title: 'Debugging Sessions',
        description: 'Finding and fixing bugs, troubleshooting issues',
        recommendedTime: 'Recommended 45-90 minutes'
      },
      {
        emoji: '👁️',
        title: 'Code Reviews',
        description: 'Reviewing pull requests and team code contributions',
        recommendedTime: 'Recommended 30-60 minutes'
      },
      {
        emoji: '📚',
        title: 'Learning & Research',
        description: 'Reading documentation, tutorials, and new technologies',
        recommendedTime: 'Recommended 25-45 minutes'
      }
    ],

    benefits: [
      {
        emoji: '🔥',
        title: 'Flow State',
        description: 'Extended focus periods help you achieve and maintain deep programming flow'
      },
      {
        emoji: '⚡',
        title: 'Code Quality',
        description: 'Uninterrupted coding sessions produce cleaner, more thoughtful code'
      },
      {
        emoji: '🚀',
        title: 'Faster Development',
        description: 'Reduced context switching leads to faster feature completion'
      },
      {
        emoji: '🧠',
        title: 'Problem Solving',
        description: 'Extended focus time improves complex problem-solving abilities'
      }
    ],

    tips: [
      {
        title: 'Plan Before Coding',
        description: 'Spend 5-10 minutes outlining your approach before starting the timer'
      },
      {
        title: 'Minimize Interruptions',
        description: 'Turn off notifications, close social media, and inform team of focus time'
      },
      {
        title: 'Use Longer Sessions',
        description: 'Programming often requires 60-90 minute blocks to build and maintain context'
      },
      {
        title: 'Take Proper Breaks',
        description: 'Step away from screen, stretch, and let your subconscious work on problems'
      },
      {
        title: 'Document Progress',
        description: 'Keep notes of what you accomplished and next steps for smooth transitions'
      }
    ],

    customFeatures: [
      {
        icon: <FaCode />,
        title: 'Flow State Optimization',
        description: 'Extended timing designed for deep programming concentration'
      },
      {
        icon: <FaBug />,
        title: 'Debug Mode',
        description: 'Flexible timing for unpredictable debugging and problem-solving'
      },
      {
        icon: <FaRocket />,
        title: 'Sprint Integration',
        description: 'Perfect for agile development cycles and sprint planning'
      }
    ],

    relatedTimers: [
      {
        title: 'Work Timer',
        description: 'General professional tasks',
        duration: '25-90 minutes',
        url: '/work-timer'
      },
      {
        title: 'Study Timer',
        description: 'Learning new technologies',
        duration: '25-60 minutes',
        url: '/study-timer'
      },
      {
        title: 'Break Timer',
        description: 'Essential coding breaks',
        duration: '10-20 minutes',
        url: '/break-timer'
      }
    ]
  };

  return <ScenarioTimerPage config={developerConfig} />;
};

export default DeveloperCodingTimerPage;