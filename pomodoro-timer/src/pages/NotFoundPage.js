import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { FaHome, FaBlog, FaSearch, FaArrowLeft } from 'react-icons/fa';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import SEOHead from '../components/SEO/SEOHead';

const NotFoundPage = () => {
  return (
    <>
      <SEOHead
        title="Page Not Found | AI Pomo"
        description="The page you're looking for doesn't exist. Explore our AI-powered Pomodoro timer and productivity tools."
        keywords="404, not found, AI Pomo, pomodoro timer, productivity"
        url="https://www.ai-pomo.com/404"
        type="website"
        robots="noindex, nofollow"
      />
      <PageHeader />
      <Container>
        <ErrorContent>
          <ErrorNumber>404</ErrorNumber>
          <ErrorTitle>Page Not Found</ErrorTitle>
          <ErrorDescription>
            The page you're looking for doesn't exist or has been moved.
            Don't worry, let's get you back on track!
          </ErrorDescription>

          <ActionButtons>
            <ActionButton as={Link} to="/" $primary>
              <FaHome />
              <span>Go Home</span>
            </ActionButton>
            <ActionButton as={Link} to="/blog">
              <FaBlog />
              <span>Read Blog</span>
            </ActionButton>
          </ActionButtons>

          <SuggestionsSection>
            <SectionTitle>Popular Pages</SectionTitle>
            <SuggestionsList>
              <SuggestionItem as={Link} to="/features">
                Features & Benefits
              </SuggestionItem>
              <SuggestionItem as={Link} to="/use-cases">
                Use Cases
              </SuggestionItem>
              <SuggestionItem as={Link} to="/pricing">
                Pricing Plans
              </SuggestionItem>
              <SuggestionItem as={Link} to="/pomodoro-for-studying">
                Pomodoro for Studying
              </SuggestionItem>
              <SuggestionItem as={Link} to="/pomodoro-for-adhd">
                Pomodoro for ADHD
              </SuggestionItem>
              <SuggestionItem as={Link} to="/contact">
                Contact Us
              </SuggestionItem>
            </SuggestionsList>
          </SuggestionsSection>

          <SearchSection>
            <SectionTitle>Search Our Content</SectionTitle>
            <SearchDescription>
              Looking for something specific? Try searching our blog for articles on productivity, time management, and focus techniques.
            </SearchDescription>
            <SearchButton as={Link} to="/blog">
              <FaSearch />
              <span>Search Blog</span>
            </SearchButton>
          </SearchSection>

          <BackButton onClick={() => window.history.back()}>
            <FaArrowLeft />
            <span>Go Back</span>
          </BackButton>
        </ErrorContent>
      </Container>
      <Footer />
    </>
  );
};

// Styled Components
const Container = styled.div`
  min-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 7rem 2rem 4rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #fff 100%);

  @media (max-width: 768px) {
    padding: 5rem 1rem 2rem;
    min-height: 70vh;
  }
`;

const ErrorContent = styled.div`
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
`;

const ErrorNumber = styled.h1`
  font-size: 8rem;
  font-weight: 800;
  background: linear-gradient(135deg, #d95550, #c44a45);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  line-height: 1;

  @media (max-width: 768px) {
    font-size: 6rem;
  }

  @media (max-width: 480px) {
    font-size: 4rem;
  }
`;

const ErrorTitle = styled.h2`
  font-size: 2.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    font-size: 2rem;
  }

  @media (max-width: 480px) {
    font-size: 1.5rem;
  }
`;

const ErrorDescription = styled.p`
  font-size: 1.125rem;
  color: #6b7280;
  margin-bottom: 3rem;
  line-height: 1.6;

  @media (max-width: 768px) {
    font-size: 1rem;
    margin-bottom: 2rem;
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 4rem;

  @media (max-width: 480px) {
    flex-direction: column;
    align-items: center;
    margin-bottom: 3rem;
  }
`;

const ActionButton = styled.a`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 2px solid transparent;

  ${props => props.$primary ? `
    background: linear-gradient(135deg, #d95550, #c44a45);
    color: white;
    box-shadow: 0 4px 12px rgba(217, 85, 80, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(217, 85, 80, 0.4);
    }
  ` : `
    background: white;
    color: #6b7280;
    border-color: #e5e7eb;

    &:hover {
      border-color: #d95550;
      color: #d95550;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  `}

  @media (max-width: 480px) {
    width: 200px;
    justify-content: center;
  }
`;

const SuggestionsSection = styled.section`
  margin-bottom: 3rem;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
`;

const SectionTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1.5rem;
`;

const SuggestionsList = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
`;

const SuggestionItem = styled.a`
  display: block;
  padding: 0.75rem 1rem;
  background: #f8f9fa;
  border-radius: 6px;
  text-decoration: none;
  color: #6b7280;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid #e5e7eb;

  &:hover {
    background: #d95550;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(217, 85, 80, 0.3);
  }
`;

const SearchSection = styled.section`
  margin-bottom: 3rem;
  padding: 2rem;
  background: linear-gradient(135deg, #f1f3f4, #f8f9fa);
  border-radius: 12px;
  border: 1px solid #e5e7eb;
`;

const SearchDescription = styled.p`
  color: #6b7280;
  margin-bottom: 1.5rem;
  line-height: 1.6;
`;

const SearchButton = styled.a`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #4f46e5, #3b82f6);
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
  }
`;

const BackButton = styled.button`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: #6b7280;
  font-size: 0.9rem;
  cursor: pointer;
  transition: color 0.2s ease;

  &:hover {
    color: #d95550;
  }
`;

export default NotFoundPage;