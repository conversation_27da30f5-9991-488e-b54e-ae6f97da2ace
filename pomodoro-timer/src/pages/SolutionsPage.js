import React from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { FaB<PERSON>ing, FaUsers, FaUserTie, FaGraduationCap, FaHome, FaIndustry } from 'react-icons/fa';
import SEOHead from '../components/SEO/SEOHead';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import { createWebsiteStructuredData } from '../utils/structuredData';

const SolutionsPage = () => {
  const structuredData = [createWebsiteStructuredData()];

  const solutions = [
    {
      icon: <FaBuilding />,
      title: "Enterprise Productivity Solutions",
      subtitle: "For Large Organizations & Corporations",
      description: "Comprehensive AI-powered productivity solutions designed for enterprise teams. AI Pomo Enterprise provides advanced analytics, team management, and seamless integration capabilities that scale across departments and global teams.",
      features: [
        "Enterprise-wide productivity dashboards and team analytics",
        "Advanced AI project management with intelligent task generation",
        "Deep integration with enterprise tools (<PERSON>lack, Microsoft Teams, <PERSON><PERSON>, <PERSON><PERSON>)",
        "Custom reporting and organizational productivity metrics",
        "Enterprise-grade security with SSO and compliance features",
        "Dedicated account management and priority support"
      ],
      benefits: [
        "Standardize productivity practices across all departments",
        "Streamline project planning with AI-assisted task breakdown",
        "Improve focus time and reduce meeting overhead",
        "Data-driven insights for better resource allocation and planning"
      ]
    },
    {
      icon: <FaUsers />,
      title: "Team Time Management Tools",
      subtitle: "For Small to Medium Teams",
      description: "Perfect for growing teams that need structured productivity without enterprise complexity. Our team time management tools help coordinate Pomodoro sessions, share project progress, and maintain collective focus with AI-enhanced collaborative workflows.",
      features: [
        "Shared project workspaces with team Pomodoro tracking",
        "Collaborative team progress tracking and milestone reporting",
        "Synchronized focus sessions for team-wide productivity",
        "AI-generated project templates and workflow best practices",
        "Team calendar integration with shared deadline management",
        "Real-time collaboration on tasks with time tracking"
      ],
      benefits: [
        "Improved team coordination through structured time management",
        "Consistent Pomodoro practices across all team members",
        "Enhanced project visibility and team accountability",
        "Reduced meeting overhead through better collaborative planning"
      ]
    },
    {
      icon: <FaUserTie />,
      title: "Business Pomodoro Software",
      subtitle: "For Consultants, Agencies & Professional Services",
      description: "Specialized business Pomodoro software designed for professional services that need precise time tracking, client project management, and billable hour reporting. Perfect for consultants, agencies, freelancers, and service providers who bill by time.",
      features: [
        "Client-specific project separation with Pomodoro time tracking",
        "Detailed billable hours reporting and invoicing integration",
        "Client-specific productivity analytics and time investment reports",
        "Project profitability analysis with time-based cost tracking",
        "Resource allocation optimization across multiple client projects",
        "Professional client reporting with detailed time breakdowns"
      ],
      benefits: [
        "Accurate Pomodoro-based time tracking for transparent billing",
        "Improved project profitability through better time management",
        "Enhanced client satisfaction with detailed progress reporting",
        "Streamlined project management with AI-assisted planning"
      ]
    },
    {
      icon: <FaGraduationCap />,
      title: "Educational Productivity Solutions",
      subtitle: "For Schools, Universities & Academic Institutions",
      description: "Comprehensive productivity solutions designed for educational environments where students and faculty need structured learning, research time, and academic project management. Supports both individual study sessions and collaborative academic research projects.",
      features: [
        "Student Pomodoro tracking for improved study habits and focus",
        "Academic research project management with AI-assisted planning",
        "Study session optimization and academic scheduling tools",
        "Academic milestone tracking and deadline management systems",
        "Collaborative research project support with team coordination",
        "Educational analytics and academic progress reporting"
      ],
      benefits: [
        "Enhanced student focus and academic performance through structured study",
        "Better organization of research projects and academic workflows",
        "Improved study habits and time management skills for students",
        "Data-driven insights for educational improvement and student success"
      ]
    },
    {
      icon: <FaHome />,
      title: "Remote Work Productivity Solutions",
      subtitle: "For Distributed & Hybrid Teams",
      description: "Specialized AI productivity solutions designed for remote and hybrid work environments. Help distributed teams maintain focus, coordinate across time zones, and preserve work-life balance while working from anywhere in the world.",
      features: [
        "Remote team Pomodoro synchronization and coordination tools",
        "Home office productivity optimization with distraction management",
        "Work-life balance tracking with focus session recommendations",
        "Asynchronous collaboration tools with time zone awareness",
        "Remote meeting optimization and focus time protection",
        "Distributed team performance analytics and productivity insights"
      ],
      benefits: [
        "Maintained productivity and focus in remote work environments",
        "Improved work-life balance through structured remote work patterns",
        "Enhanced remote team collaboration and coordination",
        "Reduced remote work isolation through shared productivity practices"
      ]
    },
    {
      icon: <FaIndustry />,
      title: "Industry-Specific AI Productivity Solutions",
      subtitle: "For Healthcare, Finance, Legal & Technology Sectors",
      description: "Customized AI productivity solutions tailored for specific industries including healthcare, finance, legal, and technology sectors. Each solution addresses industry-specific workflows, compliance requirements, and professional productivity challenges.",
      features: [
        "Industry-specific AI project templates and workflow optimization",
        "Compliance-ready reporting and documentation with audit trails",
        "Specialized integrations for industry-standard tools and platforms",
        "Custom productivity metrics and KPIs for your specific sector",
        "Industry best practices integration and productivity benchmarking",
        "Regulatory compliance support with secure data handling"
      ],
      benefits: [
        "Industry-optimized productivity workflows that match sector requirements",
        "Full compliance with sector-specific regulations and standards",
        "Performance benchmarking against industry productivity standards",
        "Specialized support for unique industry challenges and workflows"
      ]
    }
  ];

  return (
    <>
      <SEOHead
        title="AI Productivity Solutions | Enterprise Pomodoro Software & Team Time Management Tools"
        description="Comprehensive AI-powered productivity solutions for enterprises, teams, and organizations. Discover how AI-enhanced Pomodoro techniques scale from individual users to enterprise-wide implementations."
        keywords="enterprise productivity solutions, AI productivity software, team time management tools, business Pomodoro software, organizational productivity, AI time management solutions, enterprise time tracking"
        url="https://www.ai-pomo.com/solutions"
        structuredData={structuredData}
      />

      <Container>
        <PageHeader />

        <HeroSection>
          <HeroContent>
            <HeroTitle>AI-Powered Productivity Solutions for Modern Organizations</HeroTitle>
            <HeroSubtitle>
              From individual professionals to enterprise teams, discover how AI-enhanced Pomodoro techniques
              transform organizational productivity. Our scalable solutions adapt to your unique workflow challenges,
              whether you're managing a small team or implementing enterprise-wide time management systems.
            </HeroSubtitle>
          </HeroContent>
        </HeroSection>

        <SolutionsSection>
          <SectionTitle>Scalable AI Productivity Solutions for Every Organization Size</SectionTitle>
          <SectionDescription>
            Every organization faces unique productivity challenges. Our comprehensive suite of AI-powered solutions
            scales from individual professionals to enterprise teams, providing the right time management tools
            and productivity features to transform how your organization works and collaborates.
          </SectionDescription>

          <SolutionsGrid>
            {solutions.map((solution, index) => (
              <SolutionCard key={index}>
                <SolutionHeader>
                  <SolutionIcon>{solution.icon}</SolutionIcon>
                  <SolutionTitleSection>
                    <SolutionTitle>{solution.title}</SolutionTitle>
                    <SolutionSubtitle>{solution.subtitle}</SolutionSubtitle>
                  </SolutionTitleSection>
                </SolutionHeader>

                <SolutionDescription>{solution.description}</SolutionDescription>

                <FeaturesSection>
                  <FeaturesTitle>Key Features:</FeaturesTitle>
                  <FeaturesList>
                    {solution.features.map((feature, idx) => (
                      <FeatureItem key={idx}>✓ {feature}</FeatureItem>
                    ))}
                  </FeaturesList>
                </FeaturesSection>

                <BenefitsSection>
                  <BenefitsTitle>Benefits:</BenefitsTitle>
                  <BenefitsList>
                    {solution.benefits.map((benefit, idx) => (
                      <BenefitItem key={idx}>• {benefit}</BenefitItem>
                    ))}
                  </BenefitsList>
                </BenefitsSection>

                <SolutionCTA>
                  <CTAButton to="/contact">Learn More</CTAButton>
                </SolutionCTA>
              </SolutionCard>
            ))}
          </SolutionsGrid>
        </SolutionsSection>

        <ComparisonSection>
          <ComparisonTitle>Why Choose AI Pomo Productivity Solutions?</ComparisonTitle>
          <ComparisonGrid>
            <ComparisonCard>
              <ComparisonCardTitle>Scalable Architecture</ComparisonCardTitle>
              <ComparisonCardText>Our AI productivity solutions grow with your organization, seamlessly scaling from individual users to enterprise-wide deployments across departments and teams.</ComparisonCardText>
            </ComparisonCard>
            <ComparisonCard>
              <ComparisonCardTitle>AI-Powered Intelligence</ComparisonCardTitle>
              <ComparisonCardText>Every solution includes advanced AI that learns and adapts to your specific workflow patterns, providing intelligent project planning and task management.</ComparisonCardText>
            </ComparisonCard>
            <ComparisonCard>
              <ComparisonCardTitle>Industry Expertise</ComparisonCardTitle>
              <ComparisonCardText>Solutions designed with deep understanding of industry-specific productivity challenges, compliance requirements, and professional workflows.</ComparisonCardText>
            </ComparisonCard>
            <ComparisonCard>
              <ComparisonCardTitle>Proven Methodology</ComparisonCardTitle>
              <ComparisonCardText>Built on the scientifically-proven Pomodoro Technique, enhanced with modern AI capabilities to address today's complex organizational productivity challenges.</ComparisonCardText>
            </ComparisonCard>
          </ComparisonGrid>
        </ComparisonSection>

        <CTASection>
          <CTAContent>
            <CTATitle>Implement AI Productivity Solutions for Your Organization</CTATitle>
            <CTADescription>
              Discover how AI-enhanced Pomodoro techniques can transform your organization's productivity.
              Our solutions experts will help you choose the perfect AI productivity solution that scales
              with your team size, industry requirements, and organizational goals.
            </CTADescription>
            <CTAButtons>
              <PrimaryButton to="/contact">Schedule Free Consultation</PrimaryButton>
              <SecondaryButton to="/pricing">View Enterprise Pricing</SecondaryButton>
            </CTAButtons>
          </CTAContent>
        </CTASection>

        <Footer />
      </Container>
    </>
  );
};

// Styled Components (similar structure to previous pages, with solution-specific styling)
const Container = styled.div`
  min-height: 100vh;
  background-color: #ffffff;
`;

const HeroSection = styled.section`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-image: url('/images/content-banner-green-no-text.svg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  color: white;
  padding: 10rem 2rem 6rem;
  text-align: center;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1;
  }

  > * {
    position: relative;
    z-index: 2;
  }
`;

const HeroContent = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const HeroTitle = styled.h1`
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const HeroSubtitle = styled.p`
  font-size: 1.25rem;
  line-height: 1.6;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
`;

const SolutionsSection = styled.section`
  padding: 6rem 2rem;
  max-width: 1400px;
  margin: 0 auto;
`;

const SectionTitle = styled.h2`
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 1.5rem;
  color: #2d3748;
`;

const SectionDescription = styled.p`
  font-size: 1.1rem;
  line-height: 1.7;
  text-align: center;
  color: #4a5568;
  max-width: 800px;
  margin: 0 auto 4rem;
`;

const SolutionsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 2.5rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const SolutionCard = styled.div`
  background: white;
  border-radius: 12px;
  padding: 2.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  transition: transform 0.2s, box-shadow 0.2s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  }
`;

const SolutionHeader = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
`;

const SolutionIcon = styled.div`
  font-size: 3rem;
  color: #d95550;
  flex-shrink: 0;
`;

const SolutionTitleSection = styled.div``;

const SolutionTitle = styled.h3`
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.25rem;
`;

const SolutionSubtitle = styled.p`
  font-size: 1rem;
  color: #d95550;
  font-weight: 500;
`;

const SolutionDescription = styled.p`
  font-size: 1rem;
  line-height: 1.6;
  color: #4a5568;
  margin-bottom: 2rem;
`;

const FeaturesSection = styled.div`
  margin-bottom: 2rem;
`;

const FeaturesTitle = styled.h4`
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
`;

const FeaturesList = styled.ul`
  list-style: none;
  padding: 0;
`;

const FeatureItem = styled.li`
  font-size: 0.95rem;
  color: #4a5568;
  margin-bottom: 0.5rem;
  line-height: 1.5;
`;

const BenefitsSection = styled.div`
  margin-bottom: 2rem;
`;

const BenefitsTitle = styled.h4`
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
`;

const BenefitsList = styled.ul`
  list-style: none;
  padding: 0;
`;

const BenefitItem = styled.li`
  font-size: 0.95rem;
  color: #4a5568;
  margin-bottom: 0.5rem;
  line-height: 1.5;
`;

const SolutionCTA = styled.div`
  text-align: center;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
`;

const CTAButton = styled(Link)`
  background-color: #d95550;
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  transition: background-color 0.2s;

  &:hover {
    background-color: #c04540;
  }
`;

const ComparisonSection = styled.section`
  background: #f7fafc;
  padding: 4rem 2rem;
`;

const ComparisonTitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  color: #2d3748;
`;

const ComparisonGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
`;

const ComparisonCard = styled.div`
  background: white;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
`;

const ComparisonCardTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
`;

const ComparisonCardText = styled.p`
  font-size: 1rem;
  color: #4a5568;
  line-height: 1.6;
`;

const CTASection = styled.section`
  background-color: #2d3748;
  color: white;
  padding: 4rem 2rem;
  text-align: center;
`;

const CTAContent = styled.div`
  max-width: 600px;
  margin: 0 auto;
`;

const CTATitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
`;

const CTADescription = styled.p`
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  opacity: 0.9;
`;

const CTAButtons = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;

  @media (max-width: 480px) {
    flex-direction: column;
  }
`;

const PrimaryButton = styled(Link)`
  background-color: #d95550;
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  transition: background-color 0.2s;

  &:hover {
    background-color: #c04540;
  }
`;

const SecondaryButton = styled(Link)`
  background-color: transparent;
  color: white;
  padding: 0.75rem 2rem;
  border: 2px solid white;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s;

  &:hover {
    background-color: white;
    color: #2d3748;
  }
`;

export default SolutionsPage;
