import React from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { <PERSON>aPlay, FaEye, FaPalette, FaHeart } from 'react-icons/fa';
import TimerComponent from '../components/Timer/TimerComponent';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import SEOHead from '../components/SEO/SEOHead';

const VisualTimerPage = () => {
  const timerOptions = [
    { minutes: 5, title: '5 Min Visual Timer', color: '#10b981' },
    { minutes: 10, title: '10 Min Visual Timer', color: '#3b82f6' },
    { minutes: 15, title: '15 Min Visual Timer', color: '#8b5cf6' },
    { minutes: 20, title: '20 Min Visual Timer', color: '#f59e0b' },
    { minutes: 25, title: '25 Min Visual Timer', color: '#d95550' },
    { minutes: 30, title: '30 Min Visual Timer', color: '#06b6d4' }
  ];

  const features = [
    {
      icon: <FaEye />,
      title: 'Visual Progress',
      description: 'Beautiful animated progress ring shows exactly how much time remains at a glance.'
    },
    {
      icon: <FaPalette />,
      title: 'Elegant Design',
      description: 'Clean, modern interface with smooth animations and pleasing color gradients.'
    },
    {
      icon: <FaHeart />,
      title: 'User Friendly',
      description: 'Intuitive controls and responsive design that works beautifully on any device.'
    }
  ];

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Visual Timer - Beautiful Online Timer",
    "description": "Beautiful visual timer with animated progress ring and elegant design. Perfect for productivity, study, and focus sessions.",
    "applicationCategory": "UtilitiesApplication",
    "operatingSystem": "Any",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "Visual progress indicator",
      "Animated countdown",
      "Beautiful design",
      "Multiple durations",
      "Sound notifications",
      "Mobile responsive"
    ]
  };

  return (
    <>
      <SEOHead
        title="Visual Timer - Beautiful Online Timer with Progress Ring"
        description="Beautiful visual timer with animated progress ring and elegant design. Free online visual timer for productivity, study, and focus sessions. Works on all devices."
        keywords="visual timer, beautiful timer, animated timer, progress ring timer, aesthetic timer, cute timer, design timer, online visual timer"
        url="https://www.ai-pomo.com/visual-timer"
        type="website"
        structuredData={structuredData}
      />
      <PageHeader />
      <Container>
        <Header>
          <Title>Visual Timer</Title>
          <Subtitle>
            Beautiful timer with animated progress ring and elegant design. 
            Perfect for productive focus sessions with visual appeal.
          </Subtitle>
        </Header>

        <TimerSection>
          <SampleTimer>
            <TimerComponent 
              initialMinutes={25}
              title="Visual Timer Demo"
              showControls={true}
            />
          </SampleTimer>
        </TimerSection>

        <FeaturesSection>
          <SectionTitle>Why Choose Visual Timer?</SectionTitle>
          <FeaturesGrid>
            {features.map((feature, index) => (
              <FeatureCard key={index}>
                <FeatureIcon>{feature.icon}</FeatureIcon>
                <FeatureTitle>{feature.title}</FeatureTitle>
                <FeatureText>{feature.description}</FeatureText>
              </FeatureCard>
            ))}
          </FeaturesGrid>
        </FeaturesSection>

        <TimerOptionsSection>
          <SectionTitle>🎨 Choose Your Visual Timer</SectionTitle>
          <TimerOptionsGrid>
            {timerOptions.map((timer, index) => (
              <TimerOptionCard key={index} $color={timer.color}>
                <TimerDuration $color={timer.color}>{timer.minutes}</TimerDuration>
                <TimerLabel>Minutes</TimerLabel>
                <TimerTitle>{timer.title}</TimerTitle>
                <TimerButton 
                  as={Link} 
                  to={`/timer/${timer.minutes}-minutes`}
                  $color={timer.color}
                >
                  <FaPlay /> Start Visual Timer
                </TimerButton>
              </TimerOptionCard>
            ))}
          </TimerOptionsGrid>
        </TimerOptionsSection>

        <BenefitsSection>
          <SectionTitle>Perfect For</SectionTitle>
          <BenefitsGrid>
            <BenefitCard>
              <BenefitEmoji>📚</BenefitEmoji>
              <BenefitTitle>Study Sessions</BenefitTitle>
              <BenefitText>Visual progress helps maintain focus during study periods</BenefitText>
            </BenefitCard>
            <BenefitCard>
              <BenefitEmoji>💻</BenefitEmoji>
              <BenefitTitle>Work Blocks</BenefitTitle>
              <BenefitText>Beautiful design keeps you motivated during work sessions</BenefitText>
            </BenefitCard>
            <BenefitCard>
              <BenefitEmoji>🧘</BenefitEmoji>
              <BenefitTitle>Meditation</BenefitTitle>
              <BenefitText>Calm, aesthetic interface perfect for mindful practices</BenefitText>
            </BenefitCard>
            <BenefitCard>
              <BenefitEmoji>🎯</BenefitEmoji>
              <BenefitTitle>Goal Tracking</BenefitTitle>
              <BenefitText>Visual progress ring shows achievement towards time goals</BenefitText>
            </BenefitCard>
          </BenefitsGrid>
        </BenefitsSection>

        <CTASection>
          <CTATitle>Start Your Visual Timer Experience</CTATitle>
          <CTAText>
            Experience the most beautiful online timer with smooth animations, 
            elegant design, and powerful functionality.
          </CTAText>
          <CTAButtons>
            <CTAButton as={Link} to="/timer/25-minutes" primary>
              Try 25 Min Visual Timer
            </CTAButton>
            <CTAButton as={Link} to="/online-timer">
              View All Timers
            </CTAButton>
          </CTAButtons>
        </CTASection>
      </Container>
      <Footer />
    </>
  );
};

// Styled Components
const Container = styled.div`
  max-width: 1000px;
  margin: 0 auto;
  padding: 7rem 2rem 4rem;

  @media (max-width: 768px) {
    padding: 5rem 1rem 2rem;
  }
`;

const Header = styled.header`
  text-align: center;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: 3rem;
  font-weight: 800;
  color: #111827;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #d95550, #f59e0b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

  @media (max-width: 768px) {
    font-size: 2.25rem;
  }
`;

const Subtitle = styled.p`
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
`;

const TimerSection = styled.section`
  margin-bottom: 4rem;
  display: flex;
  justify-content: center;
`;

const SampleTimer = styled.div`
  background: linear-gradient(135deg, #f8f9fa, #f1f3f4);
  border-radius: 24px;
  padding: 2rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
`;

const FeaturesSection = styled.section`
  margin-bottom: 4rem;
`;

const SectionTitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2rem;
  text-align: center;
`;

const FeaturesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
`;

const FeatureCard = styled.div`
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const FeatureIcon = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #d95550, #f59e0b);
  color: white;
  border-radius: 50%;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
`;

const FeatureTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
`;

const FeatureText = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
`;

const TimerOptionsSection = styled.section`
  margin-bottom: 4rem;
`;

const TimerOptionsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
`;

const TimerOptionCard = styled.div`
  background: white;
  border-radius: 16px;
  padding: 2rem 1rem;
  border: 2px solid ${props => props.$color};
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  text-align: center;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
`;

const TimerDuration = styled.div`
  font-size: 3rem;
  font-weight: 800;
  color: ${props => props.$color};
  line-height: 1;
`;

const TimerLabel = styled.div`
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 1rem;
`;

const TimerTitle = styled.h3`
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1.5rem;
`;

const TimerButton = styled.a`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: ${props => props.$color};
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.9;
    transform: translateY(-2px);
  }

  svg {
    font-size: 0.8rem;
  }
`;

const BenefitsSection = styled.section`
  margin-bottom: 4rem;
`;

const BenefitsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
`;

const BenefitCard = styled.div`
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #fef7f0, #fef3ec);
  border-radius: 12px;
  border: 1px solid #fed7aa;
`;

const BenefitEmoji = styled.div`
  font-size: 2.5rem;
  margin-bottom: 1rem;
`;

const BenefitTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const BenefitText = styled.p`
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
`;

const CTASection = styled.section`
  text-align: center;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #f8f9fa, #f1f3f4);
  border-radius: 16px;
  border: 1px solid #e5e7eb;
`;

const CTATitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
`;

const CTAText = styled.p`
  font-size: 1.125rem;
  color: #6b7280;
  margin-bottom: 2rem;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
`;

const CTAButtons = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;

  @media (max-width: 480px) {
    flex-direction: column;
    align-items: center;
  }
`;

const CTAButton = styled.a`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;

  ${props => props.primary ? `
    background: linear-gradient(135deg, #d95550, #f59e0b);
    color: white;
    box-shadow: 0 4px 12px rgba(217, 85, 80, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(217, 85, 80, 0.4);
    }
  ` : `
    background: white;
    color: #6b7280;
    border: 2px solid #e5e7eb;

    &:hover {
      border-color: #d95550;
      color: #d95550;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  `}

  @media (max-width: 480px) {
    width: 200px;
    justify-content: center;
  }
`;

export default VisualTimerPage;