import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { FaPlay, FaPause, FaStop, FaTasks, FaChartLine, FaArrowRight, FaCog } from 'react-icons/fa';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import SEOHead from '../components/SEO/SEOHead';

const TomatoTimerPage = () => {
  const [currentPhase, setCurrentPhase] = useState('work'); // work, shortBreak, longBreak
  const [timeLeft, setTimeLeft] = useState(25 * 60); // 25 minutes
  const [isRunning, setIsRunning] = useState(false);
  const [pomodoroCount, setPomodoroCount] = useState(0);
  const [sessionCount, setSessionCount] = useState(0);
  const [currentTask, setCurrentTask] = useState('');
  const [tasks, setTasks] = useState([]);
  const [settings, setSettings] = useState({
    workDuration: 25,
    shortBreakDuration: 5,
    longBreakDuration: 15,
    longBreakInterval: 4
  });
  const [showSettings, setShowSettings] = useState(false);
  
  const intervalRef = useRef(null);
  const audioRef = useRef(null);

  const phaseConfig = {
    work: {
      duration: settings.workDuration * 60,
      title: 'Focus Time',
      color: '#ef4444',
      nextPhase: pomodoroCount + 1 >= settings.longBreakInterval ? 'longBreak' : 'shortBreak',
      emoji: '🍅'
    },
    shortBreak: {
      duration: settings.shortBreakDuration * 60,
      title: 'Short Break',
      color: '#10b981',
      nextPhase: 'work',
      emoji: '☕'
    },
    longBreak: {
      duration: settings.longBreakDuration * 60,
      title: 'Long Break',
      color: '#3b82f6',
      nextPhase: 'work',
      emoji: '🏖️'
    }
  };

  useEffect(() => {
    if (isRunning && timeLeft > 0) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            completePhase();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      clearInterval(intervalRef.current);
    }

    return () => clearInterval(intervalRef.current);
  }, [isRunning, timeLeft]);

  const playAlarmSound = () => {
    if (audioRef.current) {
      audioRef.current.play().catch(e => console.log('Audio play failed:', e));
    }
    
    // Browser notification
    if (Notification.permission === 'granted') {
      const phase = phaseConfig[currentPhase];
      new Notification(`🍅 ${phase.title} Complete!`, {
        body: `Time to ${phase.nextPhase === 'work' ? 'get back to work' : 'take a break'}`,
        icon: '/ai-pomo.png'
      });
    }
  };

  const requestNotificationPermission = () => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  };

  const completePhase = () => {
    setIsRunning(false);
    playAlarmSound();
    
    const nextPhase = phaseConfig[currentPhase].nextPhase;
    
    if (currentPhase === 'work') {
      setPomodoroCount(prev => prev + 1);
      setSessionCount(prev => prev + 1);
      
      // Mark current task as completed if exists
      if (currentTask.trim()) {
        setTasks(prev => [...prev, {
          id: Date.now(),
          text: currentTask,
          completed: true,
          completedAt: new Date()
        }]);
        setCurrentTask('');
      }
    }

    if (nextPhase === 'work' && currentPhase === 'longBreak') {
      setPomodoroCount(0); // Reset pomodoro count after long break
    }
    
    setCurrentPhase(nextPhase);
    setTimeLeft(phaseConfig[nextPhase].duration);

    if (window.gtag) {
      window.gtag('event', 'tomato_timer_complete', {
        'phase': currentPhase,
        'pomodoro_count': pomodoroCount,
        'session_count': sessionCount
      });
    }
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const startTimer = () => {
    setIsRunning(true);
    requestNotificationPermission();
  };

  const pauseTimer = () => {
    setIsRunning(false);
  };

  const resetTimer = () => {
    setIsRunning(false);
    setTimeLeft(phaseConfig[currentPhase].duration);
  };

  const switchPhase = (phase) => {
    setIsRunning(false);
    setCurrentPhase(phase);
    setTimeLeft(phaseConfig[phase].duration);
  };

  const addTask = () => {
    if (currentTask.trim()) {
      setTasks(prev => [...prev, {
        id: Date.now(),
        text: currentTask,
        completed: false,
        createdAt: new Date()
      }]);
      setCurrentTask('');
    }
  };

  const deleteTask = (taskId) => {
    setTasks(prev => prev.filter(task => task.id !== taskId));
  };

  const updateSettings = (newSettings) => {
    setSettings(newSettings);
    setTimeLeft(phaseConfig[currentPhase].duration);
    setShowSettings(false);
  };

  const progress = (phaseConfig[currentPhase].duration - timeLeft) / phaseConfig[currentPhase].duration * 100;
  const currentConfig = phaseConfig[currentPhase];

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Tomato Timer - Pomodoro Technique Timer",
    "description": "Professional Pomodoro timer with task management. Boost productivity using the proven tomato timer technique with 25-minute focus sessions and smart breaks.",
    "applicationCategory": "ProductivityApplication",
    "operatingSystem": "Any",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "Pomodoro technique timer",
      "Task management",
      "Progress tracking",
      "Customizable durations",
      "Sound notifications",
      "Session statistics"
    ]
  };

  return (
    <>
      <SEOHead
        title="Tomato Timer - Pomodoro Technique Timer | AI Pomo"
        description="Professional Pomodoro timer with task management. Boost productivity using the proven tomato timer technique with 25-minute focus sessions and smart breaks. Track your progress and complete more tasks!"
        keywords="tomato timer, pomodoro timer, pomodoro technique, productivity timer, focus timer, work timer, time management, task management"
        url="https://www.ai-pomo.com/tomato-timer"
        type="website"
        structuredData={structuredData}
      />
      <PageHeader />
      <Container>
        <Header>
          <Title>🍅 Tomato Timer</Title>
          <Subtitle>
            Professional Pomodoro technique timer. Boost your productivity with 25-minute focus sessions, 
            smart breaks, and integrated task management.
          </Subtitle>
        </Header>

        <MainContent>
          <TimerSection>
            <PhaseIndicator>
              <PhaseButton 
                $active={currentPhase === 'work'} 
                $color="#ef4444"
                onClick={() => switchPhase('work')}
                disabled={isRunning}
              >
                🍅 Focus ({settings.workDuration}m)
              </PhaseButton>
              <PhaseButton 
                $active={currentPhase === 'shortBreak'} 
                $color="#10b981"
                onClick={() => switchPhase('shortBreak')}
                disabled={isRunning}
              >
                ☕ Short Break ({settings.shortBreakDuration}m)
              </PhaseButton>
              <PhaseButton 
                $active={currentPhase === 'longBreak'} 
                $color="#3b82f6"
                onClick={() => switchPhase('longBreak')}
                disabled={isRunning}
              >
                🏖️ Long Break ({settings.longBreakDuration}m)
              </PhaseButton>
            </PhaseIndicator>

            <TimerDisplay $color={currentConfig.color} $isRunning={isRunning}>
              <PhaseEmoji>{currentConfig.emoji}</PhaseEmoji>
              <TimeText>{formatTime(timeLeft)}</TimeText>
              <PhaseTitle>{currentConfig.title}</PhaseTitle>
              <ProgressBar>
                <ProgressFill $progress={progress} $color={currentConfig.color} />
              </ProgressBar>
            </TimerDisplay>

            <ControlButtons>
              {!isRunning ? (
                <StartButton onClick={startTimer}>
                  <FaPlay /> Start Focus
                </StartButton>
              ) : (
                <PauseButton onClick={pauseTimer}>
                  <FaPause /> Pause
                </PauseButton>
              )}
              <ResetButton onClick={resetTimer}>
                <FaStop /> Reset
              </ResetButton>
              <SettingsButton onClick={() => setShowSettings(true)}>
                <FaCog /> Settings
              </SettingsButton>
            </ControlButtons>

            <StatsRow>
              <StatCard>
                <StatNumber>{sessionCount}</StatNumber>
                <StatLabel>Sessions Today</StatLabel>
              </StatCard>
              <StatCard>
                <StatNumber>{pomodoroCount}/{settings.longBreakInterval}</StatNumber>
                <StatLabel>Progress to Long Break</StatLabel>
              </StatCard>
              <StatCard>
                <StatNumber>{tasks.filter(t => t.completed).length}</StatNumber>
                <StatLabel>Tasks Completed</StatLabel>
              </StatCard>
            </StatsRow>
          </TimerSection>

          <TaskSection>
            <SectionTitle><FaTasks /> Current Task</SectionTitle>
            <TaskInput>
              <TaskInputField
                type="text"
                placeholder="What are you working on?"
                value={currentTask}
                onChange={(e) => setCurrentTask(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addTask()}
              />
              <AddTaskButton onClick={addTask}>Add</AddTaskButton>
            </TaskInput>
            
            {tasks.length > 0 && (
              <TaskList>
                <TaskListTitle>Task History</TaskListTitle>
                {tasks.slice(-5).reverse().map(task => (
                  <TaskItem key={task.id} $completed={task.completed}>
                    <TaskText>{task.text}</TaskText>
                    <TaskActions>
                      {task.completed && <TaskStatus>✅ Completed</TaskStatus>}
                      <DeleteButton onClick={() => deleteTask(task.id)}>×</DeleteButton>
                    </TaskActions>
                  </TaskItem>
                ))}
              </TaskList>
            )}
          </TaskSection>

          <InfoSection>
            <SectionTitle>How Pomodoro Technique Works</SectionTitle>
            <StepsGrid>
              <Step>
                <StepNumber>1</StepNumber>
                <StepTitle>Choose a Task</StepTitle>
                <StepDescription>Pick a task you want to work on and enter it above</StepDescription>
              </Step>
              <Step>
                <StepNumber>2</StepNumber>
                <StepTitle>Focus for 25 Minutes</StepTitle>
                <StepDescription>Work on your task without any distractions until the timer rings</StepDescription>
              </Step>
              <Step>
                <StepNumber>3</StepNumber>
                <StepTitle>Take a 5-Minute Break</StepTitle>
                <StepDescription>Relax, stretch, or do something unrelated to work</StepDescription>
              </Step>
              <Step>
                <StepNumber>4</StepNumber>
                <StepTitle>Repeat & Long Break</StepTitle>
                <StepDescription>After 4 pomodoros, take a longer 15-30 minute break</StepDescription>
              </Step>
            </StepsGrid>
          </InfoSection>

          <BenefitsSection>
            <SectionTitle>Why Use Tomato Timer?</SectionTitle>
            <BenefitsGrid>
              <Benefit>
                <BenefitIcon>🎯</BenefitIcon>
                <BenefitTitle>Improved Focus</BenefitTitle>
                <BenefitText>25-minute sessions help maintain concentration and reduce mental fatigue</BenefitText>
              </Benefit>
              <Benefit>
                <BenefitIcon>📈</BenefitIcon>
                <BenefitTitle>Better Time Management</BenefitTitle>
                <BenefitText>Track your productivity and understand how long tasks actually take</BenefitText>
              </Benefit>
              <Benefit>
                <BenefitIcon>🧠</BenefitIcon>
                <BenefitTitle>Reduced Burnout</BenefitTitle>
                <BenefitText>Regular breaks prevent mental exhaustion and maintain peak performance</BenefitText>
              </Benefit>
            </BenefitsGrid>
          </BenefitsSection>

          <RelatedSection>
            <SectionTitle>More Productivity Tools</SectionTitle>
            <RelatedGrid>
              <RelatedTimer as={Link} to="/work-timer">
                <RelatedTitle>Work Timer</RelatedTitle>
                <RelatedDescription>Professional timer for work sessions</RelatedDescription>
                <RelatedAction>
                  Try Work Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
              <RelatedTimer as={Link} to="/study-timer">
                <RelatedTitle>Study Timer</RelatedTitle>
                <RelatedDescription>Optimized for learning and education</RelatedDescription>
                <RelatedAction>
                  Try Study Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
              <RelatedTimer as={Link} to="/custom-timer">
                <RelatedTitle>Custom Timer</RelatedTitle>
                <RelatedDescription>Set any duration for any activity</RelatedDescription>
                <RelatedAction>
                  Try Custom Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
            </RelatedGrid>
          </RelatedSection>
        </MainContent>

        {showSettings && (
          <SettingsModal>
            <SettingsContent>
              <SettingsTitle>Timer Settings</SettingsTitle>
              <SettingsForm>
                <SettingItem>
                  <SettingLabel>Focus Duration (minutes)</SettingLabel>
                  <SettingInput
                    type="number"
                    min="1"
                    max="60"
                    value={settings.workDuration}
                    onChange={(e) => setSettings(prev => ({
                      ...prev,
                      workDuration: parseInt(e.target.value) || 25
                    }))}
                  />
                </SettingItem>
                <SettingItem>
                  <SettingLabel>Short Break (minutes)</SettingLabel>
                  <SettingInput
                    type="number"
                    min="1"
                    max="30"
                    value={settings.shortBreakDuration}
                    onChange={(e) => setSettings(prev => ({
                      ...prev,
                      shortBreakDuration: parseInt(e.target.value) || 5
                    }))}
                  />
                </SettingItem>
                <SettingItem>
                  <SettingLabel>Long Break (minutes)</SettingLabel>
                  <SettingInput
                    type="number"
                    min="1"
                    max="60"
                    value={settings.longBreakDuration}
                    onChange={(e) => setSettings(prev => ({
                      ...prev,
                      longBreakDuration: parseInt(e.target.value) || 15
                    }))}
                  />
                </SettingItem>
                <SettingItem>
                  <SettingLabel>Long Break Interval</SettingLabel>
                  <SettingInput
                    type="number"
                    min="2"
                    max="8"
                    value={settings.longBreakInterval}
                    onChange={(e) => setSettings(prev => ({
                      ...prev,
                      longBreakInterval: parseInt(e.target.value) || 4
                    }))}
                  />
                </SettingItem>
              </SettingsForm>
              <SettingsButtons>
                <SaveButton onClick={() => updateSettings(settings)}>Save Changes</SaveButton>
                <CancelButton onClick={() => setShowSettings(false)}>Cancel</CancelButton>
              </SettingsButtons>
            </SettingsContent>
          </SettingsModal>
        )}

        <audio ref={audioRef} preload="auto">
          <source src="/sounds/completion.mp3" type="audio/mpeg" />
        </audio>
      </Container>
      <Footer />
    </>
  );
};

// Styled Components
const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 6rem 2rem 4rem;

  @media (max-width: 768px) {
    padding: 5rem 1rem 2rem;
  }
`;

const Header = styled.header`
  text-align: center;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: 3rem;
  font-weight: 800;
  color: #111827;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    font-size: 2.25rem;
  }
`;

const Subtitle = styled.p`
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  max-width: 700px;
  margin: 0 auto;
`;

const MainContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4rem;
`;

const TimerSection = styled.section`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
`;

const PhaseIndicator = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0.5rem;
  }
`;

const PhaseButton = styled.button`
  padding: 0.75rem 1.5rem;
  border: 2px solid ${props => props.$active ? props.$color : '#e5e7eb'};
  border-radius: 12px;
  background: ${props => props.$active ? props.$color : 'white'};
  color: ${props => props.$active ? 'white' : '#6b7280'};
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;

  &:hover:not(:disabled) {
    border-color: ${props => props.$color};
    color: ${props => props.$active ? 'white' : props.$color};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const TimerDisplay = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 350px;
  height: 350px;
  border-radius: 50%;
  background: linear-gradient(135deg, ${props => props.$color}, ${props => props.$color}dd);
  box-shadow: 0 20px 40px ${props => props.$color}40;
  color: white;
  position: relative;
  animation: ${props => props.$isRunning ? 'pulse 2s infinite' : 'none'};

  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
  }

  @media (max-width: 480px) {
    width: 300px;
    height: 300px;
  }
`;

const PhaseEmoji = styled.div`
  font-size: 3rem;
  margin-bottom: 0.5rem;
`;

const TimeText = styled.div`
  font-size: 4rem;
  font-weight: 800;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
  margin-bottom: 0.5rem;

  @media (max-width: 480px) {
    font-size: 3rem;
  }
`;

const PhaseTitle = styled.div`
  font-size: 1.25rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
  margin-bottom: 1rem;
`;

const ProgressBar = styled.div`
  position: absolute;
  bottom: 2rem;
  left: 2rem;
  right: 2rem;
  height: 8px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  overflow: hidden;
`;

const ProgressFill = styled.div`
  height: 100%;
  background: white;
  border-radius: 4px;
  width: ${props => props.$progress}%;
  transition: width 1s ease;
`;

const ControlButtons = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
  justify-content: center;
`;

const StartButton = styled(Button)`
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
  }
`;

const PauseButton = styled(Button)`
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
  }
`;

const ResetButton = styled(Button)`
  background: white;
  color: #6b7280;
  border: 2px solid #e5e7eb;

  &:hover {
    border-color: #ef4444;
    color: #ef4444;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;

const SettingsButton = styled(Button)`
  background: #f3f4f6;
  color: #6b7280;
  border: none;
  min-width: 120px;

  &:hover {
    background: #e5e7eb;
    transform: translateY(-2px);
  }
`;

const StatsRow = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-top: 2rem;
  width: 100%;
  max-width: 500px;

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
`;

const StatCard = styled.div`
  text-align: center;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const StatNumber = styled.div`
  font-size: 2rem;
  font-weight: 800;
  color: #ef4444;
  margin-bottom: 0.5rem;
`;

const StatLabel = styled.div`
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 600;
`;

const TaskSection = styled.section`
  max-width: 600px;
  margin: 0 auto;
  width: 100%;
`;

const SectionTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const TaskInput = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;

  @media (max-width: 480px) {
    flex-direction: column;
  }
`;

const TaskInputField = styled.input`
  flex: 1;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #ef4444;
  }
`;

const AddTaskButton = styled.button`
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
  }
`;

const TaskList = styled.div`
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
`;

const TaskListTitle = styled.h3`
  padding: 1rem 1.5rem;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
`;

const TaskItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f3f4f6;
  opacity: ${props => props.$completed ? 0.7 : 1};

  &:last-child {
    border-bottom: none;
  }
`;

const TaskText = styled.span`
  flex: 1;
  color: #111827;
`;

const TaskActions = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const TaskStatus = styled.span`
  font-size: 0.875rem;
  color: #10b981;
  font-weight: 600;
`;

const DeleteButton = styled.button`
  width: 2rem;
  height: 2rem;
  border: none;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:hover {
    background: #ef4444;
    color: white;
  }
`;

const InfoSection = styled.section``;

const StepsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
`;

const Step = styled.div`
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
`;

const StepNumber = styled.div`
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 800;
  font-size: 1.25rem;
  margin: 0 auto 1rem;
`;

const StepTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.75rem;
`;

const StepDescription = styled.p`
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
`;

const BenefitsSection = styled.section``;

const BenefitsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
`;

const Benefit = styled.div`
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const BenefitIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const BenefitTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
`;

const BenefitText = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
`;

const RelatedSection = styled.section``;

const RelatedGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
`;

const RelatedTimer = styled.a`
  display: block;
  padding: 2rem;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  text-decoration: none;
  transition: all 0.3s ease;

  &:hover {
    border-color: #ef4444;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
`;

const RelatedTitle = styled.h4`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const RelatedDescription = styled.p`
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
`;

const RelatedAction = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ef4444;
  font-weight: 600;
  font-size: 0.875rem;

  svg {
    font-size: 0.75rem;
  }
`;

const SettingsModal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const SettingsContent = styled.div`
  background: white;
  border-radius: 16px;
  padding: 2rem;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
`;

const SettingsTitle = styled.h3`
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2rem;
  text-align: center;
`;

const SettingsForm = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const SettingItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const SettingLabel = styled.label`
  font-weight: 600;
  color: #111827;
`;

const SettingInput = styled.input`
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: #ef4444;
  }
`;

const SettingsButtons = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;
`;

const SaveButton = styled.button`
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
  }
`;

const CancelButton = styled.button`
  padding: 0.75rem 1.5rem;
  background: #f3f4f6;
  color: #6b7280;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: #e5e7eb;
  }
`;

export default TomatoTimerPage;