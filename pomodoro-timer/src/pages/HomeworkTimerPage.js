import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { FaPlay, FaPause, FaStop, FaBookOpen, FaPencilAlt, FaCheckCircle, FaArrowRight, FaStar, FaTrophy } from 'react-icons/fa';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import SEOHead from '../components/SEO/SEOHead';

const HomeworkTimerPage = () => {
  const [timeLeft, setTimeLeft] = useState(25 * 60); // 25 minutes default
  const [isRunning, setIsRunning] = useState(false);
  const [inputMinutes, setInputMinutes] = useState(25);
  const [originalTime, setOriginalTime] = useState(25 * 60);
  const [currentSubject, setCurrentSubject] = useState('');
  const [completedSessions, setCompletedSessions] = useState(0);
  const [todayGoal, setTodayGoal] = useState(4);
  const [sessionType, setSessionType] = useState('study'); // study, break
  const [streakCount, setStreakCount] = useState(0);
  
  const intervalRef = useRef(null);
  const audioRef = useRef(null);

  const subjects = [
    { name: 'Math', emoji: '🔢', color: '#3b82f6' },
    { name: 'Science', emoji: '🔬', color: '#10b981' },
    { name: 'English', emoji: '📚', color: '#8b5cf6' },
    { name: 'History', emoji: '🏛️', color: '#f59e0b' },
    { name: 'Art', emoji: '🎨', color: '#ef4444' },
    { name: 'Music', emoji: '🎵', color: '#6366f1' },
    { name: 'Reading', emoji: '📖', color: '#059669' },
    { name: 'Writing', emoji: '✍️', color: '#dc2626' }
  ];

  const sessionDurations = {
    study: { duration: 25, nextSession: 'break', title: 'Study Time', color: '#3b82f6' },
    break: { duration: 5, nextSession: 'study', title: 'Break Time', color: '#10b981' }
  };

  useEffect(() => {
    if (isRunning && timeLeft > 0) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            completeSession();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      clearInterval(intervalRef.current);
    }

    return () => clearInterval(intervalRef.current);
  }, [isRunning, timeLeft]);

  const playAlarmSound = () => {
    if (audioRef.current) {
      audioRef.current.play().catch(e => console.log('Audio play failed:', e));
    }
    
    // Browser notification
    if (Notification.permission === 'granted') {
      const session = sessionDurations[sessionType];
      new Notification(`🎓 ${session.title} Complete!`, {
        body: sessionType === 'study' ? 
          'Great job studying! Time for a break.' : 
          'Break time over! Ready to get back to learning?',
        icon: '/ai-pomo.png'
      });
    }
  };

  const completeSession = () => {
    setIsRunning(false);
    playAlarmSound();
    
    if (sessionType === 'study') {
      setCompletedSessions(prev => prev + 1);
      setStreakCount(prev => prev + 1);
    }

    // Auto-switch to next session type
    const nextType = sessionDurations[sessionType].nextSession;
    setSessionType(nextType);
    const nextDuration = sessionDurations[nextType].duration * 60;
    setTimeLeft(nextDuration);
    setOriginalTime(nextDuration);

    if (window.gtag) {
      window.gtag('event', 'homework_timer_complete', {
        'session_type': sessionType,
        'subject': currentSubject,
        'duration': originalTime,
        'completed_sessions': completedSessions
      });
    }
  };

  const requestNotificationPermission = () => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const startTimer = () => {
    if (inputMinutes > 0) {
      const totalSeconds = inputMinutes * 60;
      setTimeLeft(totalSeconds);
      setOriginalTime(totalSeconds);
      setIsRunning(true);
      requestNotificationPermission();
    }
  };

  const pauseTimer = () => {
    setIsRunning(false);
  };

  const resetTimer = () => {
    setIsRunning(false);
    setTimeLeft(originalTime);
  };

  const quickStart = (minutes, type = 'study') => {
    setSessionType(type);
    setInputMinutes(minutes);
    const totalSeconds = minutes * 60;
    setTimeLeft(totalSeconds);
    setOriginalTime(totalSeconds);
    setIsRunning(true);
    requestNotificationPermission();
  };

  const currentSession = sessionDurations[sessionType];
  const progress = originalTime > 0 ? ((originalTime - timeLeft) / originalTime) * 100 : 0;
  const progressToGoal = (completedSessions / todayGoal) * 100;

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Homework Timer - Study Timer for Students",
    "description": "Specialized homework timer for students. Track study sessions, subjects, and progress. Perfect for managing homework time with Pomodoro technique and goal tracking.",
    "applicationCategory": "EducationalApplication",
    "operatingSystem": "Any",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "Subject tracking",
      "Study session management",
      "Progress tracking",
      "Goal setting",
      "Break reminders",
      "Streak counting"
    ]
  };

  return (
    <>
      <SEOHead
        title="Homework Timer - Study Timer for Students | AI Pomo"
        description="Specialized homework timer for students. Track study sessions, subjects, and progress. Perfect for managing homework time with Pomodoro technique and goal tracking."
        keywords="homework timer, study timer, student timer, assignment timer, school timer, study session tracker, homework planner"
        url="https://www.ai-pomo.com/homework-timer"
        type="website"
        structuredData={structuredData}
      />
      <PageHeader />
      <Container>
        <Header>
          <Title>📚 Homework Timer</Title>
          <Subtitle>
            Stay focused and organized with your homework! Track study sessions by subject, 
            set daily goals, and build healthy study habits.
          </Subtitle>
        </Header>

        <MainContent>
          <TimerSection>
            <SessionTypeToggle>
              <SessionButton 
                $active={sessionType === 'study'} 
                $color="#3b82f6"
                onClick={() => setSessionType('study')}
                disabled={isRunning}
              >
                📖 Study Session
              </SessionButton>
              <SessionButton 
                $active={sessionType === 'break'} 
                $color="#10b981"
                onClick={() => setSessionType('break')}
                disabled={isRunning}
              >
                ☕ Break Time
              </SessionButton>
            </SessionTypeToggle>

            <SubjectSelector>
              <SubjectLabel>What subject are you working on?</SubjectLabel>
              <SubjectGrid>
                {subjects.map(subject => (
                  <SubjectButton
                    key={subject.name}
                    $active={currentSubject === subject.name}
                    $color={subject.color}
                    onClick={() => setCurrentSubject(subject.name)}
                    disabled={isRunning && sessionType === 'break'}
                  >
                    <SubjectEmoji>{subject.emoji}</SubjectEmoji>
                    <SubjectName>{subject.name}</SubjectName>
                  </SubjectButton>
                ))}
              </SubjectGrid>
            </SubjectSelector>

            <TimerDisplay $color={currentSession.color} $isRunning={isRunning}>
              <SessionIcon>
                {sessionType === 'study' ? '📚' : '☕'}
              </SessionIcon>
              <TimeText>{formatTime(timeLeft)}</TimeText>
              <SessionTitle>{currentSession.title}</SessionTitle>
              {currentSubject && sessionType === 'study' && (
                <CurrentSubject>
                  {subjects.find(s => s.name === currentSubject)?.emoji} {currentSubject}
                </CurrentSubject>
              )}
              <ProgressBar>
                <ProgressFill $progress={progress} />
              </ProgressBar>
            </TimerDisplay>

            <TimeInputSection>
              <InputGroup>
                <TimeInput
                  type="number"
                  min="1"
                  max="120"
                  value={inputMinutes}
                  onChange={(e) => setInputMinutes(Math.max(1, parseInt(e.target.value) || 1))}
                  disabled={isRunning}
                />
                <InputLabel>minutes</InputLabel>
              </InputGroup>
            </TimeInputSection>

            <ControlButtons>
              {!isRunning ? (
                <StartButton onClick={startTimer}>
                  <FaPlay /> Start {currentSession.title}
                </StartButton>
              ) : (
                <PauseButton onClick={pauseTimer}>
                  <FaPause /> Pause
                </PauseButton>
              )}
              <ResetButton onClick={resetTimer}>
                <FaStop /> Reset
              </ResetButton>
            </ControlButtons>

            <QuickButtons>
              <QuickButtonsTitle>Quick Study Sessions</QuickButtonsTitle>
              <QuickButtonsGrid>
                <QuickButton onClick={() => quickStart(15, 'study')} disabled={isRunning}>
                  15m Study
                </QuickButton>
                <QuickButton onClick={() => quickStart(25, 'study')} disabled={isRunning}>
                  25m Pomodoro
                </QuickButton>
                <QuickButton onClick={() => quickStart(30, 'study')} disabled={isRunning}>
                  30m Focus
                </QuickButton>
                <QuickButton onClick={() => quickStart(45, 'study')} disabled={isRunning}>
                  45m Deep Work
                </QuickButton>
                <QuickButton onClick={() => quickStart(5, 'break')} disabled={isRunning}>
                  5m Break
                </QuickButton>
                <QuickButton onClick={() => quickStart(15, 'break')} disabled={isRunning}>
                  15m Long Break
                </QuickButton>
              </QuickButtonsGrid>
            </QuickButtons>
          </TimerSection>

          <ProgressSection>
            <SectionTitle>📊 Today's Progress</SectionTitle>
            <StatsGrid>
              <StatCard>
                <StatIcon>🎯</StatIcon>
                <StatNumber>{completedSessions}</StatNumber>
                <StatLabel>Sessions Completed</StatLabel>
              </StatCard>
              <StatCard>
                <StatIcon>🏆</StatIcon>
                <StatNumber>{todayGoal}</StatNumber>
                <StatLabel>Daily Goal</StatLabel>
              </StatCard>
              <StatCard>
                <StatIcon>🔥</StatIcon>
                <StatNumber>{streakCount}</StatNumber>
                <StatLabel>Study Streak</StatLabel>
              </StatCard>
            </StatsGrid>
            
            <GoalProgress>
              <GoalLabel>Daily Goal Progress</GoalLabel>
              <GoalBar>
                <GoalFill $progress={Math.min(progressToGoal, 100)} />
              </GoalBar>
              <GoalText>
                {completedSessions}/{todayGoal} sessions completed
                {completedSessions >= todayGoal && ' 🎉 Goal achieved!'}
              </GoalText>
            </GoalProgress>

            <GoalSetter>
              <GoalSetterLabel>Set Daily Goal:</GoalSetterLabel>
              <GoalInput
                type="number"
                min="1"
                max="12"
                value={todayGoal}
                onChange={(e) => setTodayGoal(Math.max(1, parseInt(e.target.value) || 1))}
              />
              <GoalInputLabel>sessions per day</GoalInputLabel>
            </GoalSetter>
          </ProgressSection>

          <TipsSection>
            <SectionTitle>📝 Study Tips for Success</SectionTitle>
            <TipsGrid>
              <TipCard>
                <TipIcon>🎯</TipIcon>
                <TipTitle>Set Clear Goals</TipTitle>
                <TipDescription>
                  Before starting, decide what you want to accomplish in this study session. Be specific!
                </TipDescription>
              </TipCard>
              <TipCard>
                <TipIcon>📱</TipIcon>
                <TipTitle>Eliminate Distractions</TipTitle>
                <TipDescription>
                  Put your phone away, close social media, and create a clean study environment.
                </TipDescription>
              </TipCard>
              <TipCard>
                <TipIcon>💧</TipIcon>
                <TipTitle>Stay Hydrated</TipTitle>
                <TipDescription>
                  Keep water nearby and take small sips during breaks to keep your brain sharp.
                </TipDescription>
              </TipCard>
              <TipCard>
                <TipIcon>🧠</TipIcon>
                <TipTitle>Use Active Learning</TipTitle>
                <TipDescription>
                  Don't just read - summarize, take notes, ask questions, and explain concepts aloud.
                </TipDescription>
              </TipCard>
            </TipsGrid>
          </TipsSection>

          <BenefitsSection>
            <SectionTitle>Why Use Homework Timer?</SectionTitle>
            <BenefitsGrid>
              <Benefit>
                <BenefitIcon>📈</BenefitIcon>
                <BenefitTitle>Improved Focus</BenefitTitle>
                <BenefitText>Timed sessions help you concentrate better and avoid procrastination</BenefitText>
              </Benefit>
              <Benefit>
                <BenefitIcon>⏰</BenefitIcon>
                <BenefitTitle>Better Time Management</BenefitTitle>
                <BenefitText>Learn to estimate how long assignments take and plan accordingly</BenefitText>
              </Benefit>
              <Benefit>
                <BenefitIcon>🎓</BenefitIcon>
                <BenefitTitle>Academic Success</BenefitTitle>
                <BenefitText>Regular study habits lead to better grades and reduced stress</BenefitText>
              </Benefit>
            </BenefitsGrid>
          </BenefitsSection>

          <RelatedSection>
            <SectionTitle>More Study Tools</SectionTitle>
            <RelatedGrid>
              <RelatedTimer as={Link} to="/study-timer">
                <RelatedTitle>Study Timer</RelatedTitle>
                <RelatedDescription>General study timer with focus techniques</RelatedDescription>
                <RelatedAction>
                  Try Study Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
              <RelatedTimer as={Link} to="/student-study-timer">
                <RelatedTitle>Student Study Timer</RelatedTitle>
                <RelatedDescription>Advanced study timer for serious students</RelatedDescription>
                <RelatedAction>
                  Try Student Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
              <RelatedTimer as={Link} to="/tomato-timer">
                <RelatedTitle>Tomato Timer</RelatedTitle>
                <RelatedDescription>Classic Pomodoro technique for productivity</RelatedDescription>
                <RelatedAction>
                  Try Tomato Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
            </RelatedGrid>
          </RelatedSection>
        </MainContent>

        <audio ref={audioRef} preload="auto">
          <source src="/sounds/completion.mp3" type="audio/mpeg" />
        </audio>
      </Container>
      <Footer />
    </>
  );
};

// Styled Components
const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 6rem 2rem 4rem;

  @media (max-width: 768px) {
    padding: 5rem 1rem 2rem;
  }
`;

const Header = styled.header`
  text-align: center;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: 3rem;
  font-weight: 800;
  color: #111827;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    font-size: 2.25rem;
  }
`;

const Subtitle = styled.p`
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  max-width: 700px;
  margin: 0 auto;
`;

const MainContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4rem;
`;

const TimerSection = styled.section`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
`;

const SessionTypeToggle = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;

  @media (max-width: 480px) {
    flex-direction: column;
  }
`;

const SessionButton = styled.button`
  padding: 0.75rem 1.5rem;
  border: 2px solid ${props => props.$active ? props.$color : '#e5e7eb'};
  border-radius: 12px;
  background: ${props => props.$active ? props.$color : 'white'};
  color: ${props => props.$active ? 'white' : '#6b7280'};
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    border-color: ${props => props.$color};
    color: ${props => props.$active ? 'white' : props.$color};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const SubjectSelector = styled.div`
  text-align: center;
  margin-bottom: 2rem;
`;

const SubjectLabel = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
`;

const SubjectGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  max-width: 500px;
  margin: 0 auto;

  @media (max-width: 768px) {
    grid-template-columns: repeat(3, 1fr);
  }

  @media (max-width: 480px) {
    grid-template-columns: repeat(2, 1fr);
  }
`;

const SubjectButton = styled.button`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem 0.5rem;
  border: 2px solid ${props => props.$active ? props.$color : '#e5e7eb'};
  border-radius: 12px;
  background: ${props => props.$active ? `${props.$color}20` : 'white'};
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    border-color: ${props => props.$color};
    background: ${props => `${props.$color}10`};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const SubjectEmoji = styled.div`
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
`;

const SubjectName = styled.div`
  font-size: 0.75rem;
  font-weight: 600;
  color: #6b7280;
`;

const TimerDisplay = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 350px;
  height: 350px;
  border-radius: 50%;
  background: linear-gradient(135deg, ${props => props.$color}, ${props => props.$color}dd);
  box-shadow: 0 20px 40px ${props => props.$color}40;
  color: white;
  position: relative;
  animation: ${props => props.$isRunning ? 'pulse 2s infinite' : 'none'};

  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
  }

  @media (max-width: 480px) {
    width: 300px;
    height: 300px;
  }
`;

const SessionIcon = styled.div`
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
`;

const TimeText = styled.div`
  font-size: 3.5rem;
  font-weight: 800;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
  margin-bottom: 0.5rem;

  @media (max-width: 480px) {
    font-size: 3rem;
  }
`;

const SessionTitle = styled.div`
  font-size: 1.125rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
  margin-bottom: 0.5rem;
`;

const CurrentSubject = styled.div`
  font-size: 1rem;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
  margin-bottom: 1rem;
`;

const ProgressBar = styled.div`
  position: absolute;
  bottom: 2rem;
  left: 2rem;
  right: 2rem;
  height: 8px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  overflow: hidden;
`;

const ProgressFill = styled.div`
  height: 100%;
  background: white;
  border-radius: 4px;
  width: ${props => props.$progress}%;
  transition: width 1s ease;
`;

const TimeInputSection = styled.div`
  display: flex;
  justify-content: center;
`;

const InputGroup = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
`;

const TimeInput = styled.input`
  width: 100px;
  padding: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #3b82f6;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const InputLabel = styled.label`
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 600;
`;

const ControlButtons = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
  justify-content: center;
`;

const StartButton = styled(Button)`
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  }
`;

const PauseButton = styled(Button)`
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
  }
`;

const ResetButton = styled(Button)`
  background: white;
  color: #6b7280;
  border: 2px solid #e5e7eb;

  &:hover {
    border-color: #3b82f6;
    color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;

const QuickButtons = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
`;

const QuickButtonsTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
`;

const QuickButtonsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
  max-width: 500px;

  @media (max-width: 480px) {
    grid-template-columns: repeat(2, 1fr);
  }
`;

const QuickButton = styled.button`
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #6b7280;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;

  &:hover:not(:disabled) {
    border-color: #3b82f6;
    color: #3b82f6;
    transform: translateY(-2px);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const ProgressSection = styled.section`
  max-width: 600px;
  margin: 0 auto;
  width: 100%;
`;

const SectionTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1.5rem;
  text-align: center;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
`;

const StatCard = styled.div`
  text-align: center;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const StatIcon = styled.div`
  font-size: 2rem;
  margin-bottom: 0.5rem;
`;

const StatNumber = styled.div`
  font-size: 2rem;
  font-weight: 800;
  color: #3b82f6;
  margin-bottom: 0.5rem;
`;

const StatLabel = styled.div`
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 600;
`;

const GoalProgress = styled.div`
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
`;

const GoalLabel = styled.h4`
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
`;

const GoalBar = styled.div`
  width: 100%;
  height: 12px;
  background: #f3f4f6;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 0.5rem;
`;

const GoalFill = styled.div`
  height: 100%;
  background: linear-gradient(135deg, #3b82f6, #10b981);
  border-radius: 6px;
  width: ${props => props.$progress}%;
  transition: width 0.5s ease;
`;

const GoalText = styled.p`
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
`;

const GoalSetter = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  background: white;
  padding: 1rem;
  border-radius: 12px;
  border: 1px solid #e5e7eb;

  @media (max-width: 480px) {
    flex-direction: column;
    text-align: center;
  }
`;

const GoalSetterLabel = styled.label`
  font-weight: 600;
  color: #111827;
`;

const GoalInput = styled.input`
  width: 80px;
  padding: 0.5rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  text-align: center;
  font-weight: 600;

  &:focus {
    outline: none;
    border-color: #3b82f6;
  }
`;

const GoalInputLabel = styled.span`
  color: #6b7280;
  font-size: 0.875rem;
`;

const TipsSection = styled.section``;

const TipsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
`;

const TipCard = styled.div`
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
  }
`;

const TipIcon = styled.div`
  font-size: 2.5rem;
  margin-bottom: 1rem;
`;

const TipTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.75rem;
`;

const TipDescription = styled.p`
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
`;

const BenefitsSection = styled.section``;

const BenefitsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
`;

const Benefit = styled.div`
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const BenefitIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const BenefitTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
`;

const BenefitText = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
`;

const RelatedSection = styled.section``;

const RelatedGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
`;

const RelatedTimer = styled.a`
  display: block;
  padding: 2rem;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  text-decoration: none;
  transition: all 0.3s ease;

  &:hover {
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
`;

const RelatedTitle = styled.h4`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const RelatedDescription = styled.p`
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
`;

const RelatedAction = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #3b82f6;
  font-weight: 600;
  font-size: 0.875rem;

  svg {
    font-size: 0.75rem;
  }
`;

export default HomeworkTimerPage;