import React from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { FaClock, FaRocket, FaPlay } from 'react-icons/fa';
import TimerComponent from '../components/Timer/TimerComponent';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import SEOHead from '../components/SEO/SEOHead';

const TimerPage = ({ 
  minutes, 
  title, 
  description, 
  keywords,
  url,
  useCases = [],
  relatedTimers = []
}) => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": title,
    "description": description,
    "applicationCategory": "UtilitiesApplication",
    "operatingSystem": "Any",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "Countdown timer",
      "Browser notifications",
      "Sound alerts",
      "Progress visualization",
      "Pause and resume functionality"
    ]
  };

  const handleTimerComplete = () => {
    // Analytics tracking
    if (window.gtag) {
      window.gtag('event', 'timer_complete', {
        'timer_duration': minutes,
        'timer_type': 'standalone'
      });
    }
  };

  return (
    <>
      <SEOHead
        title={title}
        description={description}
        keywords={keywords}
        url={url}
        type="website"
        structuredData={structuredData}
      />
      <PageHeader />
      <Container>
        <TimerSection>
          <TimerComponent 
            initialMinutes={minutes}
            title={`${minutes} Minute Timer`}
            onTimerComplete={handleTimerComplete}
            showControls={true}
          />
        </TimerSection>

        <Header>
          <Title>{title}</Title>
          <Subtitle>{description}</Subtitle>
        </Header>

        {useCases.length > 0 && (
          <UseCasesSection>
            <SectionTitle>Perfect For</SectionTitle>
            <UseCasesGrid>
              {useCases.map((useCase, index) => (
                <UseCaseCard key={index}>
                  <UseCaseIcon>{useCase.icon}</UseCaseIcon>
                  <UseCaseTitle>{useCase.title}</UseCaseTitle>
                  <UseCaseDescription>{useCase.description}</UseCaseDescription>
                </UseCaseCard>
              ))}
            </UseCasesGrid>
          </UseCasesSection>
        )}

        <FeaturesSection>
          <SectionTitle>Why Use Our {minutes} Minute Timer?</SectionTitle>
          <FeaturesGrid>
            <FeatureCard>
              <FeatureIcon><FaClock /></FeatureIcon>
              <FeatureTitle>Precise Timing</FeatureTitle>
              <FeatureText>
                Accurate countdown with visual progress indicator. Perfect for focused work sessions, 
                breaks, or any activity requiring exact timing.
              </FeatureText>
            </FeatureCard>
            <FeatureCard>
              <FeatureIcon><FaRocket /></FeatureIcon>
              <FeatureTitle>Instant Start</FeatureTitle>
              <FeatureText>
                No registration required. Simply click start and begin your {minutes}-minute session 
                immediately. Works on all devices and browsers.
              </FeatureText>
            </FeatureCard>
            <FeatureCard>
              <FeatureIcon><FaPlay /></FeatureIcon>
              <FeatureTitle>Smart Notifications</FeatureTitle>
              <FeatureText>
                Get browser notifications and audio alerts when your timer completes. 
                Never miss the end of your session again.
              </FeatureText>
            </FeatureCard>
          </FeaturesGrid>
        </FeaturesSection>

        {relatedTimers.length > 0 && (
          <RelatedSection>
            <SectionTitle>Other Timer Durations</SectionTitle>
            <RelatedGrid>
              {relatedTimers.map((timer, index) => (
                <RelatedTimer key={index} as={Link} to={timer.url}>
                  <RelatedTitle>{timer.minutes} Minutes</RelatedTitle>
                  <RelatedDescription>{timer.description}</RelatedDescription>
                </RelatedTimer>
              ))}
            </RelatedGrid>
          </RelatedSection>
        )}

        <CTASection>
          <CTATitle>Need More Productivity Tools?</CTATitle>
          <CTAText>
            Discover AI Pomo's complete productivity suite with intelligent project planning, 
            Pomodoro technique integration, and advanced time management features.
          </CTAText>
          <CTAButtons>
            <CTAButton as={Link} to="/register" primary>
              Try AI Pomo Free
            </CTAButton>
            <CTAButton as={Link} to="/features">
              Explore Features
            </CTAButton>
          </CTAButtons>
        </CTASection>
      </Container>
      <Footer />
    </>
  );
};

// Styled Components
const Container = styled.div`
  max-width: 1000px;
  margin: 0 auto;
  padding: 6rem 2rem 4rem;

  @media (max-width: 768px) {
    padding: 5rem 1rem 2rem;
  }
`;

const Header = styled.header`
  text-align: center;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: 3rem;
  font-weight: 800;
  color: #111827;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #d95550, #c44a45);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

  @media (max-width: 768px) {
    font-size: 2.25rem;
  }
`;

const Subtitle = styled.p`
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
`;

const TimerSection = styled.section`
  margin-bottom: 4rem;
  padding: 6rem 1rem;
  background: transparent;
  border-radius: 24px;
  display: flex;
  justify-content: center;
  align-items: center;

  @media (max-width: 768px) {
    padding: 4rem 1rem;
  }
`;

const UseCasesSection = styled.section`
  margin-bottom: 4rem;
`;

const SectionTitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2rem;
  text-align: center;
`;

const UseCasesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
`;

const UseCaseCard = styled.div`
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa, #f1f3f4);
  border-radius: 12px;
  border: 1px solid #e5e7eb;
`;

const UseCaseIcon = styled.div`
  font-size: 2rem;
  margin-bottom: 1rem;
`;

const UseCaseTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const UseCaseDescription = styled.p`
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
`;

const FeaturesSection = styled.section`
  margin-bottom: 4rem;
`;

const FeaturesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
`;

const FeatureCard = styled.div`
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const FeatureIcon = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #d95550, #c44a45);
  color: white;
  border-radius: 50%;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
`;

const FeatureTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
`;

const FeatureText = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
`;

const RelatedSection = styled.section`
  margin-bottom: 4rem;
`;

const RelatedGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
`;

const RelatedTimer = styled.a`
  display: block;
  padding: 1.5rem;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  text-decoration: none;
  transition: all 0.3s ease;

  &:hover {
    border-color: #d95550;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;

const RelatedTitle = styled.h4`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const RelatedDescription = styled.p`
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
`;

const CTASection = styled.section`
  text-align: center;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #f8f9fa, #f1f3f4);
  border-radius: 16px;
  border: 1px solid #e5e7eb;
`;

const CTATitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
`;

const CTAText = styled.p`
  font-size: 1.125rem;
  color: #6b7280;
  margin-bottom: 2rem;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
`;

const CTAButtons = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;

  @media (max-width: 480px) {
    flex-direction: column;
    align-items: center;
  }
`;

const CTAButton = styled.a`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;

  ${props => props.primary ? `
    background: linear-gradient(135deg, #d95550, #c44a45);
    color: white;
    box-shadow: 0 4px 12px rgba(217, 85, 80, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(217, 85, 80, 0.4);
    }
  ` : `
    background: white;
    color: #6b7280;
    border: 2px solid #e5e7eb;

    &:hover {
      border-color: #d95550;
      color: #d95550;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  `}

  @media (max-width: 480px) {
    width: 200px;
    justify-content: center;
  }
`;

export default TimerPage;