import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { FaPlay, FaPause, FaStop, FaDumbbell, FaFire, FaArrowRight, FaClock, FaSync } from 'react-icons/fa';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import SEOHead from '../components/SEO/SEOHead';

const IntervalTimerPage = () => {
  const [workTime, setWorkTime] = useState(30); // seconds
  const [restTime, setRestTime] = useState(15); // seconds
  const [totalRounds, setTotalRounds] = useState(8);
  const [currentRound, setCurrentRound] = useState(1);
  const [timeLeft, setTimeLeft] = useState(30);
  const [isRunning, setIsRunning] = useState(false);
  const [isWorkPhase, setIsWorkPhase] = useState(true);
  const [isCompleted, setIsCompleted] = useState(false);
  const [selectedPreset, setSelectedPreset] = useState('hiit');
  
  const intervalRef = useRef(null);
  const audioRef = useRef(null);
  const workAudioRef = useRef(null);
  const restAudioRef = useRef(null);

  const presets = {
    hiit: {
      name: 'HIIT Training',
      emoji: '🔥',
      workTime: 30,
      restTime: 15,
      rounds: 8,
      description: 'High-intensity interval training'
    },
    tabata: {
      name: 'Tabata',
      emoji: '⚡',
      workTime: 20,
      restTime: 10,
      rounds: 8,
      description: '4-minute Tabata protocol'
    },
    cardio: {
      name: 'Cardio Blast',
      emoji: '❤️',
      workTime: 45,
      restTime: 15,
      rounds: 6,
      description: 'Medium-intensity cardio'
    },
    strength: {
      name: 'Strength Circuit',
      emoji: '💪',
      workTime: 60,
      restTime: 30,
      rounds: 5,
      description: 'Strength training circuits'
    },
    boxing: {
      name: 'Boxing Rounds',
      emoji: '🥊',
      workTime: 180,
      restTime: 60,
      rounds: 3,
      description: 'Professional boxing rounds'
    },
    yoga: {
      name: 'Yoga Flow',
      emoji: '🧘',
      workTime: 90,
      restTime: 30,
      rounds: 4,
      description: 'Gentle yoga intervals'
    }
  };

  useEffect(() => {
    if (isRunning && timeLeft > 0) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(prev => prev - 1);
      }, 1000);
    } else if (isRunning && timeLeft === 0) {
      handlePhaseComplete();
    } else {
      clearInterval(intervalRef.current);
    }

    return () => clearInterval(intervalRef.current);
  }, [isRunning, timeLeft]);

  const handlePhaseComplete = () => {
    if (isWorkPhase) {
      // Work phase complete, start rest
      playRestSound();
      setIsWorkPhase(false);
      setTimeLeft(restTime);
      
      if (Notification.permission === 'granted') {
        new Notification('💪 Work Complete!', {
          body: `Great job! Rest for ${restTime} seconds.`,
          icon: '/ai-pomo.png'
        });
      }
    } else {
      // Rest phase complete
      playWorkSound();
      setIsWorkPhase(true);
      setCurrentRound(prev => prev + 1);
      
      if (currentRound >= totalRounds) {
        // Workout complete
        completeWorkout();
        return;
      }
      
      setTimeLeft(workTime);
      
      if (Notification.permission === 'granted') {
        new Notification('🔥 Rest Complete!', {
          body: `Round ${currentRound + 1} starting. Work for ${workTime} seconds!`,
          icon: '/ai-pomo.png'
        });
      }
    }
  };

  const completeWorkout = () => {
    setIsRunning(false);
    setIsCompleted(true);
    
    if (audioRef.current) {
      audioRef.current.play().catch(e => console.log('Audio play failed:', e));
    }
    
    if (Notification.permission === 'granted') {
      new Notification('🎉 Workout Complete!', {
        body: `Amazing! You completed all ${totalRounds} rounds!`,
        icon: '/ai-pomo.png'
      });
    }

    if (window.gtag) {
      window.gtag('event', 'interval_workout_complete', {
        'preset': selectedPreset,
        'total_rounds': totalRounds,
        'work_time': workTime,
        'rest_time': restTime
      });
    }
  };

  const playWorkSound = () => {
    if (workAudioRef.current) {
      workAudioRef.current.play().catch(e => console.log('Work audio play failed:', e));
    }
  };

  const playRestSound = () => {
    if (restAudioRef.current) {
      restAudioRef.current.play().catch(e => console.log('Rest audio play failed:', e));
    }
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const startTimer = () => {
    setIsRunning(true);
    setIsCompleted(false);
    
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  };

  const pauseTimer = () => {
    setIsRunning(false);
  };

  const resetTimer = () => {
    setIsRunning(false);
    setIsCompleted(false);
    setCurrentRound(1);
    setIsWorkPhase(true);
    setTimeLeft(workTime);
  };

  const loadPreset = (presetKey) => {
    if (isRunning) return;
    
    const preset = presets[presetKey];
    setSelectedPreset(presetKey);
    setWorkTime(preset.workTime);
    setRestTime(preset.restTime);
    setTotalRounds(preset.rounds);
    setTimeLeft(preset.workTime);
    setCurrentRound(1);
    setIsWorkPhase(true);
    setIsCompleted(false);
  };

  const getTotalWorkoutTime = () => {
    return (workTime + restTime) * totalRounds - restTime; // Last round doesn't need rest
  };

  const getProgress = () => {
    const totalTime = getTotalWorkoutTime();
    const elapsedRounds = currentRound - 1;
    const elapsedTime = elapsedRounds * (workTime + restTime);
    const currentPhaseElapsed = isWorkPhase ? (workTime - timeLeft) : workTime + (restTime - timeLeft);
    return Math.min(((elapsedTime + currentPhaseElapsed) / totalTime) * 100, 100);
  };

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Interval Timer - HIIT & Tabata Workout Timer",
    "description": "Professional interval timer for HIIT, Tabata, boxing, and circuit training. Customizable work/rest periods with audio cues and progress tracking.",
    "applicationCategory": "HealthApplication",
    "operatingSystem": "Any",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "HIIT and Tabata presets",
      "Custom interval settings", 
      "Audio workout cues",
      "Round tracking",
      "Progress visualization",
      "Multiple workout types"
    ]
  };

  return (
    <>
      <SEOHead
        title="Interval Timer - HIIT & Tabata Workout Timer | AI Pomo"
        description="Professional interval timer for HIIT, Tabata, boxing, and circuit training. Customizable work/rest periods with audio cues and progress tracking for effective workouts."
        keywords="interval timer, HIIT timer, Tabata timer, workout timer, fitness timer, boxing timer, circuit training timer, exercise timer"
        url="https://www.ai-pomo.com/interval-timer"
        type="website"
        structuredData={structuredData}
      />
      <PageHeader />
      <Container>
        <Header>
          <Title>🏃‍♂️ Interval Timer</Title>
          <Subtitle>
            Professional interval timer for HIIT, Tabata, boxing, and circuit training. 
            Maximize your workout effectiveness with precise timing and audio cues.
          </Subtitle>
        </Header>

        <MainContent>
          <TimerSection>
            <PresetSection>
              <PresetTitle>Choose Your Workout</PresetTitle>
              <PresetGrid>
                {Object.entries(presets).map(([key, preset]) => (
                  <PresetButton
                    key={key}
                    $active={selectedPreset === key}
                    onClick={() => loadPreset(key)}
                    disabled={isRunning}
                  >
                    <PresetEmoji>{preset.emoji}</PresetEmoji>
                    <PresetName>{preset.name}</PresetName>
                    <PresetDescription>{preset.description}</PresetDescription>
                    <PresetDetails>
                      {preset.workTime}s work / {preset.restTime}s rest × {preset.rounds}
                    </PresetDetails>
                  </PresetButton>
                ))}
              </PresetGrid>
            </PresetSection>

            {isCompleted ? (
              <CompletionDisplay>
                <CompletionIcon>🎉</CompletionIcon>
                <CompletionTitle>Workout Complete!</CompletionTitle>
                <CompletionMessage>
                  Amazing! You completed all {totalRounds} rounds of {presets[selectedPreset].name}
                </CompletionMessage>
                <CompletionStats>
                  <StatItem>Total Time: {formatTime(getTotalWorkoutTime())}</StatItem>
                  <StatItem>Work Time: {formatTime(workTime * totalRounds)}</StatItem>
                  <StatItem>Rest Time: {formatTime(restTime * (totalRounds - 1))}</StatItem>
                </CompletionStats>
                <RestartButton onClick={resetTimer}>
                  <FaSync /> Start New Workout
                </RestartButton>
              </CompletionDisplay>
            ) : (
              <TimerDisplay $phase={isWorkPhase ? 'work' : 'rest'} $isRunning={isRunning}>
                <PhaseIndicator $phase={isWorkPhase ? 'work' : 'rest'}>
                  {isWorkPhase ? '💪 WORK' : '😴 REST'}
                </PhaseIndicator>
                <TimeText>{formatTime(timeLeft)}</TimeText>
                <RoundInfo>
                  Round {currentRound} of {totalRounds}
                </RoundInfo>
                <ProgressContainer>
                  <ProgressBar>
                    <ProgressFill $progress={getProgress()} />
                  </ProgressBar>
                  <ProgressText>{Math.round(getProgress())}% Complete</ProgressText>
                </ProgressContainer>
                <NextPhaseInfo>
                  Next: {isWorkPhase ? `Rest ${restTime}s` : `Work ${workTime}s`}
                </NextPhaseInfo>
              </TimerDisplay>
            )}

            <CustomSettingsSection>
              <SettingsTitle>Custom Settings</SettingsTitle>
              <SettingsGrid>
                <SettingGroup>
                  <SettingLabel>Work Time (seconds)</SettingLabel>
                  <SettingInput
                    type="number"
                    min="5"
                    max="600"
                    value={workTime}
                    onChange={(e) => {
                      if (!isRunning) {
                        const value = Math.max(5, parseInt(e.target.value) || 5);
                        setWorkTime(value);
                        if (isWorkPhase) setTimeLeft(value);
                      }
                    }}
                    disabled={isRunning}
                  />
                </SettingGroup>
                <SettingGroup>
                  <SettingLabel>Rest Time (seconds)</SettingLabel>
                  <SettingInput
                    type="number"
                    min="5"
                    max="300"
                    value={restTime}
                    onChange={(e) => {
                      if (!isRunning) {
                        const value = Math.max(5, parseInt(e.target.value) || 5);
                        setRestTime(value);
                        if (!isWorkPhase) setTimeLeft(value);
                      }
                    }}
                    disabled={isRunning}
                  />
                </SettingGroup>
                <SettingGroup>
                  <SettingLabel>Total Rounds</SettingLabel>
                  <SettingInput
                    type="number"
                    min="1"
                    max="50"
                    value={totalRounds}
                    onChange={(e) => {
                      if (!isRunning) {
                        setTotalRounds(Math.max(1, parseInt(e.target.value) || 1));
                      }
                    }}
                    disabled={isRunning}
                  />
                </SettingGroup>
              </SettingsGrid>
              <WorkoutSummary>
                <SummaryItem>
                  <SummaryIcon>⏱️</SummaryIcon>
                  <SummaryText>Total Workout: {formatTime(getTotalWorkoutTime())}</SummaryText>
                </SummaryItem>
                <SummaryItem>
                  <SummaryIcon>💪</SummaryIcon>
                  <SummaryText>Total Work: {formatTime(workTime * totalRounds)}</SummaryText>
                </SummaryItem>
                <SummaryItem>
                  <SummaryIcon>😴</SummaryIcon>
                  <SummaryText>Total Rest: {formatTime(restTime * (totalRounds - 1))}</SummaryText>
                </SummaryItem>
              </WorkoutSummary>
            </CustomSettingsSection>

            <ControlButtons>
              {!isRunning ? (
                <StartButton onClick={startTimer} disabled={isCompleted}>
                  <FaPlay /> {currentRound === 1 && isWorkPhase && timeLeft === workTime ? 'Start Workout' : 'Resume'}
                </StartButton>
              ) : (
                <PauseButton onClick={pauseTimer}>
                  <FaPause /> Pause
                </PauseButton>
              )}
              <ResetButton onClick={resetTimer}>
                <FaStop /> Reset
              </ResetButton>
            </ControlButtons>
          </TimerSection>

          <BenefitsSection>
            <SectionTitle>Why Interval Training?</SectionTitle>
            <BenefitsGrid>
              <Benefit>
                <BenefitIcon>🔥</BenefitIcon>
                <BenefitTitle>Burns More Calories</BenefitTitle>
                <BenefitText>HIIT can burn up to 30% more calories than other forms of exercise in the same time period</BenefitText>
              </Benefit>
              <Benefit>
                <BenefitIcon>⏰</BenefitIcon>
                <BenefitTitle>Time Efficient</BenefitTitle>
                <BenefitText>Get maximum results in minimum time - perfect for busy schedules</BenefitText>
              </Benefit>
              <Benefit>
                <BenefitIcon>❤️</BenefitIcon>
                <BenefitTitle>Improves Heart Health</BenefitTitle>
                <BenefitText>Boosts cardiovascular fitness and heart health more effectively than steady-state cardio</BenefitText>
              </Benefit>
              <Benefit>
                <BenefitIcon>💪</BenefitIcon>
                <BenefitTitle>Builds Muscle</BenefitTitle>
                <BenefitText>Preserves muscle mass while burning fat, leading to a toned physique</BenefitText>
              </Benefit>
            </BenefitsGrid>
          </BenefitsSection>

          <WorkoutTypesSection>
            <SectionTitle>Popular Interval Workouts</SectionTitle>
            <WorkoutTypesGrid>
              <WorkoutType>
                <WorkoutIcon>⚡</WorkoutIcon>
                <WorkoutName>Tabata Protocol</WorkoutName>
                <WorkoutDescription>
                  20 seconds all-out effort, 10 seconds rest, repeated 8 times for 4 minutes total
                </WorkoutDescription>
                <WorkoutBenefits>• Improves anaerobic capacity • Burns fat for hours after</WorkoutBenefits>
              </WorkoutType>
              <WorkoutType>
                <WorkoutIcon>🔥</WorkoutIcon>
                <WorkoutName>HIIT Training</WorkoutName>
                <WorkoutDescription>
                  30 seconds high intensity, 15 seconds rest, customizable rounds and duration
                </WorkoutDescription>
                <WorkoutBenefits>• Versatile and adaptable • Great for all fitness levels</WorkoutBenefits>
              </WorkoutType>
              <WorkoutType>
                <WorkoutIcon>🥊</WorkoutIcon>
                <WorkoutName>Boxing Rounds</WorkoutName>
                <WorkoutDescription>
                  3-minute work periods with 1-minute rest, simulating professional boxing training
                </WorkoutDescription>
                <WorkoutBenefits>• Builds endurance • Improves coordination</WorkoutBenefits>
              </WorkoutType>
              <WorkoutType>
                <WorkoutIcon>🏋️</WorkoutIcon>
                <WorkoutName>Circuit Training</WorkoutName>
                <WorkoutDescription>
                  60 seconds exercise, 30 seconds transition, rotating through different movements
                </WorkoutDescription>
                <WorkoutBenefits>• Full-body workout • Builds strength and cardio</WorkoutBenefits>
              </WorkoutType>
            </WorkoutTypesGrid>
          </WorkoutTypesSection>

          <TipsSection>
            <SectionTitle>📝 Interval Training Tips</SectionTitle>
            <TipsList>
              <TipItem>
                <TipIcon>🎯</TipIcon>
                <TipContent>
                  <TipTitle>Start with Your Fitness Level</TipTitle>
                  <TipDescription>
                    Beginners should start with longer rest periods and shorter work intervals. Gradually increase intensity as you build endurance.
                  </TipDescription>
                </TipContent>
              </TipItem>
              <TipItem>
                <TipIcon>💧</TipIcon>
                <TipContent>
                  <TipTitle>Stay Hydrated</TipTitle>
                  <TipDescription>
                    Interval training is intense. Keep water nearby and take small sips during rest periods to maintain performance.
                  </TipDescription>
                </TipContent>
              </TipItem>
              <TipItem>
                <TipIcon>🔊</TipIcon>
                <TipContent>
                  <TipTitle>Use Audio Cues</TipTitle>
                  <TipDescription>
                    Enable sound notifications to know when to switch between work and rest without watching the timer constantly.
                  </TipDescription>
                </TipContent>
              </TipItem>
              <TipItem>
                <TipIcon>📈</TipIcon>
                <TipContent>
                  <TipTitle>Track Your Progress</TipTitle>
                  <TipDescription>
                    Note how you feel after each workout and gradually increase the intensity or duration as you get stronger.
                  </TipDescription>
                </TipContent>
              </TipItem>
            </TipsList>
          </TipsSection>

          <RelatedSection>
            <SectionTitle>More Fitness Tools</SectionTitle>
            <RelatedGrid>
              <RelatedTimer as={Link} to="/exercise-timer">
                <RelatedTitle>Exercise Timer</RelatedTitle>
                <RelatedDescription>General exercise timer for various workout routines</RelatedDescription>
                <RelatedAction>
                  Try Exercise Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
              <RelatedTimer as={Link} to="/online-stopwatch">
                <RelatedTitle>Stopwatch</RelatedTitle>
                <RelatedDescription>Precise timing for runs, laps, and performance tracking</RelatedDescription>
                <RelatedAction>
                  Try Stopwatch <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
              <RelatedTimer as={Link} to="/focus-timer">
                <RelatedTitle>Focus Timer</RelatedTitle>
                <RelatedDescription>Pomodoro-style timer for workout planning and recovery</RelatedDescription>
                <RelatedAction>
                  Try Focus Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
            </RelatedGrid>
          </RelatedSection>
        </MainContent>

        <audio ref={audioRef} preload="auto">
          <source src="/sounds/completion.mp3" type="audio/mpeg" />
        </audio>
        <audio ref={workAudioRef} preload="auto">
          <source src="/sounds/work-bell.mp3" type="audio/mpeg" />
        </audio>
        <audio ref={restAudioRef} preload="auto">
          <source src="/sounds/rest-bell.mp3" type="audio/mpeg" />
        </audio>
      </Container>
      <Footer />
    </>
  );
};

// Styled Components
const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 6rem 2rem 4rem;

  @media (max-width: 768px) {
    padding: 5rem 1rem 2rem;
  }
`;

const Header = styled.header`
  text-align: center;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: 3rem;
  font-weight: 800;
  color: #111827;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    font-size: 2.25rem;
  }
`;

const Subtitle = styled.p`
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  max-width: 700px;
  margin: 0 auto;
`;

const MainContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4rem;
`;

const TimerSection = styled.section`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
`;

const PresetSection = styled.div`
  text-align: center;
  width: 100%;
  max-width: 900px;
`;

const PresetTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1.5rem;
`;

const PresetGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
`;

const PresetButton = styled.button`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  border: 3px solid ${props => props.$active ? '#ef4444' : '#e5e7eb'};
  border-radius: 16px;
  background: ${props => props.$active ? '#fef2f2' : 'white'};
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;

  &:hover:not(:disabled) {
    border-color: #ef4444;
    background: #fef2f2;
    transform: translateY(-2px);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const PresetEmoji = styled.div`
  font-size: 2rem;
  margin-bottom: 0.5rem;
`;

const PresetName = styled.div`
  font-size: 1rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const PresetDescription = styled.div`
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
`;

const PresetDetails = styled.div`
  font-size: 0.75rem;
  color: #9ca3af;
  font-weight: 600;
`;

const TimerDisplay = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 400px;
  height: 400px;
  border-radius: 50%;
  background: ${props => props.$phase === 'work' ? 
    'linear-gradient(135deg, #ef4444, #dc2626)' : 
    'linear-gradient(135deg, #10b981, #059669)'
  };
  box-shadow: 0 20px 40px ${props => props.$phase === 'work' ? 
    'rgba(239, 68, 68, 0.3)' : 
    'rgba(16, 185, 129, 0.3)'
  };
  color: white;
  position: relative;
  animation: ${props => props.$isRunning ? 'pulse 2s infinite' : 'none'};

  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
  }

  @media (max-width: 768px) {
    width: 350px;
    height: 350px;
  }

  @media (max-width: 480px) {
    width: 300px;
    height: 300px;
  }
`;

const PhaseIndicator = styled.div`
  font-size: 1.25rem;
  font-weight: 800;
  margin-bottom: 1rem;
  color: ${props => props.$phase === 'work' ? '#fed7d7' : '#d1fae5'};
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
`;

const TimeText = styled.div`
  font-size: 4rem;
  font-weight: 800;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
  margin-bottom: 0.5rem;

  @media (max-width: 768px) {
    font-size: 3.5rem;
  }

  @media (max-width: 480px) {
    font-size: 3rem;
  }
`;

const RoundInfo = styled.div`
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
`;

const ProgressContainer = styled.div`
  position: absolute;
  bottom: 2rem;
  left: 2rem;
  right: 2rem;
  text-align: center;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
`;

const ProgressFill = styled.div`
  height: 100%;
  background: white;
  border-radius: 4px;
  width: ${props => props.$progress}%;
  transition: width 1s ease;
`;

const ProgressText = styled.div`
  font-size: 0.875rem;
  font-weight: 600;
  opacity: 0.9;
`;

const NextPhaseInfo = styled.div`
  position: absolute;
  top: 2rem;
  font-size: 0.875rem;
  font-weight: 600;
  opacity: 0.8;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
`;

const CompletionDisplay = styled.div`
  text-align: center;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #10b981, #059669);
  border-radius: 20px;
  color: white;
  max-width: 500px;
`;

const CompletionIcon = styled.div`
  font-size: 4rem;
  margin-bottom: 1rem;
`;

const CompletionTitle = styled.h2`
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
`;

const CompletionMessage = styled.p`
  font-size: 1.125rem;
  margin-bottom: 2rem;
  opacity: 0.9;
`;

const CompletionStats = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 2rem;
`;

const StatItem = styled.div`
  font-size: 1rem;
  font-weight: 600;
  opacity: 0.9;
`;

const RestartButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: 2px solid white;
  border-radius: 12px;
  background: transparent;
  color: white;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0 auto;

  &:hover {
    background: white;
    color: #10b981;
  }
`;

const CustomSettingsSection = styled.div`
  text-align: center;
  max-width: 600px;
  width: 100%;
`;

const SettingsTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1.5rem;
`;

const SettingsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const SettingGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const SettingLabel = styled.label`
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
`;

const SettingInput = styled.input`
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  text-align: center;
  font-weight: 600;
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: #ef4444;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f9fafb;
  }
`;

const WorkoutSummary = styled.div`
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;

  @media (max-width: 480px) {
    flex-direction: column;
    gap: 1rem;
  }
`;

const SummaryItem = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const SummaryIcon = styled.div`
  font-size: 1.25rem;
`;

const SummaryText = styled.div`
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
`;

const ControlButtons = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
  justify-content: center;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const StartButton = styled(Button)`
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
  }
`;

const PauseButton = styled(Button)`
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
  }
`;

const ResetButton = styled(Button)`
  background: white;
  color: #6b7280;
  border: 2px solid #e5e7eb;

  &:hover:not(:disabled) {
    border-color: #ef4444;
    color: #ef4444;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
  }
`;

const SectionTitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2rem;
  text-align: center;
`;

const BenefitsSection = styled.section``;

const BenefitsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
`;

const Benefit = styled.div`
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
  }
`;

const BenefitIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const BenefitTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
`;

const BenefitText = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
`;

const WorkoutTypesSection = styled.section``;

const WorkoutTypesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
`;

const WorkoutType = styled.div`
  padding: 2rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
  }
`;

const WorkoutIcon = styled.div`
  font-size: 2.5rem;
  margin-bottom: 1rem;
`;

const WorkoutName = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
`;

const WorkoutDescription = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 1rem;
`;

const WorkoutBenefits = styled.div`
  font-size: 0.875rem;
  color: #059669;
  font-weight: 600;
`;

const TipsSection = styled.section``;

const TipsList = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const TipItem = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  padding: 2rem;
  background: white;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const TipIcon = styled.div`
  font-size: 2rem;
  flex-shrink: 0;
`;

const TipContent = styled.div`
  flex: 1;
`;

const TipTitle = styled.h4`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const TipDescription = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
`;

const RelatedSection = styled.section``;

const RelatedGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
`;

const RelatedTimer = styled.a`
  display: block;
  padding: 2rem;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  text-decoration: none;
  transition: all 0.3s ease;

  &:hover {
    border-color: #ef4444;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
`;

const RelatedTitle = styled.h4`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const RelatedDescription = styled.p`
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
`;

const RelatedAction = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ef4444;
  font-weight: 600;
  font-size: 0.875rem;

  svg {
    font-size: 0.75rem;
  }
`;

export default IntervalTimerPage;