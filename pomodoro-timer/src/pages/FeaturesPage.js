import React from 'react';
import styled from 'styled-components';
import { <PERSON> } from 'react-router-dom';
import { FaClock, FaBrain, FaChartLine, FaCalendarAlt, FaTasks, FaRocket } from 'react-icons/fa';
import SEOHead from '../components/SEO/SEOHead';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import { createWebsiteStructuredData, createSoftwareApplicationStructuredData } from '../utils/structuredData';

const FeaturesPage = () => {
  const structuredData = [
    createWebsiteStructuredData(),
    createSoftwareApplicationStructuredData()
  ];

  const features = [
    {
      icon: <FaClock />,
      title: "Smart AI Pomodoro Timer for Maximum Focus",
      description: "The most advanced Pomodoro timer available. Our AI-powered timer learns your productivity patterns, automatically adjusts session lengths for optimal focus, and provides intelligent break recommendations. Perfect for professionals who want to maximize their focus time and reduce distractions.",
      benefits: [
        "AI-optimized work and break intervals (25/50 min sessions)",
        "Smart notifications with customizable sound alerts",
        "Automatic Pomodoro session tracking and counting",
        "Advanced focus mode that blocks distractions",
        "Works seamlessly on Mac, Windows, and all devices"
      ]
    },
    {
      icon: <FaBrain />,
      title: "Revolutionary AI Assisted Project Planning",
      description: "Stop spending hours planning—start doing. Our AI project generator transforms any project description into a complete, structured plan with tasks, milestones, and Pomodoro estimates in seconds. This innovative feature helps users significantly reduce project planning time while improving project organization and success rates.",
      benefits: [
        "Instant AI-generated project structures from descriptions",
        "Smart task breakdown with Pomodoro time estimates",
        "Automatic milestone creation and deadline suggestions",
        "Intelligent task prioritization based on dependencies",
        "Export projects to popular tools like Notion and Trello"
      ]
    },
    {
      icon: <FaTasks />,
      title: "Advanced Pomodoro Task Management System",
      description: "Experience task management that actually works. Our intelligent system combines traditional to-do lists with Pomodoro time tracking, giving you clear visibility into how much focused time each task requires. Perfect for professionals managing multiple projects and deadlines simultaneously.",
      benefits: [
        "Smart task prioritization with AI recommendations",
        "Automatic Pomodoro time estimation for each task",
        "Subtask generation and hierarchical task management",
        "Real-time progress tracking with visual indicators",
        "Integration with popular productivity tools and calendars"
      ]
    },
    {
      icon: <FaChartLine />,
      title: "Comprehensive Pomodoro Productivity Tracker",
      description: "Transform your productivity data into actionable insights. Our advanced analytics track every Pomodoro session, analyze your focus patterns, and provide detailed statistics that help you optimize your work schedule. See exactly how much time you've invested in each project and identify your peak performance hours.",
      benefits: [
        "Detailed Pomodoro statistics and productivity reports",
        "Focus time tracking with daily, weekly, and monthly views",
        "Performance trend analysis and productivity patterns",
        "Personalized recommendations for optimal work schedules",
        "Project-specific time investment tracking and ROI analysis"
      ]
    },
    {
      icon: <FaCalendarAlt />,
      title: "Intelligent Pomodoro Calendar Integration",
      description: "Never miss a deadline again. Our smart calendar seamlessly integrates with your existing calendar apps, displaying completed Pomodoros, project milestones, and deadlines in one unified view. Perfect for visualizing your productivity journey and planning future work sessions.",
      benefits: [
        "Visual Pomodoro progress tracking on calendar view",
        "Automatic deadline and milestone management",
        "Daily productivity overview with completed sessions",
        "Long-term goal visualization and achievement tracking",
        "Sync with Google Calendar, Outlook, and other calendar apps"
      ]
    },
    {
      icon: <FaRocket />,
      title: "AI-Powered Productivity Acceleration",
      description: "Experience productivity like never before. Our AI continuously learns from your work patterns to eliminate friction, suggest optimal work schedules, and automate routine planning tasks. Designed to help you achieve more in less time while reducing stress and improving focus.",
      benefits: [
        "Automated workflow optimization and friction elimination",
        "Intelligent break timing based on your energy patterns",
        "AI-powered focus session recommendations",
        "Productivity habit building with personalized coaching",
        "Distraction reduction with smart notification management"
      ]
    }
  ];

  return (
    <>
      <SEOHead
        title="AI Pomodoro Timer Features | Best AI Assisted Project Planning & Task Management"
        description="Discover why AI Pomo is the best AI Pomodoro timer with intelligent project planning, smart task management, productivity tracking, and focus enhancement features. Transform your workflow today."
        keywords="AI Pomodoro timer, AI assisted project planning, Pomodoro task management, Pomodoro productivity tracker, best Pomodoro app, AI project generator, smart Pomodoro timer, Pomodoro for focus"
        url="https://www.ai-pomo.com/features"
        structuredData={structuredData}
      />

      <Container>
        <PageHeader />

        <HeroSection>
          <HeroContent>
            <HeroTitle>AI-Enhanced Pomodoro Timer with Intelligent Project Planning</HeroTitle>
            <HeroSubtitle>
              Experience the future of productivity with AI Pomo's innovative features. Our AI-powered Pomodoro timer
              doesn't just track time—it transforms your ideas into structured projects, manages tasks intelligently,
              and provides insights that help you work more efficiently and achieve your goals faster.
            </HeroSubtitle>
          </HeroContent>
        </HeroSection>

        <FeaturesSection>
          <SectionTitle>Advanced AI-Powered Features for Modern Professionals</SectionTitle>
          <SectionDescription>
            Unlike traditional Pomodoro timers, AI Pomo combines proven time management techniques with cutting-edge
            artificial intelligence. From AI-assisted project planning to intelligent task management and comprehensive
            productivity tracking, every feature is designed to help you work smarter, not harder. Experience the next
            generation of productivity tools built for modern professionals.
          </SectionDescription>

          <FeaturesGrid>
            {features.map((feature, index) => (
              <FeatureCard key={index}>
                <FeatureIcon>{feature.icon}</FeatureIcon>
                <FeatureContent>
                  <FeatureTitle>{feature.title}</FeatureTitle>
                  <FeatureDescription>{feature.description}</FeatureDescription>
                  <BenefitsList>
                    {feature.benefits.map((benefit, idx) => (
                      <BenefitItem key={idx}>✓ {benefit}</BenefitItem>
                    ))}
                  </BenefitsList>
                </FeatureContent>
              </FeatureCard>
            ))}
          </FeaturesGrid>
        </FeaturesSection>

        <ComparisonSection>
          <ComparisonTitle>Why Choose AI Pomo Over Other Pomodoro Apps?</ComparisonTitle>
          <ComparisonGrid>
            <ComparisonCard>
              <ComparisonCardTitle>Traditional Pomodoro Timers</ComparisonCardTitle>
              <ComparisonCardText>
                ❌ Basic timer functionality only<br/>
                ❌ No project planning features<br/>
                ❌ Limited analytics and insights<br/>
                ❌ No AI assistance or automation
              </ComparisonCardText>
            </ComparisonCard>
            <ComparisonCard $highlighted>
              <ComparisonCardTitle>AI Pomo - The Smart Choice</ComparisonCardTitle>
              <ComparisonCardText>
                ✅ AI-powered project planning and task generation<br/>
                ✅ Intelligent Pomodoro timer with adaptive features<br/>
                ✅ Comprehensive productivity tracking and analytics<br/>
                ✅ Seamless integration with existing tools and calendars<br/>
                ✅ Works perfectly on Mac, Windows, and all devices
              </ComparisonCardText>
            </ComparisonCard>
          </ComparisonGrid>
          <ComparisonFooter>
            <strong>Perfect for:</strong> Students studying, software engineers coding, freelancers managing projects,
            professionals with ADHD, and anyone who wants to maximize their focus time with intelligent productivity tools.
          </ComparisonFooter>
        </ComparisonSection>

        <CTASection>
          <CTAContent>
            <CTATitle>Start Using AI Pomo's Advanced Pomodoro Timer Today</CTATitle>
            <CTADescription>
              Experience the future of productivity with AI Pomo's intelligent features. Get AI-assisted project planning,
              smart task management, and comprehensive productivity tracking—all in one powerful platform. Start your free
              trial and discover how AI can transform your workflow and help you achieve your goals faster.
            </CTADescription>
            <CTAButtons>
              <PrimaryButton to="/register">Start Free Trial - No Credit Card Required</PrimaryButton>
              <SecondaryButton to="/pricing">View Pricing Plans</SecondaryButton>
            </CTAButtons>
          </CTAContent>
        </CTASection>

        <Footer />
      </Container>
    </>
  );
};

// Styled Components
const Container = styled.div`
  min-height: 100vh;
  background-color: #ffffff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: #111827;
  overflow-x: hidden;
`;

const HeroSection = styled.section`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-image: url('/images/content-banner-no-text.svg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  color: white;
  padding: 10rem 2rem 6rem;
  text-align: center;
  margin-top: 0;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1;
  }

  > * {
    position: relative;
    z-index: 2;
  }
`;

const HeroContent = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const HeroTitle = styled.h1`
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const HeroSubtitle = styled.p`
  font-size: 1.25rem;
  line-height: 1.6;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
`;

const FeaturesSection = styled.section`
  padding: 6rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const SectionTitle = styled.h2`
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 1.5rem;
  color: #2d3748;
`;

const SectionDescription = styled.p`
  font-size: 1.1rem;
  line-height: 1.7;
  text-align: center;
  color: #4a5568;
  max-width: 800px;
  margin: 0 auto 4rem;
`;

const FeaturesGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
`;

const FeatureCard = styled.div`
  display: flex;
  gap: 2rem;
  padding: 2.5rem;
  background: #f7fafc;
  border-radius: 12px;
  border-left: 4px solid #d95550;

  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }
`;

const FeatureIcon = styled.div`
  font-size: 3rem;
  color: #d95550;
  flex-shrink: 0;
`;

const FeatureContent = styled.div`
  flex: 1;
`;

const FeatureTitle = styled.h3`
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2d3748;
`;

const FeatureDescription = styled.p`
  font-size: 1rem;
  line-height: 1.6;
  color: #4a5568;
  margin-bottom: 1.5rem;
`;

const BenefitsList = styled.ul`
  list-style: none;
  padding: 0;
`;

const BenefitItem = styled.li`
  font-size: 0.95rem;
  color: #2d3748;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
`;

const CTASection = styled.section`
  background-color: #2d3748;
  color: white;
  padding: 4rem 2rem;
  text-align: center;
`;

const CTAContent = styled.div`
  max-width: 600px;
  margin: 0 auto;
`;

const CTATitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
`;

const CTADescription = styled.p`
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  opacity: 0.9;
`;

const CTAButtons = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;

  @media (max-width: 480px) {
    flex-direction: column;
  }
`;

const PrimaryButton = styled(Link)`
  background-color: #d95550;
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  transition: background-color 0.2s;

  &:hover {
    background-color: #c04540;
  }
`;

const SecondaryButton = styled(Link)`
  background-color: transparent;
  color: white;
  padding: 0.75rem 2rem;
  border: 2px solid white;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s;

  &:hover {
    background-color: white;
    color: #2d3748;
  }
`;

const ComparisonSection = styled.section`
  background: #f7fafc;
  padding: 4rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const ComparisonTitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  color: #2d3748;
`;

const ComparisonGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
`;

const ComparisonCard = styled.div`
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: ${props => props.$highlighted ? '3px solid #d95550' : '1px solid #e2e8f0'};
  position: relative;

  ${props => props.$highlighted && `
    &::before {
      content: 'RECOMMENDED';
      position: absolute;
      top: -12px;
      left: 50%;
      transform: translateX(-50%);
      background: #d95550;
      color: white;
      padding: 0.25rem 1rem;
      border-radius: 20px;
      font-size: 0.75rem;
      font-weight: 600;
    }
  `}
`;

const ComparisonCardTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2d3748;
  text-align: center;
`;

const ComparisonCardText = styled.div`
  font-size: 0.95rem;
  line-height: 1.8;
  color: #4a5568;
`;

const ComparisonFooter = styled.p`
  text-align: center;
  font-size: 1rem;
  color: #4a5568;
  margin-top: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  border-left: 4px solid #d95550;
`;

export default FeaturesPage;
