import React, { useState } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { FaCog, FaClock, FaPlay, FaRandom, FaArrowRight } from 'react-icons/fa';
import TimerComponent from '../components/Timer/TimerComponent';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import SEOHead from '../components/SEO/SEOHead';

const CustomTimerPage = () => {
  const [customMinutes, setCustomMinutes] = useState(25);
  const [timerStarted, setTimerStarted] = useState(false);

  const presetDurations = [
    { minutes: 5, label: '5 min', description: 'Quick task' },
    { minutes: 10, label: '10 min', description: 'Short break' },
    { minutes: 15, label: '15 min', description: 'Mini session' },
    { minutes: 25, label: '25 min', description: 'Pomodoro' },
    { minutes: 30, label: '30 min', description: 'Half hour' },
    { minutes: 45, label: '45 min', description: 'Focus block' },
    { minutes: 60, label: '60 min', description: 'Deep work' },
    { minutes: 90, label: '90 min', description: 'Extended session' }
  ];

  const useCases = [
    {
      emoji: '🎨',
      title: 'Creative Work',
      description: 'Art, design, writing, and creative projects',
      recommendedTime: '30-90 minutes'
    },
    {
      emoji: '🏃',
      title: 'Exercise & Fitness',
      description: 'Workouts, yoga, running, and physical activities',
      recommendedTime: '15-60 minutes'
    },
    {
      emoji: '🍳',
      title: 'Cooking & Baking',
      description: 'Food preparation, cooking times, and kitchen tasks',
      recommendedTime: '10-45 minutes'
    },
    {
      emoji: '🧹',
      title: 'Household Tasks',
      description: 'Cleaning, organizing, and home maintenance',
      recommendedTime: '15-30 minutes'
    }
  ];

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Custom Timer - Flexible Time Management Tool",
    "description": "Fully customizable timer for any duration and purpose. Perfect for creative work, exercise, cooking, and personal productivity.",
    "applicationCategory": "ProductivityApplication",
    "operatingSystem": "Any",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "Custom duration setting",
      "Preset time options",
      "Visual progress tracking",
      "Sound notifications",
      "Flexible use cases",
      "Mobile responsive"
    ]
  };

  const handleMinutesChange = (e) => {
    const value = parseInt(e.target.value);
    if (value >= 1 && value <= 480) { // Max 8 hours
      setCustomMinutes(value);
    }
  };

  const handlePresetClick = (minutes) => {
    setCustomMinutes(minutes);
    setTimerStarted(false);
  };

  const handleTimerComplete = () => {
    if (window.gtag) {
      window.gtag('event', 'custom_timer_complete', {
        'timer_duration': customMinutes,
        'timer_type': 'custom'
      });
    }
  };

  return (
    <>
      <SEOHead
        title="Custom Timer - Flexible Time Management Tool | AI Pomo"
        description="Fully customizable timer for any duration and purpose. Perfect for creative work, exercise, cooking, household tasks, and personal productivity. Set your own time from 1 minute to 8 hours."
        keywords="custom timer, flexible timer, any duration timer, personalized timer, creative work timer, exercise timer, cooking timer"
        url="https://www.ai-pomo.com/custom-timer"
        type="website"
        structuredData={structuredData}
      />
      <PageHeader />
      <Container>
        <Header>
          <Title>Custom Timer</Title>
          <Subtitle>
            Create your perfect timer for any task or activity. 
            Set any duration from 1 minute to 8 hours for maximum flexibility.
          </Subtitle>
        </Header>

        <MainContent>
          <TimerSection>
            <TimerControls>
              <TimeInputSection>
                <InputLabel>Set Timer Duration (minutes)</InputLabel>
                <TimeInputContainer>
                  <TimeInput
                    type="number"
                    min="1"
                    max="480"
                    value={customMinutes}
                    onChange={handleMinutesChange}
                    disabled={timerStarted}
                  />
                  <TimeUnit>minutes</TimeUnit>
                </TimeInputContainer>
                <TimeNote>Range: 1 minute to 8 hours (480 minutes)</TimeNote>
              </TimeInputSection>

              <PresetSection>
                <PresetTitle>Quick Presets</PresetTitle>
                <PresetGrid>
                  {presetDurations.map((preset, index) => (
                    <PresetButton
                      key={index}
                      onClick={() => handlePresetClick(preset.minutes)}
                      $active={customMinutes === preset.minutes}
                      disabled={timerStarted}
                    >
                      <PresetTime>{preset.label}</PresetTime>
                      <PresetDesc>{preset.description}</PresetDesc>
                    </PresetButton>
                  ))}
                </PresetGrid>
              </PresetSection>
            </TimerControls>

            <TimerDisplay>
              <TimerComponent 
                initialMinutes={customMinutes}
                title={`${customMinutes} Minute Custom Timer`}
                onTimerComplete={handleTimerComplete}
                showControls={true}
                key={customMinutes} // Force re-render when minutes change
              />
            </TimerDisplay>
          </TimerSection>

          <UseCasesSection>
            <SectionTitle>Perfect For Any Activity</SectionTitle>
            <UseCasesGrid>
              {useCases.map((useCase, index) => (
                <UseCaseCard key={index}>
                  <UseCaseIcon>{useCase.emoji}</UseCaseIcon>
                  <UseCaseTitle>{useCase.title}</UseCaseTitle>
                  <UseCaseDescription>{useCase.description}</UseCaseDescription>
                  <UseCaseTime>{useCase.recommendedTime}</UseCaseTime>
                </UseCaseCard>
              ))}
            </UseCasesGrid>
          </UseCasesSection>

          <FeaturesSection>
            <SectionTitle>Why Use Custom Timer?</SectionTitle>
            <FeaturesGrid>
              <FeatureCard>
                <FeatureIcon><FaCog /></FeatureIcon>
                <FeatureTitle>Total Flexibility</FeatureTitle>
                <FeatureText>
                  Set any duration from 1 minute to 8 hours. Perfect for tasks that don't fit standard time blocks.
                </FeatureText>
              </FeatureCard>
              <FeatureCard>
                <FeatureIcon><FaRandom /></FeatureIcon>
                <FeatureTitle>Multiple Use Cases</FeatureTitle>
                <FeatureText>
                  From cooking and exercise to creative work and household tasks - one timer for everything.
                </FeatureText>
              </FeatureCard>
              <FeatureCard>
                <FeatureIcon><FaClock /></FeatureIcon>
                <FeatureTitle>Quick Presets</FeatureTitle>
                <FeatureText>
                  Choose from common durations or set your own. Easy switching between different time needs.
                </FeatureText>
              </FeatureCard>
            </FeaturesGrid>
          </FeaturesSection>

          <TipsSection>
            <SectionTitle>Custom Timer Tips</SectionTitle>
            <TipsList>
              <TipItem>
                <TipNumber>1</TipNumber>
                <TipContent>
                  <TipTitle>Match Duration to Task</TipTitle>
                  <TipDescription>
                    Consider the natural rhythm of your activity. Creative work might need 60-90 minutes, while household tasks work well in 15-30 minute blocks.
                  </TipDescription>
                </TipContent>
              </TipItem>
              <TipItem>
                <TipNumber>2</TipNumber>
                <TipContent>
                  <TipTitle>Start with Shorter Times</TipTitle>
                  <TipDescription>
                    If you're new to time management, start with shorter durations and gradually increase as you build focus stamina.
                  </TipDescription>
                </TipContent>
              </TipItem>
              <TipItem>
                <TipNumber>3</TipNumber>
                <TipContent>
                  <TipTitle>Plan Your Breaks</TipTitle>
                  <TipDescription>
                    For longer custom sessions (60+ minutes), plan mini-breaks every 20-30 minutes to maintain focus and prevent fatigue.
                  </TipDescription>
                </TipContent>
              </TipItem>
            </TipsList>
          </TipsSection>

          <RelatedSection>
            <SectionTitle>Specialized Timers</SectionTitle>
            <RelatedGrid>
              <RelatedTimer as={Link} to="/work-timer">
                <RelatedTitle>Work Timer</RelatedTitle>
                <RelatedDescription>Optimized for professional productivity</RelatedDescription>
                <RelatedAction>
                  Try Work Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
              <RelatedTimer as={Link} to="/study-timer">
                <RelatedTitle>Study Timer</RelatedTitle>
                <RelatedDescription>Perfect for learning and education</RelatedDescription>
                <RelatedAction>
                  Try Study Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
              <RelatedTimer as={Link} to="/break-timer">
                <RelatedTitle>Break Timer</RelatedTitle>
                <RelatedDescription>Rejuvenating rest periods</RelatedDescription>
                <RelatedAction>
                  Try Break Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
            </RelatedGrid>
          </RelatedSection>
        </MainContent>

        <CTASection>
          <CTATitle>Ready to Optimize Your Time?</CTATitle>
          <CTAText>
            Join AI Pomo for comprehensive time management with project planning, 
            goal tracking, and advanced productivity features.
          </CTAText>
          <CTAButtons>
            <CTAButton as={Link} to="/register" primary>
              Get Started Free
            </CTAButton>
            <CTAButton as={Link} to="/features">
              Explore Features
            </CTAButton>
          </CTAButtons>
        </CTASection>
      </Container>
      <Footer />
    </>
  );
};

// Styled Components
const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 6rem 2rem 4rem;

  @media (max-width: 768px) {
    padding: 5rem 1rem 2rem;
  }
`;

const Header = styled.header`
  text-align: center;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: 3rem;
  font-weight: 800;
  color: #111827;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

  @media (max-width: 768px) {
    font-size: 2.25rem;
  }
`;

const Subtitle = styled.p`
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  max-width: 700px;
  margin: 0 auto;
`;

const MainContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4rem;
`;

const TimerSection = styled.section`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;

  @media (max-width: 968px) {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
`;

const TimerControls = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const TimeInputSection = styled.div`
  background: white;
  padding: 2rem;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
`;

const InputLabel = styled.label`
  display: block;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
`;

const TimeInputContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
`;

const TimeInput = styled.input`
  width: 120px;
  padding: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: #f9fafb;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #7c3aed;
    background: white;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const TimeUnit = styled.span`
  font-size: 1.125rem;
  color: #6b7280;
  font-weight: 500;
`;

const TimeNote = styled.p`
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
`;

const PresetSection = styled.div`
  background: white;
  padding: 2rem;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
`;

const PresetTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
`;

const PresetGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.75rem;

  @media (max-width: 480px) {
    grid-template-columns: repeat(2, 1fr);
  }
`;

const PresetButton = styled.button`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem 0.5rem;
  border: 2px solid ${props => props.$active ? '#7c3aed' : '#e5e7eb'};
  border-radius: 8px;
  background: ${props => props.$active ? '#f3f4f6' : 'white'};
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    border-color: #7c3aed;
    transform: translateY(-2px);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const PresetTime = styled.span`
  font-weight: 600;
  color: #111827;
  font-size: 0.875rem;
`;

const PresetDesc = styled.span`
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
`;

const TimerDisplay = styled.div`
  display: flex;
  justify-content: center;
`;

const UseCasesSection = styled.section``;

const SectionTitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2rem;
  text-align: center;
`;

const UseCasesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
`;

const UseCaseCard = styled.div`
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
  }
`;

const UseCaseIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const UseCaseTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.75rem;
`;

const UseCaseDescription = styled.p`
  color: #6b7280;
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 1rem;
`;

const UseCaseTime = styled.div`
  display: inline-block;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  color: white;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
`;

const FeaturesSection = styled.section``;

const FeaturesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
`;

const FeatureCard = styled.div`
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const FeatureIcon = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  color: white;
  border-radius: 50%;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
`;

const FeatureTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
`;

const FeatureText = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
`;

const TipsSection = styled.section``;

const TipsList = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const TipItem = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const TipNumber = styled.div`
  width: 2.5rem;
  height: 2.5rem;
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.125rem;
  flex-shrink: 0;
`;

const TipContent = styled.div`
  flex: 1;
`;

const TipTitle = styled.h4`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const TipDescription = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
`;

const RelatedSection = styled.section``;

const RelatedGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
`;

const RelatedTimer = styled.a`
  display: block;
  padding: 2rem;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  text-decoration: none;
  transition: all 0.3s ease;

  &:hover {
    border-color: #7c3aed;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
`;

const RelatedTitle = styled.h4`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const RelatedDescription = styled.p`
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
`;

const RelatedAction = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #7c3aed;
  font-weight: 600;
  font-size: 0.875rem;

  svg {
    font-size: 0.75rem;
  }
`;

const CTASection = styled.section`
  text-align: center;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #f8f9fa, #f1f3f4);
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  margin-top: 2rem;
`;

const CTATitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
`;

const CTAText = styled.p`
  font-size: 1.125rem;
  color: #6b7280;
  margin-bottom: 2rem;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
`;

const CTAButtons = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;

  @media (max-width: 480px) {
    flex-direction: column;
    align-items: center;
  }
`;

const CTAButton = styled.a`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;

  ${props => props.primary ? `
    background: linear-gradient(135deg, #7c3aed, #a855f7);
    color: white;
    box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(124, 58, 237, 0.4);
    }
  ` : `
    background: white;
    color: #6b7280;
    border: 2px solid #e5e7eb;

    &:hover {
      border-color: #7c3aed;
      color: #7c3aed;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  `}

  @media (max-width: 480px) {
    width: 200px;
    justify-content: center;
  }
`;

export default CustomTimerPage;