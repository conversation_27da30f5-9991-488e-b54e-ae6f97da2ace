import React from 'react';
import { <PERSON>a<PERSON>en, FaBook<PERSON><PERSON>, FaLightbulb } from 'react-icons/fa';
import ScenarioTimerPage from '../components/Timer/ScenarioTimerPage';

const WriterTimerPage = () => {
  const writerConfig = {
    scenario: 'Writer',
    title: 'Writer Timer - Creative Writing Productivity Timer | AI Pomo',
    description: 'Professional timer for writers, authors, and content creators. Perfect for writing sessions, editing, research, and maintaining creative flow for all types of writing projects.',
    keywords: 'writing timer, author timer, creative writing, content creation timer, writing productivity, novelist timer, blogger timer',
    url: 'https://www.ai-pomo.com/writer-timer',
    heroTitle: 'Writer Timer',
    heroDescription: 'Unleash your creative writing potential with structured writing sessions. Designed for authors, bloggers, and content creators to maintain flow and meet deadlines.',
    defaultMinutes: 60,
    gradientColors: ['#8b5cf6', '#7c3aed'],
    backgroundColor: 'linear-gradient(135deg, #faf5ff, #f3e8ff)',
    
    scenarios: [
      {
        emoji: '✍️',
        title: 'Creative Writing',
        description: 'Novels, short stories, poetry, and creative content',
        recommendedTime: 'Recommended 60-90 minutes'
      },
      {
        emoji: '📝',
        title: 'Content Creation',
        description: 'Blog posts, articles, marketing copy, and web content',
        recommendedTime: 'Recommended 45-75 minutes'
      },
      {
        emoji: '✏️',
        title: 'Editing & Revision',
        description: 'Proofreading, editing drafts, and content refinement',
        recommendedTime: 'Recommended 30-60 minutes'
      },
      {
        emoji: '🔍',
        title: 'Research & Planning',
        description: 'Story research, outline creation, and content planning',
        recommendedTime: 'Recommended 25-45 minutes'
      }
    ],

    benefits: [
      {
        emoji: '🎨',
        title: 'Creative Flow',
        description: 'Extended writing sessions help you enter and maintain creative flow state'
      },
      {
        emoji: '📈',
        title: 'Higher Word Count',
        description: 'Structured timing leads to more consistent and higher daily word counts'
      },
      {
        emoji: '✨',
        title: 'Better Quality',
        description: 'Focused writing time produces more polished and coherent content'
      },
      {
        emoji: '📅',
        title: 'Deadline Management',
        description: 'Consistent writing habits help you meet publishing and submission deadlines'
      }
    ],

    tips: [
      {
        title: 'Start with Brain Dump',
        description: 'Begin sessions by writing anything to get thoughts flowing before focusing on quality'
      },
      {
        title: 'Avoid Editing While Writing',
        description: 'Separate creation and editing phases to maintain creative momentum'
      },
      {
        title: 'Set Word Count Goals',
        description: 'Combine timed sessions with specific word count targets for measurable progress'
      },
      {
        title: 'Create Writing Rituals',
        description: 'Develop pre-writing routines to signal your brain it\'s time to create'
      },
      {
        title: 'End Mid-Sentence',
        description: 'Stop writing in the middle of a thought to make it easier to resume next session'
      }
    ],

    customFeatures: [
      {
        icon: <FaPen />,
        title: 'Creative Flow',
        description: 'Optimized timing for sustained creative thinking and artistic expression'
      },
      {
        icon: <FaBookOpen />,
        title: 'Genre Flexibility',
        description: 'Adaptable for fiction, non-fiction, journalism, and academic writing'
      },
      {
        icon: <FaLightbulb />,
        title: 'Inspiration Capture',
        description: 'Perfect timing for capturing and developing creative ideas'
      }
    ],

    relatedTimers: [
      {
        title: 'Study Timer',
        description: 'Research and learning',
        duration: '25-60 minutes',
        url: '/study-timer'
      },
      {
        title: 'Break Timer',
        description: 'Creative rejuvenation',
        duration: '10-20 minutes',
        url: '/break-timer'
      },
      {
        title: 'Work Timer',
        description: 'Professional writing tasks',
        duration: '25-90 minutes',
        url: '/work-timer'
      }
    ]
  };

  return <ScenarioTimerPage config={writerConfig} />;
};

export default WriterTimerPage;