import React, { useState } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { FaCheck, FaRocket, FaStar } from 'react-icons/fa';
import SEOHead from '../components/SEO/SEOHead';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import PaddleCheckout from '../components/PaddleCheckout';
import { isAuthenticated } from '../services/authService';
import { createWebsiteStructuredData, createSoftwareApplicationStructuredData } from '../utils/structuredData';

const PricingPage = () => {
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [isCheckoutOpen, setIsCheckoutOpen] = useState(false);
  const [paymentMessage, setPaymentMessage] = useState('');

  const structuredData = [
    createWebsiteStructuredData(),
    createSoftwareApplicationStructuredData()
  ];

  const handlePlanSelect = (planType) => {
    if (!isAuthenticated()) {
      // Redirect to login/register if not authenticated
      window.location.href = '/register';
      return;
    }

    if (planType === 'free') {
      // Free plan - redirect to register
      window.location.href = '/register';
      return;
    }

    // For premium plans, open Paddle checkout
    setSelectedPlan(planType);
    setIsCheckoutOpen(true);
  };

  const handlePaymentSuccess = () => {
    setIsCheckoutOpen(false);
    setPaymentMessage('🎉 Payment successful! Welcome to Premium!');
    setTimeout(() => {
      window.location.href = '/';
    }, 2000);
  };

  const handlePaymentError = (error) => {
    setIsCheckoutOpen(false);
    setPaymentMessage(`Payment failed: ${error}`);
  };

  const handlePaymentCancel = () => {
    setIsCheckoutOpen(false);
    setPaymentMessage('Payment cancelled. You can try again anytime.');
  };

  const pricingPlans = [
    {
      id: 'free',
      title: 'Free Plan',
      subtitle: 'Perfect for Getting Started',
      price: 0,
      period: 'forever',
      description: 'Experience the power of AI-enhanced Pomodoro technique with essential features for personal productivity.',
      features: [
        { text: 'Up to 2 open projects', included: true },
        { text: 'Unlimited tasks per project', included: true },
        { text: 'AI Pomodoro timer with focus sessions', included: true },
        { text: 'Basic productivity statistics (7 days)', included: true },
        { text: 'Limited AI project generation (3 per month)', included: true },
        { text: 'Calendar integration for milestone tracking', included: true },
        { text: 'More than 2 open projects', included: false },
        { text: 'Unlimited AI project generation', included: false },
        { text: 'Priority customer support', included: false },
        { text: 'Advanced analytics and insights', included: false },
        { text: 'Premium themes and customization', included: false },
        { text: 'Export data capabilities', included: false }
      ],
      buttonText: 'Start Free',
      isPopular: false,
      badge: null
    },
    {
      id: 'monthly',
      title: 'Monthly Premium',
      subtitle: 'Flexible Monthly Billing',
      price: 8,
      period: 'per month',
      description: 'Perfect for trying out premium features with monthly flexibility and full access to all productivity tools.',
      features: [
        { text: 'Unlimited open projects', included: true },
        { text: 'Unlimited tasks per project', included: true },
        { text: 'AI Pomodoro timer with advanced customization', included: true },
        { text: 'Advanced productivity statistics and analytics', included: true },
        { text: 'Unlimited AI project generation', included: true },
        { text: 'Advanced calendar integration and planning', included: true },
        { text: 'Priority customer support', included: true },
        { text: 'Export data to popular productivity tools', included: true },
        { text: 'Premium themes and customization', included: true },
        { text: 'Advanced focus session insights', included: true },
        { text: 'Future premium features included', included: true },
        { text: 'Cancel anytime', included: true }
      ],
      buttonText: 'Start Monthly Plan',
      isPopular: false,
      badge: null
    },
    {
      id: 'yearly',
      title: 'Yearly Premium',
      subtitle: 'Save 37% with Annual Billing',
      price: 60,
      period: 'per year',
      originalPrice: 96,
      monthlyEquivalent: 5,
      description: 'Best value for serious productivity enthusiasts. Get 2 months free compared to monthly billing!',
      features: [
        { text: 'All Monthly Premium features', included: true },
        { text: 'Unlimited open projects', included: true },
        { text: 'Unlimited AI project generation', included: true },
        { text: 'Advanced productivity analytics', included: true },
        { text: 'Priority customer support', included: true },
        { text: 'Export data capabilities', included: true },
        { text: 'Premium themes and customization', included: true },
        { text: '37% savings vs monthly billing', included: true },
        { text: 'Annual feature roadmap input', included: true },
        { text: 'Extended data history', included: true }
      ],
      buttonText: 'Start Yearly Plan',
      isPopular: true,
      badge: 'Most Popular'
    },
    {
      id: 'lifetime',
      title: 'Lifetime Premium',
      subtitle: 'One-Time Payment, Forever Access',
      price: 100,
      period: 'lifetime access',
      originalPrice: 960,
      yearsBreakEven: 1.25,
      description: 'Make a single payment and enjoy all premium features forever. Best long-term value for productivity power users.',
      features: [
        { text: 'All Premium Plan features included forever', included: true },
        { text: 'Unlimited open projects forever', included: true },
        { text: 'Unlimited AI project generation forever', included: true },
        { text: 'Priority support for life', included: true },
        { text: 'All future premium features included', included: true },
        { text: 'No recurring payments ever', included: true },
        { text: 'Lifetime software updates', included: true },
        { text: 'Early access to new features', included: true },
        { text: 'Exclusive lifetime member benefits', included: true },
        { text: 'Pay once, save $860+ over 10 years', included: true },
        { text: 'VIP community access', included: true },
        { text: 'Priority feature requests', included: true }
      ],
      buttonText: 'Get Lifetime Access',
      isPopular: false,
      badge: 'Best Value'
    }
  ];

  return (
    <>
      <SEOHead
        title="AI Pomodoro Timer Pricing | Affordable AI Productivity Software Plans"
        description="Discover affordable pricing for AI Pomo's AI-powered Pomodoro timer. Choose from Free, Premium ($8/month), or Lifetime ($100) plans. Start your productivity transformation today."
        keywords="AI Pomodoro timer pricing, Pomodoro app cost, AI productivity software pricing, Pomodoro timer subscription, affordable productivity tools, AI time management pricing"
        url="https://www.ai-pomo.com/pricing"
        structuredData={structuredData}
      />

      <Container>
        <PageHeader />

        <HeroSection>
          <HeroContent>
            <HeroTitle>Simple, Transparent Pricing for AI-Powered Productivity</HeroTitle>
            <HeroSubtitle>
              Choose the perfect plan for your productivity needs. Start free and upgrade anytime to unlock
              unlimited AI project generation, advanced analytics, and premium features that transform
              how you work and manage time.
            </HeroSubtitle>
          </HeroContent>
        </HeroSection>

        <PricingSection>
          <SectionTitle>Choose Your Productivity Plan</SectionTitle>
          <SectionDescription>
            All plans include our core AI-enhanced Pomodoro timer. Upgrade to Premium or Lifetime
            for unlimited projects, advanced AI features, and comprehensive productivity analytics.
          </SectionDescription>

          <PricingGrid>
            {pricingPlans.map((plan, index) => (
              <PricingCard key={plan.id} $isPopular={plan.isPopular}>
                {plan.badge && (
                  <PricingBadge $isPopular={plan.isPopular}>
                    {plan.isPopular && <FaStar />}
                    {plan.badge}
                  </PricingBadge>
                )}

                <PricingHeader>
                  <PlanTitle>{plan.title}</PlanTitle>
                  <PlanSubtitle>{plan.subtitle}</PlanSubtitle>
                  <PriceContainer>
                    {plan.price === 0 ? (
                      <Price>Free</Price>
                    ) : (
                      <>
                        <PriceSymbol>$</PriceSymbol>
                        <Price>{plan.price}</Price>
                        {plan.originalPrice && (
                          <OriginalPrice>was ${plan.originalPrice}</OriginalPrice>
                        )}
                      </>
                    )}
                    <PricePeriod>{plan.period}</PricePeriod>
                    {plan.monthlyEquivalent && (
                      <MonthlyEquivalent>Just ${plan.monthlyEquivalent}/month</MonthlyEquivalent>
                    )}
                    {plan.yearsBreakEven && (
                      <BreakEvenInfo>Pays for itself in {plan.yearsBreakEven} years</BreakEvenInfo>
                    )}
                  </PriceContainer>
                  <PlanDescription>{plan.description}</PlanDescription>
                </PricingHeader>

                <FeaturesList>
                  {plan.features.map((feature, idx) => (
                    <FeatureItem key={idx} $included={feature.included}>
                      <FeatureIcon $included={feature.included}>
                        {feature.included ? <FaCheck /> : '×'}
                      </FeatureIcon>
                      <FeatureText $included={feature.included}>{feature.text}</FeatureText>
                    </FeatureItem>
                  ))}
                </FeaturesList>

                <PricingButton
                  onClick={() => handlePlanSelect(plan.id)}
                  $isPopular={plan.isPopular}
                >
                  {plan.buttonText}
                </PricingButton>
              </PricingCard>
            ))}
          </PricingGrid>
        </PricingSection>

        <FAQSection>
          <FAQTitle>Frequently Asked Questions</FAQTitle>
          <FAQGrid>
            <FAQCard>
              <FAQQuestion>Can I upgrade or downgrade my plan anytime?</FAQQuestion>
              <FAQAnswer>
                Yes! You can upgrade from Free to Premium or Lifetime at any time. Your new features
                will be available immediately after payment confirmation.
              </FAQAnswer>
            </FAQCard>
            <FAQCard>
              <FAQQuestion>What's included in the AI project generation?</FAQQuestion>
              <FAQAnswer>
                Our AI analyzes your project descriptions and automatically generates structured task lists,
                milestones, and Pomodoro time estimates, saving hours of planning time.
              </FAQAnswer>
            </FAQCard>
            <FAQCard>
              <FAQQuestion>Is there a free trial for premium features?</FAQQuestion>
              <FAQAnswer>
                Yes! All new users start with our Free plan which includes limited AI features. You can
                experience the core functionality before deciding to upgrade.
              </FAQAnswer>
            </FAQCard>
            <FAQCard>
              <FAQQuestion>What happens to my data if I cancel?</FAQQuestion>
              <FAQAnswer>
                Your data remains safe and accessible. You'll continue to have access to the Free plan
                features, and can export your data anytime.
              </FAQAnswer>
            </FAQCard>
          </FAQGrid>
        </FAQSection>

        <CTASection>
          <CTAContent>
            <CTATitle>Ready to Transform Your Productivity?</CTATitle>
            <CTADescription>
              Join thousands of professionals who use AI Pomo to boost focus, manage projects intelligently,
              and achieve their goals faster. Start free and experience the power of AI-enhanced productivity.
            </CTADescription>
            <CTAButtons>
              <PrimaryButton to="/register">Start Free Trial - No Credit Card Required</PrimaryButton>
              <SecondaryButton to="/contact">Contact Sales for Enterprise</SecondaryButton>
            </CTAButtons>
          </CTAContent>
        </CTASection>

        <Footer />
      </Container>

      {/* Add PaddleCheckout component */}
      {isCheckoutOpen && (
        <PaddleCheckout
          planType={selectedPlan}
          isOpen={isCheckoutOpen}
          onClose={handlePaymentCancel}
          onSuccess={handlePaymentSuccess}
          onError={handlePaymentError}
          onCancel={handlePaymentCancel}
        />
      )}

      {/* Payment messages */}
      {paymentMessage && (
        <div style={{
          position: 'fixed',
          top: '20px',
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 10000,
          padding: '1rem 2rem',
          background: paymentMessage.includes('successful') ? '#4CAF50' : '#FF9800',
          color: 'white',
          borderRadius: '8px',
          boxShadow: '0 4px 20px rgba(0,0,0,0.3)',
          maxWidth: '400px',
          textAlign: 'center'
        }}>
          {paymentMessage}
        </div>
      )}
    </>
  );
};

// Styled Components
const Container = styled.div`
  min-height: 100vh;
  background-color: #ffffff;
`;

const HeroSection = styled.section`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 10rem 2rem 6rem;
  text-align: center;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
  }

  > * {
    position: relative;
    z-index: 2;
  }
`;

const HeroContent = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const HeroTitle = styled.h1`
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const HeroSubtitle = styled.p`
  font-size: 1.25rem;
  line-height: 1.6;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
`;

const PricingSection = styled.section`
  padding: 6rem 2rem;
  max-width: 1400px;
  margin: 0 auto;
`;

const SectionTitle = styled.h2`
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 1.5rem;
  color: #2d3748;
`;

const SectionDescription = styled.p`
  font-size: 1.1rem;
  line-height: 1.7;
  text-align: center;
  color: #4a5568;
  max-width: 800px;
  margin: 0 auto 4rem;
`;

const PricingGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2.5rem;
  max-width: 1200px;
  margin: 0 auto;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
`;

const PricingCard = styled.div`
  background: white;
  border-radius: 16px;
  padding: 2.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: ${props => props.$isPopular ? '3px solid #d95550' : '2px solid #e2e8f0'};
  position: relative;
  transition: transform 0.2s, box-shadow 0.2s;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  }
`;

const PricingBadge = styled.div`
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: ${props => props.$isPopular ? '#d95550' : '#4a5568'};
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const PricingHeader = styled.div`
  text-align: center;
  margin-bottom: 2rem;
`;

const PlanTitle = styled.h3`
  font-size: 1.75rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
`;

const PlanSubtitle = styled.p`
  font-size: 1rem;
  color: #d95550;
  font-weight: 500;
  margin-bottom: 1.5rem;
`;

const PriceContainer = styled.div`
  margin-bottom: 1.5rem;
  position: relative;
`;

const PriceSymbol = styled.span`
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  vertical-align: top;
`;

const OriginalPrice = styled.span`
  font-size: 0.9rem;
  color: #888;
  text-decoration: line-through;
  margin-left: 0.5rem;
`;

const MonthlyEquivalent = styled.div`
  font-size: 0.9rem;
  color: #4caf50;
  font-weight: 600;
  margin-top: 0.25rem;
`;

const BreakEvenInfo = styled.div`
  font-size: 0.8rem;
  color: #667eea;
  font-weight: 500;
  margin-top: 0.25rem;
`;

const Price = styled.span`
  font-size: 3rem;
  font-weight: 700;
  color: #2d3748;
`;

const PricePeriod = styled.div`
  font-size: 1rem;
  color: #4a5568;
  margin-top: 0.5rem;
`;

const PlanDescription = styled.p`
  font-size: 0.95rem;
  color: #4a5568;
  line-height: 1.6;
`;

const FeaturesList = styled.ul`
  list-style: none;
  padding: 0;
  margin-bottom: 2rem;
`;

const FeatureItem = styled.li`
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  opacity: ${props => props.$included ? 1 : 0.5};
`;

const FeatureIcon = styled.div`
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: ${props => props.$included ? '#d95550' : '#e2e8f0'};
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
`;

const FeatureText = styled.span`
  font-size: 0.95rem;
  color: ${props => props.$included ? '#2d3748' : '#a0aec0'};
  line-height: 1.5;
`;

const PricingButton = styled.button`
  display: block;
  width: 100%;
  padding: 1rem 2rem;
  background: ${props => props.$isPopular ? '#d95550' : '#4a5568'};
  color: white;
  text-align: center;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background: ${props => props.$isPopular ? '#c04540' : '#2d3748'};
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const FAQSection = styled.section`
  padding: 6rem 2rem;
  max-width: 1000px;
  margin: 0 auto;
`;

const FAQTitle = styled.h2`
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  color: #2d3748;
`;

const FAQGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
`;

const FAQCard = styled.div`
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #d95550;
`;

const FAQQuestion = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2d3748;
`;

const FAQAnswer = styled.p`
  font-size: 1rem;
  line-height: 1.6;
  color: #4a5568;
`;

const CTASection = styled.section`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4rem 2rem;
  text-align: center;
`;

const CTAContent = styled.div`
  max-width: 600px;
  margin: 0 auto;
`;

const CTATitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
`;

const CTADescription = styled.p`
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  opacity: 0.9;
`;

const CTAButtons = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;

  @media (max-width: 480px) {
    flex-direction: column;
  }
`;

const PrimaryButton = styled(Link)`
  background-color: #d95550;
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  transition: background-color 0.2s;

  &:hover {
    background-color: #c04540;
  }
`;

const SecondaryButton = styled(Link)`
  background-color: transparent;
  color: white;
  padding: 0.75rem 2rem;
  border: 2px solid white;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s;

  &:hover {
    background-color: white;
    color: #2d3748;
  }
`;

export default PricingPage;
