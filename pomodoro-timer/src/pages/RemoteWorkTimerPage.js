import React from 'react';
import { FaHome, FaWifi, FaVideo } from 'react-icons/fa';
import ScenarioTimerPage from '../components/Timer/ScenarioTimerPage';

const RemoteWorkTimerPage = () => {
  const remoteWorkConfig = {
    scenario: 'Remote Work',
    title: 'Remote Work Timer - Home Office Productivity Timer | AI Pomo',
    description: 'Specialized timer for remote workers and digital nomads. Optimize your home office productivity with structured work sessions, virtual meetings, and work-life balance management.',
    keywords: 'remote work timer, home office timer, work from home, digital nomad timer, virtual meeting timer, telecommuting productivity',
    url: 'https://www.ai-pomo.com/remote-work-timer',
    heroTitle: 'Remote Work Timer',
    heroDescription: 'Master your home office productivity with structured timing. Perfect for distributed teams, virtual meetings, and maintaining work-life balance while working remotely.',
    defaultMinutes: 45,
    gradientColors: ['#0ea5e9', '#06b6d4'],
    backgroundColor: 'linear-gradient(135deg, #f0f9ff, #e0f7fa)',
    
    scenarios: [
      {
        emoji: '💻',
        title: 'Deep Work Blocks',
        description: 'Uninterrupted focus time for complex projects and coding',
        recommendedTime: 'Recommended 60-90 minutes'
      },
      {
        emoji: '📞',
        title: 'Virtual Meetings',
        description: 'Video calls, team standups, and client consultations',
        recommendedTime: 'Recommended 30-60 minutes'
      },
      {
        emoji: '📧',
        title: 'Email & Communication',
        description: 'Batch process emails, Slack, and team communications',
        recommendedTime: 'Recommended 20-30 minutes'
      },
      {
        emoji: '📋',
        title: 'Administrative Tasks',
        description: 'Documentation, reporting, and routine office work',
        recommendedTime: 'Recommended 25-45 minutes'
      }
    ],

    benefits: [
      {
        emoji: '🏠',
        title: 'Work-Life Balance',
        description: 'Clear boundaries between work time and personal time at home'
      },
      {
        emoji: '🎯',
        title: 'Reduced Distractions',
        description: 'Stay focused despite home environment distractions and interruptions'
      },
      {
        emoji: '⚡',
        title: 'Enhanced Productivity',
        description: 'Structured time blocks maximize output in remote work settings'
      },
      {
        emoji: '🤝',
        title: 'Team Synchronization',
        description: 'Better coordination with distributed team members and time zones'
      }
    ],

    tips: [
      {
        title: 'Create a Dedicated Workspace',
        description: 'Set up a specific area for work to mentally separate professional and personal spaces'
      },
      {
        title: 'Use Virtual Backgrounds',
        description: 'Maintain professionalism in video calls while working from various home locations'
      },
      {
        title: 'Schedule Communication Windows',
        description: 'Batch process emails and messages during designated times to avoid constant interruptions'
      },
      {
        title: 'Take Proper Breaks',
        description: 'Step away from your workspace during breaks to refresh and avoid home-office burnout'
      },
      {
        title: 'Communicate Your Schedule',
        description: 'Share your focused work blocks with family members and teammates for respect of boundaries'
      }
    ],

    customFeatures: [
      {
        icon: <FaHome />,
        title: 'Home Office Optimization',
        description: 'Designed specifically for the unique challenges of working from home'
      },
      {
        icon: <FaWifi />,
        title: 'Digital Nomad Friendly',
        description: 'Perfect for location-independent workers and flexible schedules'
      },
      {
        icon: <FaVideo />,
        title: 'Meeting Integration',
        description: 'Optimize virtual meeting productivity and reduce video call fatigue'
      }
    ],

    relatedTimers: [
      {
        title: 'Work Timer',
        description: 'General professional productivity',
        duration: '25-90 minutes',
        url: '/work-timer'
      },
      {
        title: 'Break Timer',
        description: 'Essential remote work breaks',
        duration: '10-20 minutes',
        url: '/break-timer'
      },
      {
        title: 'Meeting Timer',
        description: 'Structured virtual meetings',
        duration: '15-60 minutes',
        url: '/meeting-timer'
      }
    ]
  };

  return <ScenarioTimerPage config={remoteWorkConfig} />;
};

export default RemoteWorkTimerPage;