import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { FaPlay, FaPause, FaStop, FaChalkboardTeacher, FaUsers, FaClock, FaArrowRight, FaVolumeUp, FaExpand } from 'react-icons/fa';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import SEOHead from '../components/SEO/SEOHead';

const ClassroomTimerPage = () => {
  const [timeLeft, setTimeLeft] = useState(5 * 60); // 5 minutes default
  const [isRunning, setIsRunning] = useState(false);
  const [inputMinutes, setInputMinutes] = useState(5);
  const [inputSeconds, setInputSeconds] = useState(0);
  const [originalTime, setOriginalTime] = useState(5 * 60);
  const [selectedActivity, setSelectedActivity] = useState('general');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(true);
  
  const intervalRef = useRef(null);
  const audioRef = useRef(null);
  const containerRef = useRef(null);

  const activities = {
    general: { name: 'General Activity', emoji: '⏰', color: '#3b82f6' },
    discussion: { name: 'Class Discussion', emoji: '💬', color: '#10b981' },
    reading: { name: 'Silent Reading', emoji: '📖', color: '#8b5cf6' },
    writing: { name: 'Writing Task', emoji: '✍️', color: '#f59e0b' },
    test: { name: 'Quiz/Test', emoji: '📝', color: '#ef4444' },
    break: { name: 'Break Time', emoji: '☕', color: '#06b6d4' },
    presentation: { name: 'Presentation', emoji: '🎤', color: '#ec4899' },
    groupwork: { name: 'Group Work', emoji: '👥', color: '#84cc16' },
    cleanup: { name: 'Clean Up', emoji: '🧹', color: '#6b7280' },
    transition: { name: 'Transition', emoji: '🚀', color: '#f97316' }
  };

  useEffect(() => {
    if (isRunning && timeLeft > 0) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            completeTimer();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      clearInterval(intervalRef.current);
    }

    return () => clearInterval(intervalRef.current);
  }, [isRunning, timeLeft]);

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  const playAlarmSound = () => {
    if (audioRef.current && soundEnabled) {
      audioRef.current.play().catch(e => console.log('Audio play failed:', e));
    }
  };

  const completeTimer = () => {
    setIsRunning(false);
    playAlarmSound();
    
    if (window.gtag) {
      window.gtag('event', 'classroom_timer_complete', {
        'activity': selectedActivity,
        'duration': originalTime,
        'fullscreen': isFullscreen
      });
    }
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const startTimer = () => {
    const totalSeconds = (inputMinutes * 60) + inputSeconds;
    if (totalSeconds > 0) {
      setTimeLeft(totalSeconds);
      setOriginalTime(totalSeconds);
      setIsRunning(true);
    }
  };

  const pauseTimer = () => {
    setIsRunning(false);
  };

  const resetTimer = () => {
    setIsRunning(false);
    setTimeLeft(originalTime);
  };

  const quickStart = (minutes, activity = selectedActivity) => {
    setSelectedActivity(activity);
    setInputMinutes(minutes);
    setInputSeconds(0);
    const totalSeconds = minutes * 60;
    setTimeLeft(totalSeconds);
    setOriginalTime(totalSeconds);
    setIsRunning(true);
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      containerRef.current?.requestFullscreen?.();
    } else {
      document.exitFullscreen?.();
    }
  };

  const currentActivity = activities[selectedActivity];
  const progress = originalTime > 0 ? ((originalTime - timeLeft) / originalTime) * 100 : 0;
  const timeRemainingPercent = originalTime > 0 ? (timeLeft / originalTime) * 100 : 100;

  const getUrgencyLevel = () => {
    if (timeRemainingPercent <= 10) return 'critical';
    if (timeRemainingPercent <= 25) return 'warning';
    return 'normal';
  };

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Classroom Timer - Teacher's Essential Tool",
    "description": "Professional classroom timer for teachers and educators. Manage class activities, transitions, and lessons with large, visible countdown timer. Perfect for classroom management.",
    "applicationCategory": "EducationalApplication",
    "operatingSystem": "Any",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "Large classroom display",
      "Activity-specific presets",
      "Fullscreen mode",
      "Visual progress indicators",
      "Sound alerts",
      "Teacher-friendly interface"
    ]
  };

  return (
    <>
      <SEOHead
        title="Classroom Timer - Teacher's Essential Tool | AI Pomo"
        description="Professional classroom timer for teachers and educators. Manage class activities, transitions, and lessons with large, visible countdown timer. Perfect for classroom management."
        keywords="classroom timer, teacher timer, school timer, education timer, class management timer, teaching tool, classroom management"
        url="https://www.ai-pomo.com/classroom-timer"
        type="website"
        structuredData={structuredData}
      />
      <PageHeader />
      <Container ref={containerRef} $isFullscreen={isFullscreen}>
        {!isFullscreen && (
          <Header>
            <Title>🏫 Classroom Timer</Title>
            <Subtitle>
              Essential timer for teachers and educators. Manage classroom activities, 
              transitions, and lessons with a large, easy-to-see display.
            </Subtitle>
          </Header>
        )}

        <MainContent $isFullscreen={isFullscreen}>
          {!isFullscreen && (
            <ControlsSection>
              <ActivitySelector>
                <ActivityLabel>Choose Activity Type</ActivityLabel>
                <ActivityGrid>
                  {Object.entries(activities).map(([key, activity]) => (
                    <ActivityButton
                      key={key}
                      $active={selectedActivity === key}
                      $color={activity.color}
                      onClick={() => setSelectedActivity(key)}
                      disabled={isRunning}
                    >
                      <ActivityEmoji>{activity.emoji}</ActivityEmoji>
                      <ActivityName>{activity.name}</ActivityName>
                    </ActivityButton>
                  ))}
                </ActivityGrid>
              </ActivitySelector>

              <TimeInputSection>
                <InputGroup>
                  <TimeInput
                    type="number"
                    min="0"
                    max="60"
                    value={inputMinutes}
                    onChange={(e) => setInputMinutes(Math.max(0, parseInt(e.target.value) || 0))}
                    disabled={isRunning}
                  />
                  <InputLabel>minutes</InputLabel>
                </InputGroup>
                <InputGroup>
                  <TimeInput
                    type="number"
                    min="0"
                    max="59"
                    value={inputSeconds}
                    onChange={(e) => setInputSeconds(Math.max(0, Math.min(59, parseInt(e.target.value) || 0)))}
                    disabled={isRunning}
                  />
                  <InputLabel>seconds</InputLabel>
                </InputGroup>
              </TimeInputSection>

              <QuickButtons>
                <QuickButtonsTitle>Common Class Activities</QuickButtonsTitle>
                <QuickButtonsGrid>
                  {[
                    { minutes: 1, activity: 'transition', label: '1m Transition' },
                    { minutes: 2, activity: 'cleanup', label: '2m Clean Up' },
                    { minutes: 5, activity: 'discussion', label: '5m Discussion' },
                    { minutes: 10, activity: 'reading', label: '10m Reading' },
                    { minutes: 15, activity: 'writing', label: '15m Writing' },
                    { minutes: 20, activity: 'groupwork', label: '20m Group Work' },
                    { minutes: 25, activity: 'test', label: '25m Quiz' },
                    { minutes: 30, activity: 'presentation', label: '30m Presentation' }
                  ].map((preset, index) => (
                    <QuickButton
                      key={index}
                      onClick={() => quickStart(preset.minutes, preset.activity)}
                      disabled={isRunning}
                    >
                      {preset.label}
                    </QuickButton>
                  ))}
                </QuickButtonsGrid>
              </QuickButtons>
            </ControlsSection>
          )}

          <TimerSection $isFullscreen={isFullscreen}>
            <TimerDisplay 
              $color={currentActivity.color} 
              $isRunning={isRunning}
              $urgency={getUrgencyLevel()}
              $isFullscreen={isFullscreen}
            >
              {!isFullscreen && (
                <ActivityInfo>
                  <ActivityEmoji>{currentActivity.emoji}</ActivityEmoji>
                  <ActivityTitle>{currentActivity.name}</ActivityTitle>
                </ActivityInfo>
              )}
              
              <TimeDisplay $isFullscreen={isFullscreen} $urgency={getUrgencyLevel()}>
                {formatTime(timeLeft)}
              </TimeDisplay>
              
              {isFullscreen && (
                <FullscreenActivity>
                  {currentActivity.emoji} {currentActivity.name}
                </FullscreenActivity>
              )}
              
              <ProgressRing $isFullscreen={isFullscreen}>
                <ProgressCircle
                  $progress={progress}
                  $color={currentActivity.color}
                  $urgency={getUrgencyLevel()}
                />
              </ProgressRing>
            </TimerDisplay>

            <ControlButtons $isFullscreen={isFullscreen}>
              {!isRunning ? (
                <StartButton onClick={startTimer}>
                  <FaPlay /> Start
                </StartButton>
              ) : (
                <PauseButton onClick={pauseTimer}>
                  <FaPause /> Pause
                </PauseButton>
              )}
              <ResetButton onClick={resetTimer}>
                <FaStop /> Reset
              </ResetButton>
              <FullscreenButton onClick={toggleFullscreen}>
                <FaExpand /> {isFullscreen ? 'Exit' : 'Fullscreen'}
              </FullscreenButton>
              <SoundButton 
                onClick={() => setSoundEnabled(!soundEnabled)}
                $active={soundEnabled}
              >
                <FaVolumeUp /> Sound {soundEnabled ? 'On' : 'Off'}
              </SoundButton>
            </ControlButtons>
          </TimerSection>

          {!isFullscreen && (
            <>
              <FeaturesSection>
                <SectionTitle>Perfect for Classroom Management</SectionTitle>
                <FeaturesGrid>
                  <Feature>
                    <FeatureIcon>👁️</FeatureIcon>
                    <FeatureTitle>Highly Visible</FeatureTitle>
                    <FeatureText>Large display that students can see from anywhere in the classroom</FeatureText>
                  </Feature>
                  <Feature>
                    <FeatureIcon>🎯</FeatureIcon>
                    <FeatureTitle>Activity-Specific</FeatureTitle>
                    <FeatureText>Pre-configured timers for common classroom activities and transitions</FeatureText>
                  </Feature>
                  <Feature>
                    <FeatureIcon>📺</FeatureIcon>
                    <FeatureTitle>Fullscreen Mode</FeatureTitle>
                    <FeatureText>Display on projector or smartboard for entire class visibility</FeatureText>
                  </Feature>
                </FeaturesGrid>
              </FeaturesSection>

              <UseCasesSection>
                <SectionTitle>Classroom Activities</SectionTitle>
                <UseCasesGrid>
                  <UseCase>
                    <UseCaseIcon>📚</UseCaseIcon>
                    <UseCaseTitle>Lesson Segments</UseCaseTitle>
                    <UseCaseDescription>
                      Time different parts of your lesson to maintain engagement and pacing
                    </UseCaseDescription>
                    <UseCasePresets>
                      <PresetTag>10m Reading</PresetTag>
                      <PresetTag>15m Writing</PresetTag>
                      <PresetTag>5m Discussion</PresetTag>
                    </UseCasePresets>
                  </UseCase>
                  <UseCase>
                    <UseCaseIcon>🔄</UseCaseIcon>
                    <UseCaseTitle>Transitions</UseCaseTitle>
                    <UseCaseDescription>
                      Smooth transitions between activities with clear time expectations
                    </UseCaseDescription>
                    <UseCasePresets>
                      <PresetTag>1m Transition</PresetTag>
                      <PresetTag>2m Setup</PresetTag>
                      <PresetTag>3m Pack Up</PresetTag>
                    </UseCasePresets>
                  </UseCase>
                  <UseCase>
                    <UseCaseIcon>👥</UseCaseIcon>
                    <UseCaseTitle>Group Activities</UseCaseTitle>
                    <UseCaseDescription>
                      Keep group work focused and ensure equal participation time
                    </UseCaseDescription>
                    <UseCasePresets>
                      <PresetTag>20m Group Work</PresetTag>
                      <PresetTag>5m Sharing</PresetTag>
                      <PresetTag>10m Planning</PresetTag>
                    </UseCasePresets>
                  </UseCase>
                  <UseCase>
                    <UseCaseIcon>📝</UseCaseIcon>
                    <UseCaseTitle>Assessments</UseCaseTitle>
                    <UseCaseDescription>
                      Fair timing for quizzes, tests, and timed writing assignments
                    </UseCaseDescription>
                    <UseCasePresets>
                      <PresetTag>25m Quiz</PresetTag>
                      <PresetTag>45m Test</PresetTag>
                      <PresetTag>30m Essay</PresetTag>
                    </UseCasePresets>
                  </UseCase>
                </UseCasesGrid>
              </UseCasesSection>

              <TipsSection>
                <SectionTitle>Teacher Tips</SectionTitle>
                <TipsList>
                  <TipItem>
                    <TipIcon>🎯</TipIcon>
                    <TipContent>
                      <TipTitle>Set Clear Expectations</TipTitle>
                      <TipDescription>
                        Tell students what they should accomplish before the timer ends. This helps them stay focused and motivated.
                      </TipDescription>
                    </TipContent>
                  </TipItem>
                  <TipItem>
                    <TipIcon>⚠️</TipIcon>
                    <TipContent>
                      <TipTitle>Give Time Warnings</TipTitle>
                      <TipDescription>
                        Announce when there are 5 minutes, 2 minutes, and 30 seconds left to help students prepare for transitions.
                      </TipDescription>
                    </TipContent>
                  </TipItem>
                  <TipItem>
                    <TipIcon>📺</TipIcon>
                    <TipContent>
                      <TipTitle>Use Fullscreen Mode</TipTitle>
                      <TipDescription>
                        Project the timer on your smartboard or screen so all students can easily see the remaining time.
                      </TipDescription>
                    </TipContent>
                  </TipItem>
                </TipsList>
              </TipsSection>

              <RelatedSection>
                <SectionTitle>More Educational Tools</SectionTitle>
                <RelatedGrid>
                  <RelatedTimer as={Link} to="/homework-timer">
                    <RelatedTitle>Homework Timer</RelatedTitle>
                    <RelatedDescription>Student-focused timer for homework and study sessions</RelatedDescription>
                    <RelatedAction>
                      Try Homework Timer <FaArrowRight />
                    </RelatedAction>
                  </RelatedTimer>
                  <RelatedTimer as={Link} to="/study-timer">
                    <RelatedTitle>Study Timer</RelatedTitle>
                    <RelatedDescription>General study timer with focus techniques</RelatedDescription>
                    <RelatedAction>
                      Try Study Timer <FaArrowRight />
                    </RelatedAction>
                  </RelatedTimer>
                  <RelatedTimer as={Link} to="/visual-timer">
                    <RelatedTitle>Visual Timer</RelatedTitle>
                    <RelatedDescription>Colorful visual countdown perfect for younger students</RelatedDescription>
                    <RelatedAction>
                      Try Visual Timer <FaArrowRight />
                    </RelatedAction>
                  </RelatedTimer>
                </RelatedGrid>
              </RelatedSection>
            </>
          )}
        </MainContent>

        <audio ref={audioRef} preload="auto">
          <source src="/sounds/completion.mp3" type="audio/mpeg" />
        </audio>
      </Container>
      {!isFullscreen && <Footer />}
    </>
  );
};

// Styled Components
const Container = styled.div`
  max-width: ${props => props.$isFullscreen ? '100vw' : '1200px'};
  margin: 0 auto;
  padding: ${props => props.$isFullscreen ? '2rem' : '6rem 2rem 4rem'};
  min-height: ${props => props.$isFullscreen ? '100vh' : 'auto'};
  background: ${props => props.$isFullscreen ? '#1f2937' : 'transparent'};

  @media (max-width: 768px) {
    padding: ${props => props.$isFullscreen ? '1rem' : '5rem 1rem 2rem'};
  }
`;

const Header = styled.header`
  text-align: center;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: 3rem;
  font-weight: 800;
  color: #111827;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    font-size: 2.25rem;
  }
`;

const Subtitle = styled.p`
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  max-width: 700px;
  margin: 0 auto;
`;

const MainContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.$isFullscreen ? '2rem' : '4rem'};
  height: ${props => props.$isFullscreen ? '100%' : 'auto'};
`;

const ControlsSection = styled.section`
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const ActivitySelector = styled.div`
  text-align: center;
`;

const ActivityLabel = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
`;

const ActivityGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1rem;
  max-width: 800px;
  margin: 0 auto;

  @media (max-width: 768px) {
    grid-template-columns: repeat(3, 1fr);
  }

  @media (max-width: 480px) {
    grid-template-columns: repeat(2, 1fr);
  }
`;

const ActivityButton = styled.button`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem 0.5rem;
  border: 2px solid ${props => props.$active ? props.$color : '#e5e7eb'};
  border-radius: 12px;
  background: ${props => props.$active ? `${props.$color}20` : 'white'};
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    border-color: ${props => props.$color};
    background: ${props => `${props.$color}10`};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const ActivityEmoji = styled.div`
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
`;

const ActivityName = styled.div`
  font-size: 0.75rem;
  font-weight: 600;
  color: #6b7280;
  text-align: center;
`;

const TimeInputSection = styled.div`
  display: flex;
  gap: 2rem;
  justify-content: center;
  align-items: center;

  @media (max-width: 480px) {
    gap: 1rem;
  }
`;

const InputGroup = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
`;

const TimeInput = styled.input`
  width: 100px;
  padding: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #3b82f6;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const InputLabel = styled.label`
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 600;
`;

const QuickButtons = styled.div`
  text-align: center;
`;

const QuickButtonsTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
`;

const QuickButtonsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.75rem;
  max-width: 800px;
  margin: 0 auto;

  @media (max-width: 768px) {
    grid-template-columns: repeat(3, 1fr);
  }

  @media (max-width: 480px) {
    grid-template-columns: repeat(2, 1fr);
  }
`;

const QuickButton = styled.button`
  padding: 0.75rem 0.5rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #6b7280;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;

  &:hover:not(:disabled) {
    border-color: #3b82f6;
    color: #3b82f6;
    transform: translateY(-2px);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const TimerSection = styled.section`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${props => props.$isFullscreen ? '3rem' : '2rem'};
  flex: ${props => props.$isFullscreen ? '1' : 'none'};
  justify-content: ${props => props.$isFullscreen ? 'center' : 'flex-start'};
`;

const TimerDisplay = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: ${props => props.$isFullscreen ? '80vh' : '400px'};
  height: ${props => props.$isFullscreen ? '80vh' : '400px'};
  max-width: ${props => props.$isFullscreen ? '90vw' : 'none'};
  max-height: ${props => props.$isFullscreen ? '90vh' : 'none'};
  border-radius: 50%;
  background: ${props => {
    if (props.$urgency === 'critical') return 'linear-gradient(135deg, #ef4444, #dc2626)';
    if (props.$urgency === 'warning') return 'linear-gradient(135deg, #f59e0b, #d97706)';
    return `linear-gradient(135deg, ${props.$color}, ${props.$color}dd)`;
  }};
  box-shadow: 0 20px 40px ${props => props.$color}40;
  color: white;
  animation: ${props => {
    if (props.$isRunning && props.$urgency === 'critical') return 'pulse 0.5s infinite';
    if (props.$isRunning && props.$urgency === 'warning') return 'pulse 1s infinite';
    return 'none';
  }};

  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @media (max-width: 480px) {
    width: ${props => props.$isFullscreen ? '90vw' : '320px'};
    height: ${props => props.$isFullscreen ? '90vw' : '320px'};
  }
`;

const ActivityInfo = styled.div`
  text-align: center;
  margin-bottom: 1rem;
`;

const ActivityTitle = styled.div`
  font-size: 1.125rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
`;

const TimeDisplay = styled.div`
  font-size: ${props => {
    if (props.$isFullscreen) return '8rem';
    return '4rem';
  }};
  font-weight: 800;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
  color: ${props => props.$urgency === 'critical' ? '#ffffff' : 'white'};

  @media (max-width: 768px) {
    font-size: ${props => props.$isFullscreen ? '6rem' : '3.5rem'};
  }

  @media (max-width: 480px) {
    font-size: ${props => props.$isFullscreen ? '4rem' : '3rem'};
  }
`;

const FullscreenActivity = styled.div`
  font-size: 2rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
  margin-top: 1rem;
  text-align: center;

  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
`;

const ProgressRing = styled.div`
  position: absolute;
  width: ${props => props.$isFullscreen ? '90%' : '90%'};
  height: ${props => props.$isFullscreen ? '90%' : '90%'};
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-90deg);
`;

const ProgressCircle = styled.div`
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 8px solid rgba(255, 255, 255, 0.2);
  border-top: 8px solid ${props => {
    if (props.$urgency === 'critical') return '#ffffff';
    if (props.$urgency === 'warning') return '#fbbf24';
    return 'rgba(255, 255, 255, 0.8)';
  }};
  transition: all 0.3s ease;
  
  ${props => `
    background: conic-gradient(
      from 0deg,
      rgba(255, 255, 255, 0.8) 0deg,
      rgba(255, 255, 255, 0.8) ${props.$progress * 3.6}deg,
      transparent ${props.$progress * 3.6}deg,
      transparent 360deg
    );
  `}
`;

const ControlButtons = styled.div`
  display: flex;
  gap: ${props => props.$isFullscreen ? '2rem' : '1rem'};
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: ${props => props.$isFullscreen ? '1.5rem 2.5rem' : '1rem 2rem'};
  border: none;
  border-radius: 12px;
  font-size: ${props => props.$isFullscreen ? '1.5rem' : '1.125rem'};
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: ${props => props.$isFullscreen ? '180px' : '140px'};
  justify-content: center;
`;

const StartButton = styled(Button)`
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
  }
`;

const PauseButton = styled(Button)`
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
  }
`;

const ResetButton = styled(Button)`
  background: white;
  color: #6b7280;
  border: 2px solid #e5e7eb;

  &:hover {
    border-color: #3b82f6;
    color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;

const FullscreenButton = styled(Button)`
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
  }
`;

const SoundButton = styled(Button)`
  background: ${props => props.$active ? 
    'linear-gradient(135deg, #06b6d4, #0891b2)' : 
    '#f3f4f6'
  };
  color: ${props => props.$active ? 'white' : '#6b7280'};
  box-shadow: ${props => props.$active ? 
    '0 4px 12px rgba(6, 182, 212, 0.3)' : 
    'none'
  };

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.$active ? 
      '0 6px 20px rgba(6, 182, 212, 0.4)' : 
      '0 4px 12px rgba(0, 0, 0, 0.1)'
    };
  }
`;

const SectionTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1.5rem;
  text-align: center;
`;

const FeaturesSection = styled.section``;

const FeaturesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
`;

const Feature = styled.div`
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const FeatureIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const FeatureTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
`;

const FeatureText = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
`;

const UseCasesSection = styled.section``;

const UseCasesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
`;

const UseCase = styled.div`
  padding: 2rem;
  background: white;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
  }
`;

const UseCaseIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const UseCaseTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.75rem;
`;

const UseCaseDescription = styled.p`
  color: #6b7280;
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 1rem;
`;

const UseCasePresets = styled.div`
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
`;

const PresetTag = styled.span`
  padding: 0.25rem 0.75rem;
  background: #f0f9ff;
  color: #0891b2;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
`;

const TipsSection = styled.section``;

const TipsList = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const TipItem = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const TipIcon = styled.div`
  font-size: 2rem;
  flex-shrink: 0;
`;

const TipContent = styled.div`
  flex: 1;
`;

const TipTitle = styled.h4`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const TipDescription = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
`;

const RelatedSection = styled.section``;

const RelatedGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
`;

const RelatedTimer = styled.a`
  display: block;
  padding: 2rem;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  text-decoration: none;
  transition: all 0.3s ease;

  &:hover {
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
`;

const RelatedTitle = styled.h4`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const RelatedDescription = styled.p`
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
`;

const RelatedAction = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #3b82f6;
  font-weight: 600;
  font-size: 0.875rem;

  svg {
    font-size: 0.75rem;
  }
`;

export default ClassroomTimerPage;