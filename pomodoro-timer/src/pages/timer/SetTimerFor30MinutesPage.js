import React from 'react';
import TimerPage from '../TimerPage';

const SetTimerFor30MinutesPage = () => {
  const useCases = [
    {
      icon: '💻',
      title: 'Deep Work',
      description: 'Extended focused work session for complex tasks and projects.'
    },
    {
      icon: '📖',
      title: 'Study Session',
      description: 'Concentrated study time for learning and information retention.'
    },
    {
      icon: '🏃‍♀️',
      title: 'Exercise Routine',
      description: 'Complete workout session or specific exercise routine timing.'
    },
    {
      icon: '🎨',
      title: 'Creative Work',
      description: 'Uninterrupted creative time for art, writing, or design projects.'
    }
  ];

  const relatedTimers = [
    {
      minutes: 15,
      description: 'Medium work sessions',
      url: '/timer/15-minutes'
    },
    {
      minutes: 20,
      description: 'Focused work blocks',
      url: '/timer/20-minutes'
    },
    {
      minutes: 25,
      description: 'Pomodoro sessions',
      url: '/timer/25-minutes'
    },
    {
      minutes: 45,
      description: 'Extended deep work',
      url: '/timer/45-minutes'
    },
    {
      minutes: 60,
      description: 'Maximum focus time',
      url: '/timer/60-minutes'
    }
  ];

  return (
    <TimerPage
      minutes={30}
      title="Set Timer for 30 Minutes - Deep Work & Study Timer"
      description="Set a timer for 30 minutes for deep work, study sessions, and extended focus. Perfect for productivity and concentration. Free timer with alerts."
      keywords="set timer for 30 minutes, set a timer for 30 minutes, 30 minute timer, deep work timer, study timer, set 30 min timer"
      url="https://www.ai-pomo.com/set-timer-for-30-minutes"
      useCases={useCases}
      relatedTimers={relatedTimers}
    />
  );
};

export default SetTimerFor30MinutesPage;