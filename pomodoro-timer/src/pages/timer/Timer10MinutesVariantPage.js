import React from 'react';
import TimerPage from '../TimerPage';

const Timer10MinutesVariantPage = () => {
  const useCases = [
    {
      icon: '📚',
      title: 'Study Burst',
      description: 'Intensive study session for maximum information retention.'
    },
    {
      icon: '💼',
      title: 'Work Sprint',
      description: 'Focused work period for tackling specific tasks efficiently.'
    },
    {
      icon: '🧹',
      title: 'Quick Cleanup',
      description: 'Timed cleaning session to organize your space rapidly.'
    },
    {
      icon: '💡',
      title: 'Brainstorming',
      description: 'Creative thinking session for generating ideas and solutions.'
    }
  ];

  const relatedTimers = [
    {
      minutes: 5,
      description: 'Quick micro sessions',
      url: '/timer/5-minutes'
    },
    {
      minutes: 15,
      description: 'Extended focus time',
      url: '/timer/15-minutes'
    },
    {
      minutes: 20,
      description: 'Deep work blocks',
      url: '/timer/20-minutes'
    },
    {
      minutes: 25,
      description: 'Pomodoro sessions',
      url: '/timer/25-minutes'
    }
  ];

  return (
    <TimerPage
      minutes={10}
      title="Timer 10 Minutes - Productivity & Study Timer"
      description="Timer 10 minutes for focused work, study sessions, and productive tasks. Free online 10-minute timer with notifications and sound alerts."
      keywords="timer 10 minutes, 10 minute timer, timer for 10 minutes, productivity timer, study timer, 10 min timer online"
      url="https://www.ai-pomo.com/timer-10-minutes"
      useCases={useCases}
      relatedTimers={relatedTimers}
    />
  );
};

export default Timer10MinutesVariantPage;