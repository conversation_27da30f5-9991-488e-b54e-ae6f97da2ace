import React from 'react';
import TimerPage from '../TimerPage';

const Timer20MinutesVariantPage = () => {
  const useCases = [
    {
      icon: '💻',
      title: 'Deep Work',
      description: 'Sustained focus period for complex tasks and problem-solving.'
    },
    {
      icon: '📖',
      title: 'Reading Session',
      description: 'Concentrated reading time for books, articles, or study materials.'
    },
    {
      icon: '🎨',
      title: 'Creative Work',
      description: 'Uninterrupted creative time for art, writing, or design projects.'
    },
    {
      icon: '🏃‍♂️',
      title: 'Exercise Block',
      description: 'Workout session or specific exercise routine timing.'
    }
  ];

  const relatedTimers = [
    {
      minutes: 15,
      description: 'Medium focus sessions',
      url: '/timer/15-minutes'
    },
    {
      minutes: 25,
      description: 'Pomodoro technique',
      url: '/timer/25-minutes'
    },
    {
      minutes: 30,
      description: 'Extended work blocks',
      url: '/timer/30-minutes'
    },
    {
      minutes: 45,
      description: 'Deep focus periods',
      url: '/timer/45-minutes'
    }
  ];

  return (
    <TimerPage
      minutes={20}
      title="Timer 20 Minutes - Deep Work & Focus Timer"
      description="Timer 20 minutes for deep work, study, and focused activities. Free online 20-minute timer with sound alerts and browser notifications."
      keywords="timer 20 minutes, 20 minute timer, timer for 20 minutes, deep work timer, focus timer, 20 min timer online"
      url="https://www.ai-pomo.com/timer-20-minutes"
      useCases={useCases}
      relatedTimers={relatedTimers}
    />
  );
};

export default Timer20MinutesVariantPage;