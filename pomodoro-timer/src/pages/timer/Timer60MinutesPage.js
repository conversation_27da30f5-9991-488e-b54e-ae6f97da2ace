import React from 'react';
import TimerPage from '../TimerPage';

const Timer60MinutesPage = () => {
  const useCases = [
    {
      icon: '⏰',
      title: '1 Hour Focus',
      description: 'Maximum concentration period for complex tasks and deep work.'
    },
    {
      icon: '📚',
      title: 'Study Block',
      description: 'Extended study sessions for exam preparation or learning.'
    },
    {
      icon: '💼',
      title: 'Meeting Timer',
      description: 'Standard meeting duration to keep discussions on track.'
    },
    {
      icon: '🏃',
      title: 'Workout Timer',
      description: 'Full workout sessions or extended physical activities.'
    }
  ];

  const relatedTimers = [
    {
      minutes: 15,
      description: 'Short break sessions',
      url: '/timer/15-minutes'
    },
    {
      minutes: 25,
      description: 'Classic Pomodoro sessions',
      url: '/timer/25-minutes'
    },
    {
      minutes: 30,
      description: 'Extended focus periods',
      url: '/timer/30-minutes'
    },
    {
      minutes: 45,
      description: 'Deep work sessions',
      url: '/timer/45-minutes'
    },
    {
      minutes: 90,
      description: 'Extended deep work',
      url: '/timer/90-minutes'
    }
  ];

  return (
    <TimerPage
      minutes={60}
      title="60 Minute Timer (1 Hour) - Maximum Focus and Deep Work"
      description="Free online 60 minute (1 hour) timer perfect for deep work, extended study, meetings, and long-duration activities. Start instantly with alerts."
      keywords="60 minute timer, 1 hour timer, 60 min timer, one hour timer, deep work timer, study timer, meeting timer, online timer 60 minutes"
      url="https://www.ai-pomo.com/timer/60-minutes"
      useCases={useCases}
      relatedTimers={relatedTimers}
    />
  );
};

export default Timer60MinutesPage;