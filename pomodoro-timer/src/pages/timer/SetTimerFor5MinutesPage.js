import React from 'react';
import TimerPage from '../TimerPage';

const SetTimerFor5MinutesPage = () => {
  const useCases = [
    {
      icon: '☕',
      title: 'Coffee Break',
      description: 'Perfect timing for a quick coffee or tea break to refresh your mind.'
    },
    {
      icon: '🧘',
      title: 'Quick Meditation',
      description: 'Short mindfulness session to reset and refocus your attention.'
    },
    {
      icon: '📱',
      title: 'Social Media Limit',
      description: 'Set boundaries for social media browsing to stay productive.'
    },
    {
      icon: '🏃',
      title: 'Quick Exercise',
      description: 'Short burst of physical activity to energize your body.'
    }
  ];

  const relatedTimers = [
    {
      minutes: 10,
      description: 'Longer break sessions',
      url: '/timer/10-minutes'
    },
    {
      minutes: 15,
      description: 'Extended meditation time',
      url: '/timer/15-minutes'
    },
    {
      minutes: 20,
      description: 'Focused work sessions',
      url: '/timer/20-minutes'
    },
    {
      minutes: 25,
      description: 'Pomodoro work blocks',
      url: '/timer/25-minutes'
    },
    {
      minutes: 30,
      description: 'Deep focus periods',
      url: '/timer/30-minutes'
    }
  ];

  return (
    <TimerPage
      minutes={5}
      title="Set Timer for 5 Minutes - Quick Break & Focus Timer"
      description="Set a timer for 5 minutes instantly. Perfect for quick breaks, meditation, exercise, or time limits. Free online timer with sound alerts and notifications."
      keywords="set timer for 5 minutes, set a timer for 5 minutes, 5 minute timer, quick timer, break timer, meditation timer, set 5 min timer"
      url="https://www.ai-pomo.com/set-timer-for-5-minutes"
      useCases={useCases}
      relatedTimers={relatedTimers}
    />
  );
};

export default SetTimerFor5MinutesPage;