import React from 'react';
import TimerPage from '../TimerPage';

const Timer35MinutesPage = () => {
  const useCases = [
    {
      icon: '📚',
      title: 'Extended Study',
      description: 'Deep learning session for complex subjects requiring sustained attention.'
    },
    {
      icon: '💻',
      title: 'Project Work',
      description: 'Focused project development time for coding, design, or planning.'
    },
    {
      icon: '🏃‍♀️',
      title: 'Workout Session',
      description: 'Complete exercise routine or cardio session timing.'
    },
    {
      icon: '🎯',
      title: 'Goal Sprint',
      description: 'Extended focus period for making significant progress on objectives.'
    }
  ];

  const relatedTimers = [
    {
      minutes: 30,
      description: 'Standard work blocks',
      url: '/timer/30-minutes'
    },
    {
      minutes: 40,
      description: 'Extended focus time',
      url: '/timer/40-minutes'
    },
    {
      minutes: 45,
      description: 'Deep work sessions',
      url: '/timer/45-minutes'
    },
    {
      minutes: 50,
      description: 'Maximum focus periods',
      url: '/timer/50-minutes'
    },
    {
      minutes: 60,
      description: 'One hour sessions',
      url: '/timer/60-minutes'
    }
  ];

  return (
    <TimerPage
      minutes={35}
      title="35 Minute Timer - Extended Focus & Study Timer"
      description="Free 35 minute timer for extended study sessions, project work, and deep focus. Perfect for sustained concentration with sound alerts and notifications."
      keywords="35 minute timer, 35 min timer, timer for 35 minutes, extended focus timer, study timer, project timer"
      url="https://www.ai-pomo.com/timer/35-minutes"
      useCases={useCases}
      relatedTimers={relatedTimers}
    />
  );
};

export default Timer35MinutesPage;