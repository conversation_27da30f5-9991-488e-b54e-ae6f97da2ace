import React from 'react';
import TimerPage from '../TimerPage';

const Timer20MinutesPage = () => {
  const useCases = [
    {
      icon: '📚',
      title: 'Study Sessions',
      description: 'Focused study time for learning new concepts or reviewing material.'
    },
    {
      icon: '💻',
      title: 'Focused Work',
      description: 'Concentrated work on specific tasks or problem-solving.'
    },
    {
      icon: '🎨',
      title: 'Creative Work',
      description: 'Design, drawing, writing, or other creative activities.'
    },
    {
      icon: '📧',
      title: 'Email Processing',
      description: 'Dedicated time for reading and responding to emails.'
    }
  ];

  const relatedTimers = [
    {
      minutes: 5,
      description: 'Quick breaks or micro tasks',
      url: '/timer/5-minutes'
    },
    {
      minutes: 10,
      description: 'Brief focused sessions',
      url: '/timer/10-minutes'
    },
    {
      minutes: 15,
      description: 'Medium-length activities',
      url: '/timer/15-minutes'
    },
    {
      minutes: 25,
      description: 'Classic Pomodoro sessions',
      url: '/timer/25-minutes'
    },
    {
      minutes: 30,
      description: 'Extended focus periods',
      url: '/timer/30-minutes'
    }
  ];

  return (
    <TimerPage
      minutes={20}
      title="20 Minute Timer - Perfect for Focused Work and Study"
      description="Free online 20 minute timer ideal for focused work sessions, study time, creative activities, and productive tasks. Start instantly with notifications."
      keywords="20 minute timer, 20 min timer, study timer, work timer, focus timer, productivity timer, online timer 20 minutes"
      url="https://www.ai-pomo.com/timer/20-minutes"
      useCases={useCases}
      relatedTimers={relatedTimers}
    />
  );
};

export default Timer20MinutesPage;