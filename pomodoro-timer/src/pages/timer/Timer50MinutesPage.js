import React from 'react';
import TimerPage from '../TimerPage';

const Timer50MinutesPage = () => {
  const useCases = [
    {
      icon: '⚡',
      title: 'Power Session',
      description: 'Intensive work period for tackling major projects or deadlines.'
    },
    {
      icon: '📖',
      title: 'Deep Reading',
      description: 'Extended reading session for books, research papers, or documentation.'
    },
    {
      icon: '💡',
      title: 'Problem Solving',
      description: 'Sustained thinking time for complex problem analysis and solutions.'
    },
    {
      icon: '🎯',
      title: 'Sprint Work',
      description: 'High-intensity work sprint for maximum productivity output.'
    }
  ];

  const relatedTimers = [
    {
      minutes: 40,
      description: 'Shorter work blocks',
      url: '/timer/40-minutes'
    },
    {
      minutes: 45,
      description: 'Standard deep work',
      url: '/timer/45-minutes'
    },
    {
      minutes: 60,
      description: 'Full hour sessions',
      url: '/timer/60-minutes'
    },
    {
      minutes: 35,
      description: 'Medium focus time',
      url: '/timer/35-minutes'
    },
    {
      minutes: 30,
      description: 'Regular sessions',
      url: '/timer/30-minutes'
    }
  ];

  return (
    <TimerPage
      minutes={50}
      title="50 Minute Timer - Power Session & Deep Focus Timer"
      description="Free 50 minute timer for power work sessions, deep reading, and intensive focus. Perfect for maximum productivity with sound alerts."
      keywords="50 minute timer, 50 min timer, timer for 50 minutes, power session timer, intensive focus timer, productivity timer"
      url="https://www.ai-pomo.com/timer/50-minutes"
      useCases={useCases}
      relatedTimers={relatedTimers}
    />
  );
};

export default Timer50MinutesPage;