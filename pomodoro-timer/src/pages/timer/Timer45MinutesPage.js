import React from 'react';
import TimerPage from '../TimerPage';

const Timer45MinutesPage = () => {
  const useCases = [
    {
      icon: '🎓',
      title: 'Class Sessions',
      description: 'Standard university class or lecture duration for focused learning.'
    },
    {
      icon: '💻',
      title: 'Deep Work',
      description: 'Extended periods of intense concentration and productivity.'
    },
    {
      icon: '📊',
      title: 'Project Work',
      description: 'Substantial time blocks for complex projects and analysis.'
    },
    {
      icon: '🎨',
      title: 'Creative Flow',
      description: 'Extended creative sessions for art, writing, or design work.'
    }
  ];

  const relatedTimers = [
    {
      minutes: 15,
      description: 'Quick break sessions',
      url: '/timer/15-minutes'
    },
    {
      minutes: 20,
      description: 'Focused work sessions',
      url: '/timer/20-minutes'
    },
    {
      minutes: 25,
      description: 'Classic Pomodoro sessions',
      url: '/timer/25-minutes'
    },
    {
      minutes: 30,
      description: 'Extended focus periods',
      url: '/timer/30-minutes'
    },
    {
      minutes: 60,
      description: 'One-hour deep work',
      url: '/timer/60-minutes'
    }
  ];

  return (
    <TimerPage
      minutes={45}
      title="45 Minute Timer - Deep Work and Extended Focus Sessions"
      description="Free online 45 minute timer ideal for deep work, class sessions, extended study, and substantial project work. Start instantly with notifications."
      keywords="45 minute timer, 45 min timer, deep work timer, class timer, extended focus timer, project timer, online timer 45 minutes"
      url="https://www.ai-pomo.com/timer/45-minutes"
      useCases={useCases}
      relatedTimers={relatedTimers}
    />
  );
};

export default Timer45MinutesPage;