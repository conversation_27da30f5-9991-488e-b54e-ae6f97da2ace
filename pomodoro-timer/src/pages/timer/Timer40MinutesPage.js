import React from 'react';
import TimerPage from '../TimerPage';

const Timer40MinutesPage = () => {
  const useCases = [
    {
      icon: '🎓',
      title: 'Academic Work',
      description: 'Extended study period for research, writing, or exam preparation.'
    },
    {
      icon: '🏢',
      title: 'Business Tasks',
      description: 'Professional work sessions for reports, analysis, or strategic planning.'
    },
    {
      icon: '🎨',
      title: 'Creative Projects',
      description: 'Sustained creative work for art, music, writing, or design projects.'
    },
    {
      icon: '🧘‍♂️',
      title: 'Deep Meditation',
      description: 'Extended mindfulness or meditation practice for mental clarity.'
    }
  ];

  const relatedTimers = [
    {
      minutes: 35,
      description: 'Slightly shorter focus',
      url: '/timer/35-minutes'
    },
    {
      minutes: 45,
      description: 'Extended work blocks',
      url: '/timer/45-minutes'
    },
    {
      minutes: 50,
      description: 'Maximum focus time',
      url: '/timer/50-minutes'
    },
    {
      minutes: 30,
      description: 'Standard sessions',
      url: '/timer/30-minutes'
    },
    {
      minutes: 60,
      description: 'Full hour sessions',
      url: '/timer/60-minutes'
    }
  ];

  return (
    <TimerPage
      minutes={40}
      title="40 Minute Timer - Deep Work & Academic Timer"
      description="Free 40 minute timer for deep work, academic study, and extended focus sessions. Ideal for sustained concentration with browser notifications."
      keywords="40 minute timer, 40 min timer, timer for 40 minutes, deep work timer, academic timer, focus timer"
      url="https://www.ai-pomo.com/timer/40-minutes"
      useCases={useCases}
      relatedTimers={relatedTimers}
    />
  );
};

export default Timer40MinutesPage;