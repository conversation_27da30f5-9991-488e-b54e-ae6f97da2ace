import React from 'react';
import TimerPage from '../TimerPage';

const Timer25MinutesPage = () => {
  const useCases = [
    {
      icon: '🍅',
      title: 'Pomodoro Sessions',
      description: 'The classic 25-minute focused work session from the Pomodoro Technique.'
    },
    {
      icon: '📚',
      title: 'Study Sessions',
      description: 'Perfect for concentrated studying with optimal focus duration.'
    },
    {
      icon: '💻',
      title: 'Coding Sprints',
      description: 'Deep work sessions for programming and software development.'
    },
    {
      icon: '✍️',
      title: 'Writing Sessions',
      description: 'Focused writing time for articles, essays, or creative content.'
    }
  ];

  const relatedTimers = [
    {
      minutes: 5,
      description: 'Quick breaks or short tasks',
      url: '/timer/5-minutes'
    },
    {
      minutes: 10,
      description: 'Brief focused sessions',
      url: '/timer/10-minutes'
    },
    {
      minutes: 15,
      description: 'Medium-length tasks',
      url: '/timer/15-minutes'
    },
    {
      minutes: 20,
      description: 'Focused work sessions',
      url: '/timer/20-minutes'
    },
    {
      minutes: 30,
      description: 'Extended focus periods',
      url: '/timer/30-minutes'
    },
    {
      minutes: 45,
      description: 'Deep work sessions',
      url: '/timer/45-minutes'
    }
  ];

  return (
    <TimerPage
      minutes={25}
      title="25 Minute Timer - Perfect for Pomodoro Technique"
      description="Free online 25 minute timer ideal for Pomodoro sessions, focused study, and productive work. Start instantly with audio alerts and browser notifications."
      keywords="25 minute timer, pomodoro timer, 25 min timer, focus timer, productivity timer, study timer, work timer, online timer 25 minutes"
      url="https://www.ai-pomo.com/timer/25-minutes"
      useCases={useCases}
      relatedTimers={relatedTimers}
    />
  );
};

export default Timer25MinutesPage;