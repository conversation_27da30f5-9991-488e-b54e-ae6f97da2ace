import React from 'react';
import TimerPage from '../TimerPage';

const Timer30MinutesPage = () => {
  const useCases = [
    {
      icon: '📖',
      title: 'Deep Reading',
      description: 'Extended reading sessions for books, research, or long articles.'
    },
    {
      icon: '💻',
      title: 'Deep Work',
      description: 'Intensive work sessions requiring sustained concentration.'
    },
    {
      icon: '🎯',
      title: 'Project Focus',
      description: 'Dedicated time for working on important projects or goals.'
    },
    {
      icon: '🏃',
      title: 'Exercise',
      description: 'Longer workout sessions, runs, or physical training.'
    }
  ];

  const relatedTimers = [
    {
      minutes: 5,
      description: 'Quick breaks or micro tasks',
      url: '/timer/5-minutes'
    },
    {
      minutes: 10,
      description: 'Brief focused sessions',
      url: '/timer/10-minutes'
    },
    {
      minutes: 15,
      description: 'Medium-length activities',
      url: '/timer/15-minutes'
    },
    {
      minutes: 20,
      description: 'Focused work sessions',
      url: '/timer/20-minutes'
    },
    {
      minutes: 25,
      description: 'Classic Pomodoro sessions',
      url: '/timer/25-minutes'
    },
    {
      minutes: 45,
      description: 'Extended deep work',
      url: '/timer/45-minutes'
    }
  ];

  return (
    <TimerPage
      minutes={30}
      title="30 Minute Timer - Extended Focus for Deep Work"
      description="Free online 30 minute timer perfect for deep work, extended study sessions, reading, and longer activities. Start immediately with sound alerts."
      keywords="30 minute timer, 30 min timer, deep work timer, extended focus timer, study timer, reading timer, online timer 30 minutes"
      url="https://www.ai-pomo.com/timer/30-minutes"
      useCases={useCases}
      relatedTimers={relatedTimers}
    />
  );
};

export default Timer30MinutesPage;