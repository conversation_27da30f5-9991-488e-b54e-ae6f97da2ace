import React from 'react';
import TimerPage from '../TimerPage';

const Timer10MinutesPage = () => {
  const useCases = [
    {
      icon: '📖',
      title: 'Quick Reading',
      description: 'Perfect for reading articles, emails, or short documents.'
    },
    {
      icon: '☕',
      title: 'Coffee Break',
      description: 'Ideal break duration to recharge between work sessions.'
    },
    {
      icon: '🎯',
      title: 'Focused Tasks',
      description: 'Quick focused work on specific small tasks or problems.'
    },
    {
      icon: '🧹',
      title: 'Quick Cleanup',
      description: 'Tidy up workspace, organize files, or quick household tasks.'
    }
  ];

  const relatedTimers = [
    {
      minutes: 5,
      description: 'Quick breaks or micro tasks',
      url: '/timer/5-minutes'
    },
    {
      minutes: 15,
      description: 'Medium-length activities',
      url: '/timer/15-minutes'
    },
    {
      minutes: 20,
      description: 'Focused work sessions',
      url: '/timer/20-minutes'
    },
    {
      minutes: 25,
      description: 'Classic Pomodoro sessions',
      url: '/timer/25-minutes'
    },
    {
      minutes: 30,
      description: 'Extended focus periods',
      url: '/timer/30-minutes'
    }
  ];

  return (
    <TimerPage
      minutes={10}
      title="10 Minute Timer - Perfect for Quick Tasks and Breaks"
      description="Free online 10 minute timer ideal for quick reading, focused tasks, coffee breaks, and short activities. Start immediately with notifications."
      keywords="10 minute timer, 10 min timer, quick task timer, reading timer, break timer, focus timer, online timer 10 minutes"
      url="https://www.ai-pomo.com/timer/10-minutes"
      useCases={useCases}
      relatedTimers={relatedTimers}
    />
  );
};

export default Timer10MinutesPage;