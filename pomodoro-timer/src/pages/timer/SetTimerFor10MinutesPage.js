import React from 'react';
import TimerPage from '../TimerPage';

const SetTimerFor10MinutesPage = () => {
  const useCases = [
    {
      icon: '📚',
      title: 'Reading Session',
      description: 'Quick reading burst for articles, emails, or study materials.'
    },
    {
      icon: '🧹',
      title: 'Quick Cleanup',
      description: 'Tidy up your workspace or room in a focused 10-minute session.'
    },
    {
      icon: '💡',
      title: 'Brainstorming',
      description: 'Generate ideas and solutions in a time-boxed creative session.'
    },
    {
      icon: '📞',
      title: 'Phone Calls',
      description: 'Keep important calls concise and time-bounded.'
    }
  ];

  const relatedTimers = [
    {
      minutes: 5,
      description: 'Quick micro breaks',
      url: '/timer/5-minutes'
    },
    {
      minutes: 15,
      description: 'Extended focus time',
      url: '/timer/15-minutes'
    },
    {
      minutes: 20,
      description: 'Deep work sessions',
      url: '/timer/20-minutes'
    },
    {
      minutes: 25,
      description: 'Pomodoro technique',
      url: '/timer/25-minutes'
    },
    {
      minutes: 30,
      description: 'Long focus blocks',
      url: '/timer/30-minutes'
    }
  ];

  return (
    <TimerPage
      minutes={10}
      title="Set Timer for 10 Minutes - Productivity & Focus Timer"
      description="Set a timer for 10 minutes easily. Ideal for focused work, reading, brainstorming, and productive tasks. Free online timer with notifications."
      keywords="set timer for 10 minutes, set a timer for 10 minutes, 10 minute timer, productivity timer, focus timer, set 10 min timer"
      url="https://www.ai-pomo.com/set-timer-for-10-minutes"
      useCases={useCases}
      relatedTimers={relatedTimers}
    />
  );
};

export default SetTimerFor10MinutesPage;