import React from 'react';
import TimerPage from '../TimerPage';

const Timer5MinutesPage = () => {
  const useCases = [
    {
      icon: '☕',
      title: 'Quick Breaks',
      description: 'Perfect short break between work sessions or study periods.'
    },
    {
      icon: '🧘',
      title: 'Meditation',
      description: 'Ideal duration for brief mindfulness or breathing exercises.'
    },
    {
      icon: '🏃',
      title: 'Quick Exercise',
      description: 'Short bursts of physical activity or stretching.'
    },
    {
      icon: '⚡',
      title: 'Micro Tasks',
      description: 'Small tasks like organizing, quick emails, or brief planning.'
    }
  ];

  const relatedTimers = [
    {
      minutes: 10,
      description: 'Extended breaks or short tasks',
      url: '/timer/10-minutes'
    },
    {
      minutes: 15,
      description: 'Medium-length activities',
      url: '/timer/15-minutes'
    },
    {
      minutes: 20,
      description: 'Focused work sessions',
      url: '/timer/20-minutes'
    },
    {
      minutes: 25,
      description: 'Classic Pomodoro sessions',
      url: '/timer/25-minutes'
    },
    {
      minutes: 30,
      description: 'Extended focus periods',
      url: '/timer/30-minutes'
    }
  ];

  return (
    <TimerPage
      minutes={5}
      title="5 Minute Timer - Quick Breaks and Micro Tasks"
      description="Free online 5 minute timer perfect for quick breaks, meditation, micro tasks, and short activities. Instant start with sound alerts."
      keywords="5 minute timer, 5 min timer, quick timer, break timer, meditation timer, micro task timer, short timer, online timer 5 minutes"
      url="https://www.ai-pomo.com/timer/5-minutes"
      useCases={useCases}
      relatedTimers={relatedTimers}
    />
  );
};

export default Timer5MinutesPage;