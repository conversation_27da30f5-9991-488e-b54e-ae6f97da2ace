import React from 'react';
import TimerPage from '../TimerPage';

const Timer5MinutesVariantPage = () => {
  const useCases = [
    {
      icon: '⚡',
      title: 'Quick Tasks',
      description: 'Perfect for brief tasks that need immediate attention and focus.'
    },
    {
      icon: '☕',
      title: 'Break Timer',
      description: 'Ideal break duration to refresh without losing momentum.'
    },
    {
      icon: '🧘',
      title: 'Mindfulness',
      description: 'Short meditation or breathing exercise to reset your mind.'
    },
    {
      icon: '📱',
      title: 'Social Limit',
      description: 'Set healthy boundaries for phone or social media use.'
    }
  ];

  const relatedTimers = [
    {
      minutes: 10,
      description: 'Extended focus time',
      url: '/timer/10-minutes'
    },
    {
      minutes: 15,
      description: 'Medium work sessions',
      url: '/timer/15-minutes'
    },
    {
      minutes: 20,
      description: 'Focused work blocks',
      url: '/timer/20-minutes'
    },
    {
      minutes: 25,
      description: 'Pomodoro technique',
      url: '/timer/25-minutes'
    }
  ];

  return (
    <TimerPage
      minutes={5}
      title="Timer 5 Minutes - Quick Focus & Break Timer"
      description="Timer 5 minutes for quick tasks, breaks, and focused sessions. Free online 5-minute timer with sound alerts and browser notifications."
      keywords="timer 5 minutes, 5 minute timer, timer for 5 minutes, quick timer, break timer, 5 min timer online"
      url="https://www.ai-pomo.com/timer-5-minutes"
      useCases={useCases}
      relatedTimers={relatedTimers}
    />
  );
};

export default Timer5MinutesVariantPage;