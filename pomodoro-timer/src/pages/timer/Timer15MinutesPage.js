import React from 'react';
import TimerPage from '../TimerPage';

const Timer15MinutesPage = () => {
  const useCases = [
    {
      icon: '🧘',
      title: 'Meditation',
      description: 'Perfect duration for mindfulness meditation and relaxation.'
    },
    {
      icon: '📞',
      title: 'Quick Meetings',
      description: 'Short team check-ins, calls, or brief discussions.'
    },
    {
      icon: '✏️',
      title: 'Quick Writing',
      description: 'Journaling, note-taking, or brief writing sessions.'
    },
    {
      icon: '🏃',
      title: 'Exercise Break',
      description: 'Short workout, stretching, or physical activity break.'
    }
  ];

  const relatedTimers = [
    {
      minutes: 5,
      description: 'Quick breaks or micro tasks',
      url: '/timer/5-minutes'
    },
    {
      minutes: 10,
      description: 'Brief focused sessions',
      url: '/timer/10-minutes'
    },
    {
      minutes: 20,
      description: 'Focused work sessions',
      url: '/timer/20-minutes'
    },
    {
      minutes: 25,
      description: 'Classic Pomodoro sessions',
      url: '/timer/25-minutes'
    },
    {
      minutes: 30,
      description: 'Extended focus periods',
      url: '/timer/30-minutes'
    }
  ];

  return (
    <TimerPage
      minutes={15}
      title="15 Minute Timer - Ideal for Meditation and Quick Tasks"
      description="Free online 15 minute timer perfect for meditation, quick meetings, short workouts, and focused activities. Instant start with audio alerts."
      keywords="15 minute timer, 15 min timer, meditation timer, meeting timer, workout timer, focus timer, online timer 15 minutes"
      url="https://www.ai-pomo.com/timer/15-minutes"
      useCases={useCases}
      relatedTimers={relatedTimers}
    />
  );
};

export default Timer15MinutesPage;