import React from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { FaGraduationCap, FaClock, FaBrain, FaChartLine, FaCheck, FaArrowRight } from 'react-icons/fa';
import SEOHead from '../components/SEO/SEOHead';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import { createWebsiteStructuredData, createSoftwareApplicationStructuredData } from '../utils/structuredData';

const PomodoroForStudyingPage = () => {
  const structuredData = [
    createWebsiteStructuredData(),
    createSoftwareApplicationStructuredData()
  ];

  return (
    <>
      <SEOHead
        title="Pomodoro Technique for Studying | Best AI Study Timer for Students"
        description="Discover how to use the Pomodoro Technique for studying effectively. Our AI-powered study timer helps students improve focus, retention, and academic performance with proven 25-minute study sessions."
        keywords="Pomodoro for studying, Pomodoro technique studying, study timer, student productivity, focus for studying, AI study timer, 25 minute study sessions, academic productivity"
        url="https://www.ai-pomo.com/pomodoro-for-studying"
        structuredData={structuredData}
      />

      <Container>
        <PageHeader />

        <HeroSection>
          <HeroContainer>
            <HeroContent>
              <HeroTitle>Master the Pomodoro Technique for Studying</HeroTitle>
              <HeroSubtitle>
                Transform your study sessions with AI-powered Pomodoro techniques. Join thousands of students
                who've improved their focus, retention, and academic performance using proven 25-minute study intervals.
                Our AI helps you structure study projects, break down complex topics, and track your learning progress.
              </HeroSubtitle>
              <HeroButtons>
                <PrimaryButton to="/register">Start Studying Smarter - Free</PrimaryButton>
                <SecondaryButton to="/features">See How It Works</SecondaryButton>
              </HeroButtons>
            </HeroContent>
            <HeroImage>
              <FaGraduationCap />
            </HeroImage>
          </HeroContainer>
        </HeroSection>

        <BenefitsSection>
          <SectionTitle>Why Students Love Pomodoro for Studying</SectionTitle>
          <BenefitsGrid>
            <BenefitCard>
              <BenefitIcon><FaClock /></BenefitIcon>
              <BenefitTitle>Perfect Study Intervals</BenefitTitle>
              <BenefitText>
                25-minute study sessions match your natural attention span, making it easier to maintain
                focus during reading, note-taking, and problem-solving.
              </BenefitText>
            </BenefitCard>
            <BenefitCard>
              <BenefitIcon><FaBrain /></BenefitIcon>
              <BenefitTitle>Better Information Retention</BenefitTitle>
              <BenefitText>
                Regular breaks allow your brain to process and consolidate information, leading to
                improved long-term retention of study material.
              </BenefitText>
            </BenefitCard>
            <BenefitCard>
              <BenefitIcon><FaChartLine /></BenefitIcon>
              <BenefitTitle>Track Study Progress</BenefitTitle>
              <BenefitText>
                Monitor how much time you spend on each subject, identify your most productive hours,
                and optimize your study schedule for maximum efficiency.
              </BenefitText>
            </BenefitCard>
          </BenefitsGrid>
        </BenefitsSection>

        <HowItWorksSection>
          <SectionTitle>How to Use Pomodoro Technique for Studying</SectionTitle>
          <StepsContainer>
            <Step>
              <StepNumber>1</StepNumber>
              <StepContent>
                <StepTitle>Plan Your Study Session</StepTitle>
                <StepText>
                  List all subjects and topics you need to study. Estimate how many 25-minute Pomodoros
                  each task will require and prioritize based on deadlines and difficulty.
                </StepText>
              </StepContent>
            </Step>
            <Step>
              <StepNumber>2</StepNumber>
              <StepContent>
                <StepTitle>Start Your Study Pomodoro</StepTitle>
                <StepText>
                  Set the timer for 25 minutes and focus solely on one subject. No multitasking,
                  no distractions - just pure, concentrated study time.
                </StepText>
              </StepContent>
            </Step>
            <Step>
              <StepNumber>3</StepNumber>
              <StepContent>
                <StepTitle>Take Strategic Breaks</StepTitle>
                <StepText>
                  After each Pomodoro, take a 5-minute break to stretch, hydrate, or review your notes.
                  After 4 Pomodoros, take a longer 15-30 minute break.
                </StepText>
              </StepContent>
            </Step>
            <Step>
              <StepNumber>4</StepNumber>
              <StepContent>
                <StepTitle>Track and Optimize</StepTitle>
                <StepText>
                  Monitor your completed Pomodoros per subject, note which topics need more time,
                  and adjust your study plan for future sessions.
                </StepText>
              </StepContent>
            </Step>
          </StepsContainer>
        </HowItWorksSection>

        <StudyTypesSection>
          <SectionTitle>Pomodoro Techniques for Different Study Activities</SectionTitle>
          <StudyTypesGrid>
            <StudyTypeCard>
              <StudyTypeTitle>Reading & Note-Taking</StudyTypeTitle>
              <StudyTypeDescription>
                <strong>1 Pomodoro:</strong> Read one chapter or section<br/>
                <strong>Break Activity:</strong> Review and organize notes<br/>
                <strong>Tip:</strong> Use active reading techniques during focus time
              </StudyTypeDescription>
            </StudyTypeCard>
            <StudyTypeCard>
              <StudyTypeTitle>Problem-Solving</StudyTypeTitle>
              <StudyTypeDescription>
                <strong>1 Pomodoro:</strong> Work through 3-5 math/science problems<br/>
                <strong>Break Activity:</strong> Review solutions and identify patterns<br/>
                <strong>Tip:</strong> Don't stop mid-problem; adjust timing if needed
              </StudyTypeDescription>
            </StudyTypeCard>
            <StudyTypeCard>
              <StudyTypeTitle>Essay Writing</StudyTypeTitle>
              <StudyTypeDescription>
                <strong>1 Pomodoro:</strong> Write 250-300 words or create outline<br/>
                <strong>Break Activity:</strong> Review and plan next section<br/>
                <strong>Tip:</strong> Separate research, outlining, and writing
              </StudyTypeDescription>
            </StudyTypeCard>
            <StudyTypeCard>
              <StudyTypeTitle>Memorization & Review</StudyTypeTitle>
              <StudyTypeDescription>
                <strong>1 Pomodoro:</strong> Review flashcards or key concepts<br/>
                <strong>Break Activity:</strong> Test yourself without looking<br/>
                <strong>Tip:</strong> Use spaced repetition between sessions
              </StudyTypeDescription>
            </StudyTypeCard>
          </StudyTypesGrid>
        </StudyTypesSection>

        <FeaturesSection>
          <SectionTitle>AI-Powered Study Features</SectionTitle>
          <FeaturesList>
            <FeatureItem>
              <FeatureIcon><FaCheck /></FeatureIcon>
              <FeatureText>AI study project generator that breaks down assignments into manageable Pomodoros</FeatureText>
            </FeatureItem>
            <FeatureItem>
              <FeatureIcon><FaCheck /></FeatureIcon>
              <FeatureText>Smart study session tracking with subject-specific time analytics</FeatureText>
            </FeatureItem>
            <FeatureItem>
              <FeatureIcon><FaCheck /></FeatureIcon>
              <FeatureText>Calendar integration to schedule study sessions around classes and deadlines</FeatureText>
            </FeatureItem>
            <FeatureItem>
              <FeatureIcon><FaCheck /></FeatureIcon>
              <FeatureText>Progress visualization to see your study time investment per subject</FeatureText>
            </FeatureItem>
            <FeatureItem>
              <FeatureIcon><FaCheck /></FeatureIcon>
              <FeatureText>Distraction-free study mode with focus-enhancing features</FeatureText>
            </FeatureItem>
            <FeatureItem>
              <FeatureIcon><FaCheck /></FeatureIcon>
              <FeatureText>Study habit building with consistent Pomodoro practice</FeatureText>
            </FeatureItem>
          </FeaturesList>
        </FeaturesSection>

        <TestimonialSection>
          <SectionTitle>What Students Say About Pomodoro for Studying</SectionTitle>
          <TestimonialsGrid>
            <TestimonialCard>
              <TestimonialText>
                "Using Pomodoro for studying completely changed my academic performance. I went from
                struggling to focus for 10 minutes to easily completing 2-hour study sessions."
              </TestimonialText>
              <TestimonialAuthor>- Sarah M., Pre-Med Student</TestimonialAuthor>
            </TestimonialCard>
            <TestimonialCard>
              <TestimonialText>
                "The AI project breakdown feature helped me tackle my thesis research. What seemed
                impossible became manageable when broken into 25-minute study chunks."
              </TestimonialText>
              <TestimonialAuthor>- David L., PhD Candidate</TestimonialAuthor>
            </TestimonialCard>
            <TestimonialCard>
              <TestimonialText>
                "I love seeing my study progress visualized. Knowing exactly how much time I've
                invested in each subject keeps me motivated and on track."
              </TestimonialText>
              <TestimonialAuthor>- Maria R., Engineering Student</TestimonialAuthor>
            </TestimonialCard>
          </TestimonialsGrid>
        </TestimonialSection>

        <CTASection>
          <CTAContent>
            <CTATitle>Start Using Pomodoro for Studying Today</CTATitle>
            <CTADescription>
              Join thousands of students who've transformed their study habits with AI-enhanced
              Pomodoro techniques. Improve your focus, boost retention, and achieve better grades
              with structured 25-minute study sessions.
            </CTADescription>
            <CTAButtons>
              <PrimaryButton to="/register">
                Start Free Study Timer
                <FaArrowRight />
              </PrimaryButton>
              <SecondaryButton to="/features">
                Explore All Features
              </SecondaryButton>
            </CTAButtons>
          </CTAContent>
        </CTASection>

        <Footer />
      </Container>
    </>
  );
};

// Styled Components
const Container = styled.div`
  min-height: 100vh;
  background-color: #ffffff;
`;

const HeroSection = styled.section`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8rem 2rem 6rem;
  min-height: 70vh;
  display: flex;
  align-items: center;
  justify-content: center;

  @media (max-width: 968px) {
    padding: 6rem 2rem 4rem;
  }
`;

const HeroContainer = styled.div`
  display: flex;
  align-items: center;
  max-width: 1200px;
  width: 100%;

  @media (max-width: 968px) {
    flex-direction: column;
    text-align: center;
  }
`;

const HeroContent = styled.div`
  flex: 1;
  max-width: 600px;
  margin-right: 3rem;

  @media (max-width: 968px) {
    margin-right: 0;
    margin-bottom: 3rem;
  }
`;

const HeroTitle = styled.h1`
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const HeroSubtitle = styled.p`
  font-size: 1.25rem;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  opacity: 0.9;
`;

const HeroButtons = styled.div`
  display: flex;
  gap: 1rem;

  @media (max-width: 480px) {
    flex-direction: column;
  }
`;

const PrimaryButton = styled(Link)`
  background-color: #d95550;
  color: white;
  padding: 1rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &:hover {
    background-color: #c04540;
  }
`;

const SecondaryButton = styled(Link)`
  background-color: transparent;
  color: white;
  padding: 1rem 2rem;
  border: 2px solid white;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.2s;

  &:hover {
    background-color: white;
    color: #2d3748;
  }
`;

const HeroImage = styled.div`
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 15rem;
  opacity: 0.3;

  @media (max-width: 968px) {
    font-size: 8rem;
  }
`;

const BenefitsSection = styled.section`
  padding: 6rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const SectionTitle = styled.h2`
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  color: #2d3748;
`;

const BenefitsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
`;

const BenefitCard = styled.div`
  background: white;
  padding: 2.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
  border: 1px solid #e2e8f0;
`;

const BenefitIcon = styled.div`
  font-size: 3rem;
  color: #d95550;
  margin-bottom: 1.5rem;
`;

const BenefitTitle = styled.h3`
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2d3748;
`;

const BenefitText = styled.p`
  font-size: 1rem;
  line-height: 1.6;
  color: #4a5568;
`;

const HowItWorksSection = styled.section`
  background: #f7fafc;
  padding: 6rem 2rem;
`;

const StepsContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const Step = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 2rem;
  margin-bottom: 3rem;

  @media (max-width: 768px) {
    gap: 1rem;
  }
`;

const StepNumber = styled.div`
  background: #d95550;
  color: white;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  font-weight: 600;
  flex-shrink: 0;
`;

const StepContent = styled.div`
  flex: 1;
`;

const StepTitle = styled.h3`
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #2d3748;
`;

const StepText = styled.p`
  font-size: 1rem;
  line-height: 1.6;
  color: #4a5568;
`;

const StudyTypesSection = styled.section`
  padding: 6rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const StudyTypesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
`;

const StudyTypeCard = styled.div`
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #d95550;
`;

const StudyTypeTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2d3748;
`;

const StudyTypeDescription = styled.div`
  font-size: 0.95rem;
  line-height: 1.6;
  color: #4a5568;

  strong {
    color: #2d3748;
    font-weight: 600;
  }
`;

const FeaturesSection = styled.section`
  background: #f7fafc;
  padding: 6rem 2rem;
`;

const FeaturesList = styled.div`
  max-width: 800px;
  margin: 0 auto;
  display: grid;
  gap: 1.5rem;
`;

const FeatureItem = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
`;

const FeatureIcon = styled.div`
  color: #d95550;
  font-size: 1.25rem;
  flex-shrink: 0;
`;

const FeatureText = styled.span`
  font-size: 1rem;
  color: #2d3748;
  line-height: 1.5;
`;

const TestimonialSection = styled.section`
  padding: 6rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const TestimonialsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
`;

const TestimonialCard = styled.div`
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
`;

const TestimonialText = styled.p`
  font-size: 1rem;
  line-height: 1.6;
  color: #4a5568;
  margin-bottom: 1rem;
  font-style: italic;
`;

const TestimonialAuthor = styled.p`
  font-size: 0.9rem;
  color: #d95550;
  font-weight: 600;
`;

const CTASection = styled.section`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 6rem 2rem;
  text-align: center;
`;

const CTAContent = styled.div`
  max-width: 600px;
  margin: 0 auto;
`;

const CTATitle = styled.h2`
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;

  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const CTADescription = styled.p`
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  opacity: 0.9;
`;

const CTAButtons = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;

  @media (max-width: 480px) {
    flex-direction: column;
  }
`;

export default PomodoroForStudyingPage;
