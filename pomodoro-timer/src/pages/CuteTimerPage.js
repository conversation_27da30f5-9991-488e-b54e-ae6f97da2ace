import React, { useState, useEffect, useRef } from 'react';
import styled, { keyframes } from 'styled-components';
import { Link } from 'react-router-dom';
import { FaPlay, FaPause, FaStop, FaHeart, FaStar, FaArrowRight, FaCat, FaPaw } from 'react-icons/fa';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import SEOHead from '../components/SEO/SEOHead';

const CuteTimerPage = () => {
  const [timeLeft, setTimeLeft] = useState(5 * 60); // 5 minutes default
  const [isRunning, setIsRunning] = useState(false);
  const [inputMinutes, setInputMinutes] = useState(5);
  const [inputSeconds, setInputSeconds] = useState(0);
  const [originalTime, setOriginalTime] = useState(5 * 60);
  const [selectedTheme, setSelectedTheme] = useState('cat');
  const [encouragementIndex, setEncouragementIndex] = useState(0);
  
  const intervalRef = useRef(null);
  const audioRef = useRef(null);

  const themes = {
    cat: {
      name: 'Kitty Cat',
      emoji: '🐱',
      color: '#f472b6',
      secondaryColor: '#fdf2f8',
      gradient: 'linear-gradient(135deg, #f472b6, #ec4899)',
      encouragements: ['You\'re pawsome! 🐾', 'Meow-some work! 😸', 'Purr-fect focus! 🐱', 'Cat-tastic! 🌟']
    },
    rabbit: {
      name: 'Bunny Hop',
      emoji: '🐰',
      color: '#a78bfa',
      secondaryColor: '#f5f3ff',
      gradient: 'linear-gradient(135deg, #a78bfa, #8b5cf6)',
      encouragements: ['Hop to it! 🐰', 'Some-bunny is amazing! 💜', 'Ears to you! 👂', 'Hop-py working! 🌸']
    },
    bear: {
      name: 'Teddy Bear',
      emoji: '🧸',
      color: '#fb7185',
      secondaryColor: '#fef7f7',
      gradient: 'linear-gradient(135deg, #fb7185, #f43f5e)',
      encouragements: ['Bear-y good! 🧸', 'You\'re un-bear-able cute! 💕', 'Bear with it! 🌟', 'Teddy-ous work! 🤗']
    },
    panda: {
      name: 'Panda Friend',
      emoji: '🐼',
      color: '#6b7280',
      secondaryColor: '#f9fafb',
      gradient: 'linear-gradient(135deg, #6b7280, #4b5563)',
      encouragements: ['Panda-stic! 🐼', 'Bamboo-zled by your focus! 🎋', 'Pan-da best! 🌟', 'Bear-ly trying! 💪']
    },
    unicorn: {
      name: 'Magic Unicorn',
      emoji: '🦄',
      color: '#c084fc',
      secondaryColor: '#faf5ff',
      gradient: 'linear-gradient(135deg, #c084fc, #a855f7)',
      encouragements: ['Magical work! 🦄', 'Uni-corn-y but sweet! ✨', 'Sparkle on! 🌟', 'Myth-ical focus! 🌈']
    }
  };

  const encouragements = themes[selectedTheme].encouragements;

  useEffect(() => {
    if (isRunning && timeLeft > 0) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            completeTimer();
            return 0;
          }
          // Show encouragement every 5 minutes
          if (prev % 300 === 0 && prev > 0) {
            showEncouragement();
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      clearInterval(intervalRef.current);
    }

    return () => clearInterval(intervalRef.current);
  }, [isRunning, timeLeft]);

  const playAlarmSound = () => {
    if (audioRef.current) {
      audioRef.current.play().catch(e => console.log('Audio play failed:', e));
    }
    
    // Browser notification with cute message
    if (Notification.permission === 'granted') {
      new Notification(`${themes[selectedTheme].emoji} Cute Timer Complete!`, {
        body: `Time for a break! ${encouragements[Math.floor(Math.random() * encouragements.length)]}`,
        icon: '/ai-pomo.png'
      });
    }
  };

  const showEncouragement = () => {
    setEncouragementIndex(prev => (prev + 1) % encouragements.length);
  };

  const completeTimer = () => {
    setIsRunning(false);
    playAlarmSound();
    showEncouragement();
    
    if (window.gtag) {
      window.gtag('event', 'cute_timer_complete', {
        'timer_duration': originalTime,
        'theme': selectedTheme
      });
    }
  };

  const requestNotificationPermission = () => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const startTimer = () => {
    const totalSeconds = (inputMinutes * 60) + inputSeconds;
    if (totalSeconds > 0) {
      setTimeLeft(totalSeconds);
      setOriginalTime(totalSeconds);
      setIsRunning(true);
      requestNotificationPermission();
    }
  };

  const pauseTimer = () => {
    setIsRunning(false);
  };

  const resetTimer = () => {
    setIsRunning(false);
    setTimeLeft(originalTime);
  };

  const quickStart = (minutes) => {
    setInputMinutes(minutes);
    setInputSeconds(0);
    const totalSeconds = minutes * 60;
    setTimeLeft(totalSeconds);
    setOriginalTime(totalSeconds);
    setIsRunning(true);
    requestNotificationPermission();
  };

  const currentTheme = themes[selectedTheme];
  const progress = originalTime > 0 ? ((originalTime - timeLeft) / originalTime) * 100 : 0;

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Cute Timer - Adorable & Fun Timer with Kawaii Themes",
    "description": "Cute and adorable timer with kawaii animal themes. Make time management fun with cats, bunnies, bears, and unicorns. Perfect for kids and anyone who loves cute aesthetics!",
    "applicationCategory": "ProductivityApplication",
    "operatingSystem": "Any",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "Cute animal themes",
      "Kawaii design",
      "Encouraging messages",
      "Sound notifications",
      "Kid-friendly interface",
      "Multiple theme options"
    ]
  };

  return (
    <>
      <SEOHead
        title="Cute Timer - Adorable & Fun Timer with Kawaii Themes | AI Pomo"
        description="Cute and adorable timer with kawaii animal themes. Make time management fun with cats, bunnies, bears, and unicorns. Perfect for kids and anyone who loves cute aesthetics!"
        keywords="cute timer, kawaii timer, adorable timer, kids timer, fun timer, animal timer, cat timer, bunny timer, cute pomodoro"
        url="https://www.ai-pomo.com/cute-timer"
        type="website"
        structuredData={structuredData}
      />
      <PageHeader />
      <Container $theme={currentTheme}>
        <Header>
          <Title>
            {currentTheme.emoji} Cute Timer {currentTheme.emoji}
          </Title>
          <Subtitle>
            Make time management adorable! Choose your favorite kawaii friend and 
            enjoy cute encouragements while you work or study.
          </Subtitle>
        </Header>

        <MainContent>
          <TimerSection>
            <ThemeSelector>
              <ThemeSelectorTitle>Choose Your Cute Friend</ThemeSelectorTitle>
              <ThemeGrid>
                {Object.entries(themes).map(([key, theme]) => (
                  <ThemeButton
                    key={key}
                    $active={selectedTheme === key}
                    $theme={theme}
                    onClick={() => setSelectedTheme(key)}
                    disabled={isRunning}
                  >
                    <ThemeEmoji>{theme.emoji}</ThemeEmoji>
                    <ThemeName>{theme.name}</ThemeName>
                  </ThemeButton>
                ))}
              </ThemeGrid>
            </ThemeSelector>

            <TimerDisplay $theme={currentTheme} $isRunning={isRunning}>
              <FloatingHearts>
                <Heart delay="0s">💕</Heart>
                <Heart delay="1s">💖</Heart>
                <Heart delay="2s">✨</Heart>
                <Heart delay="3s">🌟</Heart>
              </FloatingHearts>
              
              <TimerCharacter $isRunning={isRunning}>
                {currentTheme.emoji}
              </TimerCharacter>
              
              <TimeText $theme={currentTheme}>{formatTime(timeLeft)}</TimeText>
              
              <ProgressPaws>
                <PawPrint $visible={progress > 25}>🐾</PawPrint>
                <PawPrint $visible={progress > 50}>🐾</PawPrint>
                <PawPrint $visible={progress > 75}>🐾</PawPrint>
                <PawPrint $visible={progress >= 100}>🌟</PawPrint>
              </ProgressPaws>
              
              {isRunning && (
                <EncouragementBubble $theme={currentTheme}>
                  {encouragements[encouragementIndex]}
                </EncouragementBubble>
              )}
            </TimerDisplay>

            <TimeInputSection>
              <InputGroup>
                <CuteInput
                  type="number"
                  min="0"
                  max="999"
                  value={inputMinutes}
                  onChange={(e) => setInputMinutes(Math.max(0, parseInt(e.target.value) || 0))}
                  disabled={isRunning}
                  $theme={currentTheme}
                />
                <InputLabel>minutes</InputLabel>
              </InputGroup>
              <InputGroup>
                <CuteInput
                  type="number"
                  min="0"
                  max="59"
                  value={inputSeconds}
                  onChange={(e) => setInputSeconds(Math.max(0, Math.min(59, parseInt(e.target.value) || 0)))}
                  disabled={isRunning}
                  $theme={currentTheme}
                />
                <InputLabel>seconds</InputLabel>
              </InputGroup>
            </TimeInputSection>

            <ControlButtons>
              {!isRunning ? (
                <StartButton $theme={currentTheme} onClick={startTimer}>
                  <FaPlay /> Start {currentTheme.emoji}
                </StartButton>
              ) : (
                <PauseButton onClick={pauseTimer}>
                  <FaPause /> Pause
                </PauseButton>
              )}
              <ResetButton onClick={resetTimer}>
                <FaStop /> Reset
              </ResetButton>
            </ControlButtons>

            <QuickButtons>
              <QuickButtonsTitle>Quick Cute Sessions</QuickButtonsTitle>
              <QuickButtonsGrid>
                {[
                  { minutes: 1, label: '1m', description: 'Super quick!' },
                  { minutes: 5, label: '5m', description: 'Mini session' },
                  { minutes: 10, label: '10m', description: 'Short & sweet' },
                  { minutes: 15, label: '15m', description: 'Perfect bite' },
                  { minutes: 25, label: '25m', description: 'Pomodoro' },
                  { minutes: 30, label: '30m', description: 'Half hour fun' }
                ].map(preset => (
                  <QuickButton
                    key={preset.minutes}
                    onClick={() => quickStart(preset.minutes)}
                    disabled={isRunning}
                    $theme={currentTheme}
                  >
                    <QuickTime>{preset.label}</QuickTime>
                    <QuickDesc>{preset.description}</QuickDesc>
                  </QuickButton>
                ))}
              </QuickButtonsGrid>
            </QuickButtons>
          </TimerSection>

          <FeaturesSection>
            <SectionTitle>Why Choose Cute Timer?</SectionTitle>
            <FeaturesGrid>
              <Feature>
                <FeatureIcon>🎨</FeatureIcon>
                <FeatureTitle>Kawaii Aesthetic</FeatureTitle>
                <FeatureText>Beautiful, cute designs that make timing fun and enjoyable</FeatureText>
              </Feature>
              <Feature>
                <FeatureIcon>💬</FeatureIcon>
                <FeatureTitle>Encouraging Messages</FeatureTitle>
                <FeatureText>Positive, cute messages to keep you motivated throughout your session</FeatureText>
              </Feature>
              <Feature>
                <FeatureIcon>👶</FeatureIcon>
                <FeatureTitle>Kid-Friendly</FeatureTitle>
                <FeatureText>Perfect for children, students, and anyone who loves adorable things</FeatureText>
              </Feature>
            </FeaturesGrid>
          </FeaturesSection>

          <UseCasesSection>
            <SectionTitle>Perfect For Cute Activities</SectionTitle>
            <UseCasesGrid>
              <UseCase>
                <UseCaseIcon>📚</UseCaseIcon>
                <UseCaseTitle>Study Sessions</UseCaseTitle>
                <UseCaseDescription>
                  Make homework and study time more enjoyable with cute companions
                </UseCaseDescription>
              </UseCase>
              <UseCase>
                <UseCaseIcon>🎨</UseCaseIcon>
                <UseCaseTitle>Creative Time</UseCaseTitle>
                <UseCaseDescription>
                  Perfect for drawing, crafting, writing, and other creative activities
                </UseCaseDescription>
              </UseCase>
              <UseCase>
                <UseCaseIcon>🧘</UseCaseIcon>
                <UseCaseTitle>Relaxation</UseCaseTitle>
                <UseCaseDescription>
                  Meditation, breathing exercises, and peaceful quiet time
                </UseCaseDescription>
              </UseCase>
              <UseCase>
                <UseCaseIcon>🎮</UseCaseIcon>
                <UseCaseTitle>Play Time</UseCaseTitle>
                <UseCaseDescription>
                  Set limits for screen time, games, or other fun activities
                </UseCaseDescription>
              </UseCase>
            </UseCasesGrid>
          </UseCasesSection>

          <TipsSection>
            <SectionTitle>Cute Timer Tips</SectionTitle>
            <TipsList>
              <TipItem>
                <TipIcon>🌟</TipIcon>
                <TipContent>
                  <TipTitle>Choose Your Mood</TipTitle>
                  <TipDescription>
                    Pick different animal friends based on how you're feeling! Cats for focus, bunnies for energy, bears for comfort.
                  </TipDescription>
                </TipContent>
              </TipItem>
              <TipItem>
                <TipIcon>💕</TipIcon>
                <TipContent>
                  <TipTitle>Celebrate Small Wins</TipTitle>
                  <TipDescription>
                    Every completed session is worth celebrating! Give yourself a little reward when you see those cute paw prints.
                  </TipDescription>
                </TipContent>
              </TipItem>
              <TipItem>
                <TipIcon>🎵</TipIcon>
                <TipContent>
                  <TipTitle>Create a Cute Atmosphere</TipTitle>
                  <TipDescription>
                    Play soft, cute music or nature sounds to match your adorable timer theme for the perfect cozy session.
                  </TipDescription>
                </TipContent>
              </TipItem>
            </TipsList>
          </TipsSection>

          <RelatedSection>
            <SectionTitle>More Fun Timers</SectionTitle>
            <RelatedGrid>
              <RelatedTimer as={Link} to="/visual-timer">
                <RelatedTitle>Visual Timer</RelatedTitle>
                <RelatedDescription>Colorful visual countdown with progress animation</RelatedDescription>
                <RelatedAction>
                  Try Visual Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
              <RelatedTimer as={Link} to="/study-timer">
                <RelatedTitle>Study Timer</RelatedTitle>
                <RelatedDescription>Focused timer designed for learning</RelatedDescription>
                <RelatedAction>
                  Try Study Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
              <RelatedTimer as={Link} to="/custom-timer">
                <RelatedTitle>Custom Timer</RelatedTitle>
                <RelatedDescription>Create your perfect timer for any activity</RelatedDescription>
                <RelatedAction>
                  Try Custom Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
            </RelatedGrid>
          </RelatedSection>
        </MainContent>

        <audio ref={audioRef} preload="auto">
          <source src="/sounds/completion.mp3" type="audio/mpeg" />
        </audio>
      </Container>
      <Footer />
    </>
  );
};

// Styled Components with cute animations
const floatAnimation = keyframes`
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
`;

const heartFloat = keyframes`
  0% { transform: translateY(0px) scale(0.8); opacity: 0.7; }
  50% { transform: translateY(-20px) scale(1); opacity: 1; }
  100% { transform: translateY(-40px) scale(0.6); opacity: 0; }
`;

const bounceAnimation = keyframes`
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
`;

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 6rem 2rem 4rem;
  background: ${props => props.$theme.secondaryColor};
  min-height: 100vh;

  @media (max-width: 768px) {
    padding: 5rem 1rem 2rem;
  }
`;

const Header = styled.header`
  text-align: center;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: 3rem;
  font-weight: 800;
  color: #111827;
  margin-bottom: 1rem;
  animation: ${bounceAnimation} 2s ease-in-out infinite;

  @media (max-width: 768px) {
    font-size: 2.25rem;
  }
`;

const Subtitle = styled.p`
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  max-width: 700px;
  margin: 0 auto;
`;

const MainContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4rem;
`;

const TimerSection = styled.section`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
`;

const ThemeSelector = styled.div`
  text-align: center;
  margin-bottom: 2rem;
`;

const ThemeSelectorTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
`;

const ThemeGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1rem;
  max-width: 500px;
  margin: 0 auto;

  @media (max-width: 768px) {
    grid-template-columns: repeat(3, 1fr);
  }

  @media (max-width: 480px) {
    grid-template-columns: repeat(2, 1fr);
  }
`;

const ThemeButton = styled.button`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem 0.5rem;
  border: 3px solid ${props => props.$active ? props.$theme.color : '#e5e7eb'};
  border-radius: 16px;
  background: ${props => props.$active ? props.$theme.secondaryColor : 'white'};
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover:not(:disabled) {
    border-color: ${props => props.$theme.color};
    transform: translateY(-2px) scale(1.05);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const ThemeEmoji = styled.div`
  font-size: 2rem;
  margin-bottom: 0.5rem;
`;

const ThemeName = styled.div`
  font-size: 0.75rem;
  font-weight: 600;
  color: #6b7280;
`;

const TimerDisplay = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 350px;
  height: 350px;
  border-radius: 50%;
  background: ${props => props.$theme.gradient};
  box-shadow: 0 20px 40px ${props => props.$theme.color}40;
  color: white;
  overflow: hidden;
  animation: ${props => props.$isRunning ? floatAnimation : 'none'} 3s ease-in-out infinite;

  @media (max-width: 480px) {
    width: 300px;
    height: 300px;
  }
`;

const FloatingHearts = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
`;

const Heart = styled.div`
  position: absolute;
  font-size: 1.5rem;
  animation: ${heartFloat} 3s ease-in-out infinite;
  animation-delay: ${props => props.delay};
  
  &:nth-child(1) { top: 10%; left: 20%; }
  &:nth-child(2) { top: 20%; right: 15%; }
  &:nth-child(3) { bottom: 30%; left: 10%; }
  &:nth-child(4) { bottom: 20%; right: 20%; }
`;

const TimerCharacter = styled.div`
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: ${props => props.$isRunning ? bounceAnimation : 'none'} 2s ease-in-out infinite;
`;

const TimeText = styled.div`
  font-size: 3rem;
  font-weight: 800;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
  margin-bottom: 1rem;

  @media (max-width: 480px) {
    font-size: 2.5rem;
  }
`;

const ProgressPaws = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
`;

const PawPrint = styled.div`
  font-size: 1.5rem;
  opacity: ${props => props.$visible ? 1 : 0.3};
  transition: all 0.5s ease;
  animation: ${props => props.$visible ? bounceAnimation : 'none'} 1s ease-in-out;
`;

const EncouragementBubble = styled.div`
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.9);
  color: ${props => props.$theme.color};
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  white-space: nowrap;
  animation: ${bounceAnimation} 1s ease-in-out;
`;

const TimeInputSection = styled.div`
  display: flex;
  gap: 2rem;
  align-items: center;

  @media (max-width: 480px) {
    flex-direction: column;
    gap: 1rem;
  }
`;

const InputGroup = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
`;

const CuteInput = styled.input`
  width: 100px;
  padding: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
  border: 3px solid ${props => props.$theme.color};
  border-radius: 20px;
  background: white;
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.$theme.color};
    box-shadow: 0 0 0 3px ${props => props.$theme.color}30;
    transform: scale(1.05);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const InputLabel = styled.label`
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 600;
`;

const ControlButtons = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 20px;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
  justify-content: center;
`;

const StartButton = styled(Button)`
  background: ${props => props.$theme.gradient};
  color: white;
  box-shadow: 0 4px 12px ${props => props.$theme.color}40;

  &:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 20px ${props => props.$theme.color}50;
  }
`;

const PauseButton = styled(Button)`
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);

  &:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
  }
`;

const ResetButton = styled(Button)`
  background: white;
  color: #6b7280;
  border: 3px solid #e5e7eb;

  &:hover {
    border-color: #f472b6;
    color: #f472b6;
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 12px rgba(244, 114, 182, 0.2);
  }
`;

const QuickButtons = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
`;

const QuickButtonsTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
`;

const QuickButtonsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  max-width: 500px;

  @media (max-width: 480px) {
    grid-template-columns: repeat(2, 1fr);
  }
`;

const QuickButton = styled.button`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border: 3px solid ${props => props.$theme.color}50;
  border-radius: 16px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    border-color: ${props => props.$theme.color};
    background: ${props => props.$theme.secondaryColor};
    transform: translateY(-2px) scale(1.05);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const QuickTime = styled.div`
  font-weight: 700;
  font-size: 1.125rem;
  color: #111827;
  margin-bottom: 0.25rem;
`;

const QuickDesc = styled.div`
  font-size: 0.75rem;
  color: #6b7280;
`;

const SectionTitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2rem;
  text-align: center;
`;

const FeaturesSection = styled.section``;

const FeaturesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
`;

const Feature = styled.div`
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 20px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
  }
`;

const FeatureIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const FeatureTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
`;

const FeatureText = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
`;

const UseCasesSection = styled.section``;

const UseCasesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
`;

const UseCase = styled.div`
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 20px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
  }
`;

const UseCaseIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const UseCaseTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.75rem;
`;

const UseCaseDescription = styled.p`
  color: #6b7280;
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0;
`;

const TipsSection = styled.section``;

const TipsList = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const TipItem = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  padding: 2rem;
  background: white;
  border-radius: 20px;
  border: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const TipIcon = styled.div`
  font-size: 2rem;
  flex-shrink: 0;
`;

const TipContent = styled.div`
  flex: 1;
`;

const TipTitle = styled.h4`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const TipDescription = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
`;

const RelatedSection = styled.section``;

const RelatedGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
`;

const RelatedTimer = styled.a`
  display: block;
  padding: 2rem;
  background: white;
  border: 3px solid #e5e7eb;
  border-radius: 20px;
  text-decoration: none;
  transition: all 0.3s ease;

  &:hover {
    border-color: #f472b6;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
`;

const RelatedTitle = styled.h4`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const RelatedDescription = styled.p`
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
`;

const RelatedAction = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #f472b6;
  font-weight: 600;
  font-size: 0.875rem;

  svg {
    font-size: 0.75rem;
  }
`;

export default CuteTimerPage;