import React from 'react';
import styled from 'styled-components';
import { <PERSON> } from 'react-router-dom';
import { FaPlay, FaClock, FaRocket } from 'react-icons/fa';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import SEOHead from '../components/SEO/SEOHead';

const TimersIndexPage = () => {
  const timers = [
    {
      minutes: 5,
      title: '5 Minute Timer',
      description: 'Perfect for quick breaks, micro tasks, and short meditation sessions.',
      color: '#10b981',
      popular: false
    },
    {
      minutes: 10,
      title: '10 Minute Timer', 
      description: 'Ideal for coffee breaks, quick reading, and brief focused tasks.',
      color: '#3b82f6',
      popular: false
    },
    {
      minutes: 15,
      title: '15 Minute Timer',
      description: 'Great for meditation, quick meetings, and short workout sessions.',
      color: '#8b5cf6',
      popular: false
    },
    {
      minutes: 20,
      title: '20 Minute Timer',
      description: 'Perfect for focused work, study sessions, and creative activities.',
      color: '#f59e0b',
      popular: true
    },
    {
      minutes: 25,
      title: '25 Minute Timer',
      description: 'The classic Pomodoro timer for maximum productivity and focus.',
      color: '#d95550',
      popular: true
    },
    {
      minutes: 30,
      title: '30 Minute Timer',
      description: 'Extended focus periods for deep work and substantial tasks.',
      color: '#06b6d4',
      popular: true
    },
    {
      minutes: 45,
      title: '45 Minute Timer',
      description: 'Deep work sessions, class periods, and extended project work.',
      color: '#84cc16',
      popular: false
    },
    {
      minutes: 35,
      title: '35 Minute Timer',
      description: 'Extended focus periods for sustained concentration and deep work.',
      color: '#06b6d4',
      popular: false
    },
    {
      minutes: 40,
      title: '40 Minute Timer',
      description: 'Academic work sessions and professional project development.',
      color: '#8b5cf6',
      popular: false
    },
    {
      minutes: 50,
      title: '50 Minute Timer',
      description: 'Power sessions for intensive work and maximum productivity.',
      color: '#f59e0b',
      popular: false
    },
    {
      minutes: 60,
      title: '60 Minute Timer (1 Hour)',
      description: 'Maximum focus time for complex projects and extended study.',
      color: '#ec4899',
      popular: false
    }
  ];

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": "Free Online Timers - Productivity Timer Tools",
    "description": "Collection of free online timers for productivity, study, work, and focus. From 5 minutes to 1 hour duration.",
    "hasPart": timers.map(timer => ({
      "@type": "WebPage",
      "name": timer.title,
      "description": timer.description,
      "url": `https://www.ai-pomo.com/timer/${timer.minutes}-minutes`
    }))
  };

  return (
    <>
      <SEOHead
        title="Free Online Timers - Productivity Timer Tools for Focus & Work"
        description="Free online timer collection from 5 minutes to 1 hour. Perfect for Pomodoro technique, study sessions, work breaks, meditation, and productivity. Start instantly!"
        keywords="online timer, free timer, productivity timer, pomodoro timer, study timer, work timer, focus timer, meditation timer, break timer"
        url="https://www.ai-pomo.com/timers"
        type="website"
        structuredData={structuredData}
      />
      <PageHeader />
      <Container>
        <Header>
          <Title>Free Online Timer Tools</Title>
          <Subtitle>
            Choose from our collection of productivity timers designed for focus, study, work, and breaks. 
            All timers are free, work instantly, and include sound notifications.
          </Subtitle>
        </Header>

        <PopularSection>
          <SectionTitle>⭐ Most Popular Timers</SectionTitle>
          <TimersGrid>
            {timers.filter(timer => timer.popular).map((timer, index) => (
              <TimerCard key={index} $color={timer.color} $popular>
                <PopularBadge>Popular</PopularBadge>
                <TimerDuration $color={timer.color}>{timer.minutes} min</TimerDuration>
                <TimerTitle>{timer.title}</TimerTitle>
                <TimerDescription>{timer.description}</TimerDescription>
                <TimerButton 
                  as={Link} 
                  to={`/timer/${timer.minutes}-minutes`}
                  $color={timer.color}
                >
                  <FaPlay /> Start Timer
                </TimerButton>
              </TimerCard>
            ))}
          </TimersGrid>
        </PopularSection>

        <AllTimersSection>
          <SectionTitle>🕐 All Timer Durations</SectionTitle>
          <TimersGrid>
            {timers.map((timer, index) => (
              <TimerCard key={index} $color={timer.color}>
                <TimerDuration $color={timer.color}>{timer.minutes} min</TimerDuration>
                <TimerTitle>{timer.title}</TimerTitle>
                <TimerDescription>{timer.description}</TimerDescription>
                <TimerButton 
                  as={Link} 
                  to={`/timer/${timer.minutes}-minutes`}
                  $color={timer.color}
                >
                  <FaPlay /> Start Timer
                </TimerButton>
              </TimerCard>
            ))}
          </TimersGrid>
        </AllTimersSection>

        <FeaturesSection>
          <SectionTitle>Why Use Our Timer Tools?</SectionTitle>
          <FeaturesGrid>
            <FeatureCard>
              <FeatureIcon><FaClock /></FeatureIcon>
              <FeatureTitle>Instant Start</FeatureTitle>
              <FeatureText>
                No registration required. Click and start timing immediately. 
                Perfect for quick productivity sessions.
              </FeatureText>
            </FeatureCard>
            <FeatureCard>
              <FeatureIcon><FaRocket /></FeatureIcon>
              <FeatureTitle>Smart Notifications</FeatureTitle>
              <FeatureText>
                Browser notifications and audio alerts ensure you never miss 
                when your timer completes.
              </FeatureText>
            </FeatureCard>
            <FeatureCard>
              <FeatureIcon><FaPlay /></FeatureIcon>
              <FeatureTitle>All Devices</FeatureTitle>
              <FeatureText>
                Works on desktop, mobile, and tablet. Responsive design 
                adapts to any screen size.
              </FeatureText>
            </FeatureCard>
          </FeaturesGrid>
        </FeaturesSection>

        <CTASection>
          <CTATitle>Ready for More Productivity?</CTATitle>
          <CTAText>
            These timers are just the beginning. Discover AI Pomo's complete productivity suite 
            with intelligent project planning and advanced time management.
          </CTAText>
          <CTAButtons>
            <CTAButton as={Link} to="/register" primary>
              Try AI Pomo Free
            </CTAButton>
            <CTAButton as={Link} to="/timer/25-minutes">
              Start 25 Min Pomodoro
            </CTAButton>
          </CTAButtons>
        </CTASection>
      </Container>
      <Footer />
    </>
  );
};

// Styled Components
const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 7rem 2rem 4rem;

  @media (max-width: 768px) {
    padding: 5rem 1rem 2rem;
  }
`;

const Header = styled.header`
  text-align: center;
  margin-bottom: 4rem;
`;

const Title = styled.h1`
  font-size: 3rem;
  font-weight: 800;
  color: #111827;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #d95550, #c44a45);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

  @media (max-width: 768px) {
    font-size: 2.25rem;
  }
`;

const Subtitle = styled.p`
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  max-width: 700px;
  margin: 0 auto;
`;

const PopularSection = styled.section`
  margin-bottom: 4rem;
`;

const AllTimersSection = styled.section`
  margin-bottom: 4rem;
`;

const SectionTitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2rem;
  text-align: center;
`;

const TimersGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
`;

const TimerCard = styled.div`
  background: white;
  border-radius: 16px;
  padding: 2rem;
  border: 2px solid ${props => props.$popular ? props.$color : '#e5e7eb'};
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  text-align: center;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: ${props => props.$color};
  }
`;

const PopularBadge = styled.div`
  position: absolute;
  top: -8px;
  right: 1rem;
  background: linear-gradient(135deg, #d95550, #c44a45);
  color: white;
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
`;

const TimerDuration = styled.div`
  font-size: 2.5rem;
  font-weight: 800;
  color: ${props => props.$color};
  margin-bottom: 1rem;
`;

const TimerTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.75rem;
`;

const TimerDescription = styled.p`
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
`;

const TimerButton = styled.a`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: ${props => props.$color};
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.9;
    transform: translateY(-2px);
  }

  svg {
    font-size: 0.9rem;
  }
`;

const FeaturesSection = styled.section`
  margin-bottom: 4rem;
`;

const FeaturesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
`;

const FeatureCard = styled.div`
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const FeatureIcon = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #d95550, #c44a45);
  color: white;
  border-radius: 50%;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
`;

const FeatureTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
`;

const FeatureText = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
`;

const CTASection = styled.section`
  text-align: center;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #f8f9fa, #f1f3f4);
  border-radius: 16px;
  border: 1px solid #e5e7eb;
`;

const CTATitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
`;

const CTAText = styled.p`
  font-size: 1.125rem;
  color: #6b7280;
  margin-bottom: 2rem;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
`;

const CTAButtons = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;

  @media (max-width: 480px) {
    flex-direction: column;
    align-items: center;
  }
`;

const CTAButton = styled.a`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;

  ${props => props.primary ? `
    background: linear-gradient(135deg, #d95550, #c44a45);
    color: white;
    box-shadow: 0 4px 12px rgba(217, 85, 80, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(217, 85, 80, 0.4);
    }
  ` : `
    background: white;
    color: #6b7280;
    border: 2px solid #e5e7eb;

    &:hover {
      border-color: #d95550;
      color: #d95550;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  `}

  @media (max-width: 480px) {
    width: 250px;
    justify-content: center;
  }
`;

export default TimersIndexPage;