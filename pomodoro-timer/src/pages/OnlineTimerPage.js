import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { FaPlay, FaPause, FaStop, FaClock, FaBell, FaDownload, FaArrowRight, FaRocket } from 'react-icons/fa';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import SEOHead from '../components/SEO/SEOHead';

const OnlineTimerPage = () => {
  const [timeLeft, setTimeLeft] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [inputHours, setInputHours] = useState(0);
  const [inputMinutes, setInputMinutes] = useState(5);
  const [inputSeconds, setInputSeconds] = useState(0);
  const [originalTime, setOriginalTime] = useState(0);
  const [timerMode, setTimerMode] = useState('countdown'); // countdown, stopwatch
  const [stopwatchTime, setStopwatchTime] = useState(0);
  
  const intervalRef = useRef(null);
  const audioRef = useRef(null);

  useEffect(() => {
    if (isRunning) {
      intervalRef.current = setInterval(() => {
        if (timerMode === 'countdown') {
          setTimeLeft(prev => {
            if (prev <= 1) {
              setIsRunning(false);
              playAlarmSound();
              if (window.gtag) {
                window.gtag('event', 'online_timer_complete', {
                  'timer_duration': originalTime,
                  'timer_type': 'countdown'
                });
              }
              return 0;
            }
            return prev - 1;
          });
        } else {
          setStopwatchTime(prev => prev + 1);
        }
      }, 1000);
    } else {
      clearInterval(intervalRef.current);
    }

    return () => clearInterval(intervalRef.current);
  }, [isRunning, timerMode, originalTime]);

  const playAlarmSound = () => {
    if (audioRef.current) {
      audioRef.current.play().catch(e => console.log('Audio play failed:', e));
    }
    
    // Browser notification
    if (Notification.permission === 'granted') {
      new Notification('⏰ Timer Complete!', {
        body: timerMode === 'countdown' ? 'Your countdown timer has finished.' : 'Stopwatch notification',
        icon: '/ai-pomo.png'
      });
    }
  };

  const requestNotificationPermission = () => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  };

  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const startTimer = () => {
    if (timerMode === 'countdown') {
      const totalSeconds = (inputHours * 3600) + (inputMinutes * 60) + inputSeconds;
      if (totalSeconds > 0) {
        setTimeLeft(totalSeconds);
        setOriginalTime(totalSeconds);
        setIsRunning(true);
        requestNotificationPermission();
      }
    } else {
      setIsRunning(true);
    }
  };

  const pauseTimer = () => {
    setIsRunning(false);
  };

  const resetTimer = () => {
    setIsRunning(false);
    if (timerMode === 'countdown') {
      setTimeLeft(originalTime);
    } else {
      setStopwatchTime(0);
    }
  };

  const clearTimer = () => {
    setIsRunning(false);
    setTimeLeft(0);
    setStopwatchTime(0);
    setOriginalTime(0);
    setInputHours(0);
    setInputMinutes(5);
    setInputSeconds(0);
  };

  const quickStart = (hours, minutes, seconds = 0) => {
    setTimerMode('countdown');
    setInputHours(hours);
    setInputMinutes(minutes);
    setInputSeconds(seconds);
    const totalSeconds = (hours * 3600) + (minutes * 60) + seconds;
    setTimeLeft(totalSeconds);
    setOriginalTime(totalSeconds);
    setIsRunning(true);
    requestNotificationPermission();
  };

  const currentDisplayTime = timerMode === 'countdown' ? timeLeft : stopwatchTime;
  const progress = timerMode === 'countdown' && originalTime > 0 ? 
    ((originalTime - timeLeft) / originalTime) * 100 : 0;

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Online Timer - Free Web Timer & Stopwatch",
    "description": "Free online timer and stopwatch. Set custom durations, use quick presets, and get notifications. Perfect for work, cooking, exercise, and productivity.",
    "applicationCategory": "ProductivityApplication",
    "operatingSystem": "Any",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "Countdown timer",
      "Stopwatch functionality", 
      "Custom time setting",
      "Quick preset buttons",
      "Sound notifications",
      "Browser notifications",
      "Progress visualization"
    ]
  };

  return (
    <>
      <SEOHead
        title="Online Timer - Free Web Timer & Stopwatch | AI Pomo"
        description="Free online timer and stopwatch. Set custom durations, use quick presets, and get notifications. Perfect for work, cooking, exercise, and productivity. No download required!"
        keywords="online timer, web timer, internet timer, stopwatch online, countdown timer, cooking timer, work timer, exercise timer, productivity timer"
        url="https://www.ai-pomo.com/online-timer"
        type="website"
        structuredData={structuredData}
      />
      <PageHeader />
      <Container>
        <Header>
          <Title>Online Timer</Title>
          <Subtitle>
            Free web-based timer and stopwatch. Set custom durations, use quick presets, 
            and stay productive with sound and visual notifications.
          </Subtitle>
        </Header>

        <MainContent>
          <TimerSection>
            <ModeToggle>
              <ModeButton 
                $active={timerMode === 'countdown'} 
                onClick={() => setTimerMode('countdown')}
              >
                <FaClock /> Timer
              </ModeButton>
              <ModeButton 
                $active={timerMode === 'stopwatch'} 
                onClick={() => setTimerMode('stopwatch')}
              >
                <FaRocket /> Stopwatch
              </ModeButton>
            </ModeToggle>

            <TimerDisplay $isRunning={isRunning} $timeLeft={currentDisplayTime} $mode={timerMode}>
              <TimeText>{formatTime(currentDisplayTime)}</TimeText>
              {timerMode === 'countdown' && originalTime > 0 && (
                <ProgressRing>
                  <ProgressCircle
                    cx="60"
                    cy="60"
                    r="54"
                    $progress={progress}
                  />
                </ProgressRing>
              )}
            </TimerDisplay>

            {timerMode === 'countdown' && (
              <TimeInputSection>
                <InputGroup>
                  <TimeInput
                    type="number"
                    min="0"
                    max="23"
                    value={inputHours}
                    onChange={(e) => setInputHours(Math.max(0, Math.min(23, parseInt(e.target.value) || 0)))}
                    disabled={isRunning}
                  />
                  <InputLabel>hours</InputLabel>
                </InputGroup>
                <InputGroup>
                  <TimeInput
                    type="number"
                    min="0"
                    max="59"
                    value={inputMinutes}
                    onChange={(e) => setInputMinutes(Math.max(0, Math.min(59, parseInt(e.target.value) || 0)))}
                    disabled={isRunning}
                  />
                  <InputLabel>minutes</InputLabel>
                </InputGroup>
                <InputGroup>
                  <TimeInput
                    type="number"
                    min="0"
                    max="59"
                    value={inputSeconds}
                    onChange={(e) => setInputSeconds(Math.max(0, Math.min(59, parseInt(e.target.value) || 0)))}
                    disabled={isRunning}
                  />
                  <InputLabel>seconds</InputLabel>
                </InputGroup>
              </TimeInputSection>
            )}

            <ControlButtons>
              {!isRunning ? (
                <StartButton onClick={startTimer}>
                  <FaPlay /> Start
                </StartButton>
              ) : (
                <PauseButton onClick={pauseTimer}>
                  <FaPause /> Pause
                </PauseButton>
              )}
              <ResetButton onClick={resetTimer}>
                <FaStop /> Reset
              </ResetButton>
              <ClearButton onClick={clearTimer}>
                Clear
              </ClearButton>
            </ControlButtons>

            {timerMode === 'countdown' && (
              <QuickButtons>
                <QuickButtonsTitle>Quick Start Presets</QuickButtonsTitle>
                <QuickButtonsGrid>
                  <QuickButton onClick={() => quickStart(0, 1)} disabled={isRunning}>1m</QuickButton>
                  <QuickButton onClick={() => quickStart(0, 5)} disabled={isRunning}>5m</QuickButton>
                  <QuickButton onClick={() => quickStart(0, 10)} disabled={isRunning}>10m</QuickButton>
                  <QuickButton onClick={() => quickStart(0, 15)} disabled={isRunning}>15m</QuickButton>
                  <QuickButton onClick={() => quickStart(0, 20)} disabled={isRunning}>20m</QuickButton>
                  <QuickButton onClick={() => quickStart(0, 25)} disabled={isRunning}>25m</QuickButton>
                  <QuickButton onClick={() => quickStart(0, 30)} disabled={isRunning}>30m</QuickButton>
                  <QuickButton onClick={() => quickStart(0, 45)} disabled={isRunning}>45m</QuickButton>
                  <QuickButton onClick={() => quickStart(1, 0)} disabled={isRunning}>1h</QuickButton>
                  <QuickButton onClick={() => quickStart(1, 30)} disabled={isRunning}>1h 30m</QuickButton>
                  <QuickButton onClick={() => quickStart(2, 0)} disabled={isRunning}>2h</QuickButton>
                  <QuickButton onClick={() => quickStart(0, 90)} disabled={isRunning}>90m</QuickButton>
                </QuickButtonsGrid>
              </QuickButtons>
            )}
          </TimerSection>

          <FeaturesSection>
            <SectionTitle>Timer Features</SectionTitle>
            <FeaturesGrid>
              <Feature>
                <FeatureIcon><FaClock /></FeatureIcon>
                <FeatureTitle>Flexible Duration</FeatureTitle>
                <FeatureText>Set any time from seconds to hours with precise control</FeatureText>
              </Feature>
              <Feature>
                <FeatureIcon><FaBell /></FeatureIcon>
                <FeatureTitle>Smart Alerts</FeatureTitle>
                <FeatureText>Sound and browser notifications ensure you never miss the end</FeatureText>
              </Feature>
              <Feature>
                <FeatureIcon><FaRocket /></FeatureIcon>
                <FeatureTitle>Dual Mode</FeatureTitle>
                <FeatureText>Switch between countdown timer and stopwatch functionality</FeatureText>
              </Feature>
            </FeaturesGrid>
          </FeaturesSection>

          <UseCasesSection>
            <SectionTitle>Perfect For</SectionTitle>
            <UseCasesGrid>
              <UseCase>
                <UseCaseIcon>💼</UseCaseIcon>
                <UseCaseTitle>Work & Productivity</UseCaseTitle>
                <UseCaseDescription>
                  Pomodoro sessions, meetings, focused work blocks, and task management
                </UseCaseDescription>
                <UseCasePresets>
                  <PresetTag>25m Pomodoro</PresetTag>
                  <PresetTag>15m Meeting</PresetTag>
                  <PresetTag>2h Deep Work</PresetTag>
                </UseCasePresets>
              </UseCase>
              <UseCase>
                <UseCaseIcon>🍳</UseCaseIcon>
                <UseCaseTitle>Cooking & Kitchen</UseCaseTitle>
                <UseCaseDescription>
                  Recipe timing, oven cooking, brewing tea, and meal preparation
                </UseCaseDescription>
                <UseCasePresets>
                  <PresetTag>3m Tea</PresetTag>
                  <PresetTag>12m Pasta</PresetTag>
                  <PresetTag>45m Roast</PresetTag>
                </UseCasePresets>
              </UseCase>
              <UseCase>
                <UseCaseIcon>💪</UseCaseIcon>
                <UseCaseTitle>Exercise & Fitness</UseCaseTitle>
                <UseCaseDescription>
                  Workout intervals, rest periods, yoga sessions, and training routines
                </UseCaseDescription>
                <UseCasePresets>
                  <PresetTag>45s HIIT</PresetTag>
                  <PresetTag>60s Plank</PresetTag>
                  <PresetTag>30m Yoga</PresetTag>
                </UseCasePresets>
              </UseCase>
              <UseCase>
                <UseCaseIcon>📚</UseCaseIcon>
                <UseCaseTitle>Study & Learning</UseCaseTitle>
                <UseCaseDescription>
                  Study sessions, exam practice, reading time, and educational activities
                </UseCaseDescription>
                <UseCasePresets>
                  <PresetTag>25m Study</PresetTag>
                  <PresetTag>5m Break</PresetTag>
                  <PresetTag>90m Exam</PresetTag>
                </UseCasePresets>
              </UseCase>
            </UseCasesGrid>
          </UseCasesSection>

          <TipsSection>
            <SectionTitle>Timer Tips & Tricks</SectionTitle>
            <TipsList>
              <TipItem>
                <TipNumber>1</TipNumber>
                <TipContent>
                  <TipTitle>Enable Browser Notifications</TipTitle>
                  <TipDescription>
                    Allow notifications to get alerts even when the tab is not active. Perfect for multitasking!
                  </TipDescription>
                </TipContent>
              </TipItem>
              <TipItem>
                <TipNumber>2</TipNumber>
                <TipContent>
                  <TipTitle>Use Quick Presets</TipTitle>
                  <TipDescription>
                    Save time by using our preset buttons for common durations like Pomodoro (25m) or short breaks (5m).
                  </TipDescription>
                </TipContent>
              </TipItem>
              <TipItem>
                <TipNumber>3</TipNumber>
                <TipContent>
                  <TipTitle>Keep Tab Open</TipTitle>
                  <TipDescription>
                    For the most reliable timing, keep this browser tab open. The timer works in background tabs but may be throttled.
                  </TipDescription>
                </TipContent>
              </TipItem>
            </TipsList>
          </TipsSection>

          <RelatedSection>
            <SectionTitle>More Timer Tools</SectionTitle>
            <RelatedGrid>
              <RelatedTimer as={Link} to="/countdown-timer">
                <RelatedTitle>Countdown Timer</RelatedTitle>
                <RelatedDescription>Specialized countdown with date targeting</RelatedDescription>
                <RelatedAction>
                  Try Countdown Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
              <RelatedTimer as={Link} to="/visual-timer">
                <RelatedTitle>Visual Timer</RelatedTitle>
                <RelatedDescription>Timer with visual progress animation</RelatedDescription>
                <RelatedAction>
                  Try Visual Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
              <RelatedTimer as={Link} to="/tomato-timer">
                <RelatedTitle>Tomato Timer</RelatedTitle>
                <RelatedDescription>Pomodoro technique with focus sessions</RelatedDescription>
                <RelatedAction>
                  Try Tomato Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
            </RelatedGrid>
          </RelatedSection>
        </MainContent>

        <audio ref={audioRef} preload="auto">
          <source src="/sounds/completion.mp3" type="audio/mpeg" />
        </audio>
      </Container>
      <Footer />
    </>
  );
};

// Styled Components
const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 6rem 2rem 4rem;

  @media (max-width: 768px) {
    padding: 5rem 1rem 2rem;
  }
`;

const Header = styled.header`
  text-align: center;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: 3rem;
  font-weight: 800;
  color: #111827;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

  @media (max-width: 768px) {
    font-size: 2.25rem;
  }
`;

const Subtitle = styled.p`
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  max-width: 700px;
  margin: 0 auto;
`;

const MainContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4rem;
`;

const TimerSection = styled.section`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
`;

const ModeToggle = styled.div`
  display: flex;
  background: #f3f4f6;
  border-radius: 12px;
  padding: 4px;
  margin-bottom: 1rem;
`;

const ModeButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  background: ${props => props.$active ? 'white' : 'transparent'};
  color: ${props => props.$active ? '#06b6d4' : '#6b7280'};
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: ${props => props.$active ? '0 2px 4px rgba(0,0,0,0.1)' : 'none'};

  &:hover {
    color: #06b6d4;
  }
`;

const TimerDisplay = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 320px;
  height: 320px;
  border-radius: 50%;
  background: ${props => {
    if (props.$mode === 'stopwatch') {
      return 'linear-gradient(135deg, #10b981, #059669)';
    }
    return props.$isRunning && props.$timeLeft <= 10 ? 
      'linear-gradient(135deg, #ef4444, #dc2626)' : 
      'linear-gradient(135deg, #06b6d4, #0891b2)';
  }};
  box-shadow: 0 20px 40px rgba(6, 182, 212, 0.3);
  transition: all 0.3s ease;
  animation: ${props => props.$isRunning && props.$timeLeft <= 10 && props.$mode === 'countdown' ? 'pulse 1s infinite' : 'none'};

  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @media (max-width: 480px) {
    width: 280px;
    height: 280px;
  }
`;

const TimeText = styled.div`
  font-size: 3rem;
  font-weight: 800;
  color: white;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;

  @media (max-width: 480px) {
    font-size: 2.5rem;
  }
`;

const ProgressRing = styled.svg`
  position: absolute;
  width: 120px;
  height: 120px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-90deg);
`;

const ProgressCircle = styled.circle`
  fill: none;
  stroke: rgba(255, 255, 255, 0.3);
  stroke-width: 8;
  stroke-dasharray: 339.292;
  stroke-dashoffset: ${props => 339.292 * (1 - props.$progress / 100)};
  transition: stroke-dashoffset 1s ease;
`;

const TimeInputSection = styled.div`
  display: flex;
  gap: 1.5rem;
  align-items: center;

  @media (max-width: 480px) {
    flex-direction: column;
    gap: 1rem;
  }
`;

const InputGroup = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
`;

const TimeInput = styled.input`
  width: 80px;
  padding: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #06b6d4;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const InputLabel = styled.label`
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 600;
`;

const ControlButtons = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  justify-content: center;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const StartButton = styled(Button)`
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
  }
`;

const PauseButton = styled(Button)`
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
  }
`;

const ResetButton = styled(Button)`
  background: white;
  color: #6b7280;
  border: 2px solid #e5e7eb;

  &:hover:not(:disabled) {
    border-color: #06b6d4;
    color: #06b6d4;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;

const ClearButton = styled(Button)`
  background: #f3f4f6;
  color: #6b7280;
  border: none;
  min-width: 100px;

  &:hover:not(:disabled) {
    background: #e5e7eb;
    transform: translateY(-2px);
  }
`;

const QuickButtons = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
`;

const QuickButtonsTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
`;

const QuickButtonsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 0.5rem;
  max-width: 600px;

  @media (max-width: 768px) {
    grid-template-columns: repeat(4, 1fr);
  }

  @media (max-width: 480px) {
    grid-template-columns: repeat(3, 1fr);
  }
`;

const QuickButton = styled.button`
  padding: 0.75rem 0.5rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #6b7280;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;

  &:hover:not(:disabled) {
    border-color: #06b6d4;
    color: #06b6d4;
    transform: translateY(-2px);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const FeaturesSection = styled.section``;

const SectionTitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2rem;
  text-align: center;
`;

const FeaturesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
`;

const Feature = styled.div`
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const FeatureIcon = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  color: white;
  border-radius: 50%;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
`;

const FeatureTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
`;

const FeatureText = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
`;

const UseCasesSection = styled.section``;

const UseCasesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
`;

const UseCase = styled.div`
  padding: 2rem;
  background: white;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
  }
`;

const UseCaseIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const UseCaseTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.75rem;
`;

const UseCaseDescription = styled.p`
  color: #6b7280;
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 1rem;
`;

const UseCasePresets = styled.div`
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
`;

const PresetTag = styled.span`
  padding: 0.25rem 0.75rem;
  background: #f0f9ff;
  color: #0891b2;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
`;

const TipsSection = styled.section``;

const TipsList = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const TipItem = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const TipNumber = styled.div`
  width: 2.5rem;
  height: 2.5rem;
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.125rem;
  flex-shrink: 0;
`;

const TipContent = styled.div`
  flex: 1;
`;

const TipTitle = styled.h4`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const TipDescription = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
`;

const RelatedSection = styled.section``;

const RelatedGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
`;

const RelatedTimer = styled.a`
  display: block;
  padding: 2rem;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  text-decoration: none;
  transition: all 0.3s ease;

  &:hover {
    border-color: #06b6d4;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
`;

const RelatedTitle = styled.h4`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const RelatedDescription = styled.p`
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
`;

const RelatedAction = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #06b6d4;
  font-weight: 600;
  font-size: 0.875rem;

  svg {
    font-size: 0.75rem;
  }
`;

export default OnlineTimerPage;