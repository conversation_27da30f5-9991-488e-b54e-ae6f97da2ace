import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { FaPlay, FaPause, FaStop, FaClock, FaCalendarAlt, FaBell, FaArrowRight } from 'react-icons/fa';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import SEOHead from '../components/SEO/SEOHead';

const CountdownTimerPage = () => {
  const [timeLeft, setTimeLeft] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [inputMinutes, setInputMinutes] = useState(5);
  const [inputSeconds, setInputSeconds] = useState(0);
  const [originalTime, setOriginalTime] = useState(0);
  const [targetDate, setTargetDate] = useState('');
  const [isDateMode, setIsDateMode] = useState(false);
  
  const intervalRef = useRef(null);
  const audioRef = useRef(null);

  useEffect(() => {
    if (isRunning && timeLeft > 0) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            setIsRunning(false);
            playAlarmSound();
            if (window.gtag) {
              window.gtag('event', 'countdown_complete', {
                'timer_duration': originalTime,
                'timer_type': 'countdown'
              });
            }
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      clearInterval(intervalRef.current);
    }

    return () => clearInterval(intervalRef.current);
  }, [isRunning, timeLeft, originalTime]);

  useEffect(() => {
    if (targetDate && isDateMode) {
      const interval = setInterval(() => {
        const now = new Date().getTime();
        const target = new Date(targetDate).getTime();
        const difference = target - now;
        
        if (difference > 0) {
          setTimeLeft(Math.floor(difference / 1000));
        } else {
          setTimeLeft(0);
          setIsRunning(false);
          playAlarmSound();
        }
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [targetDate, isDateMode]);

  const playAlarmSound = () => {
    if (audioRef.current) {
      audioRef.current.play().catch(e => console.log('Audio play failed:', e));
    }
    
    // Browser notification
    if (Notification.permission === 'granted') {
      new Notification('⏰ Countdown Complete!', {
        body: 'Your countdown timer has finished.',
        icon: '/ai-pomo.png'
      });
    }
  };

  const requestNotificationPermission = () => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  };

  const formatTime = (seconds) => {
    if (isDateMode && seconds > 86400) {
      const days = Math.floor(seconds / 86400);
      const hours = Math.floor((seconds % 86400) / 3600);
      const mins = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;
      return `${days}d ${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = seconds % 60;
      
      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
      }
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
  };

  const startTimer = () => {
    if (!isDateMode) {
      const totalSeconds = (inputMinutes * 60) + inputSeconds;
      if (totalSeconds > 0) {
        setTimeLeft(totalSeconds);
        setOriginalTime(totalSeconds);
        setIsRunning(true);
        requestNotificationPermission();
      }
    } else if (targetDate) {
      setIsRunning(true);
      requestNotificationPermission();
    }
  };

  const pauseTimer = () => {
    setIsRunning(false);
  };

  const resetTimer = () => {
    setIsRunning(false);
    if (isDateMode) {
      setTimeLeft(0);
    } else {
      setTimeLeft(originalTime);
    }
  };

  const quickStart = (minutes) => {
    setIsDateMode(false);
    setInputMinutes(minutes);
    setInputSeconds(0);
    const totalSeconds = minutes * 60;
    setTimeLeft(totalSeconds);
    setOriginalTime(totalSeconds);
    setIsRunning(true);
    requestNotificationPermission();
  };

  const progress = originalTime > 0 ? ((originalTime - timeLeft) / originalTime) * 100 : 0;

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Countdown Timer - Free Online Countdown Clock",
    "description": "Free online countdown timer with custom duration and date targeting. Perfect for events, cooking, workouts, and deadlines.",
    "applicationCategory": "ProductivityApplication",
    "operatingSystem": "Any",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "Custom countdown duration",
      "Date and time targeting",
      "Visual progress indicator",
      "Sound notifications",
      "Browser notifications",
      "Quick preset buttons"
    ]
  };

  return (
    <>
      <SEOHead
        title="Countdown Timer - Free Online Countdown Clock | AI Pomo"
        description="Free online countdown timer with custom duration and date targeting. Set countdowns for events, cooking, workouts, and deadlines. No registration required - start counting down instantly!"
        keywords="countdown timer, online countdown, timer countdown, countdown clock, event countdown, cooking timer, workout timer, deadline countdown"
        url="https://www.ai-pomo.com/countdown-timer"
        type="website"
        structuredData={structuredData}
      />
      <PageHeader />
      <Container>
        <Header>
          <Title>Countdown Timer</Title>
          <Subtitle>
            Free online countdown timer for any duration or target date. 
            Perfect for events, cooking, workouts, and deadlines.
          </Subtitle>
        </Header>

        <MainContent>
          <TimerSection>
            <ModeToggle>
              <ModeButton 
                $active={!isDateMode} 
                onClick={() => setIsDateMode(false)}
              >
                <FaClock /> Duration
              </ModeButton>
              <ModeButton 
                $active={isDateMode} 
                onClick={() => setIsDateMode(true)}
              >
                <FaCalendarAlt /> Target Date
              </ModeButton>
            </ModeToggle>

            <TimerDisplay $isRunning={isRunning} $timeLeft={timeLeft}>
              <TimeText>{formatTime(timeLeft)}</TimeText>
              {!isDateMode && originalTime > 0 && (
                <ProgressRing>
                  <ProgressCircle
                    cx="60"
                    cy="60"
                    r="54"
                    $progress={progress}
                  />
                </ProgressRing>
              )}
            </TimerDisplay>

            {!isDateMode ? (
              <TimeInputSection>
                <InputGroup>
                  <TimeInput
                    type="number"
                    min="0"
                    max="999"
                    value={inputMinutes}
                    onChange={(e) => setInputMinutes(Math.max(0, parseInt(e.target.value) || 0))}
                    disabled={isRunning}
                  />
                  <InputLabel>minutes</InputLabel>
                </InputGroup>
                <InputGroup>
                  <TimeInput
                    type="number"
                    min="0"
                    max="59"
                    value={inputSeconds}
                    onChange={(e) => setInputSeconds(Math.max(0, Math.min(59, parseInt(e.target.value) || 0)))}
                    disabled={isRunning}
                  />
                  <InputLabel>seconds</InputLabel>
                </InputGroup>
              </TimeInputSection>
            ) : (
              <DateInputSection>
                <DateInput
                  type="datetime-local"
                  value={targetDate}
                  onChange={(e) => setTargetDate(e.target.value)}
                  disabled={isRunning}
                  min={new Date().toISOString().slice(0, 16)}
                />
              </DateInputSection>
            )}

            <ControlButtons>
              {!isRunning ? (
                <StartButton onClick={startTimer}>
                  <FaPlay /> Start
                </StartButton>
              ) : (
                <PauseButton onClick={pauseTimer}>
                  <FaPause /> Pause
                </PauseButton>
              )}
              <ResetButton onClick={resetTimer}>
                <FaStop /> Reset
              </ResetButton>
            </ControlButtons>

            {!isDateMode && (
              <QuickButtons>
                <QuickButtonsTitle>Quick Start</QuickButtonsTitle>
                <QuickButtonsGrid>
                  {[1, 5, 10, 15, 20, 30, 45, 60].map(minutes => (
                    <QuickButton 
                      key={minutes}
                      onClick={() => quickStart(minutes)}
                      disabled={isRunning}
                    >
                      {minutes}m
                    </QuickButton>
                  ))}
                </QuickButtonsGrid>
              </QuickButtons>
            )}
          </TimerSection>

          <UseCasesSection>
            <SectionTitle>Perfect For</SectionTitle>
            <UseCasesGrid>
              <UseCase>
                <UseCaseIcon>🎂</UseCaseIcon>
                <UseCaseTitle>Events & Celebrations</UseCaseTitle>
                <UseCaseDescription>Birthdays, holidays, weddings, and special occasions</UseCaseDescription>
              </UseCase>
              <UseCase>
                <UseCaseIcon>🍳</UseCaseIcon>
                <UseCaseTitle>Cooking & Baking</UseCaseTitle>
                <UseCaseDescription>Perfect timing for recipes, oven cooking, and meal prep</UseCaseDescription>
              </UseCase>
              <UseCase>
                <UseCaseIcon>💪</UseCaseIcon>
                <UseCaseTitle>Workouts & Exercise</UseCaseTitle>
                <UseCaseDescription>HIIT intervals, rest periods, and exercise routines</UseCaseDescription>
              </UseCase>
              <UseCase>
                <UseCaseIcon>📅</UseCaseIcon>
                <UseCaseTitle>Deadlines & Goals</UseCaseTitle>
                <UseCaseDescription>Project deadlines, exam dates, and important milestones</UseCaseDescription>
              </UseCase>
            </UseCasesGrid>
          </UseCasesSection>

          <FeaturesSection>
            <SectionTitle>Why Choose Our Countdown Timer?</SectionTitle>
            <FeaturesGrid>
              <Feature>
                <FeatureIcon><FaBell /></FeatureIcon>
                <FeatureTitle>Smart Notifications</FeatureTitle>
                <FeatureText>Sound alerts and browser notifications ensure you never miss the end</FeatureText>
              </Feature>
              <Feature>
                <FeatureIcon><FaClock /></FeatureIcon>
                <FeatureTitle>Flexible Timing</FeatureTitle>
                <FeatureText>Set any duration or countdown to a specific date and time</FeatureText>
              </Feature>
              <Feature>
                <FeatureIcon><FaPlay /></FeatureIcon>
                <FeatureTitle>Instant Start</FeatureTitle>
                <FeatureText>No registration required - start your countdown timer immediately</FeatureText>
              </Feature>
            </FeaturesGrid>
          </FeaturesSection>

          <RelatedSection>
            <SectionTitle>More Timer Tools</SectionTitle>
            <RelatedGrid>
              <RelatedTimer as={Link} to="/online-timer">
                <RelatedTitle>Online Timer</RelatedTitle>
                <RelatedDescription>General purpose timer for any task</RelatedDescription>
                <RelatedAction>
                  Try Online Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
              <RelatedTimer as={Link} to="/visual-timer">
                <RelatedTitle>Visual Timer</RelatedTitle>
                <RelatedDescription>Visual countdown with progress animation</RelatedDescription>
                <RelatedAction>
                  Try Visual Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
              <RelatedTimer as={Link} to="/tomato-timer">
                <RelatedTitle>Tomato Timer</RelatedTitle>
                <RelatedDescription>Pomodoro technique timer with focus sessions</RelatedDescription>
                <RelatedAction>
                  Try Tomato Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
            </RelatedGrid>
          </RelatedSection>
        </MainContent>

        <audio ref={audioRef} preload="auto">
          <source src="/sounds/completion.mp3" type="audio/mpeg" />
        </audio>
      </Container>
      <Footer />
    </>
  );
};

// Styled Components
const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 6rem 2rem 4rem;

  @media (max-width: 768px) {
    padding: 5rem 1rem 2rem;
  }
`;

const Header = styled.header`
  text-align: center;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: 3rem;
  font-weight: 800;
  color: #111827;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

  @media (max-width: 768px) {
    font-size: 2.25rem;
  }
`;

const Subtitle = styled.p`
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  max-width: 700px;
  margin: 0 auto;
`;

const MainContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4rem;
`;

const TimerSection = styled.section`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
`;

const ModeToggle = styled.div`
  display: flex;
  background: #f3f4f6;
  border-radius: 12px;
  padding: 4px;
  margin-bottom: 1rem;
`;

const ModeButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  background: ${props => props.$active ? 'white' : 'transparent'};
  color: ${props => props.$active ? '#3b82f6' : '#6b7280'};
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: ${props => props.$active ? '0 2px 4px rgba(0,0,0,0.1)' : 'none'};

  &:hover {
    color: #3b82f6;
  }
`;

const TimerDisplay = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 320px;
  height: 320px;
  border-radius: 50%;
  background: ${props => props.$isRunning && props.$timeLeft <= 10 ? 
    'linear-gradient(135deg, #ef4444, #dc2626)' : 
    'linear-gradient(135deg, #3b82f6, #1d4ed8)'
  };
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
  animation: ${props => props.$isRunning && props.$timeLeft <= 10 ? 'pulse 1s infinite' : 'none'};

  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @media (max-width: 480px) {
    width: 280px;
    height: 280px;
  }
`;

const TimeText = styled.div`
  font-size: 3rem;
  font-weight: 800;
  color: white;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;

  @media (max-width: 480px) {
    font-size: 2.5rem;
  }
`;

const ProgressRing = styled.svg`
  position: absolute;
  width: 120px;
  height: 120px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-90deg);
`;

const ProgressCircle = styled.circle`
  fill: none;
  stroke: rgba(255, 255, 255, 0.3);
  stroke-width: 8;
  stroke-dasharray: 339.292;
  stroke-dashoffset: ${props => 339.292 * (1 - props.$progress / 100)};
  transition: stroke-dashoffset 1s ease;
`;

const TimeInputSection = styled.div`
  display: flex;
  gap: 2rem;
  align-items: center;

  @media (max-width: 480px) {
    flex-direction: column;
    gap: 1rem;
  }
`;

const InputGroup = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
`;

const TimeInput = styled.input`
  width: 100px;
  padding: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #3b82f6;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const InputLabel = styled.label`
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 600;
`;

const DateInputSection = styled.div`
  display: flex;
  justify-content: center;
`;

const DateInput = styled.input`
  padding: 1rem 1.5rem;
  font-size: 1.125rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #3b82f6;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const ControlButtons = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  justify-content: center;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const StartButton = styled(Button)`
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
  }
`;

const PauseButton = styled(Button)`
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
  }
`;

const ResetButton = styled(Button)`
  background: white;
  color: #6b7280;
  border: 2px solid #e5e7eb;

  &:hover:not(:disabled) {
    border-color: #3b82f6;
    color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;

const QuickButtons = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
`;

const QuickButtonsTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
`;

const QuickButtonsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.75rem;

  @media (max-width: 480px) {
    grid-template-columns: repeat(3, 1fr);
  }
`;

const QuickButton = styled.button`
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #6b7280;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 60px;

  &:hover:not(:disabled) {
    border-color: #3b82f6;
    color: #3b82f6;
    transform: translateY(-2px);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const UseCasesSection = styled.section``;

const SectionTitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2rem;
  text-align: center;
`;

const UseCasesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
`;

const UseCase = styled.div`
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
  }
`;

const UseCaseIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const UseCaseTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.75rem;
`;

const UseCaseDescription = styled.p`
  color: #6b7280;
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0;
`;

const FeaturesSection = styled.section``;

const FeaturesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
`;

const Feature = styled.div`
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const FeatureIcon = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 50%;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
`;

const FeatureTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
`;

const FeatureText = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
`;

const RelatedSection = styled.section``;

const RelatedGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
`;

const RelatedTimer = styled.a`
  display: block;
  padding: 2rem;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  text-decoration: none;
  transition: all 0.3s ease;

  &:hover {
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
`;

const RelatedTitle = styled.h4`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const RelatedDescription = styled.p`
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
`;

const RelatedAction = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #3b82f6;
  font-weight: 600;
  font-size: 0.875rem;

  svg {
    font-size: 0.75rem;
  }
`;

export default CountdownTimerPage;