import React from 'react';
import { FaGraduationCap, FaBook, FaPencilAlt } from 'react-icons/fa';
import ScenarioTimerPage from '../components/Timer/ScenarioTimerPage';

const StudentStudyTimerPage = () => {
  const studentConfig = {
    scenario: 'Student Study',
    title: 'Student Study Timer - Academic Success Timer | AI Pomo',
    description: 'Study timer designed for students of all levels. Perfect for homework sessions, exam preparation, reading assignments, and building effective study habits for academic success.',
    keywords: 'student timer, study timer, homework timer, exam preparation, academic study, college study timer, high school study',
    url: 'https://www.ai-pomo.com/student-study-timer',
    heroTitle: 'Student Study Timer',
    heroDescription: 'Boost your academic performance with structured study sessions. Designed specifically for students to master homework, exams, and long-term learning goals.',
    defaultMinutes: 25,
    gradientColors: ['#f59e0b', '#d97706'],
    backgroundColor: 'linear-gradient(135deg, #fffbeb, #fef3c7)',
    
    scenarios: [
      {
        emoji: '📖',
        title: 'Reading Assignments',
        description: 'Textbook chapters, research papers, and literature',
        recommendedTime: 'Recommended 30-45 minutes'
      },
      {
        emoji: '✍️',
        title: 'Essay Writing',
        description: 'Research papers, essays, and written assignments',
        recommendedTime: 'Recommended 45-60 minutes'
      },
      {
        emoji: '🧮',
        title: 'Problem Sets',
        description: 'Math homework, physics problems, and practice exercises',
        recommendedTime: 'Recommended 25-40 minutes'
      },
      {
        emoji: '📚',
        title: 'Exam Review',
        description: 'Flashcards, review sessions, and test preparation',
        recommendedTime: 'Recommended 20-30 minutes'
      }
    ],

    benefits: [
      {
        emoji: '🎯',
        title: 'Better Grades',
        description: 'Consistent study sessions lead to improved academic performance and higher grades'
      },
      {
        emoji: '🧠',
        title: 'Enhanced Memory',
        description: 'Spaced study sessions improve long-term retention and recall of information'
      },
      {
        emoji: '💪',
        title: 'Study Stamina',
        description: 'Build concentration muscle and ability to focus for extended periods'
      },
      {
        emoji: '⏰',
        title: 'Time Management',
        description: 'Develop essential time management skills for academic and future career success'
      }
    ],

    tips: [
      {
        title: 'Create a Study Schedule',
        description: 'Plan study sessions in advance and treat them as important appointments with yourself'
      },
      {
        title: 'Use Active Learning',
        description: 'Take notes, ask questions, and engage with material rather than passive reading'
      },
      {
        title: 'Find Your Peak Hours',
        description: 'Identify when you focus best and schedule challenging subjects during these times'
      },
      {
        title: 'Eliminate Distractions',
        description: 'Put phone in another room, use website blockers, and create a study-only environment'
      },
      {
        title: 'Review and Reflect',
        description: 'End each study session by reviewing what you learned and planning next steps'
      }
    ],

    customFeatures: [
      {
        icon: <FaGraduationCap />,
        title: 'Academic Optimization',
        description: 'Timing strategies based on educational research and learning science'
      },
      {
        icon: <FaBook />,
        title: 'Subject Flexibility',
        description: 'Adapt timing for different subjects - STEM vs humanities vs languages'
      },
      {
        icon: <FaPencilAlt />,
        title: 'Assignment Tracking',
        description: 'Perfect for managing multiple assignments and deadline pressure'
      }
    ],

    relatedTimers: [
      {
        title: 'Study Timer',
        description: 'General study and learning',
        duration: '25-60 minutes',
        url: '/study-timer'
      },
      {
        title: 'Break Timer',
        description: 'Essential study breaks',
        duration: '5-15 minutes',
        url: '/break-timer'
      },
      {
        title: 'Exam Timer',
        description: 'Test preparation and review',
        duration: '30-90 minutes',
        url: '/exam-timer'
      }
    ]
  };

  return <ScenarioTimerPage config={studentConfig} />;
};

export default StudentStudyTimerPage;