import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { FaPlay, FaPause, FaStop, FaBell, FaClock, FaVolumeUp, FaArrowRight, FaSun, FaMoon } from 'react-icons/fa';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import SEOHead from '../components/SEO/SEOHead';

const OnlineAlarmPage = () => {
  const [alarmTime, setAlarmTime] = useState('');
  const [isAlarmSet, setIsAlarmSet] = useState(false);
  const [isAlarmRinging, setIsAlarmRinging] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [timeUntilAlarm, setTimeUntilAlarm] = useState(null);
  const [selectedSound, setSelectedSound] = useState('classic');
  const [alarmMessage, setAlarmMessage] = useState('Wake up! Your alarm is ringing!');
  const [is24Hour, setIs24Hour] = useState(false);
  
  const intervalRef = useRef(null);
  const audioRef = useRef(null);
  const alarmAudioRef = useRef(null);

  const alarmSounds = {
    classic: { name: 'Classic Bell', file: '/sounds/alarm-classic.mp3', emoji: '🔔' },
    gentle: { name: 'Gentle Chimes', file: '/sounds/alarm-gentle.mp3', emoji: '🎐' },
    rooster: { name: 'Rooster Call', file: '/sounds/alarm-rooster.mp3', emoji: '🐓' },
    digital: { name: 'Digital Beep', file: '/sounds/alarm-digital.mp3', emoji: '⏰' },
    nature: { name: 'Nature Sounds', file: '/sounds/alarm-nature.mp3', emoji: '🌿' },
    piano: { name: 'Piano Melody', file: '/sounds/alarm-piano.mp3', emoji: '🎹' }
  };

  useEffect(() => {
    intervalRef.current = setInterval(() => {
      const now = new Date();
      setCurrentTime(now);

      if (isAlarmSet && alarmTime) {
        const [hours, minutes] = alarmTime.split(':').map(Number);
        const alarmDate = new Date();
        alarmDate.setHours(hours, minutes, 0, 0);
        
        // If alarm time has passed today, set it for tomorrow
        if (alarmDate <= now) {
          alarmDate.setDate(alarmDate.getDate() + 1);
        }
        
        const timeDiff = alarmDate.getTime() - now.getTime();
        setTimeUntilAlarm(timeDiff);
        
        // Check if alarm should ring (within 1 second of target time)
        if (timeDiff <= 1000 && timeDiff > -1000 && !isAlarmRinging) {
          triggerAlarm();
        }
      }
    }, 1000);

    return () => clearInterval(intervalRef.current);
  }, [isAlarmSet, alarmTime, isAlarmRinging]);

  const formatCurrentTime = (date) => {
    return date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit', 
      second: '2-digit',
      hour12: !is24Hour 
    });
  };

  const formatTimeUntil = (milliseconds) => {
    if (!milliseconds || milliseconds <= 0) return '';
    
    const totalSeconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${seconds}s`;
  };

  const getCurrentTimeForInput = () => {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  };

  const triggerAlarm = () => {
    setIsAlarmRinging(true);
    setIsAlarmSet(false);
    
    // Play alarm sound
    if (alarmAudioRef.current) {
      alarmAudioRef.current.loop = true;
      alarmAudioRef.current.play().catch(e => console.log('Audio play failed:', e));
    }
    
    // Browser notification
    if (Notification.permission === 'granted') {
      new Notification('🚨 Alarm Ringing!', {
        body: alarmMessage,
        icon: '/ai-pomo.png',
        requireInteraction: true
      });
    }

    // Analytics
    if (window.gtag) {
      window.gtag('event', 'alarm_triggered', {
        'alarm_sound': selectedSound,
        'time_until_alarm': timeUntilAlarm ? Math.floor(timeUntilAlarm / 1000) : 0
      });
    }
  };

  const setAlarm = () => {
    if (!alarmTime) return;
    
    setIsAlarmSet(true);
    setIsAlarmRinging(false);
    
    // Request notification permission
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  };

  const cancelAlarm = () => {
    setIsAlarmSet(false);
    setIsAlarmRinging(false);
    setTimeUntilAlarm(null);
    
    if (alarmAudioRef.current) {
      alarmAudioRef.current.pause();
      alarmAudioRef.current.currentTime = 0;
      alarmAudioRef.current.loop = false;
    }
  };

  const stopAlarm = () => {
    setIsAlarmRinging(false);
    
    if (alarmAudioRef.current) {
      alarmAudioRef.current.pause();
      alarmAudioRef.current.currentTime = 0;
      alarmAudioRef.current.loop = false;
    }
  };

  const testSound = () => {
    if (alarmAudioRef.current) {
      alarmAudioRef.current.loop = false;
      alarmAudioRef.current.play().catch(e => console.log('Audio play failed:', e));
      setTimeout(() => {
        if (alarmAudioRef.current) {
          alarmAudioRef.current.pause();
          alarmAudioRef.current.currentTime = 0;
        }
      }, 2000);
    }
  };

  const quickSetAlarm = (minutesFromNow) => {
    const now = new Date();
    now.setMinutes(now.getMinutes() + minutesFromNow);
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    setAlarmTime(`${hours}:${minutes}`);
    setIsAlarmSet(true);
    setIsAlarmRinging(false);
    
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  };

  const isNightTime = () => {
    const hour = currentTime.getHours();
    return hour >= 18 || hour < 6;
  };

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Online Alarm Clock - Digital Alarm Timer",
    "description": "Free online alarm clock with multiple alarm sounds, custom messages, and precise timing. Perfect wake-up tool with browser notifications and various alarm tones.",
    "applicationCategory": "ProductivityApplication",
    "operatingSystem": "Any",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "Multiple alarm sounds",
      "Custom alarm messages",
      "Browser notifications",
      "12/24 hour format",
      "Quick alarm presets",
      "Test alarm sounds"
    ]
  };

  return (
    <>
      <SEOHead
        title="Online Alarm Clock - Digital Alarm Timer | AI Pomo"
        description="Free online alarm clock with multiple sounds and custom messages. Set alarms, wake up on time, and stay organized with our reliable digital alarm timer."
        keywords="online alarm clock, digital alarm, alarm timer, wake up alarm, online alarm, alarm clock online, free alarm clock, browser alarm"
        url="https://www.ai-pomo.com/online-alarm"
        type="website"
        structuredData={structuredData}
      />
      <PageHeader />
      <Container $isNight={isNightTime()}>
        <Header>
          <Title>
            {isNightTime() ? '🌙' : '☀️'} Online Alarm Clock
          </Title>
          <Subtitle>
            Reliable online alarm clock with multiple sounds and custom messages. 
            Never oversleep again with our precision digital alarm timer.
          </Subtitle>
        </Header>

        <MainContent>
          <AlarmSection>
            <CurrentTimeDisplay $isNight={isNightTime()}>
              <TimeText>{formatCurrentTime(currentTime)}</TimeText>
              <DateText>{currentTime.toLocaleDateString([], { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}</DateText>
              <FormatToggle>
                <FormatButton 
                  $active={!is24Hour} 
                  onClick={() => setIs24Hour(false)}
                >
                  12h
                </FormatButton>
                <FormatButton 
                  $active={is24Hour} 
                  onClick={() => setIs24Hour(true)}
                >
                  24h
                </FormatButton>
              </FormatToggle>
            </CurrentTimeDisplay>

            {isAlarmRinging && (
              <AlarmRingingDisplay>
                <AlarmIcon>🚨</AlarmIcon>
                <AlarmRingingTitle>ALARM RINGING!</AlarmRingingTitle>
                <AlarmMessage>{alarmMessage}</AlarmMessage>
                <StopAlarmButton onClick={stopAlarm}>
                  <FaStop /> Stop Alarm
                </StopAlarmButton>
              </AlarmRingingDisplay>
            )}

            {isAlarmSet && !isAlarmRinging && (
              <AlarmSetDisplay>
                <AlarmSetIcon>⏰</AlarmSetIcon>
                <AlarmSetTitle>Alarm Set for {alarmTime}</AlarmSetTitle>
                <CountdownText>
                  Time remaining: {formatTimeUntil(timeUntilAlarm)}
                </CountdownText>
                <CancelAlarmButton onClick={cancelAlarm}>
                  <FaStop /> Cancel Alarm
                </CancelAlarmButton>
              </AlarmSetDisplay>
            )}

            {!isAlarmSet && !isAlarmRinging && (
              <AlarmSetupSection>
                <AlarmInputGroup>
                  <AlarmInputLabel>Set Alarm Time</AlarmInputLabel>
                  <AlarmTimeInput
                    type="time"
                    value={alarmTime}
                    onChange={(e) => setAlarmTime(e.target.value)}
                  />
                  <SetAlarmButton onClick={setAlarm} disabled={!alarmTime}>
                    <FaBell /> Set Alarm
                  </SetAlarmButton>
                </AlarmInputGroup>

                <QuickAlarmSection>
                  <QuickAlarmTitle>Quick Alarms</QuickAlarmTitle>
                  <QuickAlarmGrid>
                    {[5, 10, 15, 30, 60].map(minutes => (
                      <QuickAlarmButton 
                        key={minutes}
                        onClick={() => quickSetAlarm(minutes)}
                      >
                        +{minutes}m
                      </QuickAlarmButton>
                    ))}
                    <QuickAlarmButton onClick={() => {
                      setAlarmTime(getCurrentTimeForInput());
                    }}>
                      Now
                    </QuickAlarmButton>
                  </QuickAlarmGrid>
                </QuickAlarmSection>

                <AlarmMessageSection>
                  <MessageLabel>Custom Alarm Message</MessageLabel>
                  <MessageInput
                    type="text"
                    value={alarmMessage}
                    onChange={(e) => setAlarmMessage(e.target.value)}
                    placeholder="Enter your wake-up message..."
                    maxLength={100}
                  />
                </AlarmMessageSection>

                <SoundSection>
                  <SoundSectionTitle>Choose Alarm Sound</SoundSectionTitle>
                  <SoundGrid>
                    {Object.entries(alarmSounds).map(([key, sound]) => (
                      <SoundButton
                        key={key}
                        $active={selectedSound === key}
                        onClick={() => setSelectedSound(key)}
                      >
                        <SoundEmoji>{sound.emoji}</SoundEmoji>
                        <SoundName>{sound.name}</SoundName>
                      </SoundButton>
                    ))}
                  </SoundGrid>
                  <TestSoundButton onClick={testSound}>
                    <FaVolumeUp /> Test Sound
                  </TestSoundButton>
                </SoundSection>
              </AlarmSetupSection>
            )}
          </AlarmSection>

          <FeaturesSection>
            <SectionTitle>Why Choose Our Online Alarm?</SectionTitle>
            <FeaturesGrid>
              <Feature>
                <FeatureIcon>🔔</FeatureIcon>
                <FeatureTitle>Multiple Alarm Sounds</FeatureTitle>
                <FeatureText>Choose from 6 different alarm tones including gentle chimes, classic bells, and nature sounds</FeatureText>
              </Feature>
              <Feature>
                <FeatureIcon>💬</FeatureIcon>
                <FeatureTitle>Custom Messages</FeatureTitle>
                <FeatureText>Set personalized wake-up messages to motivate and inspire your morning routine</FeatureText>
              </Feature>
              <Feature>
                <FeatureIcon>🔔</FeatureIcon>
                <FeatureTitle>Browser Notifications</FeatureTitle>
                <FeatureText>Get notifications even when the tab is not active, ensuring you never miss an alarm</FeatureText>
              </Feature>
              <Feature>
                <FeatureIcon>⚡</FeatureIcon>
                <FeatureTitle>Quick Presets</FeatureTitle>
                <FeatureText>Set alarms quickly with preset buttons for 5, 10, 15, 30, or 60 minutes from now</FeatureText>
              </Feature>
            </FeaturesGrid>
          </FeaturesSection>

          <UseCasesSection>
            <SectionTitle>Perfect For Every Wake-Up Need</SectionTitle>
            <UseCasesGrid>
              <UseCase>
                <UseCaseIcon>🌅</UseCaseIcon>
                <UseCaseTitle>Morning Wake-Up</UseCaseTitle>
                <UseCaseDescription>
                  Start your day right with a reliable alarm that works on any device, anywhere
                </UseCaseDescription>
              </UseCase>
              <UseCase>
                <UseCaseIcon>💤</UseCaseIcon>
                <UseCaseTitle>Power Naps</UseCaseTitle>
                <UseCaseDescription>
                  Set quick alarms for short naps to recharge without oversleeping
                </UseCaseDescription>
              </UseCase>
              <UseCase>
                <UseCaseIcon>💊</UseCaseIcon>
                <UseCaseTitle>Medication Reminders</UseCaseTitle>
                <UseCaseDescription>
                  Never forget important medications with custom message reminders
                </UseCaseDescription>
              </UseCase>
              <UseCase>
                <UseCaseIcon>📞</UseCaseIcon>
                <UseCaseTitle>Meeting Alerts</UseCaseTitle>
                <UseCaseDescription>
                  Get alerted before important calls, meetings, or appointments
                </UseCaseDescription>
              </UseCase>
              <UseCase>
                <UseCaseIcon>🍳</UseCaseIcon>
                <UseCaseTitle>Cooking Timers</UseCaseTitle>
                <UseCaseDescription>
                  Time your cooking and baking with audible alarms to prevent overcooking
                </UseCaseDescription>
              </UseCase>
              <UseCase>
                <UseCaseIcon>✈️</UseCaseIcon>
                <UseCaseTitle>Travel Alarms</UseCaseTitle>
                <UseCaseDescription>
                  Reliable wake-up calls when traveling, works in any timezone
                </UseCaseDescription>
              </UseCase>
            </UseCasesGrid>
          </UseCasesSection>

          <TipsSection>
            <SectionTitle>📝 Alarm Clock Tips</SectionTitle>
            <TipsList>
              <TipItem>
                <TipIcon>🔊</TipIcon>
                <TipContent>
                  <TipTitle>Test Your Sound First</TipTitle>
                  <TipDescription>
                    Always test your chosen alarm sound to ensure it's loud enough and you like the tone.
                  </TipDescription>
                </TipContent>
              </TipItem>
              <TipItem>
                <TipIcon>🔔</TipIcon>
                <TipContent>
                  <TipTitle>Enable Browser Notifications</TipTitle>
                  <TipDescription>
                    Allow notifications so your alarm works even if you switch to another tab or minimize the browser.
                  </TipDescription>
                </TipContent>
              </TipItem>
              <TipItem>
                <TipIcon>💡</TipIcon>
                <TipContent>
                  <TipTitle>Use Custom Messages</TipTitle>
                  <TipDescription>
                    Set motivational or specific reminder messages to make your alarm more effective and personal.
                  </TipDescription>
                </TipContent>
              </TipItem>
              <TipItem>
                <TipIcon>🔋</TipIcon>
                <TipContent>
                  <TipTitle>Keep Your Device Charged</TipTitle>
                  <TipDescription>
                    Make sure your device has enough battery, especially for overnight alarms or long-duration timers.
                  </TipDescription>
                </TipContent>
              </TipItem>
            </TipsList>
          </TipsSection>

          <RelatedSection>
            <SectionTitle>More Timing Tools</SectionTitle>
            <RelatedGrid>
              <RelatedTimer as={Link} to="/online-stopwatch">
                <RelatedTitle>Online Stopwatch</RelatedTitle>
                <RelatedDescription>Precise stopwatch with lap timing and millisecond accuracy</RelatedDescription>
                <RelatedAction>
                  Try Stopwatch <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
              <RelatedTimer as={Link} to="/countdown-timer">
                <RelatedTitle>Countdown Timer</RelatedTitle>
                <RelatedDescription>Set specific durations and count down to zero</RelatedDescription>
                <RelatedAction>
                  Try Countdown Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
              <RelatedTimer as={Link} to="/online-timer">
                <RelatedTitle>Online Timer</RelatedTitle>
                <RelatedDescription>Versatile timer with multiple timing modes</RelatedDescription>
                <RelatedAction>
                  Try Online Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
            </RelatedGrid>
          </RelatedSection>
        </MainContent>

        <audio ref={alarmAudioRef} preload="auto">
          <source src={alarmSounds[selectedSound]?.file} type="audio/mpeg" />
        </audio>
      </Container>
      <Footer />
    </>
  );
};

// Styled Components
const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 6rem 2rem 4rem;
  background: ${props => props.$isNight ? 
    'linear-gradient(135deg, #1f2937 0%, #111827 100%)' : 
    'linear-gradient(135deg, #fef3c7 0%, #fcd34d 50%, #f59e0b 100%)'
  };
  color: ${props => props.$isNight ? '#f9fafb' : '#111827'};
  min-height: 100vh;

  @media (max-width: 768px) {
    padding: 5rem 1rem 2rem;
  }
`;

const Header = styled.header`
  text-align: center;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    font-size: 2.25rem;
  }
`;

const Subtitle = styled.p`
  font-size: 1.125rem;
  opacity: 0.8;
  line-height: 1.6;
  max-width: 700px;
  margin: 0 auto;
`;

const MainContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4rem;
`;

const AlarmSection = styled.section`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
`;

const CurrentTimeDisplay = styled.div`
  text-align: center;
  padding: 2rem;
  background: ${props => props.$isNight ? 
    'rgba(255, 255, 255, 0.1)' : 
    'rgba(255, 255, 255, 0.3)'
  };
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid ${props => props.$isNight ? 
    'rgba(255, 255, 255, 0.2)' : 
    'rgba(255, 255, 255, 0.5)'
  };
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
`;

const TimeText = styled.div`
  font-size: 4rem;
  font-weight: 800;
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);

  @media (max-width: 768px) {
    font-size: 3rem;
  }

  @media (max-width: 480px) {
    font-size: 2.5rem;
  }
`;

const DateText = styled.div`
  font-size: 1.125rem;
  opacity: 0.8;
  margin-bottom: 1rem;
`;

const FormatToggle = styled.div`
  display: flex;
  gap: 0.5rem;
  justify-content: center;
`;

const FormatButton = styled.button`
  padding: 0.5rem 1rem;
  border: 2px solid ${props => props.$active ? '#3b82f6' : 'rgba(255, 255, 255, 0.3)'};
  border-radius: 8px;
  background: ${props => props.$active ? '#3b82f6' : 'transparent'};
  color: ${props => props.$active ? 'white' : 'inherit'};
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;

  &:hover {
    border-color: #3b82f6;
  }
`;

const AlarmRingingDisplay = styled.div`
  text-align: center;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border-radius: 20px;
  color: white;
  animation: shake 0.5s infinite;

  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
  }
`;

const AlarmIcon = styled.div`
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: pulse 1s infinite;

  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
  }
`;

const AlarmRingingTitle = styled.h2`
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
`;

const AlarmMessage = styled.p`
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
`;

const StopAlarmButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  background: white;
  color: #ef4444;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0 auto;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }
`;

const AlarmSetDisplay = styled.div`
  text-align: center;
  padding: 2rem;
  background: rgba(16, 185, 129, 0.1);
  border: 2px solid #10b981;
  border-radius: 20px;
  color: #10b981;
`;

const AlarmSetIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const AlarmSetTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
`;

const CountdownText = styled.p`
  font-size: 1.125rem;
  margin-bottom: 1.5rem;
  font-family: monospace;
`;

const CancelAlarmButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: 2px solid #ef4444;
  border-radius: 8px;
  background: transparent;
  color: #ef4444;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0 auto;

  &:hover {
    background: #ef4444;
    color: white;
  }
`;

const AlarmSetupSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2rem;
  width: 100%;
  max-width: 600px;
`;

const AlarmInputGroup = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
`;

const AlarmInputLabel = styled.label`
  font-size: 1.125rem;
  font-weight: 600;
`;

const AlarmTimeInput = styled.input`
  padding: 1rem 1.5rem;
  font-size: 1.5rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.9);
  color: #111827;
  text-align: center;
  font-family: monospace;
  font-weight: 600;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  }
`;

const SetAlarmButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
  justify-content: center;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const QuickAlarmSection = styled.div`
  text-align: center;
`;

const QuickAlarmTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
`;

const QuickAlarmGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  max-width: 300px;
  margin: 0 auto;

  @media (max-width: 480px) {
    grid-template-columns: repeat(2, 1fr);
  }
`;

const QuickAlarmButton = styled.button`
  padding: 0.75rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: inherit;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.2);
    transform: translateY(-2px);
  }
`;

const AlarmMessageSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const MessageLabel = styled.label`
  font-weight: 600;
  font-size: 1rem;
`;

const MessageInput = styled.input`
  padding: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.9);
  color: #111827;
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  }

  &::placeholder {
    color: #6b7280;
  }
`;

const SoundSection = styled.div`
  text-align: center;
`;

const SoundSectionTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
`;

const SoundGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;

  @media (max-width: 480px) {
    grid-template-columns: repeat(2, 1fr);
  }
`;

const SoundButton = styled.button`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem 0.5rem;
  border: 2px solid ${props => props.$active ? '#3b82f6' : 'rgba(255, 255, 255, 0.3)'};
  border-radius: 12px;
  background: ${props => props.$active ? 'rgba(59, 130, 246, 0.2)' : 'rgba(255, 255, 255, 0.1)'};
  color: inherit;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.2);
    transform: translateY(-2px);
  }
`;

const SoundEmoji = styled.div`
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
`;

const SoundName = styled.div`
  font-size: 0.75rem;
  font-weight: 600;
`;

const TestSoundButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: inherit;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0 auto;

  &:hover {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.2);
    transform: translateY(-2px);
  }
`;

const SectionTitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2rem;
  text-align: center;
`;

const FeaturesSection = styled.section``;

const FeaturesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
`;

const Feature = styled.div`
  text-align: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.2);
  }
`;

const FeatureIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const FeatureTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
`;

const FeatureText = styled.p`
  opacity: 0.8;
  line-height: 1.6;
  margin: 0;
`;

const UseCasesSection = styled.section``;

const UseCasesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
`;

const UseCase = styled.div`
  text-align: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.2);
  }
`;

const UseCaseIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const UseCaseTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
`;

const UseCaseDescription = styled.p`
  opacity: 0.8;
  line-height: 1.5;
  margin: 0;
`;

const TipsSection = styled.section``;

const TipsList = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const TipItem = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 1.5rem;
`;

const TipIcon = styled.div`
  font-size: 2rem;
  flex-shrink: 0;
`;

const TipContent = styled.div`
  flex: 1;
`;

const TipTitle = styled.h4`
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
`;

const TipDescription = styled.p`
  opacity: 0.8;
  line-height: 1.6;
  margin: 0;
`;

const RelatedSection = styled.section``;

const RelatedGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
`;

const RelatedTimer = styled.a`
  display: block;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  text-decoration: none;
  color: inherit;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;

  &:hover {
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  }
`;

const RelatedTitle = styled.h4`
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
`;

const RelatedDescription = styled.p`
  opacity: 0.8;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
`;

const RelatedAction = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #3b82f6;
  font-weight: 600;
  font-size: 0.875rem;

  svg {
    font-size: 0.75rem;
  }
`;

export default OnlineAlarmPage;