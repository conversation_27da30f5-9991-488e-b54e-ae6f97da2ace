import React from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { FaBrain, FaClock, FaHeart, FaChart<PERSON>ine, <PERSON>a<PERSON>heck, FaArrowRight } from 'react-icons/fa';
import SEOHead from '../components/SEO/SEOHead';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import { createWebsiteStructuredData, createSoftwareApplicationStructuredData } from '../utils/structuredData';

const PomodoroForADHDPage = () => {
  const structuredData = [
    createWebsiteStructuredData(),
    createSoftwareApplicationStructuredData()
  ];

  return (
    <>
      <SEOHead
        title="Pomodoro Technique for ADHD | Best Focus Timer for Neurodivergent Minds"
        description="Discover how the Pomodoro Technique helps ADHD. Our ADHD-friendly timer provides structure, manages attention spans, and creates sustainable work patterns for neurodivergent professionals."
        keywords="Pomodoro for ADHD, ADHD timer, ADHD productivity, neurodivergent productivity, ADHD focus techniques, attention deficit timer, ADHD work strategies, focus timer ADHD"
        url="https://www.ai-pomo.com/pomodoro-for-adhd"
        structuredData={structuredData}
      />

      <Container>
        <PageHeader />

        <HeroSection>
          <HeroContainer>
            <HeroContent>
              <HeroTitle>Pomodoro Technique for ADHD: Focus That Actually Works</HeroTitle>
              <HeroSubtitle>
                Finally, a productivity method designed for neurodivergent minds. Our ADHD-friendly Pomodoro timer
                provides the structure and flexibility you need to work with your brain, not against it. With AI-powered
                project structuring and customizable focus sessions, manage ADHD symptoms while boosting productivity.
              </HeroSubtitle>
              <HeroButtons>
                <PrimaryButton to="/register">Start ADHD-Friendly Timer - Free</PrimaryButton>
                <SecondaryButton to="/features">Explore ADHD Features</SecondaryButton>
              </HeroButtons>
            </HeroContent>
            <HeroImage>
              <FaBrain />
            </HeroImage>
          </HeroContainer>
        </HeroSection>

        <WhyItWorksSection>
          <SectionTitle>Why Pomodoro Technique is Perfect for ADHD</SectionTitle>
          <BenefitsGrid>
            <BenefitCard>
              <BenefitIcon><FaClock /></BenefitIcon>
              <BenefitTitle>Matches ADHD Attention Spans</BenefitTitle>
              <BenefitText>
                25-minute intervals align perfectly with natural ADHD attention patterns, making sustained
                focus achievable without overwhelming your brain.
              </BenefitText>
            </BenefitCard>
            <BenefitCard>
              <BenefitIcon><FaBrain /></BenefitIcon>
              <BenefitTitle>Provides External Structure</BenefitTitle>
              <BenefitText>
                ADHD brains struggle with internal time awareness. Our timer provides the external structure
                and accountability you need to stay on track.
              </BenefitText>
            </BenefitCard>
            <BenefitCard>
              <BenefitIcon><FaHeart /></BenefitIcon>
              <BenefitTitle>Prevents Overwhelm & Burnout</BenefitTitle>
              <BenefitText>
                Regular breaks prevent the mental fatigue that often leads to ADHD shutdown. Built-in rest
                periods keep you energized and motivated.
              </BenefitText>
            </BenefitCard>
            <BenefitCard>
              <BenefitIcon><FaChartLine /></BenefitIcon>
              <BenefitTitle>Creates Positive Momentum</BenefitTitle>
              <BenefitText>
                Completing Pomodoros provides regular dopamine hits through achievement, which ADHD brains
                crave for motivation and focus.
              </BenefitText>
            </BenefitCard>
          </BenefitsGrid>
        </WhyItWorksSection>

        <ADHDChallengesSection>
          <SectionTitle>Common ADHD Challenges & Pomodoro Solutions</SectionTitle>
          <ChallengesGrid>
            <ChallengeCard>
              <ChallengeTitle>Time Blindness</ChallengeTitle>
              <ChallengeProblem>Difficulty estimating and tracking time</ChallengeProblem>
              <ChallengeSolution>
                <strong>Pomodoro Solution:</strong> Visual timers and regular alerts provide external time
                awareness, helping you develop better time estimation skills.
              </ChallengeSolution>
            </ChallengeCard>
            <ChallengeCard>
              <ChallengeTitle>Hyperfocus vs. Distractibility</ChallengeTitle>
              <ChallengeProblem>Either can't focus at all or can't stop focusing</ChallengeProblem>
              <ChallengeSolution>
                <strong>Pomodoro Solution:</strong> Flexible timing allows shorter sessions for difficult tasks
                and longer ones during hyperfocus, with gentle break reminders.
              </ChallengeSolution>
            </ChallengeCard>
            <ChallengeCard>
              <ChallengeTitle>Task Overwhelm</ChallengeTitle>
              <ChallengeProblem>Large projects feel impossible to start</ChallengeProblem>
              <ChallengeSolution>
                <strong>Pomodoro Solution:</strong> Breaking work into 25-minute chunks makes even the most
                daunting tasks feel manageable and achievable.
              </ChallengeSolution>
            </ChallengeCard>
            <ChallengeCard>
              <ChallengeTitle>Procrastination</ChallengeTitle>
              <ChallengeProblem>Difficulty starting tasks, especially boring ones</ChallengeProblem>
              <ChallengeSolution>
                <strong>Pomodoro Solution:</strong> "Just 25 minutes" feels less intimidating than "finish this
                project," making it easier to overcome initial resistance.
              </ChallengeSolution>
            </ChallengeCard>
          </ChallengesGrid>
        </ADHDChallengesSection>

        <ADHDStrategiesSection>
          <SectionTitle>ADHD-Specific Pomodoro Strategies</SectionTitle>
          <StrategiesContainer>
            <StrategyCard>
              <StrategyTitle>🎯 The Hyperfocus Protocol</StrategyTitle>
              <StrategyDescription>
                When experiencing hyperfocus, use longer timers (45-90 minutes) with gentle reminders for breaks.
                Keep water and snacks nearby to maintain energy without breaking flow.
              </StrategyDescription>
            </StrategyCard>
            <StrategyCard>
              <StrategyTitle>🧩 The Overwhelm Breaker</StrategyTitle>
              <StrategyDescription>
                For overwhelming tasks, start with 10-15 minute Pomodoros. Focus on just starting, not finishing.
                Celebrate small wins and gradually increase session length.
              </StrategyDescription>
            </StrategyCard>
            <StrategyCard>
              <StrategyTitle>🔄 The Transition Helper</StrategyTitle>
              <StrategyDescription>
                Use 5-minute "transition Pomodoros" between different types of tasks. Create transition rituals
                like clearing your desk or reviewing notes to help your brain switch gears.
              </StrategyDescription>
            </StrategyCard>
            <StrategyCard>
              <StrategyTitle>⚡ The Dopamine Booster</StrategyTitle>
              <StrategyDescription>
                Enhance motivation with immediate rewards after each Pomodoro, visual progress tracking,
                and variety in tasks and environments to keep your brain engaged.
              </StrategyDescription>
            </StrategyCard>
          </StrategiesContainer>
        </ADHDStrategiesSection>

        <FeaturesSection>
          <SectionTitle>ADHD-Friendly Features</SectionTitle>
          <FeaturesList>
            <FeatureItem>
              <FeatureIcon><FaCheck /></FeatureIcon>
              <FeatureText>Flexible timer lengths that adapt to your attention span and energy levels</FeatureText>
            </FeatureItem>
            <FeatureItem>
              <FeatureIcon><FaCheck /></FeatureIcon>
              <FeatureText>Visual progress tracking for motivation and dopamine hits</FeatureText>
            </FeatureItem>
            <FeatureItem>
              <FeatureIcon><FaCheck /></FeatureIcon>
              <FeatureText>Gentle, customizable alerts that don't startle or overwhelm</FeatureText>
            </FeatureItem>
            <FeatureItem>
              <FeatureIcon><FaCheck /></FeatureIcon>
              <FeatureText>Task breakdown assistance to prevent overwhelm and paralysis</FeatureText>
            </FeatureItem>
            <FeatureItem>
              <FeatureIcon><FaCheck /></FeatureIcon>
              <FeatureText>Distraction blocking features to maintain focus during sessions</FeatureText>
            </FeatureItem>
            <FeatureItem>
              <FeatureIcon><FaCheck /></FeatureIcon>
              <FeatureText>ADHD-specific break suggestions for optimal mental restoration</FeatureText>
            </FeatureItem>
          </FeaturesList>
        </FeaturesSection>

        <TestimonialSection>
          <SectionTitle>Success Stories from the ADHD Community</SectionTitle>
          <TestimonialsGrid>
            <TestimonialCard>
              <TestimonialText>
                "Having ADHD made traditional productivity methods impossible for me. Pomodoro technique
                is a game-changer - the 25-minute sessions match my attention span perfectly."
              </TestimonialText>
              <TestimonialAuthor>- Alex M., Graphic Designer with ADHD</TestimonialAuthor>
            </TestimonialCard>
            <TestimonialCard>
              <TestimonialText>
                "The visual progress tracking keeps me motivated when my ADHD brain wants to give up.
                Seeing completed Pomodoros gives me the dopamine hit I need to keep going."
              </TestimonialText>
              <TestimonialAuthor>- Jordan K., Software Developer</TestimonialAuthor>
            </TestimonialCard>
            <TestimonialCard>
              <TestimonialText>
                "Finally, a productivity system that works WITH my ADHD instead of against it. The
                flexible timing and break reminders prevent my usual burnout cycles."
              </TestimonialText>
              <TestimonialAuthor>- Sam R., Marketing Professional</TestimonialAuthor>
            </TestimonialCard>
          </TestimonialsGrid>
        </TestimonialSection>

        <ResearchSection>
          <SectionTitle>Research-Backed Benefits for ADHD</SectionTitle>
          <ResearchContent>
            <ResearchText>
              Studies show that structured time management techniques like the Pomodoro Technique can
              significantly help individuals with ADHD by providing external structure, improving task
              completion rates, and reducing procrastination behaviors.
            </ResearchText>
            <ResearchStats>
              <StatItem>
                <StatNumber>40-60%</StatNumber>
                <StatLabel>Improvement in task completion rates</StatLabel>
              </StatItem>
              <StatItem>
                <StatNumber>Reduced</StatNumber>
                <StatLabel>Procrastination and avoidance behaviors</StatLabel>
              </StatItem>
              <StatItem>
                <StatNumber>Better</StatNumber>
                <StatLabel>Time estimation and awareness</StatLabel>
              </StatItem>
            </ResearchStats>
          </ResearchContent>
        </ResearchSection>

        <CTASection>
          <CTAContent>
            <CTATitle>Start Your ADHD-Friendly Productivity Journey</CTATitle>
            <CTADescription>
              Join thousands of neurodivergent professionals who've found focus and success with our
              ADHD-friendly Pomodoro timer. Work with your brain, not against it.
            </CTADescription>
            <CTAButtons>
              <PrimaryButton to="/register">
                Try ADHD Timer Free
                <FaArrowRight />
              </PrimaryButton>
              <SecondaryButton to="/features">
                Explore ADHD Features
              </SecondaryButton>
            </CTAButtons>
          </CTAContent>
        </CTASection>

        <Footer />
      </Container>
    </>
  );
};

// Styled Components (reusing many from PomodoroForStudyingPage)
const Container = styled.div`
  min-height: 100vh;
  background-color: #ffffff;
`;

const HeroSection = styled.section`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8rem 2rem 6rem;
  min-height: 70vh;
  display: flex;
  align-items: center;
  justify-content: center;

  @media (max-width: 968px) {
    padding: 6rem 2rem 4rem;
  }
`;

const HeroContainer = styled.div`
  display: flex;
  align-items: center;
  max-width: 1200px;
  width: 100%;

  @media (max-width: 968px) {
    flex-direction: column;
    text-align: center;
  }
`;

const HeroContent = styled.div`
  flex: 1;
  max-width: 600px;
  margin-right: 3rem;

  @media (max-width: 968px) {
    margin-right: 0;
    margin-bottom: 3rem;
  }
`;

const HeroTitle = styled.h1`
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const HeroSubtitle = styled.p`
  font-size: 1.25rem;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  opacity: 0.9;
`;

const HeroButtons = styled.div`
  display: flex;
  gap: 1rem;

  @media (max-width: 480px) {
    flex-direction: column;
  }
`;

const PrimaryButton = styled(Link)`
  background-color: #d95550;
  color: white;
  padding: 1rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &:hover {
    background-color: #c04540;
  }
`;

const SecondaryButton = styled(Link)`
  background-color: transparent;
  color: white;
  padding: 1rem 2rem;
  border: 2px solid white;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.2s;

  &:hover {
    background-color: white;
    color: #2d3748;
  }
`;

const HeroImage = styled.div`
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 15rem;
  opacity: 0.3;

  @media (max-width: 968px) {
    font-size: 8rem;
  }
`;

const WhyItWorksSection = styled.section`
  padding: 6rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const SectionTitle = styled.h2`
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  color: #2d3748;
`;

const BenefitsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
`;

const BenefitCard = styled.div`
  background: white;
  padding: 2.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
  border: 1px solid #e2e8f0;
`;

const BenefitIcon = styled.div`
  font-size: 3rem;
  color: #d95550;
  margin-bottom: 1.5rem;
`;

const BenefitTitle = styled.h3`
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2d3748;
`;

const BenefitText = styled.p`
  font-size: 1rem;
  line-height: 1.6;
  color: #4a5568;
`;

const ADHDChallengesSection = styled.section`
  background: #f7fafc;
  padding: 6rem 2rem;
`;

const ChallengesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const ChallengeCard = styled.div`
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #d95550;
`;

const ChallengeTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #2d3748;
`;

const ChallengeProblem = styled.p`
  font-size: 0.95rem;
  color: #e53e3e;
  margin-bottom: 1rem;
  font-style: italic;
`;

const ChallengeSolution = styled.p`
  font-size: 0.95rem;
  line-height: 1.6;
  color: #4a5568;

  strong {
    color: #d95550;
    font-weight: 600;
  }
`;

const ADHDStrategiesSection = styled.section`
  padding: 6rem 2rem;
  max-width: 1000px;
  margin: 0 auto;
`;

const StrategiesContainer = styled.div`
  display: grid;
  gap: 2rem;
`;

const StrategyCard = styled.div`
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
`;

const StrategyTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2d3748;
`;

const StrategyDescription = styled.p`
  font-size: 1rem;
  line-height: 1.6;
  color: #4a5568;
`;

const FeaturesSection = styled.section`
  background: #f7fafc;
  padding: 6rem 2rem;
`;

const FeaturesList = styled.div`
  max-width: 800px;
  margin: 0 auto;
  display: grid;
  gap: 1.5rem;
`;

const FeatureItem = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
`;

const FeatureIcon = styled.div`
  color: #d95550;
  font-size: 1.25rem;
  flex-shrink: 0;
`;

const FeatureText = styled.span`
  font-size: 1rem;
  color: #2d3748;
  line-height: 1.5;
`;

const TestimonialSection = styled.section`
  padding: 6rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const TestimonialsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
`;

const TestimonialCard = styled.div`
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
`;

const TestimonialText = styled.p`
  font-size: 1rem;
  line-height: 1.6;
  color: #4a5568;
  margin-bottom: 1rem;
  font-style: italic;
`;

const TestimonialAuthor = styled.p`
  font-size: 0.9rem;
  color: #d95550;
  font-weight: 600;
`;

const ResearchSection = styled.section`
  background: #f7fafc;
  padding: 6rem 2rem;
`;

const ResearchContent = styled.div`
  max-width: 1000px;
  margin: 0 auto;
  text-align: center;
`;

const ResearchText = styled.p`
  font-size: 1.1rem;
  line-height: 1.6;
  color: #4a5568;
  margin-bottom: 3rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
`;

const ResearchStats = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
`;

const StatItem = styled.div`
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
`;

const StatNumber = styled.div`
  font-size: 2.5rem;
  font-weight: 700;
  color: #d95550;
  margin-bottom: 0.5rem;
`;

const StatLabel = styled.p`
  font-size: 1rem;
  color: #4a5568;
  font-weight: 500;
`;

const CTASection = styled.section`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 6rem 2rem;
  text-align: center;
`;

const CTAContent = styled.div`
  max-width: 600px;
  margin: 0 auto;
`;

const CTATitle = styled.h2`
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;

  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const CTADescription = styled.p`
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  opacity: 0.9;
`;

const CTAButtons = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;

  @media (max-width: 480px) {
    flex-direction: column;
  }
`;

export default PomodoroForADHDPage;
