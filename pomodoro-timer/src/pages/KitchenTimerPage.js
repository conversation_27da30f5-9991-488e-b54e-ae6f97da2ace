import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { FaPlay, FaPause, FaStop, FaUtensils, FaClock, FaArrowRight, FaVolumeUp, FaPlus } from 'react-icons/fa';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import SEOHead from '../components/SEO/SEOHead';

const KitchenTimerPage = () => {
  const [timers, setTimers] = useState([
    { id: 1, name: 'Main Timer', timeLeft: 0, originalTime: 0, isRunning: false, isCompleted: false }
  ]);
  const [selectedTimer, setSelectedTimer] = useState(1);
  const [newTimerTime, setNewTimerTime] = useState({ minutes: 10, seconds: 0 });
  const [selectedRecipe, setSelectedRecipe] = useState('custom');
  const [currentStep, setCurrentStep] = useState(0);
  
  const intervalRef = useRef(null);
  const audioRef = useRef(null);

  const cookingPresets = {
    pasta: {
      name: 'Pasta Cooking',
      emoji: '🍝',
      steps: [
        { name: 'Boil Water', time: 8 * 60 },
        { name: 'Cook Pasta', time: 10 * 60 },
        { name: 'Rest & Drain', time: 2 * 60 }
      ]
    },
    rice: {
      name: 'Perfect Rice',
      emoji: '🍚',
      steps: [
        { name: 'Rinse Rice', time: 3 * 60 },
        { name: 'Cook Rice', time: 18 * 60 },
        { name: 'Steam Rest', time: 10 * 60 }
      ]
    },
    steak: {
      name: 'Steak Cooking',
      emoji: '🥩',
      steps: [
        { name: 'Room Temperature', time: 30 * 60 },
        { name: 'Sear Side 1', time: 3 * 60 },
        { name: 'Sear Side 2', time: 3 * 60 },
        { name: 'Rest Steak', time: 5 * 60 }
      ]
    },
    bread: {
      name: 'Bread Baking',
      emoji: '🍞',
      steps: [
        { name: 'First Rise', time: 90 * 60 },
        { name: 'Shape & Rest', time: 15 * 60 },
        { name: 'Second Rise', time: 60 * 60 },
        { name: 'Bake Bread', time: 45 * 60 }
      ]
    },
    eggs: {
      name: 'Boiled Eggs',
      emoji: '🥚',
      steps: [
        { name: 'Soft Boiled', time: 6 * 60 },
        { name: 'Medium Boiled', time: 8 * 60 },
        { name: 'Hard Boiled', time: 12 * 60 }
      ]
    },
    tea: {
      name: 'Tea Brewing',
      emoji: '🍵',
      steps: [
        { name: 'Green Tea', time: 3 * 60 },
        { name: 'Black Tea', time: 5 * 60 },
        { name: 'Herbal Tea', time: 7 * 60 }
      ]
    }
  };

  const quickTimers = [
    { name: '1 min', time: 60, emoji: '⚡' },
    { name: '3 min', time: 180, emoji: '☕' },
    { name: '5 min', time: 300, emoji: '🥚' },
    { name: '10 min', time: 600, emoji: '🍝' },
    { name: '15 min', time: 900, emoji: '🍞' },
    { name: '30 min', time: 1800, emoji: '🍗' }
  ];

  useEffect(() => {
    intervalRef.current = setInterval(() => {
      setTimers(prevTimers => 
        prevTimers.map(timer => {
          if (timer.isRunning && timer.timeLeft > 0) {
            const newTimeLeft = timer.timeLeft - 1;
            if (newTimeLeft === 0) {
              completeTimer(timer.id, timer.name);
              return { ...timer, timeLeft: 0, isRunning: false, isCompleted: true };
            }
            return { ...timer, timeLeft: newTimeLeft };
          }
          return timer;
        })
      );
    }, 1000);

    return () => clearInterval(intervalRef.current);
  }, []);

  const completeTimer = (timerId, timerName) => {
    if (audioRef.current) {
      audioRef.current.play().catch(e => console.log('Audio play failed:', e));
    }
    
    if (Notification.permission === 'granted') {
      new Notification(`🍳 ${timerName} Complete!`, {
        body: 'Your cooking timer has finished. Check your food!',
        icon: '/ai-pomo.png'
      });
    }

    if (window.gtag) {
      window.gtag('event', 'kitchen_timer_complete', {
        'timer_name': timerName,
        'timer_duration': getTimerById(timerId)?.originalTime || 0
      });
    }
  };

  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getTimerById = (id) => timers.find(timer => timer.id === id);

  const startTimer = (timerId) => {
    setTimers(prevTimers =>
      prevTimers.map(timer =>
        timer.id === timerId ? { ...timer, isRunning: true, isCompleted: false } : timer
      )
    );
    
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  };

  const pauseTimer = (timerId) => {
    setTimers(prevTimers =>
      prevTimers.map(timer =>
        timer.id === timerId ? { ...timer, isRunning: false } : timer
      )
    );
  };

  const resetTimer = (timerId) => {
    setTimers(prevTimers =>
      prevTimers.map(timer =>
        timer.id === timerId ? { 
          ...timer, 
          timeLeft: timer.originalTime, 
          isRunning: false, 
          isCompleted: false 
        } : timer
      )
    );
  };

  const setNewTimer = (time, name = 'Custom Timer') => {
    const newTimer = {
      id: Date.now(),
      name,
      timeLeft: time,
      originalTime: time,
      isRunning: false,
      isCompleted: false
    };
    setTimers(prev => [...prev, newTimer]);
    setSelectedTimer(newTimer.id);
  };

  const addCustomTimer = () => {
    const totalSeconds = (newTimerTime.minutes * 60) + newTimerTime.seconds;
    if (totalSeconds > 0) {
      setNewTimer(totalSeconds);
    }
  };

  const removeTimer = (timerId) => {
    if (timers.length > 1) {
      setTimers(prev => prev.filter(timer => timer.id !== timerId));
      if (selectedTimer === timerId) {
        setSelectedTimer(timers[0].id);
      }
    }
  };

  const loadRecipePreset = (recipeKey) => {
    const recipe = cookingPresets[recipeKey];
    if (recipe) {
      // Clear existing timers and add recipe steps
      const newTimers = recipe.steps.map((step, index) => ({
        id: Date.now() + index,
        name: step.name,
        timeLeft: step.time,
        originalTime: step.time,
        isRunning: false,
        isCompleted: false
      }));
      setTimers(newTimers);
      setSelectedTimer(newTimers[0].id);
      setSelectedRecipe(recipeKey);
      setCurrentStep(0);
    }
  };

  const selectedTimerData = getTimerById(selectedTimer);
  const progress = selectedTimerData ? 
    ((selectedTimerData.originalTime - selectedTimerData.timeLeft) / selectedTimerData.originalTime) * 100 : 0;

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Kitchen Timer - Cooking Timer for Recipes",
    "description": "Professional kitchen timer with multiple timers, cooking presets, and recipe guidance. Perfect for baking, cooking, and meal preparation timing.",
    "applicationCategory": "ProductivityApplication",
    "operatingSystem": "Any",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "Multiple simultaneous timers",
      "Cooking recipe presets",
      "Step-by-step cooking guidance",
      "Quick timer buttons",
      "Audio notifications",
      "Visual progress tracking"
    ]
  };

  return (
    <>
      <SEOHead
        title="Kitchen Timer - Cooking Timer for Recipes & Baking | AI Pomo"
        description="Professional kitchen timer with multiple timers, cooking presets, and recipe guidance. Perfect for pasta, bread, steak, eggs, and all your cooking needs."
        keywords="kitchen timer, cooking timer, recipe timer, baking timer, pasta timer, tea timer, multi timer, cooking alarm"
        url="https://www.ai-pomo.com/kitchen-timer"
        type="website"
        structuredData={structuredData}
      />
      <PageHeader />
      <Container>
        <Header>
          <Title>🍳 Kitchen Timer</Title>
          <Subtitle>
            Professional kitchen timer with multiple timers and cooking presets. 
            Perfect for cooking, baking, and timing all your culinary creations.
          </Subtitle>
        </Header>

        <MainContent>
          <TimerSection>
            <RecipePresets>
              <PresetTitle>🧑‍🍳 Recipe Presets</PresetTitle>
              <PresetGrid>
                {Object.entries(cookingPresets).map(([key, recipe]) => (
                  <PresetButton
                    key={key}
                    $active={selectedRecipe === key}
                    onClick={() => loadRecipePreset(key)}
                  >
                    <PresetEmoji>{recipe.emoji}</PresetEmoji>
                    <PresetName>{recipe.name}</PresetName>
                    <PresetSteps>{recipe.steps.length} steps</PresetSteps>
                  </PresetButton>
                ))}
              </PresetGrid>
            </RecipePresets>

            <TimerTabs>
              {timers.map(timer => (
                <TimerTab
                  key={timer.id}
                  $active={selectedTimer === timer.id}
                  $isRunning={timer.isRunning}
                  $isCompleted={timer.isCompleted}
                  onClick={() => setSelectedTimer(timer.id)}
                >
                  <TabName>{timer.name}</TabName>
                  <TabTime>{formatTime(timer.timeLeft)}</TabTime>
                  {timers.length > 1 && (
                    <RemoveButton
                      onClick={(e) => {
                        e.stopPropagation();
                        removeTimer(timer.id);
                      }}
                    >
                      ×
                    </RemoveButton>
                  )}
                </TimerTab>
              ))}
            </TimerTabs>

            {selectedTimerData && (
              <MainTimerDisplay>
                <TimerDisplay 
                  $isRunning={selectedTimerData.isRunning}
                  $isCompleted={selectedTimerData.isCompleted}
                >
                  <TimerName>{selectedTimerData.name}</TimerName>
                  <TimeText>{formatTime(selectedTimerData.timeLeft)}</TimeText>
                  <StatusText>
                    {selectedTimerData.isCompleted ? '✅ COMPLETED' :
                     selectedTimerData.isRunning ? '⏱️ RUNNING' : '⏸️ PAUSED'}
                  </StatusText>
                  <ProgressBar>
                    <ProgressFill $progress={progress} />
                  </ProgressBar>
                </TimerDisplay>

                <ControlButtons>
                  {!selectedTimerData.isRunning ? (
                    <StartButton 
                      onClick={() => startTimer(selectedTimer)}
                      disabled={selectedTimerData.timeLeft === 0}
                    >
                      <FaPlay /> Start
                    </StartButton>
                  ) : (
                    <PauseButton onClick={() => pauseTimer(selectedTimer)}>
                      <FaPause /> Pause
                    </PauseButton>
                  )}
                  <ResetButton onClick={() => resetTimer(selectedTimer)}>
                    <FaStop /> Reset
                  </ResetButton>
                </ControlButtons>
              </MainTimerDisplay>
            )}

            <QuickTimersSection>
              <QuickTitle>⚡ Quick Timers</QuickTitle>
              <QuickTimerGrid>
                {quickTimers.map(quick => (
                  <QuickButton
                    key={quick.time}
                    onClick={() => setNewTimer(quick.time, quick.name)}
                  >
                    <QuickEmoji>{quick.emoji}</QuickEmoji>
                    <QuickName>{quick.name}</QuickName>
                  </QuickButton>
                ))}
              </QuickTimerGrid>
            </QuickTimersSection>

            <CustomTimerSection>
              <CustomTitle>➕ Add Custom Timer</CustomTitle>
              <CustomInputs>
                <InputGroup>
                  <InputLabel>Minutes</InputLabel>
                  <TimeInput
                    type="number"
                    min="0"
                    max="999"
                    value={newTimerTime.minutes}
                    onChange={(e) => setNewTimerTime(prev => ({
                      ...prev,
                      minutes: Math.max(0, parseInt(e.target.value) || 0)
                    }))}
                  />
                </InputGroup>
                <InputGroup>
                  <InputLabel>Seconds</InputLabel>
                  <TimeInput
                    type="number"
                    min="0"
                    max="59"
                    value={newTimerTime.seconds}
                    onChange={(e) => setNewTimerTime(prev => ({
                      ...prev,
                      seconds: Math.max(0, Math.min(59, parseInt(e.target.value) || 0))
                    }))}
                  />
                </InputGroup>
                <AddTimerButton onClick={addCustomTimer}>
                  <FaPlus /> Add Timer
                </AddTimerButton>
              </CustomInputs>
            </CustomTimerSection>
          </TimerSection>

          <CookingTipsSection>
            <SectionTitle>👨‍🍳 Cooking Tips & Timing</SectionTitle>
            <TipsGrid>
              <TipCard>
                <TipIcon>🍝</TipIcon>
                <TipTitle>Pasta Perfection</TipTitle>
                <TipText>Cook pasta for package time minus 1 minute, then finish in sauce for perfect al dente</TipText>
              </TipCard>
              <TipCard>
                <TipIcon>🥚</TipIcon>
                <TipTitle>Egg Timing</TipTitle>
                <TipText>6 min soft, 8 min medium, 12 min hard. Start timer when water returns to boil</TipText>
              </TipCard>
              <TipCard>
                <TipIcon>🥩</TipIcon>
                <TipTitle>Meat Rest</TipTitle>
                <TipText>Always rest meat 5-10 minutes after cooking to redistribute juices</TipText>
              </TipCard>
              <TipCard>
                <TipIcon>🍞</TipIcon>
                <TipTitle>Bread Rising</TipTitle>
                <TipText>First rise: double in size (1-2 hours). Second rise: 50% bigger (30-60 min)</TipText>
              </TipCard>
              <TipCard>
                <TipIcon>🍵</TipIcon>
                <TipTitle>Tea Brewing</TipTitle>
                <TipText>Green tea: 3-4 min at 175°F. Black tea: 4-5 min at 212°F</TipText>
              </TipCard>
              <TipCard>
                <TipIcon>🔥</TipIcon>
                <TipTitle>High Heat Cooking</TipTitle>
                <TipText>Preheat pan until water droplets dance, then add oil and ingredients</TipText>
              </TipCard>
            </TipsGrid>
          </CookingTipsSection>

          <FeaturesSection>
            <SectionTitle>Why Use Kitchen Timer?</SectionTitle>
            <FeaturesGrid>
              <Feature>
                <FeatureIcon>⏰</FeatureIcon>
                <FeatureTitle>Multiple Timers</FeatureTitle>
                <FeatureText>Run multiple cooking timers simultaneously for complex recipes</FeatureText>
              </Feature>
              <Feature>
                <FeatureIcon>📖</FeatureIcon>
                <FeatureTitle>Recipe Presets</FeatureTitle>
                <FeatureText>Built-in timing for common cooking tasks and recipes</FeatureText>
              </Feature>
              <Feature>
                <FeatureIcon>🔔</FeatureIcon>
                <FeatureTitle>Audio Alerts</FeatureTitle>
                <FeatureText>Clear notifications so you never overcook your food</FeatureText>
              </Feature>
              <Feature>
                <FeatureIcon>📱</FeatureIcon>
                <FeatureTitle>Easy to Use</FeatureTitle>
                <FeatureText>Simple interface perfect for busy kitchen environments</FeatureText>
              </Feature>
            </FeaturesGrid>
          </FeaturesSection>

          <UseCasesSection>
            <SectionTitle>Perfect For Every Kitchen Task</SectionTitle>
            <UseCasesGrid>
              <UseCase>
                <UseCaseIcon>🍝</UseCaseIcon>
                <UseCaseTitle>Cooking Pasta</UseCaseTitle>
                <UseCaseDescription>
                  Time pasta cooking, sauce preparation, and simultaneous cooking steps
                </UseCaseDescription>
              </UseCase>
              <UseCase>
                <UseCaseIcon>🍞</UseCaseIcon>
                <UseCaseTitle>Baking Bread</UseCaseTitle>
                <UseCaseDescription>
                  Track rising times, proofing stages, and baking duration accurately
                </UseCaseDescription>
              </UseCase>
              <UseCase>
                <UseCaseIcon>🥩</UseCaseIcon>
                <UseCaseTitle>Grilling & Roasting</UseCaseTitle>
                <UseCaseDescription>
                  Perfect timing for steaks, roasts, and grilled vegetables
                </UseCaseDescription>
              </UseCase>
              <UseCase>
                <UseCaseIcon>☕</UseCaseIcon>
                <UseCaseTitle>Brewing Coffee & Tea</UseCaseTitle>
                <UseCaseDescription>
                  Precise timing for optimal extraction and perfect beverages
                </UseCaseDescription>
              </UseCase>
              <UseCase>
                <UseCaseIcon>🍰</UseCaseIcon>
                <UseCaseTitle>Baking Desserts</UseCaseTitle>
                <UseCaseDescription>
                  Time mixing, resting, baking, and cooling for perfect results
                </UseCaseDescription>
              </UseCase>
              <UseCase>
                <UseCaseIcon>🥘</UseCaseIcon>
                <UseCaseTitle>Multi-Course Meals</UseCaseTitle>
                <UseCaseDescription>
                  Coordinate timing for complex meals with multiple dishes
                </UseCaseDescription>
              </UseCase>
            </UseCasesGrid>
          </UseCasesSection>

          <RelatedSection>
            <SectionTitle>More Timing Tools</SectionTitle>
            <RelatedGrid>
              <RelatedTimer as={Link} to="/online-alarm">
                <RelatedTitle>Online Alarm</RelatedTitle>
                <RelatedDescription>Set alarms for meal prep and cooking schedules</RelatedDescription>
                <RelatedAction>
                  Try Alarm Clock <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
              <RelatedTimer as={Link} to="/countdown-timer">
                <RelatedTitle>Countdown Timer</RelatedTitle>
                <RelatedDescription>Simple countdown for single cooking tasks</RelatedDescription>
                <RelatedAction>
                  Try Countdown Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
              <RelatedTimer as={Link} to="/online-stopwatch">
                <RelatedTitle>Stopwatch</RelatedTitle>
                <RelatedDescription>Time cooking processes and techniques</RelatedDescription>
                <RelatedAction>
                  Try Stopwatch <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
            </RelatedGrid>
          </RelatedSection>
        </MainContent>

        <audio ref={audioRef} preload="auto">
          <source src="/sounds/kitchen-bell.mp3" type="audio/mpeg" />
        </audio>
      </Container>
      <Footer />
    </>
  );
};

// Styled Components
const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 6rem 2rem 4rem;

  @media (max-width: 768px) {
    padding: 5rem 1rem 2rem;
  }
`;

const Header = styled.header`
  text-align: center;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: 3rem;
  font-weight: 800;
  color: #111827;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    font-size: 2.25rem;
  }
`;

const Subtitle = styled.p`
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  max-width: 700px;
  margin: 0 auto;
`;

const MainContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4rem;
`;

const TimerSection = styled.section`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
`;

const RecipePresets = styled.div`
  text-align: center;
  width: 100%;
  max-width: 800px;
`;

const PresetTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1.5rem;
`;

const PresetGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
`;

const PresetButton = styled.button`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem 1rem;
  border: 3px solid ${props => props.$active ? '#f59e0b' : '#e5e7eb'};
  border-radius: 16px;
  background: ${props => props.$active ? '#fef3c7' : 'white'};
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #f59e0b;
    background: #fef3c7;
    transform: translateY(-2px);
  }
`;

const PresetEmoji = styled.div`
  font-size: 2rem;
  margin-bottom: 0.5rem;
`;

const PresetName = styled.div`
  font-size: 0.875rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.25rem;
`;

const PresetSteps = styled.div`
  font-size: 0.75rem;
  color: #6b7280;
`;

const TimerTabs = styled.div`
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  max-width: 800px;
`;

const TimerTab = styled.button`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem 1.5rem;
  border: 2px solid ${props => 
    props.$isCompleted ? '#10b981' :
    props.$isRunning ? '#ef4444' :
    props.$active ? '#3b82f6' : '#e5e7eb'
  };
  border-radius: 12px;
  background: ${props => 
    props.$isCompleted ? '#ecfdf5' :
    props.$isRunning ? '#fef2f2' :
    props.$active ? '#eff6ff' : 'white'
  };
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;

const TabName = styled.div`
  font-size: 0.875rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.25rem;
`;

const TabTime = styled.div`
  font-size: 1rem;
  font-weight: 700;
  font-family: monospace;
  color: #374151;
`;

const RemoveButton = styled.div`
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 700;
  cursor: pointer;
  
  &:hover {
    background: #dc2626;
  }
`;

const MainTimerDisplay = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
`;

const TimerDisplay = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 350px;
  height: 350px;
  border-radius: 50%;
  background: ${props => 
    props.$isCompleted ? 'linear-gradient(135deg, #10b981, #059669)' :
    props.$isRunning ? 'linear-gradient(135deg, #f59e0b, #d97706)' :
    'linear-gradient(135deg, #6b7280, #4b5563)'
  };
  box-shadow: 0 20px 40px ${props => 
    props.$isCompleted ? 'rgba(16, 185, 129, 0.3)' :
    props.$isRunning ? 'rgba(245, 158, 11, 0.3)' :
    'rgba(107, 114, 128, 0.3)'
  };
  color: white;
  position: relative;
  animation: ${props => props.$isRunning ? 'pulse 2s infinite' : 'none'};

  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
  }

  @media (max-width: 480px) {
    width: 300px;
    height: 300px;
  }
`;

const TimerName = styled.div`
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
`;

const TimeText = styled.div`
  font-size: 3.5rem;
  font-weight: 800;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
  margin-bottom: 0.5rem;

  @media (max-width: 480px) {
    font-size: 3rem;
  }
`;

const StatusText = styled.div`
  font-size: 1rem;
  font-weight: 600;
  opacity: 0.9;
  margin-bottom: 1rem;
`;

const ProgressBar = styled.div`
  position: absolute;
  bottom: 2rem;
  left: 2rem;
  right: 2rem;
  height: 8px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  overflow: hidden;
`;

const ProgressFill = styled.div`
  height: 100%;
  background: white;
  border-radius: 4px;
  width: ${props => props.$progress}%;
  transition: width 1s ease;
`;

const ControlButtons = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  justify-content: center;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const StartButton = styled(Button)`
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
  }
`;

const PauseButton = styled(Button)`
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
  }
`;

const ResetButton = styled(Button)`
  background: white;
  color: #6b7280;
  border: 2px solid #e5e7eb;

  &:hover:not(:disabled) {
    border-color: #ef4444;
    color: #ef4444;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
  }
`;

const QuickTimersSection = styled.div`
  text-align: center;
  width: 100%;
  max-width: 600px;
`;

const QuickTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
`;

const QuickTimerGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;

  @media (max-width: 480px) {
    grid-template-columns: repeat(2, 1fr);
  }
`;

const QuickButton = styled.button`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #f59e0b;
    background: #fef3c7;
    transform: translateY(-2px);
  }
`;

const QuickEmoji = styled.div`
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
`;

const QuickName = styled.div`
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
`;

const CustomTimerSection = styled.div`
  text-align: center;
  width: 100%;
  max-width: 500px;
`;

const CustomTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
`;

const CustomInputs = styled.div`
  display: flex;
  gap: 1rem;
  align-items: end;
  justify-content: center;
  flex-wrap: wrap;
`;

const InputGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const InputLabel = styled.label`
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
`;

const TimeInput = styled.input`
  width: 80px;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  text-align: center;
  font-weight: 600;
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: #f59e0b;
  }
`;

const AddTimerButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }
`;

const CookingTipsSection = styled.section``;

const TipsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
`;

const TipCard = styled.div`
  text-align: center;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
  }
`;

const TipIcon = styled.div`
  font-size: 2.5rem;
  margin-bottom: 1rem;
`;

const TipTitle = styled.h4`
  font-size: 1rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.75rem;
`;

const TipText = styled.p`
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
`;

const SectionTitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2rem;
  text-align: center;
`;

const FeaturesSection = styled.section``;

const FeaturesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
`;

const Feature = styled.div`
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
  }
`;

const FeatureIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const FeatureTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
`;

const FeatureText = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
`;

const UseCasesSection = styled.section``;

const UseCasesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
`;

const UseCase = styled.div`
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
  }
`;

const UseCaseIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const UseCaseTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.75rem;
`;

const UseCaseDescription = styled.p`
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
`;

const RelatedSection = styled.section``;

const RelatedGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
`;

const RelatedTimer = styled.a`
  display: block;
  padding: 2rem;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  text-decoration: none;
  transition: all 0.3s ease;

  &:hover {
    border-color: #f59e0b;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
`;

const RelatedTitle = styled.h4`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const RelatedDescription = styled.p`
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
`;

const RelatedAction = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #f59e0b;
  font-weight: 600;
  font-size: 0.875rem;

  svg {
    font-size: 0.75rem;
  }
`;

export default KitchenTimerPage;