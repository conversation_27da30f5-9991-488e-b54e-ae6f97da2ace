import React from 'react';
import { FaL<PERSON>f, FaHeart, FaMedkit } from 'react-icons/fa';
import ScenarioTimerPage from '../components/Timer/ScenarioTimerPage';

const BreakTimerPage = () => {
  const breakTimerConfig = {
    scenario: 'Break',
    title: 'Break Timer - Rejuvenating Rest Timer | AI Pomo',
    description: 'Essential break timer for optimal productivity recovery. Perfect for micro-breaks, lunch breaks, eye rest, and mental recovery. Maintain your energy and focus throughout the day.',
    keywords: 'break timer, rest timer, micro break, lunch break, eye rest timer, mental recovery, productivity break, wellness timer',
    url: 'https://www.ai-pomo.com/break-timer',
    heroTitle: 'Break Timer',
    heroDescription: 'Take purposeful breaks to recharge and maintain peak performance. Scientifically-timed rest periods to prevent burnout and boost productivity.',
    defaultMinutes: 15,
    gradientColors: ['#06b6d4', '#0891b2'],
    backgroundColor: 'linear-gradient(135deg, #f0fdfa, #ccfbf1)',
    
    scenarios: [
      {
        emoji: '☕',
        title: 'Coffee Break',
        description: 'Short refreshing break with your favorite beverage',
        recommendedTime: 'Recommended 10-15 minutes'
      },
      {
        emoji: '👀',
        title: 'Eye Rest',
        description: 'Reduce eye strain from screen time with purposeful breaks',
        recommendedTime: 'Recommended 5-10 minutes'
      },
      {
        emoji: '🚶',
        title: 'Walking Break',
        description: 'Light physical activity to boost circulation and energy',
        recommendedTime: 'Recommended 10-20 minutes'
      },
      {
        emoji: '🧘',
        title: 'Mindfulness Break',
        description: 'Meditation, breathing exercises, and mental reset',
        recommendedTime: 'Recommended 5-15 minutes'
      }
    ],

    benefits: [
      {
        emoji: '🔋',
        title: 'Energy Restoration',
        description: 'Regular breaks help maintain consistent energy levels throughout the day'
      },
      {
        emoji: '🧠',
        title: 'Mental Clarity',
        description: 'Short breaks improve focus and cognitive function when returning to tasks'
      },
      {
        emoji: '💪',
        title: 'Prevent Burnout',
        description: 'Structured rest periods prevent mental fatigue and maintain long-term productivity'
      },
      {
        emoji: '😌',
        title: 'Stress Reduction',
        description: 'Intentional breaks reduce stress hormones and promote overall well-being'
      }
    ],

    tips: [
      {
        title: 'Step Away from Screens',
        description: 'Use break time to rest your eyes and reduce digital fatigue by looking at distant objects'
      },
      {
        title: 'Move Your Body',
        description: 'Include light stretching, walking, or simple exercises to counteract prolonged sitting'
      },
      {
        title: 'Hydrate and Nourish',
        description: 'Use breaks to drink water, have healthy snacks, and maintain physical well-being'
      },
      {
        title: 'Practice Mindfulness',
        description: 'Engage in brief meditation, deep breathing, or simply observe your surroundings mindfully'
      },
      {
        title: 'Avoid Work Talk',
        description: 'Keep breaks truly restful by avoiding work-related conversations and thoughts'
      }
    ],

    customFeatures: [
      {
        icon: <FaLeaf />,
        title: 'Wellness Focus',
        description: 'Promotes physical and mental health through structured rest periods'
      },
      {
        icon: <FaHeart />,
        title: 'Stress Management',
        description: 'Reduces cortisol levels and promotes relaxation through timed breaks'
      },
      {
        icon: <FaMedkit />,
        title: 'Health Reminders',
        description: 'Encourages healthy habits like hydration, posture checks, and eye care'
      }
    ],

    relatedTimers: [
      {
        title: 'Work Timer',
        description: 'Focused work sessions',
        duration: '25-90 minutes',
        url: '/work-timer'
      },
      {
        title: 'Study Timer',
        description: 'Concentrated learning periods',
        duration: '25-60 minutes',
        url: '/study-timer'
      },
      {
        title: 'Exercise Timer',
        description: 'Workout and fitness routines',
        duration: '15-45 minutes',
        url: '/exercise-timer'
      }
    ]
  };

  return <ScenarioTimerPage config={breakTimerConfig} />;
};

export default BreakTimerPage;