import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { FaPlay, FaPause, FaStop, FaFlag, FaStopwatch, FaClock, FaArrowRight, FaDownload, FaShare } from 'react-icons/fa';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import SEOHead from '../components/SEO/SEOHead';

const OnlineStopwatchPage = () => {
  const [elapsedTime, setElapsedTime] = useState(0); // in centiseconds (1/100 second)
  const [isRunning, setIsRunning] = useState(false);
  const [laps, setLaps] = useState([]);
  const [startTime, setStartTime] = useState(null);
  const [pausedTime, setPausedTime] = useState(0);
  
  const intervalRef = useRef(null);
  const audioRef = useRef(null);

  useEffect(() => {
    if (isRunning) {
      intervalRef.current = setInterval(() => {
        const now = Date.now();
        setElapsedTime(Math.floor((now - startTime - pausedTime) / 10));
      }, 10); // Update every 10ms for smooth animation
    } else {
      clearInterval(intervalRef.current);
    }

    return () => clearInterval(intervalRef.current);
  }, [isRunning, startTime, pausedTime]);

  const formatTime = (centiseconds) => {
    const totalSeconds = Math.floor(centiseconds / 100);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    const cs = centiseconds % 100;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${cs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${cs.toString().padStart(2, '0')}`;
  };

  const formatLapTime = (centiseconds) => {
    const totalSeconds = Math.floor(centiseconds / 100);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    const cs = centiseconds % 100;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${cs.toString().padStart(2, '0')}`;
  };

  const startStopwatch = () => {
    if (!isRunning) {
      const now = Date.now();
      if (elapsedTime === 0) {
        setStartTime(now);
        setPausedTime(0);
      } else {
        // Resuming from pause
        setPausedTime(prev => prev + (now - startTime));
        setStartTime(now);
      }
      setIsRunning(true);
    }
  };

  const pauseStopwatch = () => {
    if (isRunning) {
      setIsRunning(false);
    }
  };

  const resetStopwatch = () => {
    setIsRunning(false);
    setElapsedTime(0);
    setLaps([]);
    setStartTime(null);
    setPausedTime(0);
  };

  const addLap = () => {
    if (isRunning || elapsedTime > 0) {
      const lapTime = elapsedTime;
      const prevLapTime = laps.length > 0 ? laps[laps.length - 1].time : 0;
      const splitTime = lapTime - prevLapTime;
      
      setLaps(prev => [...prev, {
        id: prev.length + 1,
        time: lapTime,
        split: splitTime,
        timestamp: new Date().toLocaleTimeString()
      }]);

      if (window.gtag) {
        window.gtag('event', 'stopwatch_lap', {
          'lap_count': laps.length + 1,
          'elapsed_time': Math.floor(elapsedTime / 100)
        });
      }
    }
  };

  const exportLaps = () => {
    if (laps.length === 0) return;
    
    const csvContent = "data:text/csv;charset=utf-8," 
      + "Lap,Split Time,Total Time,Timestamp\n"
      + laps.map(lap => 
          `${lap.id},${formatLapTime(lap.split)},${formatLapTime(lap.time)},${lap.timestamp}`
        ).join("\n");
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `stopwatch_laps_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const shareTime = async () => {
    const shareText = `I just timed ${formatTime(elapsedTime)} with AI Pomo's Online Stopwatch!`;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Online Stopwatch - AI Pomo',
          text: shareText,
          url: window.location.href
        });
      } catch (err) {
        copyToClipboard(shareText);
      }
    } else {
      copyToClipboard(shareText);
    }
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      alert('Time copied to clipboard!');
    });
  };

  // Find fastest and slowest laps
  const fastestLap = laps.length > 0 ? laps.reduce((fastest, lap) => lap.split < fastest.split ? lap : fastest) : null;
  const slowestLap = laps.length > 0 ? laps.reduce((slowest, lap) => lap.split > slowest.split ? lap : slowest) : null;

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Online Stopwatch - Digital Stopwatch Timer",
    "description": "Precise online stopwatch with lap timing, split times, and millisecond accuracy. Perfect for sports, fitness, cooking, and productivity timing. Free digital stopwatch tool.",
    "applicationCategory": "ProductivityApplication",
    "operatingSystem": "Any",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "Millisecond precision timing",
      "Lap and split time tracking", 
      "Export lap times to CSV",
      "Share timing results",
      "Large display for easy reading",
      "Keyboard shortcuts support"
    ]
  };

  return (
    <>
      <SEOHead
        title="Online Stopwatch - Precise Digital Stopwatch Timer | AI Pomo"
        description="Free online stopwatch with millisecond precision. Perfect for sports timing, fitness workouts, cooking, and productivity. Features lap timing, split times, and export functionality."
        keywords="online stopwatch, digital stopwatch, stopwatch timer, lap timer, split time, sports timer, fitness stopwatch, precise timer, millisecond timer"
        url="https://www.ai-pomo.com/online-stopwatch"
        type="website"
        structuredData={structuredData}
      />
      <PageHeader />
      <Container>
        <Header>
          <Title>⏱️ Online Stopwatch</Title>
          <Subtitle>
            Precise digital stopwatch with millisecond accuracy. Perfect for sports, fitness, 
            cooking, and productivity timing with lap and split time tracking.
          </Subtitle>
        </Header>

        <MainContent>
          <StopwatchSection>
            <StopwatchDisplay $isRunning={isRunning}>
              <TimeDisplay>
                <MainTime>{formatTime(elapsedTime)}</MainTime>
                <StatusIndicator $isRunning={isRunning}>
                  {isRunning ? '🟢 RUNNING' : elapsedTime > 0 ? '⏸️ PAUSED' : '⏹️ STOPPED'}
                </StatusIndicator>
              </TimeDisplay>
            </StopwatchDisplay>

            <ControlButtons>
              {!isRunning ? (
                <StartButton onClick={startStopwatch}>
                  <FaPlay /> {elapsedTime > 0 ? 'Resume' : 'Start'}
                </StartButton>
              ) : (
                <PauseButton onClick={pauseStopwatch}>
                  <FaPause /> Pause
                </PauseButton>
              )}
              
              <LapButton 
                onClick={addLap} 
                disabled={elapsedTime === 0}
                $isRunning={isRunning}
              >
                <FaFlag /> Lap
              </LapButton>
              
              <ResetButton onClick={resetStopwatch}>
                <FaStop /> Reset
              </ResetButton>
            </ControlButtons>

            <ActionButtons>
              <ActionButton onClick={shareTime} disabled={elapsedTime === 0}>
                <FaShare /> Share Time
              </ActionButton>
              <ActionButton onClick={exportLaps} disabled={laps.length === 0}>
                <FaDownload /> Export Laps
              </ActionButton>
            </ActionButtons>

            <KeyboardShortcuts>
              <ShortcutTitle>⌨️ Keyboard Shortcuts</ShortcutTitle>
              <ShortcutGrid>
                <Shortcut><ShortcutKey>Space</ShortcutKey> Start/Pause</Shortcut>
                <Shortcut><ShortcutKey>L</ShortcutKey> Add Lap</Shortcut>
                <Shortcut><ShortcutKey>R</ShortcutKey> Reset</Shortcut>
                <Shortcut><ShortcutKey>S</ShortcutKey> Share</Shortcut>
              </ShortcutGrid>
            </KeyboardShortcuts>
          </StopwatchSection>

          {laps.length > 0 && (
            <LapsSection>
              <LapsHeader>
                <LapsTitle>📊 Lap Times ({laps.length} laps)</LapsTitle>
                <LapsStats>
                  {fastestLap && (
                    <StatItem>
                      🏆 Fastest: Lap {fastestLap.id} - {formatLapTime(fastestLap.split)}
                    </StatItem>
                  )}
                  {slowestLap && laps.length > 1 && (
                    <StatItem>
                      🐌 Slowest: Lap {slowestLap.id} - {formatLapTime(slowestLap.split)}
                    </StatItem>
                  )}
                </LapsStats>
              </LapsHeader>
              
              <LapsTable>
                <LapsTableHeader>
                  <HeaderCell>Lap</HeaderCell>
                  <HeaderCell>Split Time</HeaderCell>
                  <HeaderCell>Total Time</HeaderCell>
                  <HeaderCell>Clock Time</HeaderCell>
                </LapsTableHeader>
                <LapsTableBody>
                  {laps.slice().reverse().map(lap => (
                    <LapRow 
                      key={lap.id}
                      $isFastest={fastestLap && lap.id === fastestLap.id}
                      $isSlowest={slowestLap && lap.id === slowestLap.id && laps.length > 1}
                    >
                      <LapCell>#{lap.id}</LapCell>
                      <LapCell $isSplit={true}>
                        {formatLapTime(lap.split)}
                        {fastestLap && lap.id === fastestLap.id && <Badge>🏆</Badge>}
                        {slowestLap && lap.id === slowestLap.id && laps.length > 1 && <Badge>🐌</Badge>}
                      </LapCell>
                      <LapCell>{formatLapTime(lap.time)}</LapCell>
                      <LapCell $isTimestamp={true}>{lap.timestamp}</LapCell>
                    </LapRow>
                  ))}
                </LapsTableBody>
              </LapsTable>
            </LapsSection>
          )}

          <FeaturesSection>
            <SectionTitle>Why Use Our Online Stopwatch?</SectionTitle>
            <FeaturesGrid>
              <Feature>
                <FeatureIcon>⚡</FeatureIcon>
                <FeatureTitle>Millisecond Precision</FeatureTitle>
                <FeatureText>Accurate to 1/100th of a second for precise timing measurements</FeatureText>
              </Feature>
              <Feature>
                <FeatureIcon>🏃</FeatureIcon>
                <FeatureTitle>Lap & Split Timing</FeatureTitle>
                <FeatureText>Track multiple laps with individual split times and statistics</FeatureText>
              </Feature>
              <Feature>
                <FeatureIcon>📱</FeatureIcon>
                <FeatureTitle>Mobile Optimized</FeatureTitle>
                <FeatureText>Works perfectly on all devices - phone, tablet, and desktop</FeatureText>
              </Feature>
              <Feature>
                <FeatureIcon>💾</FeatureIcon>
                <FeatureTitle>Export & Share</FeatureTitle>
                <FeatureText>Export lap times to CSV and share your timing results</FeatureText>
              </Feature>
            </FeaturesGrid>
          </FeaturesSection>

          <UseCasesSection>
            <SectionTitle>Perfect For Every Timing Need</SectionTitle>
            <UseCasesGrid>
              <UseCase>
                <UseCaseIcon>🏃‍♂️</UseCaseIcon>
                <UseCaseTitle>Sports & Fitness</UseCaseTitle>
                <UseCaseDescription>
                  Track running laps, workout intervals, swimming times, and athletic performance
                </UseCaseDescription>
              </UseCase>
              <UseCase>
                <UseCaseIcon>🍳</UseCaseIcon>
                <UseCaseTitle>Cooking & Baking</UseCaseTitle>
                <UseCaseDescription>
                  Time cooking processes, bread rising, marinating, and multi-step recipes
                </UseCaseDescription>
              </UseCase>
              <UseCase>
                <UseCaseIcon>📚</UseCaseIcon>
                <UseCaseTitle>Study & Work</UseCaseTitle>
                <UseCaseDescription>
                  Measure productivity sessions, presentation practice, and task completion times
                </UseCaseDescription>
              </UseCase>
              <UseCase>
                <UseCaseIcon>🎮</UseCaseIcon>
                <UseCaseTitle>Gaming & Competitions</UseCaseTitle>
                <UseCaseDescription>
                  Speedrun timing, game challenges, competition events, and reaction time tests
                </UseCaseDescription>
              </UseCase>
              <UseCase>
                <UseCaseIcon>🔬</UseCaseIcon>
                <UseCaseTitle>Science & Research</UseCaseTitle>
                <UseCaseDescription>
                  Lab experiments, data collection, observation periods, and precise measurements
                </UseCaseDescription>
              </UseCase>
              <UseCase>
                <UseCaseIcon>👶</UseCaseIcon>
                <UseCaseTitle>Parenting & Care</UseCaseTitle>
                <UseCaseDescription>
                  Baby feeding times, sleep tracking, medication schedules, and developmental milestones
                </UseCaseDescription>
              </UseCase>
            </UseCasesGrid>
          </UseCasesSection>

          <TipsSection>
            <SectionTitle>📝 Stopwatch Pro Tips</SectionTitle>
            <TipsList>
              <TipItem>
                <TipIcon>⌨️</TipIcon>
                <TipContent>
                  <TipTitle>Use Keyboard Shortcuts</TipTitle>
                  <TipDescription>
                    Press Space to start/pause, L for lap, R to reset, and S to share. Much faster than clicking!
                  </TipDescription>
                </TipContent>
              </TipItem>
              <TipItem>
                <TipIcon>📊</TipIcon>
                <TipContent>
                  <TipTitle>Analyze Your Lap Times</TipTitle>
                  <TipDescription>
                    Watch for the fastest and slowest lap indicators to identify patterns and improve consistency.
                  </TipDescription>
                </TipContent>
              </TipItem>
              <TipItem>
                <TipIcon>💾</TipIcon>
                <TipContent>
                  <TipTitle>Export for Record Keeping</TipTitle>
                  <TipDescription>
                    Download your lap times as CSV to track progress over time and maintain detailed records.
                  </TipDescription>
                </TipContent>
              </TipItem>
            </TipsList>
          </TipsSection>

          <RelatedSection>
            <SectionTitle>More Timing Tools</SectionTitle>
            <RelatedGrid>
              <RelatedTimer as={Link} to="/countdown-timer">
                <RelatedTitle>Countdown Timer</RelatedTitle>
                <RelatedDescription>Set specific time duration and count down to zero</RelatedDescription>
                <RelatedAction>
                  Try Countdown Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
              <RelatedTimer as={Link} to="/online-timer">
                <RelatedTitle>Online Timer</RelatedTitle>
                <RelatedDescription>Versatile timer with both countdown and stopwatch modes</RelatedDescription>
                <RelatedAction>
                  Try Online Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
              <RelatedTimer as={Link} to="/interval-timer">
                <RelatedTitle>Interval Timer</RelatedTitle>
                <RelatedDescription>Perfect for HIIT workouts and interval training</RelatedDescription>
                <RelatedAction>
                  Try Interval Timer <FaArrowRight />
                </RelatedAction>
              </RelatedTimer>
            </RelatedGrid>
          </RelatedSection>
        </MainContent>

        <audio ref={audioRef} preload="auto">
          <source src="/sounds/completion.mp3" type="audio/mpeg" />
        </audio>
      </Container>
      <Footer />
    </>
  );
};

// Styled Components
const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 6rem 2rem 4rem;

  @media (max-width: 768px) {
    padding: 5rem 1rem 2rem;
  }
`;

const Header = styled.header`
  text-align: center;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: 3rem;
  font-weight: 800;
  color: #111827;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    font-size: 2.25rem;
  }
`;

const Subtitle = styled.p`
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  max-width: 700px;
  margin: 0 auto;
`;

const MainContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4rem;
`;

const StopwatchSection = styled.section`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
`;

const StopwatchDisplay = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 400px;
  height: 400px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.3);
  color: white;
  position: relative;
  animation: ${props => props.$isRunning ? 'pulse 2s infinite' : 'none'};

  @keyframes pulse {
    0%, 100% { transform: scale(1); box-shadow: 0 20px 40px rgba(59, 130, 246, 0.3); }
    50% { transform: scale(1.02); box-shadow: 0 25px 50px rgba(59, 130, 246, 0.4); }
  }

  @media (max-width: 768px) {
    width: 350px;
    height: 350px;
  }

  @media (max-width: 480px) {
    width: 300px;
    height: 300px;
  }
`;

const TimeDisplay = styled.div`
  text-align: center;
`;

const MainTime = styled.div`
  font-size: 3.5rem;
  font-weight: 800;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;

  @media (max-width: 768px) {
    font-size: 2.8rem;
  }

  @media (max-width: 480px) {
    font-size: 2.2rem;
  }
`;

const StatusIndicator = styled.div`
  font-size: 1rem;
  font-weight: 600;
  opacity: 0.9;
  color: ${props => props.$isRunning ? '#10b981' : '#f59e0b'};
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
`;

const ControlButtons = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  justify-content: center;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const StartButton = styled(Button)`
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
  }
`;

const PauseButton = styled(Button)`
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
  }
`;

const LapButton = styled(Button)`
  background: ${props => props.$isRunning ? 
    'linear-gradient(135deg, #8b5cf6, #7c3aed)' : 
    'linear-gradient(135deg, #6b7280, #4b5563)'};
  color: white;
  box-shadow: 0 4px 12px ${props => props.$isRunning ? 
    'rgba(139, 92, 246, 0.3)' : 
    'rgba(107, 114, 128, 0.3)'};

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px ${props => props.$isRunning ? 
      'rgba(139, 92, 246, 0.4)' : 
      'rgba(107, 114, 128, 0.4)'};
  }
`;

const ResetButton = styled(Button)`
  background: white;
  color: #6b7280;
  border: 2px solid #e5e7eb;

  &:hover:not(:disabled) {
    border-color: #ef4444;
    color: #ef4444;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #6b7280;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    border-color: #3b82f6;
    color: #3b82f6;
    transform: translateY(-2px);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const KeyboardShortcuts = styled.div`
  text-align: center;
  padding: 1.5rem;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  max-width: 400px;
`;

const ShortcutTitle = styled.h3`
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
`;

const ShortcutGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
`;

const Shortcut = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
`;

const ShortcutKey = styled.kbd`
  background: #e5e7eb;
  color: #374151;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.75rem;
  font-weight: 600;
`;

const LapsSection = styled.section`
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
`;

const LapsHeader = styled.div`
  margin-bottom: 1.5rem;
`;

const LapsTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
  text-align: center;
`;

const LapsStats = styled.div`
  display: flex;
  gap: 2rem;
  justify-content: center;
  flex-wrap: wrap;
`;

const StatItem = styled.div`
  padding: 0.5rem 1rem;
  background: #f3f4f6;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
`;

const LapsTable = styled.div`
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const LapsTableHeader = styled.div`
  display: grid;
  grid-template-columns: 1fr 2fr 2fr 1.5fr;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
`;

const HeaderCell = styled.div`
  padding: 1rem;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  text-align: center;
`;

const LapsTableBody = styled.div`
  max-height: 400px;
  overflow-y: auto;
`;

const LapRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 2fr 2fr 1.5fr;
  border-bottom: 1px solid #f3f4f6;
  transition: all 0.2s ease;
  background: ${props => 
    props.$isFastest ? '#ecfdf5' : 
    props.$isSlowest ? '#fef2f2' : 
    'white'};

  &:hover {
    background: ${props => 
      props.$isFastest ? '#d1fae5' : 
      props.$isSlowest ? '#fecaca' : 
      '#f9fafb'};
  }

  &:last-child {
    border-bottom: none;
  }
`;

const LapCell = styled.div`
  padding: 1rem;
  text-align: center;
  font-family: ${props => props.$isSplit ? "'SF Mono', 'Monaco', 'Cascadia Code', monospace" : 'inherit'};
  font-weight: ${props => props.$isSplit ? '600' : '400'};
  font-size: ${props => props.$isTimestamp ? '0.75rem' : '0.875rem'};
  color: ${props => props.$isTimestamp ? '#6b7280' : '#111827'};
  position: relative;
`;

const Badge = styled.span`
  margin-left: 0.5rem;
  font-size: 0.75rem;
`;

const SectionTitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2rem;
  text-align: center;
`;

const FeaturesSection = styled.section``;

const FeaturesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
`;

const Feature = styled.div`
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
  }
`;

const FeatureIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const FeatureTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
`;

const FeatureText = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
`;

const UseCasesSection = styled.section``;

const UseCasesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
`;

const UseCase = styled.div`
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
  }
`;

const UseCaseIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const UseCaseTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.75rem;
`;

const UseCaseDescription = styled.p`
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
`;

const TipsSection = styled.section``;

const TipsList = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const TipItem = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  padding: 2rem;
  background: white;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const TipIcon = styled.div`
  font-size: 2rem;
  flex-shrink: 0;
`;

const TipContent = styled.div`
  flex: 1;
`;

const TipTitle = styled.h4`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const TipDescription = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
`;

const RelatedSection = styled.section``;

const RelatedGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
`;

const RelatedTimer = styled.a`
  display: block;
  padding: 2rem;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  text-decoration: none;
  transition: all 0.3s ease;

  &:hover {
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
`;

const RelatedTitle = styled.h4`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const RelatedDescription = styled.p`
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
`;

const RelatedAction = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #3b82f6;
  font-weight: 600;
  font-size: 0.875rem;

  svg {
    font-size: 0.75rem;
  }
`;

export default OnlineStopwatchPage;