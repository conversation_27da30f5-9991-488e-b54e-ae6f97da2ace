import React from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { FaLaptopCode, FaGraduationCap, FaBriefcase, FaUsers, FaHome, FaRocket } from 'react-icons/fa';
import SEOHead from '../components/SEO/SEOHead';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import { createWebsiteStructuredData } from '../utils/structuredData';

const UseCasesPage = () => {
  const structuredData = [createWebsiteStructuredData()];

  const useCases = [
    {
      icon: <FaLaptopCode />,
      title: "Pomodoro for Software Engineers & Developers",
      description: "The Pomodoro Technique is perfect for coding sessions, debugging, and technical project management. Software engineers use AI Pomo to maintain deep focus during complex problem-solving, estimate development tasks in Pomodoro intervals, and manage sprint goals effectively.",
      scenarios: [
        "Deep focus coding sessions with 25-minute Pomodoro intervals",
        "Breaking down complex features into manageable development tasks",
        "Tracking time spent on different coding projects and bug fixes",
        "Estimating development effort using Pomodoro-based time tracking",
        "Balancing coding work with code reviews and team meetings"
      ],
      testimonial: "As a software engineer, AI Pomo helps me estimate how many Pomodoros each feature will take. The AI breaks down complex coding tasks perfectly, and the timer keeps me focused during long debugging sessions."
    },
    {
      icon: <FaGraduationCap />,
      title: "Pomodoro for Studying & Academic Research",
      description: "Students discover that using the Pomodoro Technique for studying dramatically improves focus and retention. Whether preparing for exams, writing research papers, or managing thesis projects, the 25-minute focused study sessions help students maintain concentration and track their academic progress effectively.",
      scenarios: [
        "Structured Pomodoro study sessions for exam preparation and review",
        "Managing thesis and research projects with AI-generated study plans",
        "Breaking down complex academic assignments into focused study blocks",
        "Tracking study time across different subjects and courses",
        "Using Pomodoro intervals for reading, note-taking, and writing"
      ],
      testimonial: "Using Pomodoro for studying changed everything! As a PhD student, I can now tackle overwhelming research projects by breaking them into 25-minute focused sessions. AI Pomo helps me plan my study schedule and track progress on my thesis."
    },
    {
      icon: <FaBriefcase />,
      title: "Pomodoro for Work & Business Professionals",
      description: "Business professionals use the Pomodoro Technique for work to manage multiple projects, improve focus during meetings preparation, and track billable hours. The structured approach helps consultants and managers maintain productivity while juggling client demands and strategic planning.",
      scenarios: [
        "Using Pomodoro sessions for focused work on client projects",
        "Preparing presentations and reports with 25-minute work blocks",
        "Strategic planning sessions with AI-generated project structures",
        "Time tracking for billable hours using Pomodoro intervals",
        "Managing multiple client deadlines with structured focus sessions"
      ],
      testimonial: "Using Pomodoro for work transformed my consulting practice. I can manage 5 client projects effortlessly by breaking work into focused sessions. AI Pomo helps me track exactly how many Pomodoros each client project requires."
    },
    {
      icon: <FaUsers />,
      title: "Pomodoro for ADHD & Focus Challenges",
      description: "The Pomodoro Technique is particularly helpful for ADHD because it provides structure and breaks tasks into manageable chunks. People with ADHD find that 25-minute focused sessions with built-in breaks help manage attention, reduce overwhelm, and create sustainable work patterns.",
      scenarios: [
        "Using structured Pomodoro sessions to manage ADHD attention spans",
        "Breaking overwhelming projects into small, achievable focus blocks",
        "Creating routine and structure with consistent Pomodoro intervals",
        "Managing hyperfocus and ensuring regular breaks for mental health",
        "Using visual progress tracking to maintain motivation and momentum"
      ],
      testimonial: "Having ADHD made traditional productivity methods impossible for me. Pomodoro for ADHD is a game-changer - the 25-minute sessions match my attention span perfectly, and the breaks prevent burnout. AI Pomo's visual progress tracking keeps me motivated."
    },
    {
      icon: <FaHome />,
      title: "Pomodoro for Freelancers & Remote Work",
      description: "Freelancers and remote workers use the Pomodoro Technique to create structure in flexible work environments. The method helps manage multiple client projects, track billable hours accurately, and maintain work-life balance when working from home.",
      scenarios: [
        "Using Pomodoro sessions to maintain focus while working from home",
        "Managing multiple freelance projects with time-boxed work sessions",
        "Tracking billable hours for clients using Pomodoro intervals",
        "Creating structure and routine in flexible remote work schedules",
        "Balancing focused work time with household responsibilities"
      ],
      testimonial: "As a freelancer, Pomodoro technique helps me manage 6 different clients efficiently. I can track exactly how many Pomodoros each project takes, which makes billing transparent and helps me estimate future work accurately."
    },
    {
      icon: <FaRocket />,
      title: "Pomodoro for Entrepreneurs & Creative Projects",
      description: "Entrepreneurs and creative professionals use the Pomodoro Technique to manage the chaos of building businesses and creative projects. The structured approach helps maintain focus during brainstorming, product development, and creative work while ensuring consistent progress on long-term goals.",
      scenarios: [
        "Using focused Pomodoro sessions for product development and innovation",
        "Balancing creative brainstorming with structured execution time",
        "Managing multiple business initiatives with time-boxed work sessions",
        "Maintaining focus during high-pressure project deadlines",
        "Tracking time investment across different creative and business projects"
      ],
      testimonial: "As an entrepreneur, Pomodoro technique helps me balance creative thinking with execution. I use 25-minute sessions for focused product development and longer sessions for strategic planning. AI Pomo keeps all my business projects organized and on track."
    }
  ];

  return (
    <>
      <SEOHead
        title="Pomodoro Technique Use Cases | Pomodoro for Studying, Work, ADHD & Software Engineers"
        description="Discover how to use the Pomodoro Technique for studying, software engineering, ADHD focus, remote work, and creative projects. Real examples of AI-enhanced Pomodoro productivity."
        keywords="Pomodoro for studying, Pomodoro for software engineers, Pomodoro for ADHD, Pomodoro for work, Pomodoro technique use cases, AI Pomodoro timer, Pomodoro for freelancers, Pomodoro for students"
        url="https://www.ai-pomo.com/use-cases"
        structuredData={structuredData}
      />

      <Container>
        <PageHeader />

        <HeroSection>
          <HeroContent>
            <HeroTitle>How to Use the Pomodoro Technique for Every Profession</HeroTitle>
            <HeroSubtitle>
              From students using Pomodoro for studying to software engineers managing complex projects,
              discover real-world applications of the Pomodoro Technique enhanced with AI. Learn how
              professionals across industries boost focus, manage ADHD, and achieve their goals.
            </HeroSubtitle>
          </HeroContent>
        </HeroSection>

        <UseCasesSection>
          <SectionTitle>Pomodoro Technique Success Stories Across Professions</SectionTitle>
          <SectionDescription>
            Whether you're using Pomodoro for studying, software engineering, managing ADHD, or creative work,
            the technique adapts to your unique needs. Here's how different professionals use AI-enhanced
            Pomodoro sessions to boost focus, manage time effectively, and achieve their goals.
          </SectionDescription>

          <UseCasesGrid>
            {useCases.map((useCase, index) => (
              <UseCaseCard key={index}>
                <UseCaseHeader>
                  <UseCaseIcon>{useCase.icon}</UseCaseIcon>
                  <UseCaseTitle>{useCase.title}</UseCaseTitle>
                </UseCaseHeader>

                <UseCaseDescription>{useCase.description}</UseCaseDescription>

                <ScenariosSection>
                  <ScenariosTitle>Common Scenarios:</ScenariosTitle>
                  <ScenariosList>
                    {useCase.scenarios.map((scenario, idx) => (
                      <ScenarioItem key={idx}>• {scenario}</ScenarioItem>
                    ))}
                  </ScenariosList>
                </ScenariosSection>

                <TestimonialSection>
                  <TestimonialText>"{useCase.testimonial}"</TestimonialText>
                </TestimonialSection>
              </UseCaseCard>
            ))}
          </UseCasesGrid>
        </UseCasesSection>

        <BenefitsSection>
          <BenefitsTitle>Why the Pomodoro Technique Works Across All Professions</BenefitsTitle>
          <BenefitsGrid>
            <BenefitCard>
              <BenefitTitle>Universal Focus Benefits</BenefitTitle>
              <BenefitText>Whether you're using Pomodoro for studying, software engineering, or managing ADHD, the 25-minute focused sessions improve concentration and reduce mental fatigue across all professions.</BenefitText>
            </BenefitCard>
            <BenefitCard>
              <BenefitTitle>Proven Time Management</BenefitTitle>
              <BenefitText>The Pomodoro Technique's structured approach works for any type of work - from creative projects to analytical tasks. AI enhancement makes it even more effective for complex, modern workflows.</BenefitText>
            </BenefitCard>
            <BenefitCard>
              <BenefitTitle>Scalable for Any Workload</BenefitTitle>
              <BenefitText>Whether you're a student managing coursework or a freelancer juggling multiple clients, Pomodoro sessions scale to meet your needs and help track progress effectively.</BenefitText>
            </BenefitCard>
          </BenefitsGrid>
        </BenefitsSection>

        <CTASection>
          <CTAContent>
            <CTATitle>Start Using the Pomodoro Technique for Your Profession</CTATitle>
            <CTADescription>
              Whether you're a student, software engineer, freelancer, or managing ADHD, discover how the Pomodoro
              Technique can transform your productivity. Start your free trial and experience AI-enhanced focus
              sessions tailored to your specific work style and challenges.
            </CTADescription>
            <CTAButtons>
              <PrimaryButton to="/register">Start Free Trial - No Credit Card Required</PrimaryButton>
              <SecondaryButton to="/features">Explore AI Features</SecondaryButton>
            </CTAButtons>
          </CTAContent>
        </CTASection>

        <Footer />
      </Container>
    </>
  );
};

// Styled Components
const Container = styled.div`
  min-height: 100vh;
  background-color: #ffffff;
`;

const HeroSection = styled.section`
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  background-image: url('/images/content-banner-blue-no-text.svg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  color: white;
  padding: 10rem 2rem 6rem;
  text-align: center;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1;
  }

  > * {
    position: relative;
    z-index: 2;
  }
`;

const HeroContent = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const HeroTitle = styled.h1`
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const HeroSubtitle = styled.p`
  font-size: 1.25rem;
  line-height: 1.6;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
`;

const UseCasesSection = styled.section`
  padding: 6rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const SectionTitle = styled.h2`
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 1.5rem;
  color: #2d3748;
`;

const SectionDescription = styled.p`
  font-size: 1.1rem;
  line-height: 1.7;
  text-align: center;
  color: #4a5568;
  max-width: 800px;
  margin: 0 auto 4rem;
`;

const UseCasesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 2.5rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const UseCaseCard = styled.div`
  background: white;
  border-radius: 12px;
  padding: 2.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
`;

const UseCaseHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const UseCaseIcon = styled.div`
  font-size: 2.5rem;
  color: #d95550;
`;

const UseCaseTitle = styled.h3`
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
`;

const UseCaseDescription = styled.p`
  font-size: 1rem;
  line-height: 1.6;
  color: #4a5568;
  margin-bottom: 2rem;
`;

const ScenariosSection = styled.div`
  margin-bottom: 2rem;
`;

const ScenariosTitle = styled.h4`
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
`;

const ScenariosList = styled.ul`
  list-style: none;
  padding: 0;
`;

const ScenarioItem = styled.li`
  font-size: 0.95rem;
  color: #4a5568;
  margin-bottom: 0.5rem;
  line-height: 1.5;
`;

const TestimonialSection = styled.div`
  background: #f7fafc;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #d95550;
`;

const TestimonialText = styled.p`
  font-size: 0.95rem;
  font-style: italic;
  color: #2d3748;
  margin: 0;
  line-height: 1.6;
`;

const BenefitsSection = styled.section`
  background: #f7fafc;
  padding: 4rem 2rem;
`;

const BenefitsTitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  color: #2d3748;
`;

const BenefitsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
`;

const BenefitCard = styled.div`
  background: white;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
`;

const BenefitTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
`;

const BenefitText = styled.p`
  font-size: 1rem;
  color: #4a5568;
  line-height: 1.6;
`;

const CTASection = styled.section`
  background-color: #2d3748;
  color: white;
  padding: 4rem 2rem;
  text-align: center;
`;

const CTAContent = styled.div`
  max-width: 600px;
  margin: 0 auto;
`;

const CTATitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
`;

const CTADescription = styled.p`
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  opacity: 0.9;
`;

const CTAButtons = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;

  @media (max-width: 480px) {
    flex-direction: column;
  }
`;

const PrimaryButton = styled(Link)`
  background-color: #d95550;
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  transition: background-color 0.2s;

  &:hover {
    background-color: #c04540;
  }
`;

const SecondaryButton = styled(Link)`
  background-color: transparent;
  color: white;
  padding: 0.75rem 2rem;
  border: 2px solid white;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s;

  &:hover {
    background-color: white;
    color: #2d3748;
  }
`;

export default UseCasesPage;
