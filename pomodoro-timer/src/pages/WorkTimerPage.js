import React from 'react';
import { FaBriefcase, FaChartLine, FaUsers } from 'react-icons/fa';
import ScenarioTimerPage from '../components/Timer/ScenarioTimerPage';

const WorkTimerPage = () => {
  const workTimerConfig = {
    scenario: 'Work',
    title: 'Work Timer - Professional Productivity Timer | AI Pomo',
    description: 'Professional work timer designed for maximum productivity. Perfect for deep work sessions, meetings, project tasks, and deadline management. Boost your workplace efficiency with focused time blocks.',
    keywords: 'work timer, productivity timer, professional timer, deep work, focus timer, workplace productivity, project management timer',
    url: 'https://www.ai-pomo.com/work-timer',
    heroTitle: 'Work Timer',
    heroDescription: 'Maximize your professional productivity with focused work sessions. Designed for deep work, meetings, and project management to help you achieve peak performance.',
    defaultMinutes: 50,
    gradientColors: ['#059669', '#10b981'],
    backgroundColor: 'linear-gradient(135deg, #ecfdf5, #d1fae5)',
    
    scenarios: [
      {
        emoji: '🎯',
        title: 'Deep Work Sessions',
        description: 'Focused work on complex tasks requiring full concentration',
        recommendedTime: 'Recommended 50-90 minutes'
      },
      {
        emoji: '📊',
        title: 'Project Planning',
        description: 'Strategic planning, roadmap creation, and project analysis',
        recommendedTime: 'Recommended 45-60 minutes'
      },
      {
        emoji: '💼',
        title: 'Administrative Tasks',
        description: 'Email management, documentation, and routine tasks',
        recommendedTime: 'Recommended 25-30 minutes'
      },
      {
        emoji: '🤝',
        title: 'Client Meetings',
        description: 'Focused client calls, presentations, and consultations',
        recommendedTime: 'Recommended 30-60 minutes'
      }
    ],

    benefits: [
      {
        emoji: '⚡',
        title: 'Peak Performance',
        description: 'Structured work blocks help you reach and maintain peak productivity levels'
      },
      {
        emoji: '🎯',
        title: 'Enhanced Focus',
        description: 'Eliminate distractions and maintain deep concentration on important tasks'
      },
      {
        emoji: '📈',
        title: 'Better Output Quality',
        description: 'Focused work sessions produce higher quality results and fewer errors'
      },
      {
        emoji: '⏰',
        title: 'Time Awareness',
        description: 'Develop better time estimation skills and deadline management'
      }
    ],

    tips: [
      {
        title: 'Prepare Your Workspace',
        description: 'Organize your desk, close unnecessary apps, and gather all materials before starting'
      },
      {
        title: 'Set Clear Objectives',
        description: 'Define specific, measurable goals for each work session to maintain focus'
      },
      {
        title: 'Batch Similar Tasks',
        description: 'Group similar activities together to minimize context switching and maintain flow'
      },
      {
        title: 'Use Communication Boundaries',
        description: 'Set status to "Do Not Disturb" and communicate your focused work time to colleagues'
      },
      {
        title: 'Track Your Progress',
        description: 'Log completed tasks and time spent to analyze productivity patterns'
      }
    ],

    customFeatures: [
      {
        icon: <FaBriefcase />,
        title: 'Professional Integration',
        description: 'Seamlessly integrates with your existing workflow and productivity tools'
      },
      {
        icon: <FaChartLine />,
        title: 'Performance Analytics',
        description: 'Track productivity metrics and identify your most effective work patterns'
      },
      {
        icon: <FaUsers />,
        title: 'Team Coordination',
        description: 'Coordinate focused work sessions with team members for better collaboration'
      }
    ],

    relatedTimers: [
      {
        title: 'Study Timer',
        description: 'Focused learning and skill development',
        duration: '25-45 minutes',
        url: '/study-timer'
      },
      {
        title: 'Break Timer',
        description: 'Rejuvenating break periods',
        duration: '5-15 minutes',
        url: '/break-timer'
      },
      {
        title: 'Meeting Timer',
        description: 'Structured meeting time management',
        duration: '15-60 minutes',
        url: '/meeting-timer'
      }
    ]
  };

  return <ScenarioTimerPage config={workTimerConfig} />;
};

export default WorkTimerPage;