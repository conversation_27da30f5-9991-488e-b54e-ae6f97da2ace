import React from 'react';
import { FaBrain, FaBookOpen, FaGraduationCap } from 'react-icons/fa';
import ScenarioTimerPage from '../components/Timer/ScenarioTimerPage';

const StudyTimerPage = () => {
  const studyTimerConfig = {
    scenario: 'Study',
    title: 'Study Timer - Professional Study Time Management Tool | AI Pomo',
    description: 'Specialized timer designed for studying with optimal time planning. Supports Pomodoro technique, review schedules, exam preparation and various study scenarios to improve learning efficiency and focus.',
    keywords: 'study timer, study time management, pomodoro study, review timer, exam countdown, focused learning, study efficiency tool',
    url: 'https://www.ai-pomo.com/study-timer',
    heroTitle: 'Study Timer',
    heroDescription: 'Scientifically plan your study time and boost learning efficiency. Smart timer optimized for various study scenarios to help you build effective study habits.',
    defaultMinutes: 25,
    gradientColors: ['#6366f1', '#8b5cf6'],
    backgroundColor: 'linear-gradient(135deg, #f0f9ff, #e0e7ff)',
    
    scenarios: [
      {
        emoji: '📚',
        title: 'Deep Reading',
        description: 'Focus on academic papers, textbooks, or professional literature',
        recommendedTime: 'Recommended 45-60 minutes'
      },
      {
        emoji: '✍️',
        title: 'Note Taking',
        description: 'Organize class notes, create study materials and mind maps',
        recommendedTime: 'Recommended 25-30 minutes'
      },
      {
        emoji: '🧮',
        title: 'Problem Solving',
        description: 'Math problems, coding practice, logical reasoning exercises',
        recommendedTime: 'Recommended 30-45 minutes'
      },
      {
        emoji: '🎯',
        title: 'Review & Memorization',
        description: 'Review learned concepts, memorization, exam preparation',
        recommendedTime: 'Recommended 20-25 minutes'
      }
    ],

    benefits: [
      {
        emoji: '🧠',
        title: 'Scientific Time Management',
        description: 'Based on Pomodoro technique and cognitive science principles for optimal study time allocation'
      },
      {
        emoji: '📈',
        title: 'Enhanced Focus',
        description: 'Train your brain focus through timed study sessions and build deep learning habits'
      },
      {
        emoji: '⚡',
        title: 'Prevent Fatigue',
        description: 'Balanced study and break periods prevent burnout and maintain learning effectiveness'
      },
      {
        emoji: '🎓',
        title: 'Learning Progress',
        description: 'Visualize study progress to enhance achievement and maintain motivation'
      }
    ],

    tips: [
      {
        title: 'Create Study Plan',
        description: 'Define clear learning objectives and content before starting, break large tasks into 25-45 minute chunks'
      },
      {
        title: 'Optimize Study Environment',
        description: 'Choose quiet, well-lit study space, turn off phone notifications and social media'
      },
      {
        title: 'Active Learning Strategies',
        description: 'Use questioning, summarizing, and practice methods rather than passive reading'
      },
      {
        title: 'Regular Rest Periods',
        description: 'Take 5-15 minute breaks after each study block for light activities to help brain recovery'
      },
      {
        title: 'Track Learning Progress',
        description: 'Record study content and achievements to build learning portfolio and sense of accomplishment'
      }
    ],

    customFeatures: [
      {
        icon: <FaBrain />,
        title: 'Cognitive Optimization',
        description: 'Based on memory curve and attention research to optimize study timing and review frequency'
      },
      {
        icon: <FaBookOpen />,
        title: 'Subject Adaptation',
        description: 'Adjust timing strategies for different subjects - STEM focuses on practice, humanities on understanding'
      }
    ],

    relatedTimers: [
      {
        title: 'Work Timer',
        description: 'Professional work task time management',
        duration: '25-60 minutes',
        url: '/work-timer'
      },
      {
        title: 'Break Timer',
        description: 'Scientific break time scheduling',
        duration: '5-15 minutes',
        url: '/break-timer'
      },
      {
        title: 'Custom Timer',
        description: 'Flexible settings for any time duration',
        duration: 'Custom',
        url: '/custom-timer'
      }
    ]
  };

  return <ScenarioTimerPage config={studyTimerConfig} />;
};

export default StudyTimerPage;