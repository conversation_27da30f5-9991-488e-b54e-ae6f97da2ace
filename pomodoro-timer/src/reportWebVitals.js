const reportWebVitals = onPerfEntry => {
  if (onPerfEntry && onPerfEntry instanceof Function) {
    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
      getCLS(onPerfEntry);
      getFID(onPerfEntry);
      getFCP(onPerfEntry);
      getLCP(onPerfEntry);
      getTTFB(onPerfEntry);
    });
  }
};

// Enhanced performance monitoring for production
export const enhancedWebVitals = () => {
  if (process.env.NODE_ENV === 'production') {
    reportWebVitals(metric => {
      // Log to console in development
      if (process.env.NODE_ENV !== 'production') {
        console.log('Web Vital:', metric);
      }
      
      // Send to analytics in production
      // You can replace this with your analytics service
      if (window.gtag) {
        window.gtag('event', metric.name, {
          event_category: 'Web Vitals',
          event_label: metric.id,
          value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
          non_interaction: true,
        });
      }
    });
  }
};

export default reportWebVitals;
