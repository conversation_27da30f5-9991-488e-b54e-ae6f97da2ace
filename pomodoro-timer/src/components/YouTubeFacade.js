import React, { useState } from 'react';
import styled from 'styled-components';
import { FaPlay } from 'react-icons/fa';

const YouTubeFacade = ({ videoId, title, width = "100%", height = "100%" }) => {
  const [isLoaded, setIsLoaded] = useState(false);

  // 生成YouTube缩略图URL
  const thumbnailUrl = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;

  const handleLoadVideo = () => {
    setIsLoaded(true);
  };

  if (isLoaded) {
    // 动态导入YouTube组件并渲染
    const YouTube = React.lazy(() => import('react-youtube'));
    return (
      <React.Suspense fallback={<LoadingSkeleton>Loading video...</LoadingSkeleton>}>
        <YouTube
          videoId={videoId}
          opts={{
            width: '100%',
            height: '100%',
            playerVars: {
              autoplay: 1, // 用户点击时自动播放
              rel: 0,
              modestbranding: 1,
              controls: 1,
              showinfo: 0,
              origin: window.location.origin
            }
          }}
          title={title}
          style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}
          onReady={(event) => {
            try {
              // 视频准备好后自动播放（因为用户已经点击了）
              event.target.playVideo();
            } catch (error) {
              console.log('YouTube player not ready');
            }
          }}
          onError={(error) => {
            console.log('YouTube video error:', error);
          }}
        />
      </React.Suspense>
    );
  }

  return (
    <FacadeContainer onClick={handleLoadVideo}>
      <ThumbnailImage src={thumbnailUrl} alt={title} />
      <PlayButtonOverlay>
        <PlayButton>
          <FaPlay />
        </PlayButton>
      </PlayButtonOverlay>
    </FacadeContainer>
  );
};

// Styled Components
const FacadeContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
`;

const ThumbnailImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;

  ${FacadeContainer}:hover & {
    transform: scale(1.05);
  }
`;

const PlayButtonOverlay = styled.div`
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  transition: all 0.3s ease;

  ${FacadeContainer}:hover & {
    background: rgba(217, 85, 80, 0.9);
    transform: scale(1.1);
  }
`;

const PlayButton = styled.div`
  color: white;
  font-size: 24px;
  margin-left: 6px; // 更好地视觉居中三角形播放图标
  display: flex;
  align-items: center;
  justify-content: center;
`;


const LoadingSkeleton = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #666;
`;

export default YouTubeFacade;