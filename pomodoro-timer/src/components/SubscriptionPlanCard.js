import React from 'react';
import styled from 'styled-components';
import { FaCheck, FaTimes, FaCrown, FaCheckCircle } from 'react-icons/fa';

const SubscriptionPlanCard = ({
  title,
  price,
  features,
  isPopular,
  isActive,
  isSelected,
  onSelect,
  buttonText = 'Select Plan'
}) => {
  return (
    <CardContainer $isPopular={isPopular} $isActive={isActive} $isSelected={isSelected}>
      {isPopular && (
        <PopularBadge>
          <FaCrown /> Most Popular
        </PopularBadge>
      )}
      {isSelected && !isActive && (
        <SelectedBadge>
          <FaCheckCircle /> Selected
        </SelectedBadge>
      )}

      <CardHeader>
        <PlanTitle $isSelected={isSelected}>
          {title}
          {isSelected && !isActive && <SelectedIndicator><FaCheckCircle /></SelectedIndicator>}
        </PlanTitle>
        <PriceContainer>
          {price === 0 ? (
            <Price $isSelected={isSelected}>Free</Price>
          ) : (
            <>
              <PriceSymbol $isSelected={isSelected}>$</PriceSymbol>
              <Price $isSelected={isSelected}>{price}</Price>
            </>
          )}
          {price > 0 && title.includes('Monthly') && <PricePeriod $isSelected={isSelected}>/month</PricePeriod>}
          {price > 0 && title.includes('Yearly') && <PricePeriod $isSelected={isSelected}>/year</PricePeriod>}
          {price > 0 && title.includes('Lifetime') && <PricePeriod $isSelected={isSelected}>lifetime</PricePeriod>}
        </PriceContainer>
      </CardHeader>

      <FeaturesList>
        {features.map((feature, index) => (
          <FeatureItem key={index} $included={feature.included}>
            {feature.included ? (
              <FeatureIcon $included={true}><FaCheck /></FeatureIcon>
            ) : (
              <FeatureIcon $included={false}><FaTimes /></FeatureIcon>
            )}
            <FeatureText>{feature.text}</FeatureText>
          </FeatureItem>
        ))}
      </FeaturesList>

      <ActionButton
        onClick={onSelect}
        $isPopular={isPopular}
        $isActive={isActive}
        $isSelected={isSelected}
        disabled={isActive}
      >
        {isActive ? 'Current Plan' : isSelected ? 'Selected' : buttonText}
      </ActionButton>
    </CardContainer>
  );
};

const CardContainer = styled.div`
  background: ${props => props.theme['--card-bg'] || '#ffffff'};
  border-radius: 6px;
  box-shadow: ${props =>
    props.$isSelected
      ? '0 4px 12px rgba(217, 85, 80, 0.2)'
      : props.$isPopular
        ? '0 4px 12px rgba(0, 0, 0, 0.15)'
        : '0 1px 4px rgba(0, 0, 0, 0.04)'};
  padding: 2rem;
  width: 100%;
  max-width: 320px;
  position: relative;
  transition: all 0.3s ease;
  border: ${props =>
    props.$isActive
      ? '2px solid #4CAF50'
      : props.$isSelected
        ? '2px solid #d95550'
        : props.$isPopular
          ? '2px solid #d95550'
          : '1px solid #ececec'};
  transform: ${props => 
    props.$isSelected 
      ? 'translateY(-4px)' 
      : 'none'};

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: ${props =>
      props.$isPopular || props.$isSelected
        ? '#d95550'
        : 'transparent'};
    border-radius: 6px 6px 0 0;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: ${props =>
      props.$isSelected
        ? '0 8px 20px rgba(217, 85, 80, 0.25)'
        : '0 4px 12px rgba(0, 0, 0, 0.1)'};
  }
`;

const PopularBadge = styled.div`
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: #d95550;
  color: white;
  padding: 0.25rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  z-index: 1;
  box-shadow: 0 2px 8px rgba(217, 85, 80, 0.3);
`;

const SelectedBadge = styled.div`
  position: absolute;
  top: -12px;
  right: -10px;
  background-color: #4CAF50;
  color: white;
  padding: 0.25rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 2;
`;

const CardHeader = styled.div`
  text-align: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
`;

const PlanTitle = styled.h3`
  font-size: 1.25rem;
  margin-bottom: 1rem;
  color: ${props => props.$isSelected ? '#1a73e8' : props.theme['--text-color'] || '#333'};
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-weight: ${props => props.$isSelected ? '700' : '600'};
`;

const SelectedIndicator = styled.span`
  color: #4CAF50;
  font-size: 0.875rem;
  display: inline-flex;
  align-items: center;
`;

const PriceContainer = styled.div`
  display: flex;
  align-items: baseline;
  justify-content: center;
`;

const PriceSymbol = styled.span`
  font-size: 1.25rem;
  font-weight: 600;
  color: ${props => props.$isSelected ? '#1a73e8' : props.theme['--text-color'] || '#333'};
`;

const Price = styled.span`
  font-size: 2.5rem;
  font-weight: 700;
  color: ${props => props.$isSelected ? '#1a73e8' : props.theme['--text-color'] || '#333'};
`;

const PricePeriod = styled.span`
  font-size: 0.875rem;
  color: ${props => props.$isSelected ? '#1a73e8' : props.theme['--text-secondary'] || '#666'};
  margin-left: 0.25rem;
  font-weight: ${props => props.$isSelected ? '600' : 'normal'};
`;

const FeaturesList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0 0 1.5rem 0;
`;

const FeatureItem = styled.li`
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  color: ${props => props.$included
    ? props.theme['--text-color'] || '#333'
    : props.theme['--text-secondary'] || '#999'};
`;

const FeatureIcon = styled.span`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-right: 0.75rem;
  color: ${props => props.$included ? '#4CAF50' : '#F44336'};
`;

const FeatureText = styled.span`
  font-size: 0.875rem;
`;

const ActionButton = styled.button`
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: ${props => props.$isActive ? 'default' : 'pointer'};
  transition: all 0.2s ease;
  border: none;
  
  background: ${props =>
    props.$isActive
      ? '#4CAF50'
      : props.$isSelected || props.$isPopular
        ? '#d95550'
        : 'transparent'};
        
  color: ${props =>
    props.$isActive || props.$isPopular || props.$isSelected
      ? 'white'
      : '#d95550'};
      
  border: ${props =>
    props.$isActive || props.$isPopular || props.$isSelected
      ? 'none'
      : '1px solid #d95550'};

  &:hover {
    background: ${props =>
      props.$isActive
        ? '#4CAF50'
        : props.$isSelected || props.$isPopular
          ? '#c0504d'
          : 'rgba(217, 85, 80, 0.1)'};
  }

  &:disabled {
    cursor: default;
    opacity: 0.8;
  }
`;

export default SubscriptionPlanCard;
