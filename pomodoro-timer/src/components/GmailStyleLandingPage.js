import React from 'react';
import styled from 'styled-components';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>lock, FaChartBar, FaRobot } from 'react-icons/fa';
import SEOHead from '../components/SEO/SEOHead';
import PageHeader from '../components/shared/PageHeader';
import Footer from '../components/shared/Footer';
import { createWebsiteStructuredData } from '../utils/structuredData';

const GmailStyleLandingPage = () => {
  const structuredData = [createWebsiteStructuredData()];

  return (
    <>
      <SEOHead
        title="AI Pomo Old Interface - Gmail-Style Productivity Timer"
        description="Experience the Gmail-inspired interface of AI Pomo. Clean, familiar design with powerful AI-enhanced Pomodoro timer and productivity features."
        keywords="gmail style pomodoro, clean interface, productivity timer, AI task management, minimalist design"
        url="https://www.ai-pomo.com/old"
        structuredData={structuredData}
      />
      <Container>
      <PageHeader />

      {/* Hero Section */}
      <HeroSection>
        <HeroContent>
          <HeroTitle>
            Focus better with AI-powered time management
          </HeroTitle>
          <HeroDescription>
            AI Pomo combines the proven Pomodoro technique with artificial intelligence to help you manage your time, track your progress, and achieve your goals.
          </HeroDescription>
          <HeroButtons>
            <PrimaryButton to="/signup">Get started for free</PrimaryButton>
          </HeroButtons>
          <HeroNote>No credit card required</HeroNote>
        </HeroContent>
        <HeroImageContainer>
          <HeroImage src="https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80" alt="AI Pomo Dashboard" />
        </HeroImageContainer>
      </HeroSection>

      {/* Features Section */}
      <SectionTitle id="features">
        <SectionTitleText>Everything you need to master your time</SectionTitleText>
        <SectionSubtitle>AI Pomo helps you stay focused and accomplish more with less stress</SectionSubtitle>
      </SectionTitle>

      <FeaturesSection>
        <FeatureCard>
          <FeatureIcon><FaClock /></FeatureIcon>
          <FeatureContent>
            <FeatureTitle>Pomodoro Timer</FeatureTitle>
            <FeatureDescription>
              Work in focused 25-minute sessions with short breaks to maintain peak productivity and avoid burnout.
            </FeatureDescription>
          </FeatureContent>
        </FeatureCard>

        <FeatureCard>
          <FeatureIcon><FaChartBar /></FeatureIcon>
          <FeatureContent>
            <FeatureTitle>Productivity Analytics</FeatureTitle>
            <FeatureDescription>
              Track your progress with detailed statistics and insights to optimize your productivity patterns.
            </FeatureDescription>
          </FeatureContent>
        </FeatureCard>

        <FeatureCard>
          <FeatureIcon><FaRobot /></FeatureIcon>
          <FeatureContent>
            <FeatureTitle>AI Project Generator</FeatureTitle>
            <FeatureDescription>
              Let AI help you structure your projects with tasks, milestones, and deadlines for optimal organization.
            </FeatureDescription>
          </FeatureContent>
        </FeatureCard>
      </FeaturesSection>

      {/* Benefits Section */}
      <BenefitsSection id="benefits">
        <SectionTitle>
          <SectionTitleText>Why Choose AI Pomo</SectionTitleText>
          <SectionSubtitle>Powerful benefits that transform your workflow and boost productivity</SectionSubtitle>
        </SectionTitle>

        <BenefitsContainer>
          <BenefitCard>
            <BenefitIconContainer>
              <BenefitIcon>🤖</BenefitIcon>
            </BenefitIconContainer>
            <BenefitContent>
              <BenefitTitle>AI-Powered Project Planning</BenefitTitle>
              <BenefitDescription>
                Simply describe your project ideas in natural language, and AI automatically generates complete project structures with milestones, tasks, subtasks, and notes. This dramatically lowers the barrier to starting complex personal projects.
              </BenefitDescription>
            </BenefitContent>
          </BenefitCard>

          <BenefitCard>
            <BenefitIconContainer>
              <BenefitIcon>🍅</BenefitIcon>
            </BenefitIconContainer>
            <BenefitContent>
              <BenefitTitle>Seamless Pomodoro Integration</BenefitTitle>
              <BenefitDescription>
                More than just a timer and task list - our app deeply integrates the Pomodoro technique into task execution and project management. Every work session is linked to specific tasks with estimated vs. actual completion tracking.
              </BenefitDescription>
            </BenefitContent>
          </BenefitCard>

          <BenefitCard>
            <BenefitIconContainer>
              <BenefitIcon>📊</BenefitIcon>
            </BenefitIconContainer>
            <BenefitContent>
              <BenefitTitle>Comprehensive Analytics</BenefitTitle>
              <BenefitDescription>
                Get detailed statistics on daily, weekly, and monthly pomodoro completion, project and task completion rates, productivity trend charts, and visual calendar showing milestones, deadlines, and daily achievements.
              </BenefitDescription>
            </BenefitContent>
          </BenefitCard>

          <BenefitCard>
            <BenefitIconContainer>
              <BenefitIcon>🎯</BenefitIcon>
            </BenefitIconContainer>
            <BenefitContent>
              <BenefitTitle>Focused Single-Task Workflow</BenefitTitle>
              <BenefitDescription>
                Enforces the principle of "focus on one task at a time" - the timer resets when switching tasks, and interrupted pomodoros are abandoned. This design actively guides users to reduce efficiency loss from multitasking.
              </BenefitDescription>
            </BenefitContent>
          </BenefitCard>
        </BenefitsContainer>
      </BenefitsSection>

      {/* How It Works Section */}
      <HowItWorksSection id="how-it-works">
        <SectionTitle>
          <SectionTitleText>How AI Pomo Works</SectionTitleText>
          <SectionSubtitle>Our approach combines the time-tested Pomodoro Technique with modern AI to supercharge your productivity</SectionSubtitle>
        </SectionTitle>

        <ProcessContainer>
          <ProcessStep>
            <ProcessStepNumber>1</ProcessStepNumber>
            <ProcessStepContent>
              <ProcessStepTitle>Create or Generate Projects</ProcessStepTitle>
              <ProcessStepDescription>
                Start by creating a new project manually or use our AI generator to automatically structure your ideas into actionable projects with tasks and milestones.
              </ProcessStepDescription>
            </ProcessStepContent>
          </ProcessStep>

          <ProcessStep>
            <ProcessStepNumber>2</ProcessStepNumber>
            <ProcessStepContent>
              <ProcessStepTitle>Set Your Focus Task</ProcessStepTitle>
              <ProcessStepDescription>
                Select a specific task from your project and estimate how many pomodoro sessions it will take. Our system ensures you stay focused on one task at a time.
              </ProcessStepDescription>
            </ProcessStepContent>
          </ProcessStep>

          <ProcessStep>
            <ProcessStepNumber>3</ProcessStepNumber>
            <ProcessStepContent>
              <ProcessStepTitle>Work in Focused Sessions</ProcessStepTitle>
              <ProcessStepDescription>
                Use the 25-minute pomodoro timer to work with complete focus. Take short breaks between sessions and longer breaks after every 4 pomodoros.
              </ProcessStepDescription>
            </ProcessStepContent>
          </ProcessStep>

          <ProcessStep>
            <ProcessStepNumber>4</ProcessStepNumber>
            <ProcessStepContent>
              <ProcessStepTitle>Track Your Progress</ProcessStepTitle>
              <ProcessStepDescription>
                Monitor your productivity with detailed analytics, see your completed pomodoros on the calendar, and watch your projects move toward completion.
              </ProcessStepDescription>
            </ProcessStepContent>
          </ProcessStep>
        </ProcessContainer>
      </HowItWorksSection>

      {/* Testimonials Section */}
      <TestimonialsSection id="testimonials">
        <SectionTitle>
          <SectionTitleText>What Our Users Say</SectionTitleText>
          <SectionSubtitle>Join thousands of professionals who have transformed their productivity with AI Pomo</SectionSubtitle>
        </SectionTitle>

        <TestimonialsContainer>
          <TestimonialCard>
            <TestimonialQuote>
              "AI Pomo has completely changed how I manage my workday. The combination of Pomodoro timing with project management is genius!"
            </TestimonialQuote>
            <TestimonialRating>
              <StarIcon>⭐</StarIcon>
              <StarIcon>⭐</StarIcon>
              <StarIcon>⭐</StarIcon>
              <StarIcon>⭐</StarIcon>
              <StarIcon>⭐</StarIcon>
            </TestimonialRating>
            <TestimonialAuthor>
              <TestimonialName>Sarah Johnson</TestimonialName>
              <TestimonialRole>Product Manager</TestimonialRole>
            </TestimonialAuthor>
          </TestimonialCard>

          <TestimonialCard>
            <TestimonialQuote>
              "As a developer, I need to stay focused for long periods. AI Pomo helps me maintain concentration while avoiding burnout."
            </TestimonialQuote>
            <TestimonialRating>
              <StarIcon>⭐</StarIcon>
              <StarIcon>⭐</StarIcon>
              <StarIcon>⭐</StarIcon>
              <StarIcon>⭐</StarIcon>
              <StarIcon>⭐</StarIcon>
            </TestimonialRating>
            <TestimonialAuthor>
              <TestimonialName>David Chen</TestimonialName>
              <TestimonialRole>Software Engineer</TestimonialRole>
            </TestimonialAuthor>
          </TestimonialCard>

          <TestimonialCard>
            <TestimonialQuote>
              "The calendar feature showing my completed pomodoros gives me a visual sense of accomplishment. I'm addicted to seeing those tomatoes fill up my days!"
            </TestimonialQuote>
            <TestimonialRating>
              <StarIcon>⭐</StarIcon>
              <StarIcon>⭐</StarIcon>
              <StarIcon>⭐</StarIcon>
              <StarIcon>⭐</StarIcon>
              <StarIcon>⭐</StarIcon>
            </TestimonialRating>
            <TestimonialAuthor>
              <TestimonialName>Emma Rodriguez</TestimonialName>
              <TestimonialRole>Freelance Designer</TestimonialRole>
            </TestimonialAuthor>
          </TestimonialCard>
        </TestimonialsContainer>
      </TestimonialsSection>

      {/* CTA Section */}
      <CtaSection>
        <CtaTitleContainer>
          <CtaTitleText>Ready to Transform Your Productivity?</CtaTitleText>
          <CtaSubtitle>Join thousands of professionals who have discovered the power of AI-enhanced focus</CtaSubtitle>
        </CtaTitleContainer>

        <CtaFeatures>
          <CtaFeatureItem>
            <CtaFeatureIcon>✓</CtaFeatureIcon>
            <CtaFeatureText>Free forever plan available</CtaFeatureText>
          </CtaFeatureItem>
          <CtaFeatureItem>
            <CtaFeatureIcon>✓</CtaFeatureIcon>
            <CtaFeatureText>No credit card required</CtaFeatureText>
          </CtaFeatureItem>
        </CtaFeatures>

        <CtaButton to="/signup">
          Get Started For Free
        </CtaButton>
      </CtaSection>

      {/* Pricing Section */}
      <SectionTitle id="pricing">
        <SectionTitleText>Simple, Transparent Pricing</SectionTitleText>
        <SectionSubtitle>Please refer to our <PricingLink to="/pricing">pricing page</PricingLink> for detailed information about our plans and features.</SectionSubtitle>
      </SectionTitle>

      <Footer />
    </Container>
    </>
  );
};

// Styled Components
const Container = styled.div`
  font-family: 'Google Sans', 'Roboto', Arial, sans-serif;
  color: #202124;
  line-height: 1.5;
  background-color: #ffffff;
`;



const PrimaryButton = styled(Link)`
  background-color: #1a73e8;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.875rem;
  text-decoration: none;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #1765cc;
  }
`;



const HeroSection = styled.section`
  display: flex;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  padding: 4rem 2rem;

  @media (max-width: 992px) {
    flex-direction: column;
    padding: 3rem 1rem;
    text-align: center;
  }
`;

const HeroContent = styled.div`
  flex: 1;
  padding-right: 2rem;

  @media (max-width: 992px) {
    padding-right: 0;
    margin-bottom: 3rem;
  }
`;

const HeroTitle = styled.h1`
  font-size: 3.5rem;
  font-weight: 400;
  color: #202124;
  margin-bottom: 1.5rem;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const HeroDescription = styled.p`
  font-size: 1.25rem;
  color: #5f6368;
  margin-bottom: 2rem;
  max-width: 600px;

  @media (max-width: 992px) {
    margin-left: auto;
    margin-right: auto;
  }
`;

const HeroButtons = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;

  @media (max-width: 576px) {
    flex-direction: column;
    align-items: center;
  }
`;

const WorkspaceButton = styled.button`
  display: flex;
  align-items: center;
  background-color: white;
  color: #5f6368;
  border: 1px solid #dadce0;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f8f9fa;
  }
`;

const GoogleIcon = styled.span`
  color: #1a73e8;
  margin-right: 0.5rem;
  font-size: 1rem;
`;

const HeroNote = styled.p`
  font-size: 0.875rem;
  color: #5f6368;
`;

const HeroImageContainer = styled.div`
  flex: 1;

  @media (max-width: 992px) {
    width: 100%;
  }
`;

const HeroImage = styled.img`
  width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 2px 6px 2px rgba(60, 64, 67, 0.15);
`;

const SectionTitle = styled.div`
  text-align: center;
  max-width: 800px;
  margin: 0 auto 4rem;
  padding: 0 2rem;

  @media (max-width: 768px) {
    margin-bottom: 3rem;
    padding: 0 1rem;
  }
`;

const SectionTitleText = styled.h2`
  font-size: 2.5rem;
  font-weight: 400;
  color: #202124;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const SectionSubtitle = styled.p`
  font-size: 1.25rem;
  color: #4a4a4a;
  font-weight: 400;

  @media (max-width: 768px) {
    font-size: 1.125rem;
  }
`;

const PricingLink = styled(Link)`
  color: #1a73e8;
  text-decoration: none;
  font-weight: 500;

  &:hover {
    text-decoration: underline;
  }
`;

const FeaturesSection = styled.section`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto 6rem;
  padding: 0 2rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    padding: 0 1rem;
    margin-bottom: 4rem;
  }
`;

const FeatureCard = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 2rem;
  border-radius: 8px;
  transition: transform 0.3s, box-shadow 0.3s;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 2px 6px 2px rgba(60, 64, 67, 0.15);
  }
`;

const FeatureIcon = styled.div`
  font-size: 2.5rem;
  color: #1a73e8;
  margin-bottom: 1.5rem;
`;

const FeatureContent = styled.div``;

const FeatureTitle = styled.h3`
  font-size: 1.5rem;
  font-weight: 500;
  color: #202124;
  margin-bottom: 1rem;
`;

const FeatureDescription = styled.p`
  color: #4a4a4a;
  line-height: 1.6;
  font-weight: 400;
`;
// Benefits Section Styles
const BenefitsSection = styled.section`
  max-width: 1400px;
  margin: 0 auto 6rem;
  padding: 0 2rem;

  @media (max-width: 768px) {
    padding: 0 1rem;
    margin-bottom: 4rem;
  }
`;

const BenefitsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
`;

const BenefitCard = styled.div`
  background-color: #ffffff;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
`;

const BenefitIconContainer = styled.div`
  margin-bottom: 1.5rem;
`;

const BenefitIcon = styled.div`
  font-size: 3rem;
  line-height: 1;
`;

const BenefitContent = styled.div``;

const BenefitTitle = styled.h3`
  font-size: 1.5rem;
  font-weight: 500;
  color: #202124;
  margin-bottom: 1rem;
`;

const BenefitDescription = styled.p`
  color: #5f6368;
  line-height: 1.6;
  font-size: 1rem;
`;

// How It Works Section Styles
const HowItWorksSection = styled.section`
  max-width: 1400px;
  margin: 0 auto 6rem;
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 12px;

  @media (max-width: 768px) {
    padding: 1rem;
    margin-bottom: 4rem;
  }
`;

const ProcessContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  padding: 2rem 0;
`;

const ProcessStep = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 1rem;
`;

const ProcessStepNumber = styled.div`
  background-color: #1a73e8;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
  flex-shrink: 0;
`;

const ProcessStepContent = styled.div`
  flex: 1;
`;

const ProcessStepTitle = styled.h4`
  font-size: 1.25rem;
  font-weight: 500;
  color: #202124;
  margin-bottom: 0.5rem;
`;

const ProcessStepDescription = styled.p`
  color: #5f6368;
  line-height: 1.6;
`;
// Testimonials Section Styles
const TestimonialsSection = styled.section`
  max-width: 1400px;
  margin: 0 auto 6rem;
  padding: 0 2rem;

  @media (max-width: 768px) {
    padding: 0 1rem;
    margin-bottom: 4rem;
  }
`;

const TestimonialsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
`;

const TestimonialCard = styled.div`
  background-color: #ffffff;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
`;

const TestimonialQuote = styled.p`
  font-size: 1.1rem;
  color: #333;
  line-height: 1.7;
  margin-bottom: 1.5rem;
  font-style: italic;
  font-weight: 400;
`;

const TestimonialRating = styled.div`
  display: flex;
  margin-bottom: 1.5rem;
`;

const StarIcon = styled.span`
  color: #ffc107;
  margin-right: 0.25rem;
`;

const TestimonialAuthor = styled.div`
  display: flex;
  flex-direction: column;
`;

const TestimonialName = styled.div`
  font-weight: 600;
  color: #202124;
  margin-bottom: 0.25rem;
`;

const TestimonialRole = styled.div`
  color: #5f6368;
  font-size: 0.9rem;
`;

// CTA Section Styles
const CtaSection = styled.section`
  max-width: 1400px;
  margin: 0 auto 6rem;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #1a73e8 0%, #1765cc 100%);
  border-radius: 12px;
  text-align: center;
  color: white;

  @media (max-width: 768px) {
    padding: 2rem 1rem;
    margin-bottom: 4rem;
  }
`;

const CtaFeatures = styled.div`
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin: 2rem 0;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
  }
`;

const CtaFeatureItem = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const CtaFeatureIcon = styled.span`
  color: #4caf50;
  font-weight: bold;
`;

const CtaFeatureText = styled.span`
  color: #ffffff;
  font-weight: 500;
`;

const CtaButton = styled(Link)`
  display: inline-block;
  background-color: white;
  color: #1a73e8;
  padding: 1rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  }
`;

// CTA Title Styles
const CtaTitleContainer = styled.div`
  text-align: center;
  margin-bottom: 2rem;
`;

const CtaTitleText = styled.h2`
  font-size: 2.5rem;
  font-weight: 400;
  color: #ffffff;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const CtaSubtitle = styled.p`
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.95);
  font-weight: 400;

  @media (max-width: 768px) {
    font-size: 1.125rem;
  }
`;





export default GmailStyleLandingPage;
