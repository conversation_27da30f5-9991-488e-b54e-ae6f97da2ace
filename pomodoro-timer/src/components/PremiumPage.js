import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FaRocket, FaCheck, FaHistory, FaCreditCard } from 'react-icons/fa';
import SubscriptionPlanCard from './SubscriptionPlanCard';
import PaddleCheckout from './PaddleCheckout';
import { subscriptionApi } from '../services/apiService';

const PremiumPage = () => {
  const [currentSubscription, setCurrentSubscription] = useState(null);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [isCheckoutOpen, setIsCheckoutOpen] = useState(false);
  const [subscriptionHistory, setSubscriptionHistory] = useState([]);
  const [showHistory, setShowHistory] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [paymentMessage, setPaymentMessage] = useState('');
  const [isCancelling, setIsCancelling] = useState(false);

  // Check for payment success/failure parameters on mount
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const success = urlParams.get('success');
    const cancelled = urlParams.get('cancelled');
    
    if (success === 'true') {
      setPaymentSuccess(true);
      setPaymentMessage('🎉 Payment successful! Your subscription has been activated. You now have access to all premium features.');
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
      
      // Auto-hide success message after 5 seconds
      setTimeout(() => {
        setPaymentMessage('');
      }, 5000);
    } else if (cancelled === 'true') {
      setPaymentMessage('Payment was cancelled. You can try again at any time.');
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
      
      // Auto-hide cancelled message after 3 seconds
      setTimeout(() => {
        setPaymentMessage('');
      }, 3000);
    }
  }, []);

  // Fetch current subscription on mount
  useEffect(() => {
    const fetchSubscription = async () => {
      try {
        setIsLoading(true);
        const currentSubscription = await subscriptionApi.getCurrentSubscription();
        setCurrentSubscription(currentSubscription);

        // Also fetch subscription history
        const history = await subscriptionApi.getSubscriptionHistory();
        setSubscriptionHistory(history);
      } catch (error) {
        console.error('Error fetching subscription data:', error);
        setError('Failed to load subscription information. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSubscription();
  }, [paymentSuccess]); // Re-fetch when payment succeeds

  // Define subscription plans
  const subscriptionPlans = [
    {
      id: 'free',
      title: 'Free Plan',
      price: 0,
      features: [
        { text: 'Up to 3 open projects', included: true },
        { text: 'Unlimited tasks per project', included: true },
        { text: 'Pomodoro timer functionality', included: true },
        { text: 'Basic statistics', included: true },
        { text: 'Limited AI project generation', included: true },
        { text: 'More than 3 open projects', included: false },
        { text: 'Priority support', included: false },
      ],
      isPopular: false,
    },
    {
      id: 'monthly',
      title: 'Monthly Plan',
      price: 8,
      features: [
        { text: 'Unlimited open projects', included: true },
        { text: 'Unlimited tasks per project', included: true },
        { text: 'Pomodoro timer functionality', included: true },
        { text: 'Advanced statistics', included: true },
        { text: 'Unlimited AI project generation', included: true },
        { text: 'Priority support', included: true },
        { text: 'Future premium features', included: true },
        { text: '7-day free trial', included: true },
      ],
      isPopular: false,
    },
    {
      id: 'yearly',
      title: 'Yearly Plan',
      price: 60,
      features: [
        { text: 'Unlimited open projects', included: true },
        { text: 'Unlimited tasks per project', included: true },
        { text: 'Pomodoro timer functionality', included: true },
        { text: 'Advanced statistics', included: true },
        { text: 'Unlimited AI project generation', included: true },
        { text: 'Priority support', included: true },
        { text: 'Future premium features', included: true },
        { text: '7-day free trial', included: true },
        { text: 'Save $36 per year vs monthly', included: true },
      ],
      isPopular: true,
    },
    {
      id: 'lifetime',
      title: 'Lifetime Plan',
      price: 100,
      features: [
        { text: 'Unlimited open projects', included: true },
        { text: 'Unlimited tasks per project', included: true },
        { text: 'Pomodoro timer functionality', included: true },
        { text: 'Advanced statistics', included: true },
        { text: 'Unlimited AI project generation', included: true },
        { text: 'Priority support', included: true },
        { text: 'Future premium features', included: true },
        { text: 'One-time payment, lifetime access', included: true },
        { text: 'Best value - never pay again!', included: true },
      ],
      isPopular: false,
    },
  ];

  const handleSelectPlan = (planId) => {
    if (planId === 'free') {
      // Free plan doesn't need payment
      return;
    }

    if (planId === selectedPlan) {
      // If same plan is selected, deselect it
      setSelectedPlan(null);
    } else {
      // Select the new plan and open checkout
      setSelectedPlan(planId);
      setIsCheckoutOpen(true);
    }
  };

  const handlePaymentSuccess = (data) => {
    console.log('Payment successful:', data);
    
    // Refresh subscription data after successful payment
    const refreshData = async () => {
      try {
        const currentSubscription = await subscriptionApi.getCurrentSubscription();
        setCurrentSubscription(currentSubscription);

        const history = await subscriptionApi.getSubscriptionHistory();
        setSubscriptionHistory(history);
      } catch (error) {
        console.error('Error refreshing subscription data:', error);
      }
    };

    refreshData();

    // Reset state
    setSelectedPlan(null);
    setIsCheckoutOpen(false);
  };

  const handlePaymentError = (error) => {
    console.error('Payment error:', error);
    setError('Payment failed. Please try again.');
  };

  const handlePaymentCancel = () => {
    console.log('Payment cancelled');
    setSelectedPlan(null);
    setIsCheckoutOpen(false);
  };

  // Handle subscription cancellation
  const handleCancelSubscription = async () => {
    // Check if user is in trial period for more accurate confirmation message
    const isInTrial = currentSubscription?.trialEndsAt && new Date() < new Date(currentSubscription.trialEndsAt);
    
    const confirmMessage = isInTrial 
      ? 'Are you sure you want to cancel your subscription? Since you\'re in the trial period, you won\'t be charged and your access will end immediately.'
      : 'Are you sure you want to cancel your subscription? You will continue to have access until the end of your current billing period.';
    
    if (!window.confirm(confirmMessage)) {
      return;
    }

    setIsCancelling(true);
    setError('');

    try {
      const result = await subscriptionApi.cancelSubscription();
      
      if (result.success) {
        setPaymentMessage(result.message);
        setPaymentSuccess(true);
        
        // Refresh subscription data
        const updatedSubscription = await subscriptionApi.getCurrentSubscription();
        setCurrentSubscription(updatedSubscription);
        
        // Auto-hide message after 5 seconds
        setTimeout(() => {
          setPaymentMessage('');
        }, 5000);
      }
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      setError(error.response?.data?.message || 'Failed to cancel subscription. Please try again.');
    } finally {
      setIsCancelling(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  return (
    <PageContainer>
      <PageHeader>
        <HeaderIcon><FaRocket /></HeaderIcon>
        <HeaderTitle>Premium Subscription</HeaderTitle>
        <HeaderDescription>
          Unlock unlimited projects and premium features to boost your productivity
        </HeaderDescription>
      </PageHeader>

      {paymentMessage && (
        <PaymentMessage $success={paymentSuccess}>
          {paymentMessage}
        </PaymentMessage>
      )}

      {isLoading ? (
        <LoadingMessage>Loading subscription information...</LoadingMessage>
      ) : error ? (
        <ErrorMessage>{error}</ErrorMessage>
      ) : (
        <>
          {currentSubscription && (
            <CurrentPlanBanner>
              <CurrentPlanHeader>
                <CurrentPlanTitle>Your Current Plan: <CurrentPlanHighlight>{currentSubscription.plan.charAt(0).toUpperCase() + currentSubscription.plan.slice(1)}</CurrentPlanHighlight></CurrentPlanTitle>
              </CurrentPlanHeader>

              <CurrentPlanDetails>
                <CurrentPlanStatus>
                  Status: <StatusText $active={currentSubscription.active}>{currentSubscription.active ? 'Active' : 'Inactive'}</StatusText>
                </CurrentPlanStatus>

                {currentSubscription.plan === 'yearly' && currentSubscription.expiryDate && (
                  <CurrentPlanExpiry>
                    Expires: <ExpiryDate>{new Date(currentSubscription.expiryDate).toLocaleDateString()}</ExpiryDate>
                  </CurrentPlanExpiry>
                )}

                {currentSubscription.plan === 'lifetime' && (
                  <CurrentPlanExpiry>
                    <ExpiryDate>Never Expires</ExpiryDate>
                  </CurrentPlanExpiry>
                )}

                {currentSubscription.plan === 'free' && (
                  <CurrentPlanMessage>
                    Upgrade to a premium plan to unlock unlimited projects and AI features!
                  </CurrentPlanMessage>
                )}

                {currentSubscription.plan !== 'free' && (
                  <CancelSubscriptionSection>
                    <CancelSubscriptionText>
                      Want to cancel your subscription? You can do so at any time.
                      {/* Check if in trial period using correct field */}
                      {currentSubscription.trialEndsAt && new Date() < new Date(currentSubscription.trialEndsAt) && (
                        <TrialWarning> Since you're in the trial period, you won't be charged.</TrialWarning>
                      )}
                    </CancelSubscriptionText>
                    <CancelSubscriptionButton 
                      onClick={handleCancelSubscription}
                      disabled={isCancelling}
                    >
                      {isCancelling ? 'Cancelling...' : 'Cancel Subscription'}
                    </CancelSubscriptionButton>
                  </CancelSubscriptionSection>
                )}
              </CurrentPlanDetails>
            </CurrentPlanBanner>
          )}

          <PlansContainer>
            {subscriptionPlans.map((plan) => (
              <SubscriptionPlanCard
                key={plan.id}
                title={plan.title}
                price={plan.price}
                features={plan.features}
                isPopular={plan.isPopular}
                isActive={currentSubscription?.plan === plan.id}
                isSelected={selectedPlan === plan.id}
                onSelect={() => handleSelectPlan(plan.id)}
                buttonText={plan.id === 'free' ? 'Current Plan' : 'Select Plan'}
              />
            ))}
          </PlansContainer>

          <TrustSection>
            <TrustTitle>Trusted by Thousands of Professionals</TrustTitle>
            <TrustBadges>
              <TrustBadge>
                <TrustIcon>🔒</TrustIcon>
                <TrustText>
                  <strong>Secure Payments</strong>
                  <span>Protected by Paddle</span>
                </TrustText>
              </TrustBadge>
              <TrustBadge>
                <TrustIcon>⚡</TrustIcon>
                <TrustText>
                  <strong>Instant Access</strong>
                  <span>Activate immediately</span>
                </TrustText>
              </TrustBadge>
              <TrustBadge>
                <TrustIcon>🎯</TrustIcon>
                <TrustText>
                  <strong>Cancel Anytime</strong>
                  <span>No long-term commitment</span>
                </TrustText>
              </TrustBadge>
              <TrustBadge>
                <TrustIcon>💳</TrustIcon>
                <TrustText>
                  <strong>Money Back Guarantee</strong>
                  <span>7-day free trial</span>
                </TrustText>
              </TrustBadge>
            </TrustBadges>
          </TrustSection>


          <HistorySection>
            <HistoryHeader onClick={() => setShowHistory(!showHistory)}>
              <FaHistory />
              <span>Subscription History</span>
            </HistoryHeader>

            {showHistory && (
              <HistoryContent>
                {subscriptionHistory.length === 0 ? (
                  <EmptyHistory>No subscription history found</EmptyHistory>
                ) : (
                  <HistoryTable>
                    <thead>
                      <tr>
                        <th>Plan</th>
                        <th>Payment Method</th>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Date</th>
                      </tr>
                    </thead>
                    <tbody>
                      {subscriptionHistory.map((sub) => (
                        <tr key={sub._id}>
                          <td>{sub.plan.charAt(0).toUpperCase() + sub.plan.slice(1)}</td>
                          <td>
                            {sub.paymentMethod === 'paddle' ? (
                              <><FaCreditCard /> Paddle</>
                            ) : sub.paymentMethod === 'paypal' ? (
                              <>PayPal (Legacy)</>
                            ) : sub.paymentMethod === 'usdt' ? (
                              <>USDT (Legacy)</>
                            ) : 'N/A'}
                          </td>
                          <td>${sub.paymentAmount || 0}</td>
                          <td>
                            <StatusBadge $status={sub.paymentStatus}>
                              {sub.paymentStatus.charAt(0).toUpperCase() + sub.paymentStatus.slice(1)}
                            </StatusBadge>
                          </td>
                          <td>{formatDate(sub.createdAt)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </HistoryTable>
                )}
              </HistoryContent>
            )}
          </HistorySection>
        </>
      )}

      <PaddleCheckout
        planType={selectedPlan}
        isOpen={isCheckoutOpen}
        onClose={() => setIsCheckoutOpen(false)}
        onSuccess={handlePaymentSuccess}
        onError={handlePaymentError}
        onCancel={handlePaymentCancel}
      />
    </PageContainer>
  );
};

const PageContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
  background: ${props => props.theme['--bg-color'] || '#f5f5f5'};
  min-height: 100vh;
`;

const PageHeader = styled.div`
  text-align: center;
  margin-bottom: 3rem;
  background: ${props => props.theme['--card-bg'] || '#ffffff'};
  padding: 2rem;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  border: 1px solid #ececec;
`;

const HeaderIcon = styled.div`
  font-size: 2.5rem;
  color: ${props => props.theme['--primary-color'] || '#d95550'};
  margin-bottom: 1rem;
`;

const HeaderTitle = styled.h1`
  font-size: 2rem;
  margin-bottom: 1rem;
  color: ${props => props.theme['--text-color'] || '#333'};
  font-weight: 600;
`;

const HeaderDescription = styled.p`
  font-size: 1.125rem;
  color: ${props => props.theme['--text-secondary'] || '#666'};
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
`;

const PlansContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 3rem;
  padding: 2rem 0;
`;


const HistorySection = styled.div`
  background: ${props => props.theme['--card-bg'] || '#ffffff'};
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  border: 1px solid #ececec;
  overflow: hidden;
  margin-top: 2rem;
`;

const HistoryHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  color: ${props => props.theme['--text-color'] || '#333'};
  border-bottom: 1px solid #eee;

  &:hover {
    background-color: ${props => props.theme['--bg-color'] || '#f5f5f5'};
  }
`;

const HistoryContent = styled.div`
  padding: 1.5rem;
`;

const EmptyHistory = styled.div`
  text-align: center;
  padding: 2rem;
  color: ${props => props.theme['--text-secondary'] || '#666'};
`;

const HistoryTable = styled.table`
  width: 100%;
  border-collapse: collapse;

  th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #eee;
  }

  th {
    font-weight: 600;
    color: ${props => props.theme['--text-color'] || '#333'};
  }

  td {
    color: ${props => props.theme['--text-secondary'] || '#666'};
  }

  tbody tr:hover {
    background-color: ${props => props.theme['--bg-color'] || '#f5f5f5'};
  }
`;

const StatusBadge = styled.span`
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;

  ${props => {
    switch (props.$status) {
      case 'confirmed':
        return `
          background-color: rgba(76, 175, 80, 0.1);
          color: #4CAF50;
        `;
      case 'pending':
        return `
          background-color: rgba(255, 152, 0, 0.1);
          color: #FF9800;
        `;
      case 'rejected':
        return `
          background-color: rgba(244, 67, 54, 0.1);
          color: #F44336;
        `;
      default:
        return `
          background-color: rgba(158, 158, 158, 0.1);
          color: #9E9E9E;
        `;
    }
  }}
`;

const LoadingMessage = styled.div`
  text-align: center;
  padding: 2rem;
  color: ${props => props.theme['--text-secondary'] || '#666'};
`;

const ErrorMessage = styled.div`
  text-align: center;
  padding: 1.5rem;
  margin: 2rem auto;
  max-width: 600px;
  background-color: rgba(244, 67, 54, 0.1);
  color: #F44336;
  border-radius: 6px;
  border: 1px solid rgba(244, 67, 54, 0.2);
`;

const PaymentMessage = styled.div`
  text-align: center;
  padding: 1.5rem;
  margin: 2rem auto;
  max-width: 600px;
  background-color: ${props => 
    props.$success 
      ? 'rgba(76, 175, 80, 0.1)' 
      : 'rgba(255, 152, 0, 0.1)'};
  color: ${props => 
    props.$success 
      ? '#4CAF50' 
      : '#FF9800'};
  border-radius: 6px;
  border: 1px solid ${props => 
    props.$success 
      ? 'rgba(76, 175, 80, 0.2)' 
      : 'rgba(255, 152, 0, 0.2)'};
  font-weight: 500;
`;

// Trust Section Styles
const TrustSection = styled.div`
  background: ${props => props.theme['--card-bg'] || '#ffffff'};
  border-radius: 6px;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  border: 1px solid #ececec;
`;

const TrustTitle = styled.h3`
  text-align: center;
  font-size: 1.25rem;
  margin-bottom: 1.5rem;
  color: ${props => props.theme['--text-color'] || '#333'};
  font-weight: 600;
`;

const TrustBadges = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
`;

const TrustBadge = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: ${props => props.theme['--bg-color'] || '#f5f5f5'};
  border-radius: 6px;
  transition: transform 0.2s;

  &:hover {
    transform: translateY(-2px);
  }
`;

const TrustIcon = styled.div`
  font-size: 1.5rem;
  min-width: 40px;
  text-align: center;
`;

const TrustText = styled.div`
  display: flex;
  flex-direction: column;
  
  strong {
    color: ${props => props.theme['--text-color'] || '#333'};
    font-weight: 600;
    margin-bottom: 0.25rem;
  }
  
  span {
    color: ${props => props.theme['--text-secondary'] || '#666'};
    font-size: 0.875rem;
  }
`;

// Current Plan Banner Styles
const CurrentPlanBanner = styled.div`
  background: ${props => props.theme['--card-bg'] || '#ffffff'};
  border: 1px solid #ececec;
  border-radius: 6px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: ${props => props.theme['--primary-color'] || '#d95550'};
    border-radius: 6px 6px 0 0;
  }
`;

const CurrentPlanHeader = styled.div`
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid ${props => props.theme['--border-color'] || '#e0e0e0'};
`;

const CurrentPlanTitle = styled.h2`
  font-size: 1.4rem;
  margin: 0;
  color: ${props => props.theme['--text-color'] || '#333'};
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
`;

const CurrentPlanHighlight = styled.span`
  font-weight: 700;
  font-size: 1.5rem;
  color: ${props => props.theme['--primary-color'] || '#d95550'};
  background-color: rgba(217, 85, 80, 0.08);
  padding: 0.2rem 0.6rem;
  border-radius: 4px;
`;

const CurrentPlanDetails = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const CurrentPlanStatus = styled.div`
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const StatusText = styled.span`
  font-weight: 600;
  color: ${props => props.$active ? '#4CAF50' : '#F44336'};
`;

const CurrentPlanExpiry = styled.div`
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const ExpiryDate = styled.span`
  font-weight: 600;
  color: ${props => props.theme['--text-color'] || '#333'};
`;

const CurrentPlanMessage = styled.p`
  margin: 0.5rem 0 0 0;
  font-size: 1rem;
  color: ${props => props.theme['--text-color'] || '#333'};
  font-style: italic;
`;

const CancelSubscriptionSection = styled.div`
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #ececec;
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const CancelSubscriptionText = styled.div`
  color: ${props => props.theme['--text-secondary'] || '#666'};
  font-size: 0.875rem;
  line-height: 1.4;
`;

const TrialWarning = styled.span`
  color: #4CAF50;
  font-weight: 500;
`;

const CancelSubscriptionButton = styled.button`
  background: transparent;
  color: #F44336;
  border: 1px solid #F44336;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  align-self: flex-start;

  &:hover:not(:disabled) {
    background: rgba(244, 67, 54, 0.1);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

export default PremiumPage;
