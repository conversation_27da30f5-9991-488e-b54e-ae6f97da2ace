import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
// 按需导入React Icons以减少bundle大小
import { FaExpand, FaTimes, FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import YouTubeFacade from './YouTubeFacade';
import SEOHead from './SEO/SEOHead';
import { createWebsiteStructuredData, createSoftwareApplicationStructuredData, createOrganizationStructuredData } from '../utils/structuredData';
// import { isAuthenticated } from '../services/authService';
import PageHeader from '../components/shared/PageHeader'; // Add this line
import ErrorBoundary from './ErrorBoundary';

const AppFlowyStyleLandingPage = () => {
  const [activeTab, setActiveTab] = useState('tasks');
  // const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [popupImage, setPopupImage] = useState(null);
  const [currentSlide, setCurrentSlide] = useState(0);
  const sliderTimerRef = useRef(null);

  // AI Project Generator slider images
  const aiSliderImages = [
    "/landing-pics/AI_Project_Generator.webp",
    "/landing-pics/AI-genrated-project-result.webp"
  ];

  // Function to handle image click and show popup
  const handleImageClick = (imageSrc) => {
    setPopupImage(imageSrc);
    // Prevent scrolling when popup is open
    document.body.style.overflow = 'hidden';
  };

  // Function to close popup
  const closePopup = () => {
    setPopupImage(null);
    // Re-enable scrolling
    document.body.style.overflow = 'auto';
  };

  // Function to navigate to next slide
  const nextSlide = () => {
    setCurrentSlide(prevSlide =>
      prevSlide === aiSliderImages.length - 1 ? 0 : prevSlide + 1
    );
  };

  // Function to navigate to previous slide
  const prevSlide = () => {
    setCurrentSlide(prevSlide =>
      prevSlide === 0 ? aiSliderImages.length - 1 : prevSlide - 1
    );
  };

  // State to track if slider is being hovered
  const [isSliderHovered] = useState(false);

  // Auto-slide functionality
  useEffect(() => {
    if (activeTab === 'ai' && !isSliderHovered) {
      sliderTimerRef.current = setInterval(() => {
        nextSlide();
      }, 5000); // Change slide every 5 seconds
    }

    return () => {
      if (sliderTimerRef.current) {
        clearInterval(sliderTimerRef.current);
      }
    };
  }, [activeTab, currentSlide, isSliderHovered]);

  // Reset current slide when tab changes
  useEffect(() => {
    setCurrentSlide(0);
  }, [activeTab]);

  // Add keyboard support for the popup and slider navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Popup escape key handling
      if (e.key === 'Escape' && popupImage) {
        closePopup();
      }

      // Slider navigation with arrow keys when AI tab is active
      if (activeTab === 'ai' && !popupImage) {
        if (e.key === 'ArrowLeft') {
          prevSlide();
        } else if (e.key === 'ArrowRight') {
          nextSlide();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [popupImage, activeTab, prevSlide, nextSlide, closePopup]);

  // Create structured data for the landing page
  const structuredData = [
    createWebsiteStructuredData(),
    createSoftwareApplicationStructuredData(),
    createOrganizationStructuredData()
  ];

  return (
    <Container>
      <SEOHead
        title="The AI Pomodoro Timer That Also Plans Your Projects"
        description="AI Pomo is an intelligent productivity app that uses an AI Pomodoro timer and project generator to help you focus deeply and reach your goals faster."
        keywords="AI pomodoro timer, productivity app, task management, time management, focus timer, AI project generator, GTD system, productivity tools"
        url="https://www.ai-pomo.com"
        image="https://www.ai-pomo.com/og-image.jpg"
        structuredData={structuredData}
      />
      {/* Header Section */}
      <PageHeader /> {/* Replace original Header with PageHeader */}

      {/* Hero Section */}
      <HeroSection>
        <HeroContainer>
          <HeroTitle>
            The AI Pomodoro Timer That Also Plans Your Projects
          </HeroTitle>
          <HeroSubtitle>
            AI Pomo is an intelligent productivity app that uses an AI Pomodoro timer and project generator to help you focus deeply and reach your goals faster.
          </HeroSubtitle>
          <FeaturesHeading>Key Features</FeaturesHeading>
        </HeroContainer>
      </HeroSection>

      {/* Features Tabs */}
      <FeaturesSection id="features">
        <FeaturesTabs role="tablist" aria-label="Product features">
          <FeatureTab
            isActive={activeTab === 'tasks'}
            onClick={() => setActiveTab('tasks')}
            role="tab"
            aria-selected={activeTab === 'tasks'}
            aria-controls="tasks-panel"
            tabIndex={activeTab === 'tasks' ? 0 : -1}
          >
            Pomodoro with Quick Tasks
          </FeatureTab>
          <FeatureTab
            isActive={activeTab === 'pomodoro'}
            onClick={() => setActiveTab('pomodoro')}
            role="tab"
            aria-selected={activeTab === 'pomodoro'}
            aria-controls="pomodoro-panel"
            tabIndex={activeTab === 'pomodoro' ? 0 : -1}
          >
            Project with Pomodoro Focus
          </FeatureTab>
          <FeatureTab
            isActive={activeTab === 'projects'}
            onClick={() => setActiveTab('projects')}
            role="tab"
            aria-selected={activeTab === 'projects'}
            aria-controls="projects-panel"
            tabIndex={activeTab === 'projects' ? 0 : -1}
          >
            Pomodoro Progress Tracking
          </FeatureTab>
          <FeatureTab
            isActive={activeTab === 'ai'}
            onClick={() => setActiveTab('ai')}
            role="tab"
            aria-selected={activeTab === 'ai'}
            aria-controls="ai-panel"
            tabIndex={activeTab === 'ai' ? 0 : -1}
          >
            AI Project Generator
          </FeatureTab>
          <FeatureTab
            isActive={activeTab === 'calendar'}
            onClick={() => setActiveTab('calendar')}
            role="tab"
            aria-selected={activeTab === 'calendar'}
            aria-controls="calendar-panel"
            tabIndex={activeTab === 'calendar' ? 0 : -1}
          >
            Calendar View
          </FeatureTab>
          <FeatureTab
            isActive={activeTab === 'statistics'}
            onClick={() => setActiveTab('statistics')}
            role="tab"
            aria-selected={activeTab === 'statistics'}
            aria-controls="statistics-panel"
            tabIndex={activeTab === 'statistics' ? 0 : -1}
          >
            Statistics View
          </FeatureTab>
        </FeaturesTabs>

        <FeatureContent>
          <div 
            style={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}
            role="tabpanel"
            id={`${activeTab}-panel`}
            aria-labelledby={`${activeTab}-tab`}
          >
            {activeTab === 'ai' && (
              <>
                <FeatureTagline>
                  Transform your ideas into structured projects with AI assistance
                </FeatureTagline>
                <FeatureDescription>
                  Describe your project goals and let AI generate a complete structure with tasks, milestones, and notes - saving you hours of planning time.
                </FeatureDescription>
                <FeatureImageWrapper>
                  <FeatureImage
                    src={aiSliderImages[currentSlide]}
                    alt={`AI Project Generator screenshot ${currentSlide + 1} of ${aiSliderImages.length}`}
                    onClick={() => handleImageClick(aiSliderImages[currentSlide])}
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleImageClick(aiSliderImages[currentSlide]);
                      }
                    }}
                    aria-label="Click to enlarge AI Project Generator screenshot"
                  />
                  <ExpandIcon><FaExpand /></ExpandIcon>

                  <SliderNavigation style={{ position: 'absolute', bottom: '10px', left: '0', right: '0' }}>
                    <SliderButton 
                      onClick={prevSlide}
                      aria-label="Previous slide"
                      type="button"
                    >
                      <FaChevronLeft />
                    </SliderButton>
                    <SliderDots role="tablist" aria-label="Slide navigation">
                      {aiSliderImages.map((_, index) => (
                        <SliderDot
                          key={index}
                          isActive={index === currentSlide}
                          onClick={() => setCurrentSlide(index)}
                          role="tab"
                          aria-selected={index === currentSlide}
                          aria-label={`Go to slide ${index + 1}`}
                          tabIndex={index === currentSlide ? 0 : -1}
                        />
                      ))}
                    </SliderDots>
                    <SliderButton 
                      onClick={nextSlide}
                      aria-label="Next slide"
                      type="button"
                    >
                      <FaChevronRight />
                    </SliderButton>
                  </SliderNavigation>
                  <SliderIndicator>
                    {currentSlide + 1}/{aiSliderImages.length}
                  </SliderIndicator>
                </FeatureImageWrapper>
              </>
            )}
            {activeTab === 'projects' && (
              <>
                <FeatureTagline>
                  Track both time investment and progress with Pomodoro counts
                </FeatureTagline>
                <FeatureDescription>
                  See exactly how much time you've invested in each project with clear Pomodoro counters. Watch your progress grow with every completed focus session, giving you a tangible measure of both effort and achievement.
                </FeatureDescription>
                <FeatureImageWrapper>
                  <FeatureImage
                    src="/landing-pics/open_projects_with_pomodoro_count.webp"
                    alt="Screenshot showing open projects with Pomodoro count tracking feature"
                    onClick={() => handleImageClick("/landing-pics/open_projects_with_pomodoro_count.webp")}
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleImageClick("/landing-pics/open_projects_with_pomodoro_count.webp");
                      }
                    }}
                    aria-label="Click to enlarge projects screenshot"
                  />
                  <ExpandIcon><FaExpand /></ExpandIcon>
                </FeatureImageWrapper>
              </>
            )}
            {activeTab === 'tasks' && (
              <>
                <FeatureTagline>
                  Capture and complete tasks with built-in Pomodoro focus sessions
                </FeatureTagline>
                <FeatureDescription>
                  Quickly add tasks, estimate required Pomodoros, and track your progress - perfect for daily to-dos and standalone tasks that need focused attention with the Pomodoro technique.
                </FeatureDescription>
                <FeatureImageWrapper>
                  <FeatureImage
                    src="/landing-pics/quick-tasks-with-pomodoros.webp"
                    alt="Screenshot showing quick tasks with Pomodoro timer integration"
                    onClick={() => handleImageClick("/landing-pics/quick-tasks-with-pomodoros.webp")}
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleImageClick("/landing-pics/quick-tasks-with-pomodoros.webp");
                      }
                    }}
                    aria-label="Click to enlarge quick tasks screenshot"
                  />
                  <ExpandIcon><FaExpand /></ExpandIcon>
                </FeatureImageWrapper>
              </>
            )}
            {activeTab === 'calendar' && (
              <>
                <FeatureTagline>
                  Visualize your productivity journey with an integrated calendar
                </FeatureTagline>
                <FeatureDescription>
                  See your completed Pomodoros, upcoming deadlines, and milestones in one view - helping you track progress and plan your time effectively.
                </FeatureDescription>
                <FeatureImageWrapper>
                  <FeatureImage
                    src="/landing-pics/calendar_with_pomodoro_task_milestones.webp"
                    alt="Screenshot showing calendar view with Pomodoro tasks and milestones"
                    onClick={() => handleImageClick("/landing-pics/calendar_with_pomodoro_task_milestones.webp")}
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleImageClick("/landing-pics/calendar_with_pomodoro_task_milestones.webp");
                      }
                    }}
                    aria-label="Click to enlarge calendar screenshot"
                  />
                  <ExpandIcon><FaExpand /></ExpandIcon>
                </FeatureImageWrapper>
              </>
            )}
            {activeTab === 'pomodoro' && (
              <>
                <FeatureTagline>
                  Supercharge your projects with integrated Pomodoro focus sessions
                </FeatureTagline>
                <FeatureDescription>
                  Seamlessly blend deep focus into your project workflow with built-in Pomodoro sessions. Enhance concentration, reduce distractions, and make consistent progress while tracking exactly how much focused time you've invested in each project.
                </FeatureDescription>
                <FeatureImageWrapper>
                  <FeatureImage
                    src="/landing-pics/project_with_pomodoros.webp"
                    alt="Screenshot showing project management with integrated Pomodoro focus sessions"
                    onClick={() => handleImageClick("/landing-pics/project_with_pomodoros.webp")}
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleImageClick("/landing-pics/project_with_pomodoros.webp");
                      }
                    }}
                    aria-label="Click to enlarge project screenshot"
                  />
                  <ExpandIcon><FaExpand /></ExpandIcon>
                </FeatureImageWrapper>
              </>
            )}
            {activeTab === 'statistics' && (
              <>
                <FeatureTagline>
                  Track your productivity with detailed statistics
                </FeatureTagline>
                <FeatureDescription>
                  Gain valuable insights into your work patterns with comprehensive statistics. Visualize your focus time, track completed Pomodoros, and analyze your productivity trends over time to optimize your workflow and achieve better results.
                </FeatureDescription>
                <FeatureImageWrapper>
                  <FeatureImage
                    src="/landing-pics/statistics.webp"
                    alt="Screenshot showing detailed productivity statistics and analytics dashboard"
                    onClick={() => handleImageClick("/landing-pics/statistics.webp")}
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleImageClick("/landing-pics/statistics.webp");
                      }
                    }}
                    aria-label="Click to enlarge statistics screenshot"
                  />
                  <ExpandIcon><FaExpand /></ExpandIcon>
                </FeatureImageWrapper>
              </>
            )}
          </div>
        </FeatureContent>
      </FeaturesSection>

      {/* AI Videos Section */}
      <AIVideosSection id="ai-videos">
        <SectionContainer>
          <VideoSectionTitle>AI-Powered Productivity Revolution</VideoSectionTitle>
          <VideoSectionSubtitle>Experience how our intelligent assistant eliminates hours of planning work and transforms your productivity in seconds</VideoSectionSubtitle>

          <VideoGrid>
            <VideoCard>
              <VideoTitle>Turn Ideas Into Complete Projects Instantly</VideoTitle>
              <VideoDescription>
                Watch as our AI transforms your simple concept into a fully structured project with tasks, milestones, and deadlines—all in under 30 seconds. Skip hours of manual setup and jump straight into productive work.
              </VideoDescription>
              <div style={{ position: 'relative', paddingBottom: '56.25%', height: 0, overflow: 'hidden' }}>
                <ErrorBoundary>
                  <YouTubeFacade
                    videoId="CTNq3qtyoDU"
                    title="Turn Ideas Into Complete Projects Instantly"
                  />
                </ErrorBoundary>
              </div>
            </VideoCard>
            {/* You can add more videos here using YouTube component if needed */}
          </VideoGrid>
        </SectionContainer>
      </AIVideosSection>

      {/* CTA Section */}
      <CTASection>
        <CTAContainer>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap' }}>
            <div style={{ flex: '1', minWidth: '200px', textAlign: 'left', marginRight: '1rem' }}>
              <CTATitle>Boost your productivity today</CTATitle>
              <CTASubtitle>Join thousands of focused professionals who've mastered their time</CTASubtitle>
            </div>
            <CTAButtons>
              <PrimaryButton to="/register">Start for free</PrimaryButton>
              <SecondaryButton to="/login">Log in</SecondaryButton>
            </CTAButtons>
          </div>
        </CTAContainer>
      </CTASection>

      {/* Footer */}
      <Footer>
        <FooterContainer>
          <FooterContent>
            <FooterSection>
              <FooterTitle>Product</FooterTitle>
              <FooterLink as={Link} to="/features">Features</FooterLink>
              <FooterLink as={Link} to="/use-cases">Use Cases</FooterLink>
              <FooterLink as={Link} to="/solutions">Solutions</FooterLink>
              <FooterLink as={Link} to="/pricing">Pricing</FooterLink>
            </FooterSection>

            <FooterSection>
              <FooterTitle>Company</FooterTitle>
              <FooterLink as={Link} to="/about">About</FooterLink>
              <FooterLink as={Link} to="/blog">Blog</FooterLink>
              <FooterLink as={Link} to="/contact">Contact</FooterLink>
            </FooterSection>

            <FooterSection>
              <FooterTitle>Resources</FooterTitle>
              <FooterLink href="/api/sitemap.xml">Sitemap</FooterLink>
            </FooterSection>

            <FooterSection>
              <FooterTitle>Legal</FooterTitle>
              <FooterLink as={Link} to="/privacy">Privacy Policy</FooterLink>
              <FooterLink as={Link} to="/terms">Terms of Service</FooterLink>
              <FooterLink as={Link} to="/refund-policy">Refund Policy</FooterLink>
            </FooterSection>

            <FooterSection>
              <FooterTitle>Get Started</FooterTitle>
              <FooterLink as={Link} to="/register">Sign Up Free</FooterLink>
              <FooterLink as={Link} to="/login">Log In</FooterLink>
            </FooterSection>
          </FooterContent>

          <FooterBottom>
            <FooterCopyright>
              Copyright © {new Date().getFullYear()} 🍅 AI Pomo. All rights reserved.
            </FooterCopyright>
            <ProductHuntBadge>
              <a href="https://www.producthunt.com/products/ai-pomo/reviews?utm_source=badge-product_review&utm_medium=badge&utm_source=badge-ai&#0045;pomo" target="_blank" rel="noopener noreferrer">
                <img src="https://api.producthunt.com/widgets/embed-image/v1/product_review.svg?product_id=1066709&theme=light" alt="AI&#0032;Pomo - A&#0032;Pomodoro&#0032;timer&#0032;that&#0032;tracks&#0032;time&#0032;per&#0032;project&#0032;and&#0032;uses&#0032;AI | Product Hunt" style={{width: '250px', height: '54px'}} width="250" height="54" />
              </a>
            </ProductHuntBadge>
          </FooterBottom>
        </FooterContainer>
      </Footer>

      {/* Image Popup/Lightbox */}
      {popupImage && (
        <ImagePopup onClick={closePopup}>
          <PopupContent onClick={(e) => e.stopPropagation()}>
            <PopupImage src={popupImage} alt="Enlarged view" />
            <ClosePopupButton onClick={closePopup}>
              <FaTimes />
            </ClosePopupButton>
            <ZoomIndicator>
              <FaExpand />
              <ZoomText>Click anywhere outside to close</ZoomText>
            </ZoomIndicator>
          </PopupContent>
        </ImagePopup>
      )}
    </Container>
  );
};

// Styled Components
const Container = styled.div`
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: #111827;
  background-color: #ffffff;
  overflow-x: hidden;
`;



const HeroSection = styled.section`
  padding: 7rem 2rem 0;
  text-align: center;
  background-color: #ffffff;

  @media (max-width: 768px) {
    padding: 5rem 1rem 0;
  }
`;

const HeroContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const HeroTitle = styled.h1`
  font-size: 2.75rem;
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 0.75rem;
  background: linear-gradient(to right, #d95550, #eb6b56);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

  @media (max-width: 768px) {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }
`;

const HeroSubtitle = styled.p`
  font-size: 1.25rem;
  color: #4b5563;
  margin-bottom: 1.5rem;
  line-height: 1.4;

  @media (max-width: 768px) {
    font-size: 1rem;
    margin-bottom: 1rem;
  }
`;

const HeroButtons = styled.div`
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
`;

const PrimaryButton = styled(Link)`
  display: inline-block;
  padding: 0.5rem 1.25rem;
  background-color: #d95550;
  color: white;
  text-decoration: none;
  border-radius: 9999px;
  font-weight: 600;
  font-size: 0.95rem;
  transition: background-color 0.2s;

  &:hover {
    background-color: #c73e39;
  }
`;

const SecondaryButton = styled(Link)`
  display: inline-block;
  padding: 0.5rem 1.25rem;
  background-color: white;
  color: #111827;
  text-decoration: none;
  border-radius: 9999px;
  font-weight: 600;
  font-size: 0.95rem;
  border: 1px solid #e5e7eb;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f3f4f6;
  }
`;

const HeroLinks = styled.div`
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  color: #6b7280;
  font-size: 0.875rem;
`;

const HeroLink = styled.a`
  color: #d95550;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
`;

const FeaturesSection = styled.section`
  padding: 0.5rem 2rem 6rem;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;

  @media (max-width: 768px) {
    padding: 0.5rem 1rem 4rem;
  }
`;

const FeaturesHeading = styled.h2`
  text-align: center;
  font-size: 2.25rem;
  font-weight: 800;
  margin-top: 0;
  margin-bottom: 0.75rem;
  color: #111827;
  position: relative;
  z-index: 1;

  &:after {
    content: '';
    position: absolute;
    bottom: -0.4rem;
    left: 50%;
    transform: translateX(-50%);
    width: 5rem;
    height: 0.25rem;
    background: linear-gradient(to right, #d95550, #eb6b56);
    border-radius: 0.125rem;
  }

  @media (max-width: 768px) {
    font-size: 1.85rem;
    margin-top: 0;
  }
`;

const FeaturesTabs = styled.div`
  display: flex;
  justify-content: center;
  gap: 0.4rem;
  margin-top: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  padding: 0.5rem 1rem;
  position: relative;
  width: 100%;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
`;

const FeatureTab = styled.button`
  padding: 0.5rem 1.25rem;
  background-color: transparent;
  border: ${props => props.isActive ? '2px solid #d95550' : '1px solid #e5e7eb'};
  border-radius: 9999px;
  font-size: 0.95rem;
  font-weight: ${props => props.isActive ? '700' : '600'};
  color: ${props => props.isActive ? '#d95550' : '#6b7280'};
  cursor: pointer;
  transition: all 0.25s;
  position: relative;
  margin: 0.15rem;

  &:hover {
    border-color: #d95550;
    color: #d95550;
  }
`;

const FeatureContent = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 600px;
  padding: 1rem;

  @media (max-width: 768px) {
    min-height: 450px;
    padding: 0.5rem;
  }
`;

const FeatureTagline = styled.h3`
  font-size: 1.75rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.75rem;
  text-align: center;
  max-width: 800px;
  background: linear-gradient(to right, #d95550, #eb6b56);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

  @media (max-width: 768px) {
    font-size: 1.35rem;
  }
`;

const FeatureDescription = styled.p`
  font-size: 1.05rem;
  color: #4b5563;
  margin-bottom: 2rem;
  text-align: center;
  max-width: 800px;
  line-height: 1.5;

  @media (max-width: 768px) {
    font-size: 0.95rem;
    margin-bottom: 1.25rem;
  }
`;

const FeatureImageWrapper = styled.div`
  position: relative;
  cursor: pointer;
  border-radius: 0.75rem;
  overflow: hidden;
  max-width: 100%;
  max-height: 700px;
  border: 2px solid #ff8c42;
  box-shadow: 0 6px 16px rgba(255, 140, 66, 0.15);
  transition: all 0.3s ease;

  &:hover {
    img {
      transform: scale(1.02);
    }

    div {
      opacity: 1;
    }
    box-shadow: 0 8px 20px rgba(255, 140, 66, 0.25);
  }

  @media (max-width: 768px) {
    max-height: 350px;
  }
`;

// Slider styled components
const SliderContainer = styled.div`
  position: relative;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
`;

const SliderWrapper = styled.div`
  position: relative;
  width: 100%;
  height: 550px;
  border-radius: 0.75rem;
  overflow: hidden;
  border: 2px solid #ff8c42;
  transition: all 0.3s ease;

  @media (max-width: 768px) {
    height: 350px;
  }
`;

const SliderSlide = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: ${props => props.isActive ? 1 : 0};
  transition: opacity 0.5s ease-in-out;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;

  &:hover {
    div {
      opacity: 1;
    }
  }
`;

const SliderNavigation = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 1.5rem;
  gap: 1rem;
`;

const SliderButton = styled.button`
  background-color: #f8f8f8;
  color: #d95550;
  border: none;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: #d95550;
    color: white;
    transform: scale(1.1);
  }
`;

const SliderDots = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const SliderDot = styled.button`
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background-color: ${props => props.isActive ? '#d95550' : '#e5e7eb'};
  border: none;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${props => props.isActive ? '#d95550' : '#d1d5db'};
    transform: scale(1.2);
  }
`;

const SliderIndicator = styled.div`
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  z-index: 10;
`;

const ExpandIcon = styled.div`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background-color: rgba(255, 255, 255, 0.8);
  color: #111827;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;

  &:hover {
    background-color: rgba(255, 255, 255, 0.9);
  }
`;

const FeatureImage = styled.img`
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 0.5rem;
  transition: transform 0.3s ease;
  cursor: pointer;

  ${SliderSlide} & {
    position: relative;
    z-index: 1;
    width: 100%;
    max-width: 1200px;
    object-fit: contain;
  }

  ${FeatureImageWrapper} &:hover,
  ${SliderSlide}:hover & {
    transform: scale(1.02);
  }
`;



// AI Videos Section Styled Components
const AIVideosSection = styled.section`
  padding: 6rem 2rem;
  background-color: #f9fafb;

  @media (max-width: 768px) {
    padding: 4rem 1rem;
  }
`;

const SectionContainer = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
`;

const VideoSectionTitle = styled.h2`
  font-size: 2.5rem;
  font-weight: 800;
  text-align: center;
  margin-bottom: 1rem;
  background: linear-gradient(to right, #d95550, #eb6b56);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;

  &:after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 5rem;
    height: 0.25rem;
    background: linear-gradient(to right, #d95550, #eb6b56);
    border-radius: 0.125rem;
  }

  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const VideoSectionSubtitle = styled.p`
  font-size: 1.25rem;
  text-align: center;
  margin-bottom: 3rem;
  color: #4b5563;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 768px) {
    font-size: 1.125rem;
    margin-bottom: 2rem;
  }
`;

const VideoGrid = styled.div`
  display: flex;
  flex-direction: column;
  gap: 3rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const VideoCard = styled.div`
  background-color: #ffffff;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s, box-shadow 0.3s;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 2rem;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }
`;

const VideoTitle = styled.h3`
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: #111827;
  position: relative;
  display: inline-block;

  &:after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 2.5rem;
    height: 0.2rem;
    background: linear-gradient(to right, #d95550, #eb6b56);
    border-radius: 0.1rem;
  }
`;

const VideoDescription = styled.p`
  font-size: 1.0625rem;
  color: #4b5563;
  margin-bottom: 1.5rem;
  line-height: 1.6;
`;

const VideoWrapper = styled.div`
  position: relative;
  width: 100%;
  padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;

  iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
  }

  .youtube-thumbnail {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
  }

  .play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 60px;
    color: white;
    cursor: pointer;
    z-index: 1;
    transition: transform 0.3s ease;

    &:hover {
      transform: translate(-50%, -50%) scale(1.1);
    }
  }
`;

const Video = styled.video`
  width: 100%;
  display: block;
  border-radius: 0.5rem;
  max-height: 600px;
  object-fit: contain;
  background-color: #f3f4f6;
`;

const CTASection = styled.section`
  padding: 3rem 2rem;
  background: linear-gradient(to right, #d95550, #eb6b56);
  color: white;
  text-align: center;

  @media (max-width: 768px) {
    padding: 2rem 1rem;
  }
`;

const CTAContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
`;

const CTATitle = styled.h2`
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.75rem;

  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const CTASubtitle = styled.p`
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
  opacity: 0.9;
`;

const CTAButtons = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  flex-shrink: 0;

  @media (max-width: 768px) {
    justify-content: flex-start;
    margin-top: 1rem;
    width: 100%;
  }
`;

const Footer = styled.footer`
  background-color: #111827;
  color: white;
  padding: 3rem 0 1rem;
`;

const FooterContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
`;





const FooterContent = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
`;

const FooterSection = styled.div`
  display: flex;
  flex-direction: column;
`;

const FooterTitle = styled.h4`
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
`;

const FooterLink = styled.a`
  color: #9ca3af;
  text-decoration: none;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  transition: color 0.2s;

  &:hover {
    color: #d95550;
  }
`;

const FooterBottom = styled.div`
  border-top: 1px solid #374151;
  padding-top: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;

  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
  }
`;

const FooterCopyright = styled.p`
  margin: 0;
  font-size: 0.875rem;
  color: #9ca3af;
`;

const ProductHuntBadge = styled.div`
  display: flex;
  align-items: center;

  a {
    display: block;
    transition: opacity 0.2s;

    &:hover {
      opacity: 0.8;
    }
  }

  img {
    display: block;
    max-width: 100%;
    height: auto;
  }

  @media (max-width: 768px) {
    img {
      width: 200px;
      height: auto;
    }
  }
`;

// Popup/Lightbox styled components
const ImagePopup = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  padding: 2rem;
  cursor: zoom-out;
  animation: fadeIn 0.3s ease;

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
`;

const PopupContent = styled.div`
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  cursor: default;
`;

const PopupImage = styled.img`
  display: block;
  max-width: 100%;
  max-height: 90vh;
  object-fit: contain;
`;

const ClosePopupButton = styled.button`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.2s;
  z-index: 2010;

  &:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
  }
`;

const ZoomIndicator = styled.div`
  position: absolute;
  bottom: 1.5rem;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  animation: fadeInUp 0.5s ease;

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translate(-50%, 20px);
    }
    to {
      opacity: 1;
      transform: translate(-50%, 0);
    }
  }
`;

const ZoomText = styled.span`
  font-size: 0.875rem;
  font-weight: 500;
`;

export default AppFlowyStyleLandingPage;
