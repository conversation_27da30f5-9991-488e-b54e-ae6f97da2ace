import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { FaChevronDown, FaClock, FaBrain, FaRobot, FaList, FaTag, FaStopwatch } from 'react-icons/fa';

const ResourcesDropdown = () => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const closeDropdown = () => {
    setIsOpen(false);
  };

  return (
    <DropdownContainer ref={dropdownRef}>
      <DropdownTrigger 
        onClick={toggleDropdown}
        aria-expanded={isOpen}
        aria-haspopup="true"
        aria-label="Resources menu"
        type="button"
      >
        Resources
        <ChevronIcon $isOpen={isOpen}>
          <FaChevronDown />
        </ChevronIcon>
      </DropdownTrigger>

      {isOpen && (
        <DropdownMenu role="menu" aria-label="Resources submenu">
          <DropdownItem as={Link} to="/resources/pomodoro-reimagined-from-kitchen-timer-to-intelligent-focus" onClick={closeDropdown}>
            <ItemIcon>
              <FaClock />
            </ItemIcon>
            <ItemContent>
              <ItemTitle>The Pomodoro Technique</ItemTitle>
              <ItemDescription>Master the fundamentals of focused work sessions</ItemDescription>
            </ItemContent>
          </DropdownItem>

          <DropdownItem as={Link} to="/resources/orchestrating-your-hours-time-management-as-personal-architecture" onClick={closeDropdown}>
            <ItemIcon>
              <FaClock />
            </ItemIcon>
            <ItemContent>
              <ItemTitle>Time Management</ItemTitle>
              <ItemDescription>Strategies for better productivity and organization</ItemDescription>
            </ItemContent>
          </DropdownItem>

          <DropdownItem as={Link} to="/resources/adhd-productivity-redefined-build-your-unique-focus-system" onClick={closeDropdown}>
            <ItemIcon>
              <FaBrain />
            </ItemIcon>
            <ItemContent>
              <ItemTitle>ADHD & Productivity</ItemTitle>
              <ItemDescription>Specialized techniques for ADHD minds</ItemDescription>
            </ItemContent>
          </DropdownItem>

          <DropdownItem as={Link} to="/resources/ai-and-productivity-the-cognitive-partnership-reshaping-work" onClick={closeDropdown}>
            <ItemIcon>
              <FaRobot />
            </ItemIcon>
            <ItemContent>
              <ItemTitle>AI & Productivity</ItemTitle>
              <ItemDescription>Leverage AI tools for enhanced productivity</ItemDescription>
            </ItemContent>
          </DropdownItem>

          <Divider />

          <DropdownItem as={Link} to="/resources/all-articles" onClick={closeDropdown}>
            <ItemIcon>
              <FaList />
            </ItemIcon>
            <ItemContent>
              <ItemTitle>All Articles</ItemTitle>
              <ItemDescription>Browse our complete article library</ItemDescription>
            </ItemContent>
          </DropdownItem>

          <DropdownItem as={Link} to="/tags" onClick={closeDropdown}>
            <ItemIcon>
              <FaTag />
            </ItemIcon>
            <ItemContent>
              <ItemTitle>Browse by Topic</ItemTitle>
              <ItemDescription>Explore articles by tags and categories</ItemDescription>
            </ItemContent>
          </DropdownItem>

          <DropdownItem as={Link} to="/timers" onClick={closeDropdown}>
            <ItemIcon>
              <FaStopwatch />
            </ItemIcon>
            <ItemContent>
              <ItemTitle>Timer Tools</ItemTitle>
              <ItemDescription>Free online timers for productivity and focus</ItemDescription>
            </ItemContent>
          </DropdownItem>
        </DropdownMenu>
      )}
    </DropdownContainer>
  );
};

// Styled Components
const DropdownContainer = styled.div`
  position: relative;
  display: inline-block;
`;

const DropdownTrigger = styled.button`
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #4b5563;
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  padding: 0;
  border-radius: 0.375rem;
  transition: all 0.2s ease;

  &:hover {
    color: #d95550;
  }
`;

const ChevronIcon = styled.span`
  margin-left: 0.5rem;
  transition: transform 0.2s ease;
  transform: ${props => props.$isOpen ? 'rotate(180deg)' : 'rotate(0deg)'};
`;

const DropdownMenu = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 320px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 0.5rem 0;
  margin-top: 0.25rem;

  @media (max-width: 768px) {
    min-width: 280px;
    right: 0;
    left: auto;
  }
`;

const DropdownItem = styled.a`
  display: flex;
  align-items: flex-start;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: #374151;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f9fafb;
    color: #d95550;
  }
`;

const ItemIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background-color: #f3f4f6;
  border-radius: 0.375rem;
  margin-right: 0.75rem;
  flex-shrink: 0;
  color: #6b7280;
  transition: all 0.2s ease;

  ${DropdownItem}:hover & {
    background-color: rgba(217, 85, 80, 0.1);
    color: #d95550;
  }
`;

const ItemContent = styled.div`
  flex: 1;
`;

const ItemTitle = styled.div`
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
`;

const ItemDescription = styled.div`
  font-size: 0.75rem;
  color: #6b7280;
  line-height: 1.3;
`;

const Divider = styled.div`
  height: 1px;
  background-color: #e5e7eb;
  margin: 0.5rem 0;
`;

export default ResourcesDropdown;
