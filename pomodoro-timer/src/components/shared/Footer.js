import React from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';

const Footer = () => {
  return (
    <FooterContainer>
      <FooterWrapper>
        <FooterContent>
          <FooterSection>
            <FooterTitle>Product</FooterTitle>
            <FooterLink as={Link} to="/features">Features</FooterLink>
            <FooterLink as={Link} to="/use-cases">Use Cases</FooterLink>
            <FooterLink as={Link} to="/solutions">Solutions</FooterLink>
            <FooterLink as={Link} to="/pricing">Pricing</FooterLink>
          </FooterSection>

          <FooterSection>
            <FooterTitle>Company</FooterTitle>
            <FooterLink as={Link} to="/about">About</FooterLink>
            <FooterLink as={Link} to="/blog">Blog</FooterLink>
            <FooterLink as={Link} to="/contact">Contact</FooterLink>
          </FooterSection>

          <FooterSection>
            <FooterTitle>Tools</FooterTitle>
            <FooterLink as={Link} to="/timer/25-minutes">25 Min Timer</FooterLink>
            <FooterLink as={Link} to="/timer/5-minutes">5 Min Timer</FooterLink>
            <FooterLink as={Link} to="/timer/10-minutes">10 Min Timer</FooterLink>
            <FooterLink as={Link} to="/timer/30-minutes">30 Min Timer</FooterLink>
          </FooterSection>

          <FooterSection>
            <FooterTitle>Resources</FooterTitle>
            <FooterLink href="/api/sitemap.xml">Sitemap</FooterLink>
          </FooterSection>

          <FooterSection>
            <FooterTitle>Legal</FooterTitle>
            <FooterLink as={Link} to="/privacy">Privacy Policy</FooterLink>
            <FooterLink as={Link} to="/terms">Terms of Service</FooterLink>
            <FooterLink as={Link} to="/refund-policy">Refund Policy</FooterLink>
          </FooterSection>

        </FooterContent>

        <FooterBottom>
          <FooterCopyright>
            Copyright © {new Date().getFullYear()} 🍅 AI Pomo. All rights reserved.
          </FooterCopyright>
          <ProductHuntBadge>
            <a href="https://www.producthunt.com/products/ai-pomo/reviews?utm_source=badge-product_review&utm_medium=badge&utm_source=badge-ai&#0045;pomo" target="_blank" rel="noopener noreferrer">
              <img src="https://api.producthunt.com/widgets/embed-image/v1/product_review.svg?product_id=1066709&theme=light" alt="AI&#0032;Pomo - A&#0032;Pomodoro&#0032;timer&#0032;that&#0032;tracks&#0032;time&#0032;per&#0032;project&#0032;and&#0032;uses&#0032;AI | Product Hunt" style={{width: '250px', height: '54px'}} width="250" height="54" />
            </a>
          </ProductHuntBadge>
        </FooterBottom>
      </FooterWrapper>
    </FooterContainer>
  );
};

// Styled Components
const FooterContainer = styled.footer`
  background-color: #111827;
  color: white;
  padding: 3rem 0 1rem;
`;

const FooterWrapper = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
`;

const FooterContent = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
`;

const FooterSection = styled.div`
  display: flex;
  flex-direction: column;
`;

const FooterTitle = styled.h4`
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
`;

const FooterLink = styled.a`
  color: #9ca3af;
  text-decoration: none;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  transition: color 0.2s;

  &:hover {
    color: #d95550;
  }
`;

const FooterBottom = styled.div`
  border-top: 1px solid #374151;
  padding-top: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;

  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
  }
`;

const FooterCopyright = styled.p`
  margin: 0;
  font-size: 0.875rem;
  color: #9ca3af;
`;

const ProductHuntBadge = styled.div`
  display: flex;
  align-items: center;

  a {
    display: block;
    transition: opacity 0.2s;

    &:hover {
      opacity: 0.8;
    }
  }

  img {
    display: block;
    max-width: 100%;
    height: auto;
  }

  @media (max-width: 768px) {
    img {
      width: 200px;
      height: auto;
    }
  }
`;

export default Footer;
