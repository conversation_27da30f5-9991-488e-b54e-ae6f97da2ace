import React, { useState } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { FaBars, FaTimes, FaGithub } from 'react-icons/fa';
import { isAuthenticated } from '../../services/authService';
import ResourcesDropdown from '../navigation/ResourcesDropdown';

const PageHeader = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  // const userIsAuthenticated = isAuthenticated();

  return (
    <Header>
      <HeaderContainer>
        <Logo to="/">
          <LogoIcon>🍅</LogoIcon>
          <LogoText>AI Pomo</LogoText>
        </Logo>
        <NavLinks isOpen={isMenuOpen}>
          <CloseButton 
            onClick={() => setIsMenuOpen(false)}
            aria-label="Close navigation menu"
            type="button"
          >
            <FaTimes />
          </CloseButton>
          <NavLink as={Link} to="/features">Features</NavLink>
          <NavLink as={Link} to="/use-cases">Use Cases</NavLink>
          <NavLink as={Link} to="/solutions">Solutions</NavLink>
          <ResourcesDropdownWrapper>
            <ResourcesDropdown />
          </ResourcesDropdownWrapper>  
          <GitHubLink 
            href="https://github.com/Hicruben/ai-pomo-free" 
            target="_blank" 
            rel="noopener noreferrer"
            aria-label="View AI Pomo source code on GitHub"
          >
            <FaGithub />
          </GitHubLink>
        </NavLinks>
        <HeaderRight>
          <GitHubLinkText 
            href="https://github.com/Hicruben/ai-pomo-free" 
            target="_blank" 
            rel="noopener noreferrer"
            aria-label="View AI Pomo source code on GitHub"
          >
            <FaGithub />
          </GitHubLinkText>
          <GetStartedButton to="/login" secondary>
            Log in
          </GetStartedButton>
          <GetStartedButton to="/register">
            Start for free
          </GetStartedButton>
          <MobileMenuButton 
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label={isMenuOpen ? "Close navigation menu" : "Open navigation menu"}
            aria-expanded={isMenuOpen}
            type="button"
          >
            <FaBars />
          </MobileMenuButton>
        </HeaderRight>
      </HeaderContainer>
    </Header>
  );
};

// Styled Components
const Header = styled.header`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(5px);
  z-index: 1000;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
`;

const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem;

  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const Logo = styled(Link)`
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #111827;
  font-weight: 700;
  font-size: 1.5rem;
`;

const LogoIcon = styled.span`
  font-size: 1.8rem;
  margin-right: 0.5rem;
`;

const LogoText = styled.span`
  font-weight: 700;
`;

const NavLinks = styled.nav`
  display: flex;
  gap: 2rem;

  @media (max-width: 768px) {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    width: 250px;
    height: 100vh; /* Add this line */
    flex-direction: column;
    background-color: white;
    padding: 5rem 2rem 2rem;
    transform: ${props => props.isOpen ? 'translateX(0)' : 'translateX(100%)'};
    transition: transform 0.3s ease;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    z-index: 1001;
  }
`;

const NavLink = styled.a`
  color: #4b5563;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;

  &:hover {
    color: #d95550;
  }
`;

const CloseButton = styled.button`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #4b5563;
  display: none;

  @media (max-width: 768px) {
    display: block;
  }
`;

const HeaderRight = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;

  @media (max-width: 768px) {
    gap: 0.5rem; // 减小移动端按钮之间的间距
  }
`;

const BlogLink = styled(Link)`
  display: inline-block;
  padding: 0.5rem 1rem;
  color: #4b5563;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;

  &:hover {
    color: #d95550;
  }

  @media (max-width: 768px) {
    display: none;
  }
`;

const GetStartedButton = styled(Link)`
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: ${props => props.secondary ? 'white' : '#d95550'};
  color: ${props => props.secondary ? '#111827' : 'white'};
  text-decoration: none;
  border-radius: 9999px;
  font-weight: 600;
  border: ${props => props.secondary ? '1px solid #e5e7eb' : 'none'};
  transition: all 0.2s;
  margin-left: ${props => props.secondary ? '0' : '0.5rem'};

  @media (max-width: 768px) {
    padding: 0.4rem 0.8rem; // 调整移动端按钮padding
    font-size: 0.9rem; // 调整移动端按钮字体大小
    margin-left: ${props => props.secondary ? '0' : '0.25rem'}; // 调整移动端按钮间距
  }

  &:hover {
    background-color: ${props => props.secondary ? '#f3f4f6' : '#c73e39'};
  }
`;

const MobileMenuButton = styled.button`
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #4b5563;

  @media (max-width: 768px) {
    display: block;
  }
`;

const GitHubLink = styled.a`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4b5563;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;
  padding: 0.5rem 0;

  &:hover {
    color: #d95550;
  }

  svg {
    font-size: 1.1rem;
  }

  @media (min-width: 769px) {
    display: none;
  }
`;

const GitHubLinkDesktop = styled.a`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  color: #111827;
  text-decoration: none;
  border-radius: 50%;
  transition: all 0.2s;
  background-color: #f8fafc;
  border: 2px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &:hover {
    color: #ffffff;
    background-color: #d95550;
    border-color: #d95550;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(217, 85, 80, 0.3);
  }

  svg {
    font-size: 1.2rem;
  }

  @media (max-width: 768px) {
    display: none;
  }
`;

const GitHubLinkText = styled.a`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  color: #4b5563;
  text-decoration: none;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.2s;
  border: 1px solid #e5e7eb;
  background-color: #ffffff;

  &:hover {
    color: #d95550;
    border-color: #d95550;
    background-color: rgba(217, 85, 80, 0.05);
  }

  svg {
    font-size: 1rem !important;
    display: inline-block !important;
    width: 1rem !important;
    height: 1rem !important;
    fill: currentColor !important;
  }

  span {
    font-size: 0.9rem;
  }

  @media (max-width: 768px) {
    span {
      display: none;
    }
    padding: 0.4rem; // 调整移动端 GitHub 按钮 padding
    font-size: 0.9rem; // 调整移动端 GitHub 按钮图标大小
  }
`;

const ResourcesDropdownWrapper = styled.div`
  @media (max-width: 768px) {
    width: 100%;

    /* Make dropdown full width on mobile */
    & > div {
      width: 100%;
    }

    /* Adjust dropdown menu positioning on mobile */
    & > div > div:last-child {
      position: static;
      width: 100%;
      box-shadow: none;
      border: none;
      background: transparent;
      padding: 0;
      margin: 0.5rem 0;
    }
  }
`;

export default PageHeader;
