# SocialShare Component

A reusable React component for social media sharing functionality that can be used across blog articles and pillar pages.

## Features

- **Multiple Platforms**: Twitter/X, Facebook, LinkedIn, WhatsApp
- **Copy to Clipboard**: Direct link copying with visual feedback
- **Icon-Only Design**: Clean circular icon buttons without text labels
- **Responsive Design**: Adapts to mobile and desktop layouts
- **Centered Layout**: Positioned under article titles for optimal visibility
- **Proper URL Encoding**: Handles special characters in titles and content
- **Hashtag Support**: Automatically includes tags as hashtags for Twitter

## Usage

### Basic Usage

```jsx
import SocialShare from '../shared/SocialShare';

<SocialShare
  title="Article Title"
  excerpt="Article excerpt or description"
  url="https://ai-pomo.com/blog/article-slug"
  tags={['productivity', 'pomodoro', 'time-management']}
/>
```

## Props

| Prop | Type | Required | Default | Description |
|------|------|----------|---------|-------------|
| `title` | string | Yes | - | The title of the content to share |
| `excerpt` | string | Yes | - | The description/excerpt to share |
| `url` | string | Yes | - | The full URL to share |
| `tags` | array | No | `[]` | Array of tags (used as hashtags for Twitter) |
| `className` | string | No | - | Additional CSS class for styling |

## Implementation Details

### Social Media URLs

- **Twitter**: Includes text, URL, and hashtags (max 3 tags)
- **Facebook**: Shares URL with quote text
- **LinkedIn**: Shares URL with title and summary
- **WhatsApp**: Combines title, excerpt, and URL in message

### Copy to Clipboard

- Uses modern `navigator.clipboard.writeText()` API
- Falls back to legacy `document.execCommand('copy')` for older browsers
- Shows visual feedback with checkmark icon for 2 seconds

### Responsive Behavior

- **Desktop**: Horizontal flex layout with 40px circular buttons
- **Mobile**: Smaller 36px buttons with optimized spacing

## Styling

The component uses styled-components with:
- Circular icon-only buttons (40px desktop, 36px mobile)
- Platform-specific brand colors
- Hover effects with scale and elevation
- Centered layout under article titles
- Smooth transitions and animations
- Mobile-optimized touch targets

## Integration Examples

### Blog Post Detail Page

```jsx
// In BlogPostDetail.js
<SocialShare
  title={post.title}
  excerpt={post.excerpt}
  url={`https://ai-pomo.com/blog/${post.slug}`}
  tags={post.tags || []}
/>
```

### Pillar Page

```jsx
// In PillarPage.js
<SocialShare
  title={pillarPage.title}
  excerpt={pillarPage.excerpt}
  url={`https://ai-pomo.com/resources/${pillarPage.slug}`}
  tags={pillarPage.category ? [pillarPage.category] : []}
/>
```



## Browser Compatibility

- Modern browsers: Full functionality with Clipboard API
- Legacy browsers: Fallback copy functionality
- Mobile browsers: Optimized touch interactions
- All major social media platforms supported

## Security Considerations

- All URLs are properly encoded to prevent XSS
- External links open in new tabs with `noopener noreferrer`
- No sensitive data is exposed in share URLs
- Clipboard access requires user interaction (security requirement)

## Performance

- Lightweight component with minimal dependencies
- CSS-in-JS with styled-components for optimal performance
- Lazy loading of social media windows
- No external API calls or tracking
