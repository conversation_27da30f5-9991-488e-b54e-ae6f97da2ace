import React, { useState } from 'react';
import styled from 'styled-components';
import { FaTwitter, FaFacebook, FaLinkedin, FaWhatsapp, FaLink, FaCheck } from 'react-icons/fa';
import { generateShareUrls, copyToClipboard, openShareWindow } from '../../utils/socialShareUtils';

const SocialShare = ({
  title,
  excerpt,
  url,
  tags = [],
  className
}) => {
  const [copied, setCopied] = useState(false);

  // Generate share URLs using utility function
  const shareUrls = generateShareUrls({ title, excerpt, url, tags });

  // Handle copy to clipboard
  const handleCopyToClipboard = async () => {
    const success = await copyToClipboard(url);
    if (success) {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  // Handle social media sharing
  const handleShare = (platform) => {
    openShareWindow(shareUrls[platform], platform);
  };

  return (
    <ShareContainer className={className}>
      <ShareButton
        onClick={() => handleShare('twitter')}
        $platform="twitter"
        title="Share on Twitter"
      >
        <FaTwitter />
      </ShareButton>

      <ShareButton
        onClick={() => handleShare('facebook')}
        $platform="facebook"
        title="Share on Facebook"
      >
        <FaFacebook />
      </ShareButton>

      <ShareButton
        onClick={() => handleShare('linkedin')}
        $platform="linkedin"
        title="Share on LinkedIn"
      >
        <FaLinkedin />
      </ShareButton>

      <ShareButton
        onClick={() => handleShare('whatsapp')}
        $platform="whatsapp"
        title="Share on WhatsApp"
      >
        <FaWhatsapp />
      </ShareButton>

      <ShareButton
        onClick={handleCopyToClipboard}
        $platform="copy"
        title="Copy link"
      >
        {copied ? <FaCheck /> : <FaLink />}
      </ShareButton>
    </ShareContainer>
  );
};

// Styled Components
const ShareContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin: 1.5rem 0;

  @media (max-width: 768px) {
    gap: 0.5rem;
    margin: 1rem 0;
  }
`;

const ShareButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;

  ${props => {
    switch (props.$platform) {
      case 'twitter':
        return `
          background: #1da1f2;
          color: white;
          &:hover {
            background: #1a91da;
            transform: translateY(-2px) scale(1.1);
            box-shadow: 0 4px 12px rgba(29, 161, 242, 0.4);
          }
        `;
      case 'facebook':
        return `
          background: #4267b2;
          color: white;
          &:hover {
            background: #365899;
            transform: translateY(-2px) scale(1.1);
            box-shadow: 0 4px 12px rgba(66, 103, 178, 0.4);
          }
        `;
      case 'linkedin':
        return `
          background: #0077b5;
          color: white;
          &:hover {
            background: #005885;
            transform: translateY(-2px) scale(1.1);
            box-shadow: 0 4px 12px rgba(0, 119, 181, 0.4);
          }
        `;
      case 'whatsapp':
        return `
          background: #25d366;
          color: white;
          &:hover {
            background: #20ba5a;
            transform: translateY(-2px) scale(1.1);
            box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
          }
        `;
      case 'copy':
        return `
          background: #6c757d;
          color: white;
          &:hover {
            background: #5a6268;
            transform: translateY(-2px) scale(1.1);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
          }
        `;
      default:
        return `
          background: #f8f9fa;
          color: #495057;
          border: 1px solid #dee2e6;
          &:hover {
            background: #e9ecef;
            transform: translateY(-2px) scale(1.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
        `;
    }
  }}

  @media (max-width: 768px) {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }
`;

export default SocialShare;
