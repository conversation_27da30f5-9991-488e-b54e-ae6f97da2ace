import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaCreditCard } from 'react-icons/fa';

const PaddleCheckout = ({
  planType,
  onSuccess,
  onError,
  onCancel,
  isOpen,
  onClose
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Initialize Paddle when component mounts
  useEffect(() => {
    const initializePaddle = async () => {
      try {
        // Load Paddle.js script if not already loaded
        if (!window.Paddle) {
          const script = document.createElement('script');
          script.src = 'https://cdn.paddle.com/paddle/v2/paddle.js';
          script.async = true;
          
          script.onload = () => {
            if (window.Paddle) {
              // Initialize Paddle with environment and client token
              const environment = process.env.REACT_APP_PADDLE_ENVIRONMENT || 'sandbox';
              const clientToken = process.env.REACT_APP_PADDLE_CLIENT_TOKEN;
              
              if (clientToken) {
                // Paddle v2 initialization
                window.Paddle.Initialize({
                  token: clientToken
                });
                // Set environment separately if needed
                if (environment === 'sandbox' && window.Paddle.Environment) {
                  window.Paddle.Environment.set('sandbox');
                }
              } else {
                // Fallback for old initialization method
                if (window.Paddle.Environment) {
                  window.Paddle.Environment.set(environment);
                }
                const vendorId = process.env.REACT_APP_PADDLE_VENDOR_ID;
                if (vendorId && window.Paddle.Setup) {
                  window.Paddle.Setup({ vendor: vendorId });
                }
              }
              
              console.log('Paddle initialized successfully');
            }
          };
          
          script.onerror = () => {
            console.error('Failed to load Paddle.js');
            setError('Failed to load payment system');
          };
          
          document.head.appendChild(script);
        }
      } catch (error) {
        console.error('Error initializing Paddle:', error);
        setError('Failed to initialize payment system');
      }
    };

    if (isOpen) {
      initializePaddle();
    }
  }, [isOpen]);

  const handleCheckout = async () => {
    if (!planType) {
      setError('Please select a plan first');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Create checkout session with backend
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Please log in to continue');
      }

      const response = await fetch('/api/paddle/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ planType }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to create checkout session');
      }

      // Check if we should use overlay checkout or URL redirect
      if (data.useOverlay && data.paddleData) {
        if (!window.Paddle) {
          throw new Error('Paddle.js not loaded. Please refresh the page and try again.');
        }
        
        console.log('Opening Paddle checkout with data:', data.paddleData);
        
        // Use Paddle.js overlay checkout
        try {
          // For Paddle v2
          if (window.Paddle.Checkout && window.Paddle.Checkout.open) {
            // Get user email from JWT token or localStorage
            let userEmail = localStorage.getItem('userEmail') || localStorage.getItem('email');
            
            // Try to extract email from JWT token if not found in localStorage
            if (!userEmail) {
              const token = localStorage.getItem('token');
              if (token) {
                try {
                  const payload = JSON.parse(atob(token.split('.')[1]));
                  userEmail = payload.email || payload.user?.email;
                } catch (err) {
                  console.warn('Failed to parse JWT token:', err);
                }
              }
            }
            
            if (!userEmail) {
              throw new Error('Please log in to continue with checkout');
            }
            
            // Configuration for subscription checkout
            const checkoutConfig = {
              items: [{
                priceId: data.paddleData.priceId,
                quantity: 1
              }],
              customer: {
                email: userEmail
              },
              settings: {
                successUrl: data.paddleData.successUrl,
                allowLogout: false
              },
              customData: data.paddleData.customData
            };

            // Add trial period if this is a subscription
            if (data.isSubscription && data.paddleData.trialDays) {
              checkoutConfig.discountId = null; // Let Paddle handle trial
              console.log('Setting up subscription with trial period:', data.paddleData.trialDays, 'days');
            }

            window.Paddle.Checkout.open(checkoutConfig);
          } else {
            // Fallback for older Paddle versions
            window.Paddle.Checkout.open({
              product: data.paddleData.priceId,
              email: localStorage.getItem('userEmail') || '',
              successCallback: (checkoutData) => {
                console.log('Payment successful:', checkoutData);
                setIsLoading(false);
                if (onSuccess) {
                  onSuccess(checkoutData);
                }
              },
              closeCallback: (checkoutData) => {
                console.log('Checkout closed:', checkoutData);
                setIsLoading(false);
                if (checkoutData && checkoutData.checkout && checkoutData.checkout.completed) {
                  // Payment was completed
                  if (onSuccess) {
                    onSuccess(checkoutData);
                  }
                } else {
                  // Payment was cancelled or closed
                  if (onCancel) {
                    onCancel();
                  }
                }
              }
            });
          }
          setIsLoading(false);
        } catch (paddleError) {
          console.error('Paddle checkout error:', paddleError);
          console.error('Paddle object:', window.Paddle);
          console.error('Paddle data:', data.paddleData);
          throw new Error(`Failed to open checkout: ${paddleError.message || 'Unknown error'}`);
        }
      } else if (data.checkoutUrl) {
        // Use URL redirect
        if (window.Paddle) {
          window.Paddle.Checkout.open({
            url: data.checkoutUrl,
            successCallback: (checkoutData) => {
              console.log('Payment successful:', checkoutData);
              setIsLoading(false);
              if (onSuccess) {
                onSuccess(checkoutData);
              }
            },
            closeCallback: (checkoutData) => {
              console.log('Checkout closed:', checkoutData);
              setIsLoading(false);
              if (checkoutData && checkoutData.checkout && checkoutData.checkout.completed) {
                // Payment was completed
                if (onSuccess) {
                  onSuccess(checkoutData);
                }
              } else {
                // Payment was cancelled or closed
                if (onCancel) {
                  onCancel();
                }
              }
            }
          });
        } else {
          // Fallback: redirect to checkout URL
          window.location.href = data.checkoutUrl;
        }
      } else {
        throw new Error('No checkout URL or product ID received');
      }

    } catch (error) {
      console.error('Checkout error:', error);
      setError(error.message);
      setIsLoading(false);
      if (onError) {
        onError(error);
      }
    }
  };

  if (!isOpen) {
    return null;
  }

  const planDetails = {
    monthly: { name: 'Monthly Plan', price: '$8', period: '/month', trial: '7-day free trial' },
    yearly: { name: 'Yearly Plan', price: '$60', period: '/year', trial: '7-day free trial' },
    lifetime: { name: 'Lifetime Plan', price: '$100', period: 'one-time' }
  };

  const plan = planDetails[planType];

  return (
    <ModalOverlay>
      <ModalContent>
        <ModalHeader>
          <h2>Complete Your Purchase</h2>
          <CloseButton onClick={onClose}>×</CloseButton>
        </ModalHeader>

        <ModalBody>
          {plan && (
            <PlanSummary>
              <h3>{plan.name}</h3>
              <PriceDisplay>
                <Price>{plan.price}</Price>
                <Period>{plan.period}</Period>
              </PriceDisplay>
              {plan.trial && (
                <TrialInfo>{plan.trial}</TrialInfo>
              )}
            </PlanSummary>
          )}

          {error && (
            <ErrorMessage>
              {error}
            </ErrorMessage>
          )}

          <CheckoutButton
            onClick={handleCheckout}
            disabled={isLoading}
            $isLoading={isLoading}
          >
            {isLoading ? (
              <>
                <FaSpinner className="spinner" />
                <span>Processing...</span>
              </>
            ) : (
              <>
                <FaCreditCard />
                <span>Continue to Payment</span>
              </>
            )}
          </CheckoutButton>

          <SecurityNote>
            <FaCheck />
            <span>Secure payment powered by Paddle</span>
          </SecurityNote>
        </ModalBody>
      </ModalContent>
    </ModalOverlay>
  );
};

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: ${props => props.theme['--card-bg'] || '#fff'};
  border-radius: 12px;
  width: 90%;
  max-width: 400px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #eee;

  h2 {
    margin: 0;
    font-size: 1.25rem;
    color: ${props => props.theme['--text-color'] || '#333'};
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: ${props => props.theme['--text-secondary'] || '#666'};
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;

  &:hover {
    background-color: ${props => props.theme['--bg-color'] || '#f5f5f5'};
    color: ${props => props.theme['--text-color'] || '#333'};
  }
`;

const ModalBody = styled.div`
  padding: 1.5rem;
`;

const PlanSummary = styled.div`
  text-align: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background-color: ${props => props.theme['--bg-color'] || '#f5f5f5'};
  border-radius: 8px;

  h3 {
    margin: 0 0 0.5rem 0;
    color: ${props => props.theme['--text-color'] || '#333'};
  }
`;

const PriceDisplay = styled.div`
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 0.25rem;
`;

const Price = styled.span`
  font-size: 2rem;
  font-weight: 700;
  color: ${props => props.theme['--primary-color'] || '#1a73e8'};
`;

const Period = styled.span`
  font-size: 0.875rem;
  color: ${props => props.theme['--text-secondary'] || '#666'};
`;

const TrialInfo = styled.div`
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 600;
  text-align: center;
`;

const ErrorMessage = styled.div`
  color: #f44336;
  background-color: rgba(244, 67, 54, 0.1);
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  font-size: 0.875rem;
`;

const CheckoutButton = styled.button`
  width: 100%;
  padding: 1rem;
  background-color: ${props => props.$isLoading ? '#ccc' : '#1a73e8'};
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: ${props => props.$isLoading ? 'not-allowed' : 'pointer'};
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: background-color 0.2s;
  margin-bottom: 1rem;

  &:hover {
    background-color: ${props => props.$isLoading ? '#ccc' : '#1565C0'};
  }

  .spinner {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const SecurityNote = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: ${props => props.theme['--text-secondary'] || '#666'};

  svg {
    color: #4CAF50;
  }
`;

export default PaddleCheckout;