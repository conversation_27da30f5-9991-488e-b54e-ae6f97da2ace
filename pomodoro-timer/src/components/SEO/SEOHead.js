import React from 'react';
import { Helmet } from 'react-helmet-async';

const SEOHead = ({
  title = 'AI Pomo - AI-Enhanced Pomodoro Timer',
  description = 'AI Pomo combines the Pomodoro technique with AI-powered task management to help you focus, track progress, and achieve your goals efficiently.',
  keywords = 'pomodoro timer, AI productivity, task management, time management, focus timer, productivity app, GTD system',
  image = 'https://www.ai-pomo.com/ai-pomo.png',
  url = 'https://www.ai-pomo.com',
  type = 'website',
  author = 'AI Pomo Team',
  publishedTime,
  modifiedTime,
  section,
  tags = [],
  canonical,
  noindex = false,
  structuredData,
  prev,
  next,
  robots
}) => {
  const fullTitle = title.includes('AI Pomo') ? title : `${title} | AI Pomo`;
  
  // Ensure all URLs use www version
  const normalizeUrl = (inputUrl) => {
    if (!inputUrl) return 'https://www.ai-pomo.com';
    return inputUrl.replace('https://www.ai-pomo.com', 'https://www.ai-pomo.com');
  };
  
  const canonicalUrl = canonical ? normalizeUrl(canonical) : normalizeUrl(url);

  // Ensure image URL is absolute and uses www version
  const absoluteImageUrl = image && image.startsWith('http') 
    ? normalizeUrl(image) 
    : `https://www.ai-pomo.com${image || '/ai-pomo.png'}`;

  // Truncate description for social media (recommended max 160 chars for Facebook)
  const socialDescription = description.length > 160 ? description.substring(0, 157) + '...' : description;

  // Debug logging in development
  if (process.env.NODE_ENV === 'development') {
    console.log('SEOHead Debug:', {
      title: fullTitle,
      description: socialDescription,
      image: absoluteImageUrl,
      url: canonicalUrl,
      type
    });
  }

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content={author} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={canonicalUrl} />
      
      {/* Robots */}
      {noindex && <meta name="robots" content="noindex,nofollow" />}
      {robots && <meta name="robots" content={robots} />}
      
      {/* Pagination */}
      {prev && <link rel="prev" href={prev} />}
      {next && <link rel="next" href={next} />}
      
      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={socialDescription} />
      <meta property="og:image" content={absoluteImageUrl} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:type" content="image/jpeg" />
      <meta property="og:image:alt" content={fullTitle} />
      <meta property="og:url" content={canonicalUrl} />
      <meta property="og:site_name" content="AI Pomo" />
      <meta property="og:locale" content="en_US" />
      
      {/* Article specific Open Graph tags */}
      {type === 'article' && (
        <>
          {publishedTime && <meta property="article:published_time" content={publishedTime} />}
          {modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
          {author && <meta property="article:author" content={author} />}
          {section && <meta property="article:section" content={section} />}
          {tags.map((tag, index) => (
            <meta key={index} property="article:tag" content={tag} />
          ))}
        </>
      )}
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={socialDescription} />
      <meta name="twitter:image" content={absoluteImageUrl} />
      <meta name="twitter:image:alt" content={fullTitle} />
      <meta name="twitter:url" content={canonicalUrl} />
      <meta name="twitter:site" content="@aipomo" />
      <meta name="twitter:creator" content="@aipomo" />
      <meta name="twitter:domain" content="www.ai-pomo.com" />

      {/* LinkedIn specific tags */}
      <meta property="linkedin:owner" content="AI Pomo" />

      {/* Additional Meta Tags */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="language" content="English" />
      <meta name="revisit-after" content="7 days" />
      
      {/* Structured Data */}
      {structuredData && (
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      )}
    </Helmet>
  );
};

export default SEOHead;
