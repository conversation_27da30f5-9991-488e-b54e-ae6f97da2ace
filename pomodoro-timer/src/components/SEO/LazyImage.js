import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';

// Lazy loading image component for better performance
const LazyImage = ({ 
  src, 
  alt, 
  placeholder = '/placeholder.jpg',
  className,
  style,
  onClick,
  ...props 
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef();

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const handleLoad = () => {
    setIsLoaded(true);
  };

  const handleError = () => {
    setHasError(true);
    setIsLoaded(true);
  };

  return (
    <ImageContainer ref={imgRef} className={className} style={style} onClick={onClick}>
      {!isInView && (
        <PlaceholderImage src={placeholder} alt="" />
      )}
      
      {isInView && (
        <Image
          src={hasError ? placeholder : src}
          alt={alt}
          onLoad={handleLoad}
          onError={handleError}
          $isLoaded={isLoaded}
          {...props}
        />
      )}
      
      {!isLoaded && isInView && (
        <LoadingOverlay>
          <LoadingSpinner />
        </LoadingOverlay>
      )}
    </ImageContainer>
  );
};

// Optimized image component with WebP support
export const OptimizedImage = ({ 
  src, 
  alt, 
  webpSrc,
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  ...props 
}) => {
  return (
    <picture>
      {webpSrc && <source srcSet={webpSrc} type="image/webp" sizes={sizes} />}
      <LazyImage src={src} alt={alt} {...props} />
    </picture>
  );
};

// Critical CSS inlining component
export const CriticalCSS = ({ css }) => {
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = css;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, [css]);

  return null;
};

// Preload component for critical resources
export const Preload = ({ href, as, type, crossorigin }) => {
  useEffect(() => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = as;
    if (type) link.type = type;
    if (crossorigin) link.crossOrigin = crossorigin;
    
    document.head.appendChild(link);

    return () => {
      if (document.head.contains(link)) {
        document.head.removeChild(link);
      }
    };
  }, [href, as, type, crossorigin]);

  return null;
};

// DNS prefetch component
export const DNSPrefetch = ({ domains }) => {
  useEffect(() => {
    const links = domains.map(domain => {
      const link = document.createElement('link');
      link.rel = 'dns-prefetch';
      link.href = domain;
      document.head.appendChild(link);
      return link;
    });

    return () => {
      links.forEach(link => {
        if (document.head.contains(link)) {
          document.head.removeChild(link);
        }
      });
    };
  }, [domains]);

  return null;
};

// Resource hints component
export const ResourceHints = () => {
  return (
    <>
      <DNSPrefetch domains={[
        'https://fonts.googleapis.com',
        'https://fonts.gstatic.com',
        'https://images.unsplash.com',
        'https://api.deepseek.com'
      ]} />
      <Preload href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossorigin="anonymous" />
    </>
  );
};

// Styled components
const ImageContainer = styled.div`
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;
`;

const Image = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
  opacity: ${props => props.$isLoaded ? 1 : 0};
`;

const PlaceholderImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: blur(5px);
  opacity: 0.7;
`;

const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
`;

const LoadingSpinner = styled.div`
  width: 24px;
  height: 24px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #d95550;
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

export default LazyImage;
