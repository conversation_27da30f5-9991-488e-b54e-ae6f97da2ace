import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';

// Component for SEO-optimized internal linking
const InternalLinks = ({ currentPage, category, tags = [] }) => {
  const getRelevantLinks = () => {
    const links = [];

    // Always include main pages
    if (currentPage !== 'home') {
      links.push({
        url: '/',
        text: 'AI Pomo - AI-Enhanced Pomodoro Timer',
        description: 'Boost your productivity with our AI-powered time management tool'
      });
    }

    if (currentPage !== 'blog') {
      links.push({
        url: '/blog',
        text: 'Productivity Blog',
        description: 'Tips and strategies for better time management'
      });
    }

    // Category-specific links
    if (category && currentPage !== 'blog-category') {
      links.push({
        url: `/blog?category=${category}`,
        text: `${category.charAt(0).toUpperCase() + category.slice(1)} Articles`,
        description: `Explore more ${category} content`
      });
    }

    // Add related category links based on current category
    const categoryRelations = {
      'productivity': ['time-management', 'focus-techniques'],
      'time-management': ['productivity', 'pomodoro-technique'],
      'pomodoro-technique': ['time-management', 'focus-techniques'],
      'focus-techniques': ['productivity', 'pomodoro-technique'],
      'ai-tools': ['productivity', 'automation'],
      'automation': ['ai-tools', 'productivity']
    };

    if (category && categoryRelations[category]) {
      categoryRelations[category].forEach(relatedCategory => {
        links.push({
          url: `/blog?category=${relatedCategory}`,
          text: `${relatedCategory.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())} Articles`,
          description: `Discover ${relatedCategory.replace('-', ' ')} insights`
        });
      });
    }

    // Add app-related links for blog pages
    if (currentPage.startsWith('blog')) {
      links.push({
        url: '/register',
        text: 'Try AI Pomo Free',
        description: 'Start your productivity journey with our AI-enhanced Pomodoro timer'
      });
    }

    return links.slice(0, 6); // Limit to 6 links to avoid over-optimization
  };

  const relevantLinks = getRelevantLinks();

  if (relevantLinks.length === 0) return null;

  return (
    <LinksContainer>
      <LinksTitle>Related Resources</LinksTitle>
      <LinksList>
        {relevantLinks.map((link, index) => (
          <LinkItem key={index}>
            <InternalLink to={link.url}>
              {link.text}
            </InternalLink>
            <LinkDescription>{link.description}</LinkDescription>
          </LinkItem>
        ))}
      </LinksList>
    </LinksContainer>
  );
};

// Breadcrumb component for better navigation and SEO
export const Breadcrumbs = ({ items }) => {
  if (!items || items.length <= 1) return null;

  return (
    <BreadcrumbContainer>
      {items.map((item, index) => (
        <BreadcrumbItem key={index}>
          {index < items.length - 1 ? (
            <>
              <BreadcrumbLink to={item.url}>{item.name}</BreadcrumbLink>
              <BreadcrumbSeparator>/</BreadcrumbSeparator>
            </>
          ) : (
            <BreadcrumbCurrent>{item.name}</BreadcrumbCurrent>
          )}
        </BreadcrumbItem>
      ))}
    </BreadcrumbContainer>
  );
};

// Related posts component for blog articles
export const RelatedPosts = ({ posts, currentPostId }) => {
  const filteredPosts = posts.filter(post => post._id !== currentPostId).slice(0, 3);

  if (filteredPosts.length === 0) return null;

  return (
    <RelatedContainer>
      <RelatedTitle>You Might Also Like</RelatedTitle>
      <RelatedGrid>
        {filteredPosts.map(post => (
          <RelatedCard key={post._id}>
            <RelatedLink to={`/blog/${post.slug}`}>
              <RelatedImage src={post.coverImage} alt={post.title} />
              <RelatedContent>
                <RelatedPostTitle>{post.title}</RelatedPostTitle>
                <RelatedExcerpt>{post.excerpt}</RelatedExcerpt>
              </RelatedContent>
            </RelatedLink>
          </RelatedCard>
        ))}
      </RelatedGrid>
    </RelatedContainer>
  );
};

// Styled components
const LinksContainer = styled.aside`
  background-color: ${props => props.theme['--card-bg'] || '#f8f9fa'};
  border-radius: 8px;
  padding: 1.5rem;
  margin: 2rem 0;
  border-left: 4px solid ${props => props.theme['--primary-color'] || '#d95550'};
`;

const LinksTitle = styled.h3`
  font-size: 1.1rem;
  margin-bottom: 1rem;
  color: ${props => props.theme['--text-color'] || '#333'};
`;

const LinksList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
`;

const LinkItem = styled.li`
  margin-bottom: 0.75rem;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const InternalLink = styled(Link)`
  color: ${props => props.theme['--primary-color'] || '#d95550'};
  text-decoration: none;
  font-weight: 500;
  display: block;
  margin-bottom: 0.25rem;
  
  &:hover {
    text-decoration: underline;
  }
`;

const LinkDescription = styled.p`
  font-size: 0.9rem;
  color: ${props => props.theme['--text-secondary'] || '#666'};
  margin: 0;
`;

const BreadcrumbContainer = styled.nav`
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
`;

const BreadcrumbItem = styled.span`
  display: flex;
  align-items: center;
`;

const BreadcrumbLink = styled(Link)`
  color: ${props => props.theme['--text-secondary'] || '#666'};
  text-decoration: none;
  
  &:hover {
    color: ${props => props.theme['--primary-color'] || '#d95550'};
  }
`;

const BreadcrumbSeparator = styled.span`
  margin: 0 0.5rem;
  color: ${props => props.theme['--text-tertiary'] || '#999'};
`;

const BreadcrumbCurrent = styled.span`
  color: ${props => props.theme['--text-color'] || '#333'};
  font-weight: 500;
`;

const RelatedContainer = styled.section`
  margin: 3rem 0;
`;

const RelatedTitle = styled.h3`
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: ${props => props.theme['--text-color'] || '#333'};
`;

const RelatedGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
`;

const RelatedCard = styled.article`
  background-color: ${props => props.theme['--card-bg'] || '#fff'};
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
`;

const RelatedLink = styled(Link)`
  text-decoration: none;
  color: inherit;
  display: block;
`;

const RelatedImage = styled.img`
  width: 100%;
  height: 150px;
  object-fit: cover;
`;

const RelatedContent = styled.div`
  padding: 1rem;
`;

const RelatedPostTitle = styled.h4`
  font-size: 1rem;
  margin-bottom: 0.5rem;
  color: ${props => props.theme['--text-color'] || '#333'};
  line-height: 1.4;
`;

const RelatedExcerpt = styled.p`
  font-size: 0.9rem;
  color: ${props => props.theme['--text-secondary'] || '#666'};
  margin: 0;
  line-height: 1.5;
`;

export default InternalLinks;
