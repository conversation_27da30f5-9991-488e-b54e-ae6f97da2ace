import React from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { FaClock, FaRocket, FaPlay, FaBell } from 'react-icons/fa';
import TimerComponent from './TimerComponent';
import PageHeader from '../shared/PageHeader';
import Footer from '../shared/Footer';
import SEOHead from '../SEO/SEOHead';

const ScenarioTimerPage = ({ config }) => {
  const {
    title,
    description,
    keywords,
    url,
    heroTitle,
    heroDescription,
    defaultMinutes,
    gradientColors,
    backgroundColor,
    scenarios,
    benefits,
    tips,
    relatedTimers,
    customFeatures
  } = config;

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": title,
    "description": description,
    "applicationCategory": "ProductivityApplication",
    "operatingSystem": "Any",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "Professional timer",
      "Browser notifications",
      "Sound alerts",
      "Progress visualization",
      "Pause and resume functionality",
      "Scenario-based settings"
    ]
  };

  const handleTimerComplete = () => {
    if (window.gtag) {
      window.gtag('event', 'scenario_timer_complete', {
        'scenario_type': config.scenario,
        'timer_duration': defaultMinutes
      });
    }
  };

  return (
    <>
      <SEOHead
        title={title}
        description={description}
        keywords={keywords}
        url={url}
        type="website"
        structuredData={structuredData}
      />
      <PageHeader />
      <Container>
        <HeroSection $backgroundColor={backgroundColor} $gradient={gradientColors}>
          <HeroContent>
            <HeroTitle $gradient={gradientColors}>{heroTitle}</HeroTitle>
            <HeroDescription>{heroDescription}</HeroDescription>
          </HeroContent>
          <TimerSection>
            <TimerComponent 
              initialMinutes={defaultMinutes}
              title={`${config.scenario} Timer`}
              onTimerComplete={handleTimerComplete}
              showControls={true}
            />
          </TimerSection>
        </HeroSection>

        {scenarios && scenarios.length > 0 && (
          <ScenariosSection>
            <SectionTitle>Use Cases</SectionTitle>
            <ScenariosGrid>
              {scenarios.map((scenario, index) => (
                <ScenarioCard key={index}>
                  <ScenarioIcon>{scenario.emoji}</ScenarioIcon>
                  <ScenarioTitle>{scenario.title}</ScenarioTitle>
                  <ScenarioDescription>{scenario.description}</ScenarioDescription>
                  <ScenarioTime>{scenario.recommendedTime}</ScenarioTime>
                </ScenarioCard>
              ))}
            </ScenariosGrid>
          </ScenariosSection>
        )}

        <FeaturesSection>
          <SectionTitle>Core Features</SectionTitle>
          <FeaturesGrid>
            <FeatureCard>
              <FeatureIcon $gradient={gradientColors}><FaClock /></FeatureIcon>
              <FeatureTitle>Precise Timing</FeatureTitle>
              <FeatureText>
                Accurate countdown with visual progress indicator to help you track time effectively.
              </FeatureText>
            </FeatureCard>
            <FeatureCard>
              <FeatureIcon $gradient={gradientColors}><FaRocket /></FeatureIcon>
              <FeatureTitle>Instant Start</FeatureTitle>
              <FeatureText>
                No registration required. Works on all devices and browsers for immediate focus sessions.
              </FeatureText>
            </FeatureCard>
            <FeatureCard>
              <FeatureIcon $gradient={gradientColors}><FaBell /></FeatureIcon>
              <FeatureTitle>Smart Notifications</FeatureTitle>
              <FeatureText>
                Browser notifications and sound alerts ensure you never miss important time milestones.
              </FeatureText>
            </FeatureCard>
            {customFeatures && customFeatures.map((feature, index) => (
              <FeatureCard key={`custom-${index}`}>
                <FeatureIcon $gradient={gradientColors}>{feature.icon}</FeatureIcon>
                <FeatureTitle>{feature.title}</FeatureTitle>
                <FeatureText>{feature.description}</FeatureText>
              </FeatureCard>
            ))}
          </FeaturesGrid>
        </FeaturesSection>

        {benefits && benefits.length > 0 && (
          <BenefitsSection>
            <SectionTitle>Key Benefits</SectionTitle>
            <BenefitsGrid>
              {benefits.map((benefit, index) => (
                <BenefitCard key={index}>
                  <BenefitEmoji>{benefit.emoji}</BenefitEmoji>
                  <BenefitTitle>{benefit.title}</BenefitTitle>
                  <BenefitText>{benefit.description}</BenefitText>
                </BenefitCard>
              ))}
            </BenefitsGrid>
          </BenefitsSection>
        )}

        {tips && tips.length > 0 && (
          <TipsSection>
            <SectionTitle>Professional Tips</SectionTitle>
            <TipsList>
              {tips.map((tip, index) => (
                <TipItem key={index}>
                  <TipNumber>{index + 1}</TipNumber>
                  <TipContent>
                    <TipTitle>{tip.title}</TipTitle>
                    <TipDescription>{tip.description}</TipDescription>
                  </TipContent>
                </TipItem>
              ))}
            </TipsList>
          </TipsSection>
        )}

        {relatedTimers && relatedTimers.length > 0 && (
          <RelatedSection>
            <SectionTitle>Related Timers</SectionTitle>
            <RelatedGrid>
              {relatedTimers.map((timer, index) => (
                <RelatedTimer key={index} as={Link} to={timer.url}>
                  <RelatedTitle>{timer.title}</RelatedTitle>
                  <RelatedDescription>{timer.description}</RelatedDescription>
                  <RelatedDuration>{timer.duration}</RelatedDuration>
                </RelatedTimer>
              ))}
            </RelatedGrid>
          </RelatedSection>
        )}

        <CTASection>
          <CTATitle>Start Your Professional Time Management</CTATitle>
          <CTAText>
            Experience AI Pomo's complete productivity suite with intelligent project planning, Pomodoro technique integration, and advanced time management features.
          </CTAText>
          <CTAButtons>
            <CTAButton as={Link} to="/register" primary>
              Try AI Pomo Free
            </CTAButton>
            <CTAButton as={Link} to="/features">
              Explore Features
            </CTAButton>
          </CTAButtons>
        </CTASection>
      </Container>
      <Footer />
    </>
  );
};

// Styled Components
const Container = styled.div`
  max-width: 1000px;
  margin: 0 auto;
  padding: 6rem 2rem 4rem;

  @media (max-width: 768px) {
    padding: 5rem 1rem 2rem;
  }
`;

const HeroSection = styled.section`
  background: ${props => props.$backgroundColor || 'linear-gradient(135deg, #f8f9fa, #f1f3f4)'};
  border-radius: 24px;
  padding: 4rem 2rem;
  margin: 2rem 0 4rem;
  border: 1px solid #e5e7eb;
  text-align: center;

  @media (max-width: 768px) {
    padding: 3rem 1rem;
    margin: 1rem 0 3rem;
  }
`;

const HeroContent = styled.div`
  margin-bottom: 3rem;
`;

const HeroTitle = styled.h1`
  font-size: 3.5rem;
  font-weight: 800;
  color: #111827;
  margin-bottom: 1.5rem;
  background: ${props => props.$gradient ? `linear-gradient(135deg, ${props.$gradient.join(', ')})` : 'linear-gradient(135deg, #d95550, #f59e0b)'};
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1.1;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const HeroDescription = styled.p`
  font-size: 1.25rem;
  color: #6b7280;
  line-height: 1.6;
  max-width: 700px;
  margin: 0 auto;
`;

const TimerSection = styled.div`
  display: flex;
  justify-content: center;
`;

const ScenariosSection = styled.section`
  margin-bottom: 4rem;
`;

const SectionTitle = styled.h2`
  font-size: 2.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2.5rem;
  text-align: center;
`;

const ScenariosGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
`;

const ScenarioCard = styled.div`
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
  }
`;

const ScenarioIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const ScenarioTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.75rem;
`;

const ScenarioDescription = styled.p`
  color: #6b7280;
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 1rem;
`;

const ScenarioTime = styled.div`
  display: inline-block;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  color: #374151;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
`;

const FeaturesSection = styled.section`
  margin-bottom: 4rem;
`;

const FeaturesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
`;

const FeatureCard = styled.div`
  text-align: center;
  padding: 2.5rem 2rem;
  background: white;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const FeatureIcon = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  background: ${props => props.$gradient ? `linear-gradient(135deg, ${props.$gradient.join(', ')})` : 'linear-gradient(135deg, #d95550, #f59e0b)'};
  color: white;
  border-radius: 50%;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
`;

const FeatureTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
`;

const FeatureText = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
`;

const BenefitsSection = styled.section`
  margin-bottom: 4rem;
`;

const BenefitsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
`;

const BenefitCard = styled.div`
  text-align: center;
  padding: 2rem;
  background: linear-gradient(135deg, #fef7f0, #fef3ec);
  border-radius: 16px;
  border: 1px solid #fed7aa;
`;

const BenefitEmoji = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const BenefitTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.75rem;
`;

const BenefitText = styled.p`
  color: #6b7280;
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0;
`;

const TipsSection = styled.section`
  margin-bottom: 4rem;
`;

const TipsList = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const TipItem = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const TipNumber = styled.div`
  width: 2.5rem;
  height: 2.5rem;
  background: linear-gradient(135deg, #d95550, #f59e0b);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.125rem;
  flex-shrink: 0;
`;

const TipContent = styled.div`
  flex: 1;
`;

const TipTitle = styled.h4`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const TipDescription = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
`;

const RelatedSection = styled.section`
  margin-bottom: 4rem;
`;

const RelatedGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
`;

const RelatedTimer = styled.a`
  display: block;
  padding: 2rem;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  text-decoration: none;
  transition: all 0.3s ease;

  &:hover {
    border-color: #d95550;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
`;

const RelatedTitle = styled.h4`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const RelatedDescription = styled.p`
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
`;

const RelatedDuration = styled.div`
  display: inline-block;
  padding: 0.375rem 0.75rem;
  background: linear-gradient(135deg, #d95550, #f59e0b);
  color: white;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
`;

const CTASection = styled.section`
  text-align: center;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #f8f9fa, #f1f3f4);
  border-radius: 16px;
  border: 1px solid #e5e7eb;
`;

const CTATitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
`;

const CTAText = styled.p`
  font-size: 1.125rem;
  color: #6b7280;
  margin-bottom: 2rem;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
`;

const CTAButtons = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;

  @media (max-width: 480px) {
    flex-direction: column;
    align-items: center;
  }
`;

const CTAButton = styled.a`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;

  ${props => props.primary ? `
    background: linear-gradient(135deg, #d95550, #c44a45);
    color: white;
    box-shadow: 0 4px 12px rgba(217, 85, 80, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(217, 85, 80, 0.4);
    }
  ` : `
    background: white;
    color: #6b7280;
    border: 2px solid #e5e7eb;

    &:hover {
      border-color: #d95550;
      color: #d95550;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  `}

  @media (max-width: 480px) {
    width: 200px;
    justify-content: center;
  }
`;

export default ScenarioTimerPage;