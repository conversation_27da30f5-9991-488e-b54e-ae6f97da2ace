import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { FaPlay, FaPause, FaStop, FaRedo, FaVolumeUp, FaVolumeMute } from 'react-icons/fa';

const TimerComponent = ({ 
  initialMinutes, 
  title, 
  onTimerComplete,
  showControls = true,
  autoStart = false 
}) => {
  const [timeLeft, setTimeLeft] = useState(initialMinutes * 60);
  const [isRunning, setIsRunning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const intervalRef = useRef(null);
  const audioRef = useRef(null);

  useEffect(() => {
    // Initialize audio
    audioRef.current = new Audio('/sounds/completion.mp3');
    audioRef.current.volume = 0.7;

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (autoStart && !isRunning) {
      startTimer();
    }
  }, [autoStart]);

  useEffect(() => {
    if (isRunning && !isPaused) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(prevTime => {
          if (prevTime <= 1) {
            setIsRunning(false);
            setIsPaused(false);
            handleTimerComplete();
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);
    } else {
      clearInterval(intervalRef.current);
    }

    return () => clearInterval(intervalRef.current);
  }, [isRunning, isPaused]);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const startTimer = () => {
    setIsRunning(true);
    setIsPaused(false);
  };

  const pauseTimer = () => {
    setIsPaused(true);
  };

  const stopTimer = () => {
    setIsRunning(false);
    setIsPaused(false);
    setTimeLeft(initialMinutes * 60);
  };

  const resetTimer = () => {
    setIsRunning(false);
    setIsPaused(false);
    setTimeLeft(initialMinutes * 60);
  };

  const handleTimerComplete = () => {
    // Play notification sound
    if (soundEnabled && audioRef.current) {
      audioRef.current.play().catch(e => console.log('Audio play failed:', e));
    }

    // Browser notification
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(`${title} Complete!`, {
        body: `Your ${initialMinutes} minute timer has finished.`,
        icon: '/favicon.ico'
      });
    }

    // Callback
    if (onTimerComplete) {
      onTimerComplete();
    }
  };

  const toggleSound = () => {
    setSoundEnabled(!soundEnabled);
  };

  const requestNotificationPermission = () => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  };

  const progress = ((initialMinutes * 60 - timeLeft) / (initialMinutes * 60)) * 100;

  return (
    <TimerContainer>
      <TimerDisplay>
        <ProgressRing viewBox="0 0 100 100">
          <ProgressCircle
            cx="50"
            cy="50"
            r="45"
            stroke="rgba(226, 232, 240, 0.5)"
            strokeWidth="8"
            fill="none"
          />
          <ProgressCircle
            cx="50"
            cy="50"
            r="45"
            stroke="url(#gradient)"
            strokeWidth="8"
            fill="none"
            strokeDasharray={`${2 * Math.PI * 45}`}
            strokeDashoffset={`${2 * Math.PI * 45 * (1 - progress / 100)}`}
            transform="rotate(-90 50 50)"
            strokeLinecap="round"
          />
          <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" style={{stopColor: '#d95550', stopOpacity: 1}} />
              <stop offset="100%" style={{stopColor: '#f59e0b', stopOpacity: 1}} />
            </linearGradient>
          </defs>
        </ProgressRing>
        <TimeDisplay $isActive={isRunning}>
          {formatTime(timeLeft)}
        </TimeDisplay>
      </TimerDisplay>

      {showControls && (
        <ControlsContainer>
          <ControlButton
            onClick={isRunning && !isPaused ? pauseTimer : startTimer}
            $primary
            disabled={timeLeft === 0}
          >
            {isRunning && !isPaused ? <FaPause /> : <FaPlay />}
            {isRunning && !isPaused ? 'Pause' : 'Start'}
          </ControlButton>

          <ControlButton onClick={stopTimer} disabled={!isRunning && !isPaused}>
            <FaStop />
            Stop
          </ControlButton>

          <ControlButton onClick={resetTimer}>
            <FaRedo />
            Reset
          </ControlButton>

          <ControlButton onClick={toggleSound} $sound={soundEnabled}>
            {soundEnabled ? <FaVolumeUp /> : <FaVolumeMute />}
          </ControlButton>
        </ControlsContainer>
      )}

      {('Notification' in window && Notification.permission === 'default') && (
        <NotificationPrompt>
          <NotificationText>
            Want to be notified when the timer finishes?
          </NotificationText>
          <NotificationButton onClick={requestNotificationPermission}>
            Enable Notifications
          </NotificationButton>
        </NotificationPrompt>
      )}
    </TimerContainer>
  );
};

// Styled Components
const TimerContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3rem;
  padding: 4rem 3rem;
  max-width: 550px;
  margin: 0 auto;
  background: transparent;
  border-radius: 32px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);

  @media (max-width: 768px) {
    max-width: 450px;
    padding: 3rem 2rem;
    gap: 2.5rem;
  }
`;

const TimerDisplay = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ProgressRing = styled.svg`
  width: 300px;
  height: 300px;
  display: block;
  filter: drop-shadow(0 6px 20px rgba(217, 85, 80, 0.25));
  
  @media (max-width: 768px) {
    width: 260px;
    height: 260px;
  }
  
  @media (max-width: 480px) {
    width: 220px;
    height: 220px;
  }
`;

const ProgressCircle = styled.circle`
  transition: stroke-dashoffset 0.3s ease;
`;

const TimeDisplay = styled.div`
  position: absolute;
  font-size: 3.5rem;
  font-weight: 800;
  font-family: 'SF Pro Display', 'Segoe UI', system-ui, sans-serif;
  color: ${props => props.$isActive ? '#d95550' : '#1f2937'};
  transition: all 0.3s ease;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.02em;

  @media (max-width: 768px) {
    font-size: 3rem;
  }

  @media (max-width: 480px) {
    font-size: 2.5rem;
  }
`;

const ControlsContainer = styled.div`
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;

  @media (max-width: 480px) {
    gap: 0.5rem;
  }
`;

const ControlButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.75rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 0.95rem;
  font-family: 'SF Pro Display', 'Segoe UI', system-ui, sans-serif;

  ${props => props.$primary ? `
    background: linear-gradient(135deg, #d95550, #c44a45);
    color: white;
    box-shadow: 0 8px 25px rgba(217, 85, 80, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.1);

    &:hover:not(:disabled) {
      transform: translateY(-3px);
      box-shadow: 0 12px 35px rgba(217, 85, 80, 0.35);
    }

    &:active {
      transform: translateY(-1px);
    }
  ` : `
    background: rgba(255, 255, 255, 0.9);
    color: #4b5563;
    border: 2px solid #e5e7eb;
    backdrop-filter: blur(10px);

    &:hover:not(:disabled) {
      border-color: #d95550;
      color: #d95550;
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
  `}

  ${props => props.$sound && !props.$primary && `
    background: rgba(217, 85, 80, 0.1);
    color: #d95550;
    border-color: #d95550;
  `}

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  svg {
    font-size: 1rem;
  }

  @media (max-width: 480px) {
    padding: 0.75rem 1.25rem;
    font-size: 0.85rem;
  }
`;

const NotificationPrompt = styled.div`
  text-align: center;
  padding: 1.25rem;
  background: linear-gradient(135deg, #fef7f0, #fef3ec);
  border-radius: 12px;
  border: 1px solid #fed7aa;
  max-width: 320px;
  box-shadow: 0 4px 12px rgba(251, 146, 60, 0.1);
`;

const NotificationText = styled.p`
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  color: #92400e;
  font-weight: 500;
`;

const NotificationButton = styled.button`
  padding: 0.625rem 1.25rem;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
  }
`;

export default TimerComponent;