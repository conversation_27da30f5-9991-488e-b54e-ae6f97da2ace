import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FaCheck, FaTimes, FaSpinner, FaPaypal, FaEthereum, FaCalendarAlt } from 'react-icons/fa';
import { adminApi } from '../services/apiService';

const AdminSubscriptionPage = () => {
  const [pendingSubscriptions, setPendingSubscriptions] = useState([]);
  const [allSubscriptions, setAllSubscriptions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('pending');
  const [selectedSubscription, setSelectedSubscription] = useState(null);
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const [rejectModalOpen, setRejectModalOpen] = useState(false);
  const [expiryDate, setExpiryDate] = useState('');
  const [adminNotes, setAdminNotes] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');
  const [actionLoading, setActionLoading] = useState(false);

  // Fetch subscriptions on mount and when tab changes
  useEffect(() => {
    fetchSubscriptions();
  }, [activeTab]);

  const fetchSubscriptions = async () => {
    setIsLoading(true);
    setError('');

    try {
      if (activeTab === 'pending') {
        const data = await adminApi.getPendingSubscriptions();
        setPendingSubscriptions(data);
      } else {
        const filters = activeTab === 'all' ? {} : { status: activeTab };
        const data = await adminApi.getAllSubscriptions(filters);
        setAllSubscriptions(data.subscriptions || []);
      }
    } catch (error) {
      console.error('Error fetching subscriptions:', error);
      setError('Failed to load subscriptions. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleConfirmClick = (subscription) => {
    setSelectedSubscription(subscription);

    // Set default expiry date based on plan
    if (subscription.plan === 'yearly') {
      const oneYearFromNow = new Date();
      oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
      setExpiryDate(oneYearFromNow.toISOString().split('T')[0]);
    } else {
      // For lifetime, set a far future date (e.g., 100 years)
      const farFuture = new Date();
      farFuture.setFullYear(farFuture.getFullYear() + 100);
      setExpiryDate(farFuture.toISOString().split('T')[0]);
    }

    setConfirmModalOpen(true);
  };

  const handleRejectClick = (subscription) => {
    setSelectedSubscription(subscription);
    setRejectModalOpen(true);
  };

  const confirmSubscription = async () => {
    if (!selectedSubscription) return;

    setActionLoading(true);

    try {
      await adminApi.confirmSubscription(selectedSubscription._id, {
        expiryDate,
        notes: adminNotes
      });

      // Refresh subscriptions
      fetchSubscriptions();

      // Close modal and reset state
      setConfirmModalOpen(false);
      setSelectedSubscription(null);
      setExpiryDate('');
      setAdminNotes('');
    } catch (error) {
      console.error('Error confirming subscription:', error);
      setError('Failed to confirm subscription. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  const rejectSubscription = async () => {
    if (!selectedSubscription) return;

    setActionLoading(true);

    try {
      await adminApi.rejectSubscription(selectedSubscription._id, rejectionReason);

      // Refresh subscriptions
      fetchSubscriptions();

      // Close modal and reset state
      setRejectModalOpen(false);
      setSelectedSubscription(null);
      setRejectionReason('');
    } catch (error) {
      console.error('Error rejecting subscription:', error);
      setError('Failed to reject subscription. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  const getPaymentMethodIcon = (method) => {
    switch (method) {
      case 'paypal':
        return <FaPaypal />;
      case 'usdt':
        return <FaEthereum />;
      default:
        return null;
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'pending':
        return <StatusBadge $status="pending">Pending</StatusBadge>;
      case 'confirmed':
        return <StatusBadge $status="confirmed">Confirmed</StatusBadge>;
      case 'rejected':
        return <StatusBadge $status="rejected">Rejected</StatusBadge>;
      default:
        return <StatusBadge $status="unknown">Unknown</StatusBadge>;
    }
  };

  const renderSubscriptionTable = (subscriptions) => {
    if (subscriptions.length === 0) {
      return <EmptyState>No subscriptions found</EmptyState>;
    }

    return (
      <SubscriptionTable>
        <thead>
          <tr>
            <th>User</th>
            <th>Plan</th>
            <th>Payment</th>
            <th>Amount</th>
            <th>Status</th>
            <th>Date</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {subscriptions.map((subscription) => (
            <tr key={subscription._id}>
              <td>
                <UserInfo>
                  <div>{subscription.user?.name || 'Unknown'}</div>
                  <UserEmail>{subscription.user?.email || 'No email'}</UserEmail>
                </UserInfo>
              </td>
              <td>{subscription.plan.charAt(0).toUpperCase() + subscription.plan.slice(1)}</td>
              <td>
                <PaymentMethod>
                  {getPaymentMethodIcon(subscription.paymentMethod)}
                  <span>{subscription.paymentMethod?.toUpperCase()}</span>
                </PaymentMethod>
              </td>
              <td>${subscription.paymentAmount}</td>
              <td>{getStatusBadge(subscription.paymentStatus)}</td>
              <td>{formatDate(subscription.createdAt)}</td>
              <td>
                {subscription.paymentStatus === 'pending' && (
                  <ActionButtons>
                    <ConfirmButton onClick={() => handleConfirmClick(subscription)}>
                      <FaCheck /> Confirm
                    </ConfirmButton>
                    <RejectButton onClick={() => handleRejectClick(subscription)}>
                      <FaTimes /> Reject
                    </RejectButton>
                  </ActionButtons>
                )}
                {subscription.paymentStatus !== 'pending' && (
                  <ViewDetailsButton onClick={() => handleConfirmClick(subscription)}>
                    View Details
                  </ViewDetailsButton>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </SubscriptionTable>
    );
  };

  return (
    <PageContainer>
      <PageHeader>
        <h1>Subscription Management</h1>
        <p>Manage user subscription payments and requests</p>
      </PageHeader>

      {error && <ErrorMessage>{error}</ErrorMessage>}

      <TabsContainer>
        <TabButton
          $isActive={activeTab === 'pending'}
          onClick={() => setActiveTab('pending')}
        >
          Pending Requests
        </TabButton>
        <TabButton
          $isActive={activeTab === 'all'}
          onClick={() => setActiveTab('all')}
        >
          All Subscriptions
        </TabButton>
        <TabButton
          $isActive={activeTab === 'confirmed'}
          onClick={() => setActiveTab('confirmed')}
        >
          Confirmed
        </TabButton>
        <TabButton
          $isActive={activeTab === 'rejected'}
          onClick={() => setActiveTab('rejected')}
        >
          Rejected
        </TabButton>
      </TabsContainer>

      <ContentContainer>
        {isLoading ? (
          <LoadingState>
            <FaSpinner className="spinner" />
            <span>Loading subscriptions...</span>
          </LoadingState>
        ) : (
          renderSubscriptionTable(activeTab === 'pending' ? pendingSubscriptions : allSubscriptions)
        )}
      </ContentContainer>

      {/* Confirm Subscription Modal */}
      {confirmModalOpen && selectedSubscription && (
        <ModalOverlay>
          <ModalContent>
            <ModalHeader>
              <h2>Confirm Subscription Payment</h2>
              <CloseButton onClick={() => setConfirmModalOpen(false)}>×</CloseButton>
            </ModalHeader>
            <ModalBody>
              <SubscriptionDetails>
                <DetailItem>
                  <DetailLabel>User:</DetailLabel>
                  <DetailValue>{selectedSubscription.user?.name} ({selectedSubscription.user?.email})</DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Plan:</DetailLabel>
                  <DetailValue>{selectedSubscription.plan.charAt(0).toUpperCase() + selectedSubscription.plan.slice(1)}</DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Payment Method:</DetailLabel>
                  <DetailValue>
                    {getPaymentMethodIcon(selectedSubscription.paymentMethod)}
                    <span>{selectedSubscription.paymentMethod?.toUpperCase()}</span>
                  </DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Amount:</DetailLabel>
                  <DetailValue>${selectedSubscription.paymentAmount}</DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Payment Date:</DetailLabel>
                  <DetailValue>{formatDate(selectedSubscription.paymentDate)}</DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Additional Info:</DetailLabel>
                  <DetailValue>{selectedSubscription.additionalInfo || 'None provided'}</DetailValue>
                </DetailItem>
              </SubscriptionDetails>

              <FormGroup>
                <Label>
                  <FaCalendarAlt /> Expiry Date
                </Label>
                <Input
                  type="date"
                  value={expiryDate}
                  onChange={(e) => setExpiryDate(e.target.value)}
                  disabled={selectedSubscription.paymentStatus !== 'pending'}
                />
              </FormGroup>

              <FormGroup>
                <Label>Admin Notes</Label>
                <Textarea
                  value={adminNotes}
                  onChange={(e) => setAdminNotes(e.target.value)}
                  placeholder="Add any notes about this subscription"
                  rows={3}
                  disabled={selectedSubscription.paymentStatus !== 'pending'}
                />
              </FormGroup>

              {selectedSubscription.paymentStatus === 'pending' && (
                <ButtonGroup>
                  <ConfirmButton
                    onClick={confirmSubscription}
                    disabled={actionLoading}
                  >
                    {actionLoading ? <FaSpinner className="spinner" /> : <FaCheck />}
                    Confirm Payment
                  </ConfirmButton>
                  <CancelButton onClick={() => setConfirmModalOpen(false)}>
                    Cancel
                  </CancelButton>
                </ButtonGroup>
              )}
            </ModalBody>
          </ModalContent>
        </ModalOverlay>
      )}

      {/* Reject Subscription Modal */}
      {rejectModalOpen && selectedSubscription && (
        <ModalOverlay>
          <ModalContent>
            <ModalHeader>
              <h2>Reject Subscription Payment</h2>
              <CloseButton onClick={() => setRejectModalOpen(false)}>×</CloseButton>
            </ModalHeader>
            <ModalBody>
              <p>Are you sure you want to reject this subscription payment?</p>

              <SubscriptionDetails>
                <DetailItem>
                  <DetailLabel>User:</DetailLabel>
                  <DetailValue>{selectedSubscription.user?.name} ({selectedSubscription.user?.email})</DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Plan:</DetailLabel>
                  <DetailValue>{selectedSubscription.plan.charAt(0).toUpperCase() + selectedSubscription.plan.slice(1)}</DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Amount:</DetailLabel>
                  <DetailValue>${selectedSubscription.paymentAmount}</DetailValue>
                </DetailItem>
              </SubscriptionDetails>

              <FormGroup>
                <Label>Reason for Rejection</Label>
                <Textarea
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  placeholder="Provide a reason for rejecting this payment"
                  rows={3}
                  required
                />
              </FormGroup>

              <ButtonGroup>
                <RejectButton
                  onClick={rejectSubscription}
                  disabled={actionLoading || !rejectionReason.trim()}
                >
                  {actionLoading ? <FaSpinner className="spinner" /> : <FaTimes />}
                  Reject Payment
                </RejectButton>
                <CancelButton onClick={() => setRejectModalOpen(false)}>
                  Cancel
                </CancelButton>
              </ButtonGroup>
            </ModalBody>
          </ModalContent>
        </ModalOverlay>
      )}
    </PageContainer>
  );
};

// Styled Components
const PageContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
`;

const PageHeader = styled.div`
  margin-bottom: 2rem;

  h1 {
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
    color: ${props => props.theme['--text-color'] || '#333'};
  }

  p {
    color: ${props => props.theme['--text-secondary'] || '#666'};
  }
`;

const TabsContainer = styled.div`
  display: flex;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
`;

const TabButton = styled.button`
  padding: 0.75rem 1.25rem;
  background: none;
  border: none;
  border-bottom: 2px solid ${props => props.$isActive ? props.theme['--primary-color'] || '#d95550' : 'transparent'};
  color: ${props => props.$isActive ? props.theme['--primary-color'] || '#d95550' : props.theme['--text-secondary'] || '#666'};
  font-weight: ${props => props.$isActive ? '600' : '400'};
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    color: ${props => props.theme['--primary-color'] || '#d95550'};
  }
`;

const ContentContainer = styled.div`
  background-color: ${props => props.theme['--card-bg'] || '#fff'};
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
`;

const SubscriptionTable = styled.table`
  width: 100%;
  border-collapse: collapse;

  th, td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #eee;
  }

  th {
    font-weight: 600;
    color: ${props => props.theme['--text-color'] || '#333'};
    background-color: ${props => props.theme['--bg-color'] || '#f5f5f5'};
  }

  tbody tr:hover {
    background-color: ${props => props.theme['--bg-color'] || '#f5f5f5'};
  }
`;

const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const UserEmail = styled.span`
  font-size: 0.8rem;
  color: ${props => props.theme['--text-secondary'] || '#666'};
`;

const PaymentMethod = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const StatusBadge = styled.span`
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;

  ${props => {
    switch (props.$status) {
      case 'confirmed':
        return `
          background-color: rgba(76, 175, 80, 0.1);
          color: #4CAF50;
        `;
      case 'pending':
        return `
          background-color: rgba(255, 152, 0, 0.1);
          color: #FF9800;
        `;
      case 'rejected':
        return `
          background-color: rgba(244, 67, 54, 0.1);
          color: #F44336;
        `;
      default:
        return `
          background-color: rgba(158, 158, 158, 0.1);
          color: #9E9E9E;
        `;
    }
  }}
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .spinner {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ConfirmButton = styled(Button)`
  background-color: #4CAF50;
  color: white;
  border: none;

  &:hover:not(:disabled) {
    background-color: #388E3C;
  }
`;

const RejectButton = styled(Button)`
  background-color: #F44336;
  color: white;
  border: none;

  &:hover:not(:disabled) {
    background-color: #D32F2F;
  }
`;

const ViewDetailsButton = styled(Button)`
  background-color: transparent;
  color: ${props => props.theme['--primary-color'] || '#d95550'};
  border: 1px solid ${props => props.theme['--primary-color'] || '#d95550'};

  &:hover:not(:disabled) {
    background-color: rgba(217, 85, 80, 0.1);
  }
`;

const CancelButton = styled(Button)`
  background-color: transparent;
  color: ${props => props.theme['--text-secondary'] || '#666'};
  border: 1px solid #ddd;

  &:hover:not(:disabled) {
    background-color: #f5f5f5;
  }
`;

const EmptyState = styled.div`
  padding: 3rem;
  text-align: center;
  color: ${props => props.theme['--text-secondary'] || '#666'};
`;

const LoadingState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: ${props => props.theme['--text-secondary'] || '#666'};

  .spinner {
    font-size: 2rem;
    margin-bottom: 1rem;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ErrorMessage = styled.div`
  background-color: rgba(244, 67, 54, 0.1);
  color: #F44336;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
`;

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: ${props => props.theme['--card-bg'] || '#fff'};
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #eee;

  h2 {
    margin: 0;
    font-size: 1.25rem;
    color: ${props => props.theme['--text-color'] || '#333'};
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: ${props => props.theme['--text-secondary'] || '#666'};

  &:hover {
    color: ${props => props.theme['--text-color'] || '#333'};
  }
`;

const ModalBody = styled.div`
  padding: 1.5rem;
`;

const SubscriptionDetails = styled.div`
  background-color: ${props => props.theme['--bg-color'] || '#f5f5f5'};
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1.5rem;
`;

const DetailItem = styled.div`
  display: flex;
  margin-bottom: 0.5rem;

  &:last-child {
    margin-bottom: 0;
  }
`;

const DetailLabel = styled.span`
  font-weight: 600;
  width: 150px;
  color: ${props => props.theme['--text-color'] || '#333'};
`;

const DetailValue = styled.span`
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: ${props => props.theme['--text-secondary'] || '#666'};
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`;

const Label = styled.label`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: ${props => props.theme['--text-color'] || '#333'};
`;

const Input = styled.input`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: ${props => props.theme['--primary-color'] || '#d95550'};
  }

  &:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
  }
`;

const Textarea = styled.textarea`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: ${props => props.theme['--primary-color'] || '#d95550'};
  }

  &:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
`;

export default AdminSubscriptionPage;
