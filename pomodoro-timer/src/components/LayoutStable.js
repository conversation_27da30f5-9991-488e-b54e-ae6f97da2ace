import React from 'react';
import styled from 'styled-components';

// Layout stability component to prevent CLS
const LayoutStable = ({ children, minHeight = '200px', width = '100%', placeholder = 'Loading...' }) => {
  return (
    <StableContainer minHeight={minHeight} width={width}>
      {children || (
        <PlaceholderContent>
          {placeholder}
        </PlaceholderContent>
      )}
    </StableContainer>
  );
};

// Skeleton component for stable loading states
export const SkeletonLoader = ({ height = '20px', width = '100%', count = 1 }) => {
  return (
    <SkeletonContainer>
      {Array.from({ length: count }, (_, index) => (
        <SkeletonItem key={index} height={height} width={width} />
      ))}
    </SkeletonContainer>
  );
};

// Image container with reserved space
export const StableImage = ({ src, alt, width = '100%', height = '200px', className }) => {
  return (
    <ImageContainer width={width} height={height} className={className}>
      <StableImg src={src} alt={alt} loading="lazy" />
    </ImageContainer>
  );
};

// Styled Components
const StableContainer = styled.div`
  min-height: ${props => props.minHeight};
  width: ${props => props.width};
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
`;

const PlaceholderContent = styled.div`
  color: #666;
  font-size: 14px;
  text-align: center;
`;

const SkeletonContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const SkeletonItem = styled.div`
  height: ${props => props.height};
  width: ${props => props.width};
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;

  @keyframes loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
`;

const ImageContainer = styled.div`
  width: ${props => props.width};
  height: ${props => props.height};
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const StableImg = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
`;

export default LayoutStable;