import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { isAuthenticated } from '../services/authService';

const ProjectLimitWarningModal = ({ isOpen, onClose }) => {
  console.log('ProjectLimitWarningModal - isOpen:', isOpen);
  const [userData, setUserData] = useState(null);

  // Fetch user data to get their project limit
  useEffect(() => {
    const fetchUserData = async () => {
      if (isAuthenticated() && isOpen) {
        try {
          // Use the API base URL from environment or default to localhost
          const apiBaseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
          const response = await fetch(`${apiBaseUrl}/users/me`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
          });

          if (response.ok) {
            const data = await response.json();
            console.log('User data for project limit warning:', data);
            setUserData(data);
          } else {
            console.error('Failed to fetch user data:', response.status, response.statusText);
          }
        } catch (error) {
          console.error('Error fetching user data for project limit warning:', error);
        }
      }
    };

    fetchUserData();
  }, [isOpen]);

  const handleUpgradeClick = () => {
    // Navigate to pricing page
    window.location.href = '/pricing';
  };

  if (!isOpen) return null;

  // Get the user's project limit
  const projectLimit = userData?.maxProjects || 2;
  const subscriptionPlan = userData?.subscription?.plan || 'free';

  return (
    <ModalOverlay>
      <ModalContent>
        <ModalHeader>
          <HeaderIcon>🚀</HeaderIcon>
          <h2>Ready to Scale Your Productivity?</h2>
          <CloseButton onClick={onClose}>×</CloseButton>
        </ModalHeader>
        <ModalBody>
          <LimitWarning>
            <WarningIcon>⚡</WarningIcon>
            <MessageText>
              You've reached the {projectLimit}-project limit on your Free plan!
            </MessageText>
          </LimitWarning>
          
          <UpgradeValue>
            <ValueTitle>🎯 Unlock Your Full Potential</ValueTitle>
            <BenefitsList>
              <BenefitItem>✅ <strong>Unlimited Projects</strong> - Manage all your goals</BenefitItem>
              <BenefitItem>✅ <strong>Advanced Statistics</strong> - Track your progress</BenefitItem>
              <BenefitItem>✅ <strong>Priority Support</strong> - Get help when you need it</BenefitItem>
              <BenefitItem>✅ <strong>Premium Themes</strong> - Customize your experience</BenefitItem>
            </BenefitsList>
          </UpgradeValue>

          <PricingHighlight>
            <PriceText>Starting at just <PriceAmount>$8/month</PriceAmount></PriceText>
            <SaveText>💰 Save 37% with annual plan!</SaveText>
          </PricingHighlight>

          <SocialProof>
            <TestimonialText>"AI-Pomo Premium transformed my productivity workflow!"</TestimonialText>
            <TestimonialAuthor>- Sarah K., Product Manager</TestimonialAuthor>
          </SocialProof>
        </ModalBody>
        <ModalFooter>
          <ButtonRow>
            <UpgradeButton onClick={handleUpgradeClick}>
              🚀 Upgrade to Premium
            </UpgradeButton>
            <LaterButton onClick={onClose}>Maybe Later</LaterButton>
          </ButtonRow>
          <TrustSignals>
            <TrustItem>🔒 Secure Payment</TrustItem>
            <TrustItem>↩️ 30-Day Refund</TrustItem>
            <TrustItem>⚡ Instant Access</TrustItem>
          </TrustSignals>
        </ModalFooter>
      </ModalContent>
    </ModalOverlay>
  );
};

// Styled components
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
`;

const ModalContent = styled.div`
  background: linear-gradient(135deg, #fff 0%, #fafafa 100%);
  border-radius: 16px;
  width: 90%;
  max-width: 520px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  animation: slideInScale 0.4s ease-out;
  border: 1px solid #e0e0e0;

  @keyframes slideInScale {
    from {
      transform: translateY(-30px) scale(0.95);
      opacity: 0;
    }
    to {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
  }
`;

const ModalHeader = styled.div`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;

  h2 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
  }
`;

const HeaderIcon = styled.div`
  font-size: 1.5rem;
  margin-right: 0.5rem;
`;

const CloseButton = styled.button`
  background: rgba(255,255,255,0.2);
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  transition: background-color 0.2s;

  &:hover {
    background: rgba(255,255,255,0.3);
  }
`;

const ModalBody = styled.div`
  padding: 2rem 1.5rem;
  display: flex;
  flex-direction: column;
  text-align: center;
`;

const LimitWarning = styled.div`
  background: linear-gradient(135deg, #ffecb3 0%, #fff3e0 100%);
  border: 1px solid #ffcc02;
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
`;

const WarningIcon = styled.div`
  font-size: 1.5rem;
`;

const MessageText = styled.p`
  font-size: 1.1rem;
  margin: 0;
  color: #e65100;
  font-weight: 600;
`;

const UpgradeValue = styled.div`
  margin-bottom: 1.5rem;
`;

const ValueTitle = styled.h3`
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: #333;
  font-weight: 700;
`;

const BenefitsList = styled.div`
  text-align: left;
  margin-bottom: 1rem;
`;

const BenefitItem = styled.div`
  padding: 0.5rem 0;
  font-size: 1rem;
  color: #555;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const PricingHighlight = styled.div`
  background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
  border: 1px solid #4caf50;
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
`;

const PriceText = styled.div`
  font-size: 1.2rem;
  color: #333;
  margin-bottom: 0.5rem;
`;

const PriceAmount = styled.span`
  font-size: 1.4rem;
  font-weight: 700;
  color: #4caf50;
`;

const SaveText = styled.div`
  font-size: 0.9rem;
  color: #2e7d32;
  font-weight: 600;
`;

const SocialProof = styled.div`
  background: #f8f9fa;
  border-left: 4px solid #667eea;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-style: italic;
`;

const TestimonialText = styled.div`
  font-size: 1rem;
  color: #555;
  margin-bottom: 0.5rem;
`;

const TestimonialAuthor = styled.div`
  font-size: 0.9rem;
  color: #888;
  font-weight: 600;
`;

const ModalFooter = styled.div`
  padding: 1.5rem;
  background: #f8f9fa;
  border-top: 1px solid #e0e0e0;
`;

const ButtonRow = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  justify-content: center;
  
  @media (max-width: 480px) {
    flex-direction: column;
  }
`;

const UpgradeButton = styled.button`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  }
`;

const LaterButton = styled.button`
  background: transparent;
  color: #888;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1rem 2rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: #f5f5f5;
    color: #666;
  }
`;

const TrustSignals = styled.div`
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
`;

const TrustItem = styled.div`
  font-size: 0.9rem;
  color: #666;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-weight: 500;
`;

export default ProjectLimitWarningModal;
