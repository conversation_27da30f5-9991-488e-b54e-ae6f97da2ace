import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import styled from 'styled-components';
import { FaCalendarAlt, FaClock, FaArrowRight, FaBook } from 'react-icons/fa';
import { format } from 'date-fns';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';
import { pillarPageApi } from '../../services/pillarPageApi';
import PageHeader from '../shared/PageHeader';
import Footer from '../shared/Footer';
import SEOHead from '../SEO/SEOHead';
import SocialShare from '../shared/SocialShare';
import DiveDeeperModule from './DiveDeeperModule';
import { createWebsiteStructuredData, createBreadcrumbStructuredData } from '../../utils/structuredData';

const PillarPage = () => {
  const { slug } = useParams();
  const [pillarPage, setPillarPage] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchPillarPage = async () => {
      try {
        setLoading(true);
        // Load pillar page with all data (optimized with caching)
        const data = await pillarPageApi.getPillarPageBySlug(slug);
        setPillarPage(data);
      } catch (err) {
        console.error('Error fetching pillar page:', err);
        setError('Failed to load pillar page');
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      fetchPillarPage();
    }
  }, [slug]);

  if (loading) {
    return (
      <>
        <PageHeader />
        <LoadingContainer>
          <LoadingSpinner />
          <LoadingText>Loading...</LoadingText>
        </LoadingContainer>
        <Footer />
      </>
    );
  }

  if (error || !pillarPage) {
    return (
      <>
        <PageHeader />
        <ErrorContainer>
          <ErrorMessage>
            <h2>Page Not Found</h2>
            <p>{error || 'The requested pillar page could not be found.'}</p>
            <BackLink to="/resources/all-articles">
              <FaArrowRight style={{ transform: 'rotate(180deg)', marginRight: '0.5rem' }} />
              Back to All Articles
            </BackLink>
          </ErrorMessage>
        </ErrorContainer>
        <Footer />
      </>
    );
  }

  const breadcrumbItems = [
    { name: 'Home', url: 'https://www.ai-pomo.com/' },
    { name: 'Resources', url: 'https://www.ai-pomo.com/resources/all-articles' },
    { name: pillarPage.title || 'Article', url: `https://www.ai-pomo.com/resources/${pillarPage.slug || ''}` }
  ];

  // Ensure absolute URL for cover image
  const absoluteCoverImage = pillarPage.coverImage && pillarPage.coverImage.startsWith('http')
    ? pillarPage.coverImage
    : `https://www.ai-pomo.com${pillarPage.coverImage || '/ai-pomo.png'}`;

  return (
    <>
      <SEOHead
        title={pillarPage.seoTitle || pillarPage.title || 'AI Pomo Article'}
        description={pillarPage.seoDescription || pillarPage.excerpt || 'AI Pomo productivity article'}
        keywords={pillarPage.seoKeywords || ''}
        image={absoluteCoverImage}
        url={`https://www.ai-pomo.com/resources/${pillarPage.slug || ''}`}
        type="article"
        structuredData={[
          createWebsiteStructuredData(),
          createBreadcrumbStructuredData(breadcrumbItems),
          {
            "@context": "https://schema.org",
            "@type": "Article",
            "headline": pillarPage.title || "AI Pomo Article",
            "description": pillarPage.excerpt || "AI Pomo productivity article",
            "image": absoluteCoverImage,
            "author": {
              "@type": "Person",
              "name": pillarPage.author?.name || "AI Pomo Team"
            },
            "publisher": {
              "@type": "Organization",
              "name": "AI Pomo",
              "logo": {
                "@type": "ImageObject",
                "url": "https://www.ai-pomo.com/logo.png"
              }
            },
            "datePublished": pillarPage.createdAt || new Date().toISOString(),
            "dateModified": pillarPage.updatedAt || new Date().toISOString(),
            "mainEntityOfPage": {
              "@type": "WebPage",
              "@id": `https://www.ai-pomo.com/resources/${pillarPage.slug || ''}`
            }
          }
        ]}
      />
      <PageHeader />

      <PageContainer>
        <Breadcrumb>
          {breadcrumbItems.map((item, index) => (
            <React.Fragment key={index}>
              {index > 0 && <BreadcrumbSeparator>/</BreadcrumbSeparator>}
              {index === breadcrumbItems.length - 1 ? (
                <BreadcrumbCurrent>{item.name}</BreadcrumbCurrent>
              ) : (
                <BreadcrumbLink as={Link} to={item.url ? item.url.replace('https://www.ai-pomo.com', '') : '#'}>{item.name}</BreadcrumbLink>
              )}
            </React.Fragment>
          ))}
        </Breadcrumb>

        <ArticleHeader>
          <CategoryBadge>
            <FaBook />
            {pillarPage.category ? pillarPage.category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'Article'}
          </CategoryBadge>
          
          <ArticleTitle>{pillarPage.title || 'Loading...'}</ArticleTitle>

          <SocialShare
            title={pillarPage.title}
            excerpt={pillarPage.excerpt}
            url={`https://www.ai-pomo.com/resources/${pillarPage.slug}`}
            tags={pillarPage.category ? [pillarPage.category] : []}
          />

          <ArticleExcerpt>{pillarPage.excerpt || ''}</ArticleExcerpt>

          <ArticleMeta>
            <MetaItem>
              <FaCalendarAlt />
              <span>{pillarPage.createdAt ? format(new Date(pillarPage.createdAt), 'MMMM d, yyyy') : 'Date not available'}</span>
            </MetaItem>
            <MetaItem>
              <FaClock />
              <span>{pillarPage.readTime || 0} min read</span>
            </MetaItem>
          </ArticleMeta>
        </ArticleHeader>

        {pillarPage.coverImage && (
          <CoverImageContainer>
            <CoverImage src={pillarPage.coverImage} alt={pillarPage.title || 'Article cover'} />
          </CoverImageContainer>
        )}

        <ContentContainer>
          <ArticleContent>
            <ContentBody>
              <ReactMarkdown
                remarkPlugins={[remarkGfm, remarkBreaks]}
                components={{
                  // Custom component for links to open external links in new tab
                  a: ({ href, children, ...props }) => {
                    const isExternal = href && (href.startsWith('http') || href.startsWith('https'));
                    return (
                      <a
                        href={href}
                        target={isExternal ? '_blank' : undefined}
                        rel={isExternal ? 'noopener noreferrer' : undefined}
                        {...props}
                      >
                        {children}
                      </a>
                    );
                  },
                  // Custom component for images with proper styling
                  img: ({ src, alt, ...props }) => (
                    <img
                      src={src}
                      alt={alt}
                      style={{ maxWidth: '100%', height: 'auto', borderRadius: '0.5rem' }}
                      {...props}
                    />
                  ),
                }}
              >
                {pillarPage.content || 'Loading content...'}
              </ReactMarkdown>
            </ContentBody>

            {/* Show DiveDeeperModule with cluster pages if available */}
            {pillarPage.clusterPages && pillarPage.clusterPages.length > 0 && (
              <DiveDeeperModule clusterPages={pillarPage.clusterPages} />
            )}
          </ArticleContent>
        </ContentContainer>
      </PageContainer>

      <Footer />
    </>
  );
};

// Styled Components
const PageContainer = styled.div`
  min-height: 100vh;
  background-color: #ffffff;
  padding-top: 80px;
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding-top: 80px;
`;

const LoadingSpinner = styled.div`
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #d95550;
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const LoadingText = styled.p`
  margin-top: 1rem;
  color: #6b7280;
  font-size: 1.1rem;
`;

const ErrorContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 2rem;
  padding-top: 80px;
`;

const ErrorMessage = styled.div`
  text-align: center;
  max-width: 500px;

  h2 {
    color: #111827;
    margin-bottom: 1rem;
  }

  p {
    color: #6b7280;
    margin-bottom: 2rem;
    line-height: 1.6;
  }
`;

const BackLink = styled(Link)`
  display: inline-flex;
  align-items: center;
  color: #d95550;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;

  &:hover {
    color: #c73e39;
  }
`;

const Breadcrumb = styled.nav`
  display: flex;
  align-items: center;
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem 2rem;
  font-size: 0.875rem;

  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const BreadcrumbLink = styled.a`
  color: #6b7280;
  text-decoration: none;
  transition: color 0.2s;

  &:hover {
    color: #d95550;
  }
`;

const BreadcrumbSeparator = styled.span`
  margin: 0 0.5rem;
  color: #d1d5db;
`;

const BreadcrumbCurrent = styled.span`
  color: #111827;
  font-weight: 500;
`;

const ArticleHeader = styled.header`
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem 2rem;
  text-align: center;

  @media (max-width: 768px) {
    padding: 0 1rem 2rem;
  }
`;

const CategoryBadge = styled.div`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background-color: rgba(217, 85, 80, 0.1);
  color: #d95550;
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  text-transform: capitalize;
`;

const ArticleTitle = styled.h1`
  font-size: 3rem;
  font-weight: 800;
  color: #111827;
  line-height: 1.1;
  margin-bottom: 1.5rem;

  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const ArticleExcerpt = styled.p`
  font-size: 1.25rem;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 768px) {
    font-size: 1.125rem;
  }
`;

const ArticleMeta = styled.div`
  display: flex;
  justify-content: center;
  gap: 2rem;
  color: #6b7280;
  font-size: 0.875rem;

  @media (max-width: 768px) {
    gap: 1rem;
  }
`;

const MetaItem = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const CoverImageContainer = styled.div`
  max-width: 1000px;
  margin: 0 auto 3rem;
  padding: 0 2rem;

  @media (max-width: 768px) {
    padding: 0 1rem;
  }
`;

const CoverImage = styled.img`
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 1rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);

  @media (max-width: 768px) {
    height: 250px;
  }
`;

const ContentContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem 4rem;

  @media (max-width: 768px) {
    padding: 0 1rem 4rem;
  }
`;

const ArticleContent = styled.article`
  background-color: #ffffff;
`;

const ContentBody = styled.div`
  font-size: 1.125rem;
  line-height: 1.8;
  color: #374151;

  h1, h2, h3, h4, h5, h6 {
    color: #111827;
    font-weight: 700;
    margin: 2rem 0 1rem;
    line-height: 1.3;
  }

  h1 { font-size: 2.25rem; }
  h2 { font-size: 1.875rem; }
  h3 { font-size: 1.5rem; }
  h4 { font-size: 1.25rem; }

  p {
    margin-bottom: 1.5rem;
  }

  ul, ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
  }

  li {
    margin-bottom: 0.5rem;
  }

  blockquote {
    border-left: 4px solid #d95550;
    padding-left: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    color: #6b7280;
  }

  img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 2rem 0;
  }

  a {
    color: #d95550;
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: border-color 0.2s;

    &:hover {
      border-bottom-color: #d95550;
    }
  }

  code {
    background-color: #f3f4f6;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875em;
  }

  pre {
    background-color: #1f2937;
    color: #f9fafb;
    padding: 1.5rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 2rem 0;

    code {
      background: none;
      padding: 0;
      color: inherit;
    }
  }

  @media (max-width: 768px) {
    font-size: 1rem;
    
    h1 { font-size: 1.875rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.25rem; }
    h4 { font-size: 1.125rem; }
  }
`;

export default PillarPage;
