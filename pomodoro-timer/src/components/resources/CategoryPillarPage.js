import React, { useState, useEffect } from 'react';
import { useLocation, Navigate } from 'react-router-dom';
import { pillarPageApi } from '../../services/pillarPageApi';
// import PillarPage from './PillarPage';
import PageHeader from '../shared/PageHeader';
import Footer from '../shared/Footer';
import styled from 'styled-components';

const CategoryPillarPage = () => {
  const location = useLocation();
  const [pillarPage, setPillarPage] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchPillarPageByCategory = async () => {
      try {
        setLoading(true);
        setError(null);

        // Extract category from the current path
        const pathParts = location.pathname.split('/');
        const category = pathParts[pathParts.length - 1]; // Get the last part of the path

        console.log('Looking for category:', category);

        // Use optimized direct category lookup (with caching for performance)
        const pillarPage = await pillarPageApi.getPillarPageByCategory(category);
        console.log('Found pillar page:', pillarPage);

        setPillarPage(pillarPage);
      } catch (err) {
        console.error('Error fetching pillar page by category:', err);
        setError('Failed to load pillar page');
      } finally {
        setLoading(false);
      }
    };

    fetchPillarPageByCategory();
  }, [location.pathname]);

  if (loading) {
    return (
      <>
        <PageHeader />
        <LoadingContainer>
          <LoadingSpinner />
          <LoadingText>Loading...</LoadingText>
        </LoadingContainer>
        <Footer />
      </>
    );
  }

  if (error || !pillarPage) {
    // If no pillar page found for this category, redirect to all articles
    return <Navigate to="/resources/all-articles" replace />;
  }

  // If we found a pillar page, redirect to its proper URL
  return <Navigate to={`/resources/${pillarPage.slug}`} replace />;
};

// Styled Components
const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding-top: 80px;
`;

const LoadingSpinner = styled.div`
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #d95550;
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const LoadingText = styled.p`
  margin-top: 1rem;
  color: #6b7280;
  font-size: 1.1rem;
`;

export default CategoryPillarPage;
