import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { FaArrowRight, FaClock, FaCalendarAlt } from 'react-icons/fa';
import { format } from 'date-fns';
import { pillarPageApi } from '../../services/pillarPageApi';

const DiveDeeperModule = ({ pillarPageSlug, clusterPages: initialClusterPages }) => {
  const [clusterPages, setClusterPages] = useState(initialClusterPages || []);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // If no initial cluster pages provided, load them lazily
  useEffect(() => {
    if (!initialClusterPages && pillarPageSlug) {
      const loadClusterPages = async () => {
        try {
          setLoading(true);
          const pages = await pillarPageApi.getPillarPageClusterPages(pillarPageSlug);
          setClusterPages(pages);
        } catch (err) {
          console.error('Error loading cluster pages:', err);
          setError('Failed to load related articles');
        } finally {
          setLoading(false);
        }
      };

      loadClusterPages();
    }
  }, [pillarPageSlug, initialClusterPages]);

  if (loading) {
    return (
      <ModuleContainer>
        <ModuleHeader>
          <ModuleTitle>Dive Deeper Into This Topic</ModuleTitle>
          <LoadingText>Loading related articles...</LoadingText>
        </ModuleHeader>
      </ModuleContainer>
    );
  }

  if (error) {
    return (
      <ModuleContainer>
        <ModuleHeader>
          <ModuleTitle>Dive Deeper Into This Topic</ModuleTitle>
          <ErrorText>{error}</ErrorText>
        </ModuleHeader>
      </ModuleContainer>
    );
  }

  if (!clusterPages || clusterPages.length === 0) {
    return null;
  }

  return (
    <ModuleContainer>
      <ModuleHeader>
        <ModuleTitle>Dive Deeper Into This Topic</ModuleTitle>
        <ModuleSubtitle>
          Explore these related articles to expand your knowledge and skills
        </ModuleSubtitle>
      </ModuleHeader>

      {clusterPages.length <= 3 ? (
        // Show card layout for 3 or fewer articles
        <ArticlesGrid>
          {clusterPages.map((cluster, index) => {
            const article = cluster.blogPost;
            if (!article) return null;

            return (
              <ArticleCard key={article._id || index}>
                <CardImageContainer>
                  <CardImage src={article.coverImage} alt={article.title} />
                  <ReadTimeOverlay>
                    <FaClock />
                    {article.readTime} min
                  </ReadTimeOverlay>
                </CardImageContainer>

                <CardContent>
                  <CardCategory>{article.category}</CardCategory>

                  <CardTitle>
                    <Link to={`/blog/${article.slug}`}>
                      {cluster.customTitle || article.title}
                    </Link>
                  </CardTitle>

                  <CardDescription>
                    {cluster.customDescription || article.excerpt}
                  </CardDescription>

                  <CardMeta>
                    <MetaItem>
                      <FaCalendarAlt />
                      <span>{format(new Date(article.createdAt), 'MMM d, yyyy')}</span>
                    </MetaItem>
                  </CardMeta>

                  <ReadMoreLink to={`/blog/${article.slug}`}>
                    Read Article
                    <FaArrowRight />
                  </ReadMoreLink>
                </CardContent>
              </ArticleCard>
            );
          })}
        </ArticlesGrid>
      ) : (
        // Show compact list layout for 4+ articles
        <ArticlesList>
          {clusterPages.map((cluster, index) => {
            const article = cluster.blogPost;
            if (!article) return null;

            return (
              <ArticleListItem key={article._id || index}>
                <ArticleNumber>{index + 1}</ArticleNumber>

                <ArticleThumbnail>
                  <ThumbnailImage src={article.coverImage} alt={article.title} />
                </ArticleThumbnail>

                <ArticleInfo>
                  <ArticleHeader>
                    <ArticleTitle>
                      <Link to={`/blog/${article.slug}`}>
                        {cluster.customTitle || article.title}
                      </Link>
                    </ArticleTitle>
                    <ArticleMeta>
                      <CategoryTag $category={article.category}>
                        {article.category}
                      </CategoryTag>
                      <ReadTime>
                        <FaClock />
                        {article.readTime} min
                      </ReadTime>
                      <PublishDate>
                        {format(new Date(article.createdAt), 'MMM d, yyyy')}
                      </PublishDate>
                    </ArticleMeta>
                  </ArticleHeader>

                  <ArticleExcerpt>
                    {cluster.customDescription || article.excerpt}
                  </ArticleExcerpt>
                </ArticleInfo>

                <ArticleAction>
                  <CompactReadLink to={`/blog/${article.slug}`}>
                    Read
                    <FaArrowRight />
                  </CompactReadLink>
                </ArticleAction>
              </ArticleListItem>
            );
          })}
        </ArticlesList>
      )}
    </ModuleContainer>
  );
};

// Styled Components
const ModuleContainer = styled.section`
  margin-top: 4rem;
  padding: 3rem 0;
  border-top: 2px solid #f3f4f6;
  background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);
  border-radius: 1rem;
  margin-left: -2rem;
  margin-right: -2rem;
  padding-left: 2rem;
  padding-right: 2rem;

  @media (max-width: 768px) {
    margin-left: -1rem;
    margin-right: -1rem;
    padding-left: 1rem;
    padding-right: 1rem;
    padding: 2rem 1rem;
  }
`;

const ModuleHeader = styled.header`
  text-align: center;
  margin-bottom: 3rem;
`;

const ModuleTitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.75rem;

  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
`;

const ModuleSubtitle = styled.p`
  font-size: 1.125rem;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;

  @media (max-width: 768px) {
    font-size: 1rem;
  }
`;

const ArticlesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
`;

const ArticleCard = styled.article`
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid #f3f4f6;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
  }
`;

const CardImageContainer = styled.div`
  position: relative;
  height: 200px;
  overflow: hidden;
`;

const CardImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;

  ${ArticleCard}:hover & {
    transform: scale(1.05);
  }
`;

const ReadTimeOverlay = styled.div`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
`;

const CardContent = styled.div`
  padding: 1.5rem;
`;

const CardCategory = styled.div`
  display: inline-block;
  background-color: rgba(217, 85, 80, 0.1);
  color: #d95550;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
  margin-bottom: 1rem;
`;

const CardTitle = styled.h3`
  margin-bottom: 0.75rem;
  
  a {
    color: #111827;
    text-decoration: none;
    font-size: 1.25rem;
    font-weight: 700;
    line-height: 1.3;
    transition: color 0.2s ease;

    &:hover {
      color: #d95550;
    }
  }

  @media (max-width: 768px) {
    a {
      font-size: 1.125rem;
    }
  }
`;

const CardDescription = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const CardMeta = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  color: #9ca3af;
  font-size: 0.75rem;
`;

const MetaItem = styled.div`
  display: flex;
  align-items: center;
  gap: 0.25rem;
`;

const ReadMoreLink = styled(Link)`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #d95550;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.2s ease;

  &:hover {
    color: #c73e39;
    gap: 0.75rem;
  }

  svg {
    font-size: 0.75rem;
    transition: transform 0.2s ease;
  }

  &:hover svg {
    transform: translateX(2px);
  }
`;

// Compact List Layout Styled Components
const ArticlesList = styled.div`
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #f3f4f6;
`;

const ArticleListItem = styled.article`
  display: grid;
  grid-template-columns: 50px 80px 1fr 100px;
  align-items: center;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;

  &:hover {
    background: #f9fafb;
  }

  &:last-child {
    border-bottom: none;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1.5rem 1rem;
    text-align: left;
  }
`;

const ArticleNumber = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: #d95550;
  color: white;
  border-radius: 50%;
  font-size: 0.875rem;
  font-weight: 600;
  flex-shrink: 0;

  @media (max-width: 768px) {
    width: 1.5rem;
    height: 1.5rem;
    font-size: 0.75rem;
    align-self: flex-start;
  }
`;

const ArticleThumbnail = styled.div`
  flex-shrink: 0;

  @media (max-width: 768px) {
    order: -1;
    margin-bottom: 0.5rem;
  }
`;

const ThumbnailImage = styled.img`
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;

  @media (max-width: 768px) {
    width: 80px;
    height: 50px;
  }
`;

const ArticleInfo = styled.div`
  flex: 1;
  min-width: 0;
  padding: 0 1rem;

  @media (max-width: 768px) {
    padding: 0;
  }
`;

const ArticleHeader = styled.div`
  margin-bottom: 0.5rem;
`;

const ArticleTitle = styled.h3`
  margin: 0 0 0.5rem 0;

  a {
    color: #111827;
    text-decoration: none;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.4;
    transition: color 0.2s ease;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;

    &:hover {
      color: #d95550;
    }
  }

  @media (max-width: 768px) {
    a {
      font-size: 1.125rem;
      -webkit-line-clamp: 3;
    }
  }
`;

const ArticleMeta = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    gap: 0.5rem;
  }
`;

const CategoryTag = styled.span`
  padding: 0.125rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;

  ${props => {
    const colors = {
      'pomodoro-technique': 'background: #fef3c7; color: #92400e;',
      'time-management': 'background: #dbeafe; color: #1e40af;',
      'adhd-productivity': 'background: #fce7f3; color: #be185d;',
      'ai-productivity': 'background: #d1fae5; color: #065f46;',
      'ai-tools': 'background: #e0e7ff; color: #3730a3;',
      'productivity': 'background: #f3e8ff; color: #6b21a8;'
    };
    return colors[props.$category] || 'background: rgba(217, 85, 80, 0.1); color: #d95550;';
  }}
`;

const ReadTime = styled.div`
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #6b7280;
  font-size: 0.75rem;
  font-weight: 500;
`;

const PublishDate = styled.div`
  color: #9ca3af;
  font-size: 0.75rem;

  @media (max-width: 768px) {
    display: none;
  }
`;

const ArticleExcerpt = styled.p`
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;

  @media (max-width: 768px) {
    -webkit-line-clamp: 3;
    margin-top: 0.5rem;
  }
`;

const ArticleAction = styled.div`
  flex-shrink: 0;

  @media (max-width: 768px) {
    margin-top: 1rem;
  }
`;

const CompactReadLink = styled(Link)`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #d95550;
  color: white;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.875rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;

  &:hover {
    background: #c73e39;
    gap: 0.75rem;
  }

  svg {
    font-size: 0.75rem;
    transition: transform 0.2s ease;
  }

  &:hover svg {
    transform: translateX(2px);
  }

  @media (max-width: 768px) {
    padding: 0.75rem 1.25rem;
    font-size: 1rem;
  }
`;

const LoadingText = styled.div`
  color: #6b7280;
  font-style: italic;
  padding: 1rem 0;
`;

const ErrorText = styled.div`
  color: #dc2626;
  font-style: italic;
  padding: 1rem 0;
`;

export default DiveDeeperModule;
