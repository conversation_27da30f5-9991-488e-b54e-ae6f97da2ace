import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { <PERSON>aB<PERSON>, Fa<PERSON><PERSON><PERSON>, <PERSON>aSearch, Fa<PERSON>lock, FaCalendarAlt, FaArrowRight } from 'react-icons/fa';
import { format } from 'date-fns';
import { blogApi } from '../../services/blogApi';
import { pillarPageApi } from '../../services/pillarPageApi';
// import BlogPostCard from '../blog/BlogPostCard';
import Pagination from '../blog/Pagination';
import CategoryFilter from '../blog/CategoryFilter';
import PageHeader from '../shared/PageHeader';
import Footer from '../shared/Footer';
import SEOHead from '../SEO/SEOHead';
import LazyImage from '../SEO/LazyImage';
import { createWebsiteStructuredData, createBreadcrumbStructuredData } from '../../utils/structuredData';

// Enhanced Blog Post Card Component
const EnhancedBlogPostCard = ({ post, index }) => {
  return (
    <EnhancedCardContainer $index={index}>
      <CardImageContainer>
        <CardImageLink to={`/blog/${post.slug}`}>
          <LazyImage
            src={post.coverImage}
            alt={post.title}
            style={{ width: '100%', height: '100%', objectFit: 'cover' }}
          />
          <ImageOverlay>
            <ReadTimeOverlay>
              <FaClock />
              {post.readTime} min
            </ReadTimeOverlay>
          </ImageOverlay>
        </CardImageLink>
      </CardImageContainer>

      <EnhancedCardContent>
        <CardHeader>
          <EnhancedCardCategory $category={post.category}>
            {post.category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </EnhancedCardCategory>
          <PublishDate>
            <FaCalendarAlt />
            {format(new Date(post.createdAt), 'MMM d, yyyy')}
          </PublishDate>
        </CardHeader>

        <EnhancedCardTitle>
          <Link to={`/blog/${post.slug}`}>{post.title}</Link>
        </EnhancedCardTitle>

        <EnhancedCardExcerpt>{post.excerpt}</EnhancedCardExcerpt>

        <CardFooter>
          <AuthorInfo>
            <AuthorAvatar>
              {post.author?.name ? post.author.name.charAt(0).toUpperCase() : 'A'}
            </AuthorAvatar>
            <AuthorName>
              {post.author?.name || 'AI Pomo Team'}
            </AuthorName>
          </AuthorInfo>

          <EnhancedReadMoreLink to={`/blog/${post.slug}`}>
            Read Article
            <FaArrowRight />
          </EnhancedReadMoreLink>
        </CardFooter>
      </EnhancedCardContent>
    </EnhancedCardContainer>
  );
};

const AllArticlesPage = () => {
  const [posts, setPosts] = useState([]);
  const [pillarPages, setPillarPages] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);



  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch pillar pages
        const pillarPagesData = await pillarPageApi.getPillarPages();
        setPillarPages(pillarPagesData);

        // Fetch blog posts
        const blogData = await blogApi.getBlogPosts(currentPage, 12, selectedCategory, '', searchQuery);
        setPosts(blogData.posts || []);
        setTotalPages(blogData.totalPages || 1);

        // Fetch categories
        const categoriesData = await blogApi.getCategories();
        setCategories(categoriesData || []);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load articles');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentPage, selectedCategory, searchQuery]);

  const handlePageChange = (page) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleCategoryChange = (category) => {
    setSelectedCategory(category);
    setCurrentPage(1);
  };

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    // Search is already triggered by handleSearchChange
  };

  const breadcrumbItems = [
    { name: 'Home', url: 'https://www.ai-pomo.com/' },
    { name: 'Resources', url: 'https://www.ai-pomo.com/resources/all-articles' },
    { name: 'All Articles', url: 'https://www.ai-pomo.com/resources/all-articles' }
  ];

  return (
    <>
      <SEOHead
        title="All Articles - AI Pomo Resources"
        description="Browse our complete library of productivity articles, Pomodoro technique guides, time management tips, and ADHD productivity strategies."
        keywords="productivity articles, pomodoro technique, time management, ADHD productivity, AI productivity, focus techniques"
        url="https://www.ai-pomo.com/resources/all-articles"
        structuredData={[
          createWebsiteStructuredData(),
          createBreadcrumbStructuredData(breadcrumbItems)
        ]}
      />
      <PageHeader />

      <PageContainer>
        <PageHeaderSection>
          <HeaderContent>
            <PageIcon>
              <FaBook />
            </PageIcon>
            <PageTitle>All Articles</PageTitle>
            <PageDescription>
              Explore our comprehensive library of productivity articles, guides, and resources
            </PageDescription>
          </HeaderContent>
        </PageHeaderSection>

        {/* Pillar Pages Section */}
        {pillarPages.length > 0 && (
          <PillarPagesSection>
            <SectionTitle>Featured Resources</SectionTitle>
            <PillarPagesGrid>
              {pillarPages.map((pillarPage) => (
                <PillarPageCard key={pillarPage._id}>
                  <PillarCardImage src={pillarPage.coverImage} alt={pillarPage.title} />
                  <PillarCardContent>
                    <PillarCardCategory>
                      {pillarPage.category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </PillarCardCategory>
                    <PillarCardTitle>
                      <Link to={`/resources/${pillarPage.slug}`}>{pillarPage.title}</Link>
                    </PillarCardTitle>
                    <PillarCardDescription>{pillarPage.excerpt}</PillarCardDescription>
                    <PillarCardLink to={`/resources/${pillarPage.slug}`}>
                      Read Guide →
                    </PillarCardLink>
                  </PillarCardContent>
                </PillarPageCard>
              ))}
            </PillarPagesGrid>
          </PillarPagesSection>
        )}

        {/* Articles Header */}
        <ArticlesHeaderSection>
          <LatestArticlesHeader>
            <SectionTitleWithIcon>
              <ArticlesIcon>📚</ArticlesIcon>
              <SectionTitle>Latest Articles</SectionTitle>
              <ArticleCount>
                {posts.length > 0 && !loading && (
                  <span>{posts.length} article{posts.length !== 1 ? 's' : ''} found</span>
                )}
              </ArticleCount>
            </SectionTitleWithIcon>
            <SectionSubtitle>
              Discover insights on productivity, time management, and focus techniques
            </SectionSubtitle>
          </LatestArticlesHeader>
        </ArticlesHeaderSection>

        {/* Search Section */}
        <SearchSection>
          <SearchForm onSubmit={handleSearchSubmit}>
            <SearchInputWrapper>
              <SearchIcon>
                <FaSearch />
              </SearchIcon>
              <SearchInput
                type="text"
                placeholder="Search articles by title, content, or keywords..."
                value={searchQuery}
                onChange={handleSearchChange}
              />
              {searchQuery && (
                <ClearSearchButton
                  type="button"
                  onClick={() => setSearchQuery('')}
                >
                  ✕
                </ClearSearchButton>
              )}
            </SearchInputWrapper>
          </SearchForm>

          {(searchQuery || selectedCategory) && (
            <ActiveFilters>
              <ActiveFiltersLabel>Active filters:</ActiveFiltersLabel>
              {searchQuery && (
                <FilterTag>
                  Search: "{searchQuery}"
                  <FilterTagClose onClick={() => setSearchQuery('')}>✕</FilterTagClose>
                </FilterTag>
              )}
              {selectedCategory && (
                <FilterTag>
                  Category: {selectedCategory}
                  <FilterTagClose onClick={() => setSelectedCategory('')}>✕</FilterTagClose>
                </FilterTag>
              )}
              <ClearAllFilters
                onClick={() => {
                  setSearchQuery('');
                  setSelectedCategory('');
                }}
              >
                Clear all
              </ClearAllFilters>
            </ActiveFilters>
          )}
        </SearchSection>

        {/* Category Filter Section */}
        <CategoryFilter
          categories={categories}
          selectedCategory={selectedCategory}
          onCategoryChange={handleCategoryChange}
        />

        {/* Enhanced Articles Grid */}
        <ArticlesSection>
          {loading ? (
            <LoadingMessage>
              <LoadingSpinner />
              <LoadingText>
                <span>Loading articles...</span>
                <LoadingSubtext>Fetching the latest productivity insights</LoadingSubtext>
              </LoadingText>
            </LoadingMessage>
          ) : error ? (
            <ErrorMessage>
              <ErrorIcon>⚠️</ErrorIcon>
              <ErrorText>
                <h3>Oops! Something went wrong</h3>
                <p>{error}</p>
                <RetryButton onClick={() => window.location.reload()}>
                  Try Again
                </RetryButton>
              </ErrorText>
            </ErrorMessage>
          ) : (
            <>
              {posts.length > 0 ? (
                <>
                  <ArticlesGridHeader>
                    <GridViewOptions>
                      <ViewOption $active={true}>
                        <span>📋</span> Grid View
                      </ViewOption>
                    </GridViewOptions>
                    <SortingInfo>
                      Showing {((currentPage - 1) * 12) + 1}-{Math.min(currentPage * 12, posts.length)}
                      {totalPages > 1 && ` of ${totalPages * 12} articles`}
                    </SortingInfo>
                  </ArticlesGridHeader>

                  <ArticlesGrid $itemCount={posts.length}>
                    {posts.map((post, index) => (
                      <EnhancedBlogPostCard key={post._id} post={post} index={index} />
                    ))}
                  </ArticlesGrid>

                  {totalPages > 1 && (
                    <PaginationWrapper>
                      <Pagination
                        currentPage={currentPage}
                        totalPages={totalPages}
                        onPageChange={handlePageChange}
                      />
                    </PaginationWrapper>
                  )}
                </>
              ) : (
                <NoPostsMessage>
                  <NoResultsIcon>🔍</NoResultsIcon>
                  <NoResultsContent>
                    <h3>No articles found</h3>
                    <p>
                      {searchQuery || selectedCategory
                        ? "Try adjusting your search terms or filters to find more articles."
                        : "We're working on adding more content. Check back soon!"
                      }
                    </p>
                    {(searchQuery || selectedCategory) && (
                      <ClearFiltersButton
                        onClick={() => {
                          setSearchQuery('');
                          setSelectedCategory('');
                        }}
                      >
                        Clear all filters
                      </ClearFiltersButton>
                    )}
                  </NoResultsContent>
                </NoPostsMessage>
              )}
            </>
          )}
        </ArticlesSection>
      </PageContainer>

      <Footer />
    </>
  );
};

// Enhanced Blog Post Card Styled Components
const EnhancedCardContainer = styled.article`
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid #f3f4f6;
  display: flex;
  flex-direction: column;
  height: 100%;
  animation: fadeInUp 0.6s ease-out;
  animation-delay: ${props => props.$index * 0.1}s;
  animation-fill-mode: both;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: #d95550;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

const CardImageContainer = styled.div`
  position: relative;
  height: 200px;
  overflow: hidden;
`;

const CardImageLink = styled(Link)`
  display: block;
  height: 100%;
  position: relative;
`;

const ImageOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0) 50%);
  opacity: 0;
  transition: opacity 0.3s ease;

  ${CardImageContainer}:hover & {
    opacity: 1;
  }
`;

const ReadTimeOverlay = styled.div`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.375rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  backdrop-filter: blur(4px);
`;

const EnhancedCardContent = styled.div`
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
`;

const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
`;

const EnhancedCardCategory = styled.div`
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;

  ${props => {
    const colors = {
      'pomodoro-technique': 'background: #fef3c7; color: #92400e;',
      'time-management': 'background: #dbeafe; color: #1e40af;',
      'adhd-productivity': 'background: #fce7f3; color: #be185d;',
      'ai-productivity': 'background: #d1fae5; color: #065f46;',
      'ai-tools': 'background: #e0e7ff; color: #3730a3;',
      'productivity': 'background: #f3e8ff; color: #6b21a8;'
    };
    return colors[props.$category] || 'background: rgba(217, 85, 80, 0.1); color: #d95550;';
  }}
`;

const PublishDate = styled.div`
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #9ca3af;
  font-size: 0.75rem;
  font-weight: 500;
`;

const EnhancedCardTitle = styled.h3`
  margin-bottom: 0.75rem;
  line-height: 1.4;

  a {
    color: #111827;
    text-decoration: none;
    font-size: 1.25rem;
    font-weight: 700;
    transition: color 0.2s ease;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;

    &:hover {
      color: #d95550;
    }
  }
`;

const EnhancedCardExcerpt = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex-grow: 1;
`;

const CardFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid #f3f4f6;
`;

const AuthorInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const AuthorAvatar = styled.div`
  width: 2rem;
  height: 2rem;
  background: linear-gradient(135deg, #d95550 0%, #c73e39 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
`;

const AuthorName = styled.div`
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
`;

const EnhancedReadMoreLink = styled(Link)`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #d95550;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  padding: 0.5rem 1rem;
  border: 1px solid #d95550;
  border-radius: 0.5rem;

  &:hover {
    background: #d95550;
    color: white;
    gap: 0.75rem;
  }

  svg {
    font-size: 0.75rem;
    transition: transform 0.2s ease;
  }

  &:hover svg {
    transform: translateX(2px);
  }
`;

// Styled Components
const PageContainer = styled.div`
  min-height: 100vh;
  background-color: #ffffff;
  padding-top: 80px;
`;

const PageHeaderSection = styled.section`
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 4rem 2rem;
  text-align: center;

  @media (max-width: 768px) {
    padding: 3rem 1rem;
  }
`;

const HeaderContent = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const PageIcon = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #d95550 0%, #c73e39 100%);
  color: white;
  border-radius: 1rem;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
`;

const PageTitle = styled.h1`
  font-size: 3rem;
  font-weight: 800;
  color: #111827;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const PageDescription = styled.p`
  font-size: 1.25rem;
  color: #6b7280;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;

  @media (max-width: 768px) {
    font-size: 1.125rem;
  }
`;

const PillarPagesSection = styled.section`
  max-width: 1200px;
  margin: 0 auto;
  padding: 4rem 2rem;

  @media (max-width: 768px) {
    padding: 3rem 1rem;
  }
`;

const SectionTitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2rem;
  text-align: center;

  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
`;

const PillarPagesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
`;

const PillarPageCard = styled.article`
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid #f3f4f6;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
  }
`;

const PillarCardImage = styled.img`
  width: 100%;
  height: 200px;
  object-fit: cover;
`;

const PillarCardContent = styled.div`
  padding: 1.5rem;
`;

const PillarCardCategory = styled.div`
  display: inline-block;
  background-color: rgba(217, 85, 80, 0.1);
  color: #d95550;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
  margin-bottom: 1rem;
`;

const PillarCardTitle = styled.h3`
  margin-bottom: 0.75rem;
  
  a {
    color: #111827;
    text-decoration: none;
    font-size: 1.25rem;
    font-weight: 700;
    line-height: 1.3;
    transition: color 0.2s ease;

    &:hover {
      color: #d95550;
    }
  }
`;

const PillarCardDescription = styled.p`
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
`;

const PillarCardLink = styled(Link)`
  color: #d95550;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.875rem;
  transition: color 0.2s ease;

  &:hover {
    color: #c73e39;
  }
`;

const FiltersSection = styled.section`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem 2rem;

  @media (max-width: 768px) {
    padding: 0 1rem 2rem;
  }
`;

const FiltersHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
`;

const FiltersToggle = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f3f4f6;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  color: #374151;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background: #e5e7eb;
  }

  @media (min-width: 769px) {
    display: none;
  }
`;

const FiltersContainer = styled.div`
  display: flex;
  gap: 2rem;
  align-items: center;

  @media (max-width: 768px) {
    display: ${props => props.$isVisible ? 'flex' : 'none'};
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
`;

const SearchForm = styled.form`
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
  min-width: 300px;

  @media (max-width: 768px) {
    min-width: auto;
    width: 100%;
  }
`;

const SearchInput = styled.input`
  flex: 1;
  padding: 0.75rem 1rem 0.75rem 3rem;
  border: none;
  outline: none;
  font-size: 0.875rem;
  background: transparent;

  &::placeholder {
    color: #9ca3af;
  }
`;

const SearchButton = styled.button`
  background: #d95550;
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background: #c73e39;
  }
`;

const ArticlesSection = styled.section`
  width: 100%;
  padding: 0 2rem 4rem;
  display: flex;
  flex-direction: column;
  align-items: center;

  @media (max-width: 768px) {
    padding: 0 1rem 4rem;
  }
`;

const ArticlesGrid = styled.div`
  display: grid;
  gap: 2rem;
  margin-bottom: 3rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;

  /* Dynamic grid based on item count */
  ${props => {
    if (props.$itemCount <= 2) {
      return `
        grid-template-columns: repeat(${props.$itemCount}, 350px);
        justify-content: center;
      `;
    } else {
      return `
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        justify-content: start;
      `;
    }
  }}

  @media (max-width: 768px) {
    grid-template-columns: 1fr !important;
    gap: 1.5rem;
    padding: 0 1rem;
    justify-content: stretch !important;
  }
`;

const LoadingMessage = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #6b7280;
  font-size: 1.1rem;
`;

const LoadingSpinner = styled.div`
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #d95550;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ErrorMessage = styled.div`
  text-align: center;
  color: #ef4444;
  font-size: 1.1rem;
  padding: 2rem;
`;

const NoPostsMessage = styled.div`
  grid-column: 1 / -1;
  text-align: center;
  padding: 4rem 2rem;
  color: #6b7280;

  h3 {
    color: #111827;
    margin-bottom: 0.5rem;
  }
`;

const NoResultsIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

// Enhanced UI Styled Components
const ArticlesHeaderSection = styled.div`
  margin-bottom: 2rem;
  text-align: center;
`;

const LatestArticlesHeader = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const SectionTitleWithIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 0.5rem;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0.5rem;
  }
`;

const ArticlesIcon = styled.div`
  font-size: 1.5rem;
`;

const ArticleCount = styled.div`
  span {
    background: linear-gradient(135deg, #d95550 0%, #c73e39 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
  }

  @media (max-width: 768px) {
    order: -1;
  }
`;

const SectionSubtitle = styled.p`
  color: #6b7280;
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
`;

const SearchSection = styled.div`
  margin-bottom: 2rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  background: none;
  border: none;
  padding: 0;
  box-shadow: none;
`;

const SearchInputWrapper = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 0.75rem;
  overflow: hidden;
  transition: all 0.2s ease;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;

  &:focus-within {
    border-color: #d95550;
    box-shadow: 0 0 0 3px rgba(217, 85, 80, 0.1);
  }
`;

const SearchIcon = styled.div`
  position: absolute;
  left: 1rem;
  color: #9ca3af;
  z-index: 1;
`;

const ClearSearchButton = styled.button`
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  transition: all 0.2s ease;

  &:hover {
    background: #f3f4f6;
    color: #6b7280;
  }
`;

const ActiveFilters = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-top: 1rem;
  flex-wrap: wrap;
`;

const ActiveFiltersLabel = styled.span`
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
`;

const FilterTag = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f3f4f6;
  color: #374151;
  padding: 0.375rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
`;

const FilterTagClose = styled.button`
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  font-size: 0.75rem;

  &:hover {
    color: #374151;
  }
`;

const ClearAllFilters = styled.button`
  background: none;
  border: 1px solid #d95550;
  color: #d95550;
  padding: 0.375rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #d95550;
    color: white;
  }
`;

const LoadingText = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;

  span {
    font-size: 1.1rem;
    font-weight: 600;
  }
`;

const LoadingSubtext = styled.div`
  font-size: 0.875rem;
  color: #9ca3af;
`;

const ErrorIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const ErrorText = styled.div`
  text-align: center;

  h3 {
    color: #111827;
    margin-bottom: 0.5rem;
  }

  p {
    color: #6b7280;
    margin-bottom: 1rem;
  }
`;

const RetryButton = styled.button`
  background: #d95550;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: #c73e39;
  }
`;

const ArticlesGridHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
  width: 100%;
  max-width: 1200px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
`;

const GridViewOptions = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const ViewOption = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  background: ${props => props.$active ? '#d95550' : 'white'};
  color: ${props => props.$active ? 'white' : '#6b7280'};
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #d95550;
    ${props => !props.$active && 'color: #d95550;'}
  }
`;

const SortingInfo = styled.div`
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
`;

const PaginationWrapper = styled.div`
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
`;

const NoResultsContent = styled.div`
  text-align: center;

  h3 {
    color: #111827;
    margin-bottom: 0.5rem;
  }

  p {
    color: #6b7280;
    margin-bottom: 1.5rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }
`;

const ClearFiltersButton = styled.button`
  background: #d95550;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: #c73e39;
  }
`;

export default AllArticlesPage;
