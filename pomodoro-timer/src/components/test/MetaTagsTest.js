import React, { useEffect, useState } from 'react';
import styled from 'styled-components';

const MetaTagsTest = () => {
  const [metaTags, setMetaTags] = useState([]);

  useEffect(() => {
    // Get all meta tags from the document
    const getAllMetaTags = () => {
      const tags = document.querySelectorAll('meta');
      const metaData = [];

      tags.forEach(tag => {
        const property = tag.getAttribute('property');
        const name = tag.getAttribute('name');
        const content = tag.getAttribute('content');

        if (property && (property.startsWith('og:') || property.startsWith('article:'))) {
          metaData.push({ type: 'Open Graph', key: property, value: content });
        } else if (name && name.startsWith('twitter:')) {
          metaData.push({ type: 'Twitter Card', key: name, value: content });
        } else if (name && ['description', 'keywords', 'author'].includes(name)) {
          metaData.push({ type: 'Basic', key: name, value: content });
        }
      });

      // Get title
      metaData.unshift({ type: 'Basic', key: 'title', value: document.title });

      // Get canonical
      const canonical = document.querySelector('link[rel="canonical"]');
      if (canonical) {
        metaData.push({ type: 'Basic', key: 'canonical', value: canonical.href });
      }

      return metaData;
    };

    setMetaTags(getAllMetaTags());

    // Update every 2 seconds to catch dynamic changes
    const interval = setInterval(() => {
      setMetaTags(getAllMetaTags());
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const groupedTags = metaTags.reduce((acc, tag) => {
    if (!acc[tag.type]) acc[tag.type] = [];
    acc[tag.type].push(tag);
    return acc;
  }, {});

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <TestContainer>
      <TestHeader>
        <h2>Meta Tags Debug Panel</h2>
        <p>Current page meta tags (Development only)</p>
      </TestHeader>

      {Object.entries(groupedTags).map(([type, tags]) => (
        <TagGroup key={type}>
          <GroupTitle>{type} Tags ({tags.length})</GroupTitle>
          <TagList>
            {tags.map((tag, index) => (
              <TagItem key={index}>
                <TagKey>{tag.key}:</TagKey>
                <TagValue>{tag.value}</TagValue>
              </TagItem>
            ))}
          </TagList>
        </TagGroup>
      ))}

      <TestActions>
        <TestButton onClick={() => window.open('https://developers.facebook.com/tools/debug/', '_blank')}>
          Test on Facebook Debugger
        </TestButton>
        <TestButton onClick={() => window.open('https://cards-dev.twitter.com/validator', '_blank')}>
          Test on Twitter Card Validator
        </TestButton>
        <TestButton onClick={() => window.open(`https://www.linkedin.com/post-inspector/inspect/${encodeURIComponent(window.location.href)}`, '_blank')}>
          Test on LinkedIn Inspector
        </TestButton>
      </TestActions>
    </TestContainer>
  );
};

// Styled Components
const TestContainer = styled.div`
  position: fixed;
  top: 20px;
  right: 20px;
  width: 400px;
  max-height: 80vh;
  background: white;
  border: 2px solid #007bff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
`;

const TestHeader = styled.div`
  background: #007bff;
  color: white;
  padding: 10px 15px;
  border-radius: 6px 6px 0 0;

  h2 {
    margin: 0 0 5px 0;
    font-size: 16px;
  }

  p {
    margin: 0;
    font-size: 12px;
    opacity: 0.9;
  }
`;

const TagGroup = styled.div`
  margin: 15px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
`;

const GroupTitle = styled.div`
  background: #f8f9fa;
  padding: 8px 12px;
  font-weight: bold;
  border-bottom: 1px solid #e9ecef;
  color: #495057;
`;

const TagList = styled.div`
  padding: 10px;
`;

const TagItem = styled.div`
  margin-bottom: 8px;
  padding: 6px;
  background: #f8f9fa;
  border-radius: 3px;
  border-left: 3px solid #007bff;
`;

const TagKey = styled.div`
  font-weight: bold;
  color: #007bff;
  margin-bottom: 2px;
`;

const TagValue = styled.div`
  color: #495057;
  word-break: break-all;
  line-height: 1.3;
`;

const TestActions = styled.div`
  padding: 15px;
  border-top: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const TestButton = styled.button`
  padding: 8px 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: background 0.3s;

  &:hover {
    background: #0056b3;
  }
`;

export default MetaTagsTest;
