import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import styled, { keyframes } from 'styled-components';
import { register } from '../../services/authService';

const Register = ({ onLogin }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState(5);
  const [registrationComplete, setRegistrationComplete] = useState(false);
  const navigate = useNavigate();

  const { name, email, password, confirmPassword } = formData;

  // Countdown effect after successful registration
  useEffect(() => {
    let timer;
    if (registrationComplete && countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
    } else if (registrationComplete && countdown === 0) {
      navigate('/login');
    }
    return () => clearTimeout(timer);
  }, [registrationComplete, countdown, navigate]);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    // Validate form
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters');
      return;
    }

    setIsLoading(true);

    try {
      const response = await register(name, email, password);
      setSuccess(response.message || 'Registration successful! Please log in.');
      setRegistrationComplete(true);
      // Clear form data
      setFormData({
        name: '',
        email: '',
        password: '',
        confirmPassword: '',
      });
    } catch (err) {
      setError(err.response?.data?.message || 'Registration failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <PageContainer>
      <RegisterContainer>
        <LogoContainer>
          <img src="/ai-pomo.png" alt="AI Pomo - Intelligent Pomodoro Timer and Productivity App Logo" style={{ height: '48px', width: 'auto' }} />
        </LogoContainer>

        <WelcomeText>Create your account</WelcomeText>

        {registrationComplete ? (
          <SuccessContainer>
            <SuccessIcon>✓</SuccessIcon>
            <SuccessTitle>Account Created Successfully!</SuccessTitle>
            <SuccessMessage>{success}</SuccessMessage>
            <CountdownMessage>
              Redirecting to login page in <CountdownNumber>{countdown}</CountdownNumber> seconds...
            </CountdownMessage>
            <LoginNowButton onClick={() => navigate('/login')}>
              Log in now
            </LoginNowButton>
          </SuccessContainer>
        ) : (
          <RegisterForm onSubmit={handleSubmit}>
            {error && <ErrorMessage>{error}</ErrorMessage>}
            {success && <SuccessMessageBox>{success}</SuccessMessageBox>}

            <FormGroup>
              <Label htmlFor="name">Full Name</Label>
              <InputWrapper>
                <Input
                  type="text"
                  id="name"
                  name="name"
                  value={name}
                  onChange={handleChange}
                  placeholder="Enter your name"
                  required
                />
              </InputWrapper>
            </FormGroup>

            <FormGroup>
              <Label htmlFor="email">Email</Label>
              <InputWrapper>
                <Input
                  type="email"
                  id="email"
                  name="email"
                  value={email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                  required
                />
              </InputWrapper>
            </FormGroup>

            <FormGroup>
              <Label htmlFor="password">Password</Label>
              <InputWrapper>
                <Input
                  type="password"
                  id="password"
                  name="password"
                  value={password}
                  onChange={handleChange}
                  placeholder="Create a password (min. 6 characters)"
                  required
                />
              </InputWrapper>
            </FormGroup>

            <FormGroup>
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <InputWrapper>
                <Input
                  type="password"
                  id="confirmPassword"
                  name="confirmPassword"
                  value={confirmPassword}
                  onChange={handleChange}
                  placeholder="Confirm your password"
                  required
                />
              </InputWrapper>
            </FormGroup>

            <TermsText>
              By creating an account, you agree to our <Link to="/terms">Terms of Service</Link> and <Link to="/privacy">Privacy Policy</Link>.
            </TermsText>

            <RegisterButton type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <ButtonSpinner /> Creating Account...
                </>
              ) : (
                'Create Account'
              )}
            </RegisterButton>

            <LoginLink>
              Already have an account? <Link to="/login">Log In</Link>
            </LoginLink>
          </RegisterForm>
        )}
      </RegisterContainer>

      <ImageContainer>
        <OverlayText>
          <OverlayTitle>Boost Your Productivity</OverlayTitle>
          <OverlayDescription>
            Join thousands of users who manage their time effectively with AI Pomo.
          </OverlayDescription>
        </OverlayText>
      </ImageContainer>
    </PageContainer>
  );
};

const spin = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

const fadeIn = keyframes`
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
`;

const PageContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background-color: ${props => props.theme['--bg-color']};

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const RegisterContainer = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  animation: ${fadeIn} 0.5s ease-out;

  @media (max-width: 768px) {
    padding: 1.5rem;
  }
`;

const LogoContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
  
  img {
    height: 48px;
    width: auto;
    object-fit: contain;
  }
`;

const WelcomeText = styled.h2`
  font-size: 1.8rem;
  margin-bottom: 2rem;
  color: ${props => props.theme['--text-color']};
  text-align: center;
`;

const RegisterForm = styled.form`
  width: 100%;
  max-width: 450px;
  padding: 2rem;
  background-color: ${props => props.theme['--card-bg']};
  border-radius: 1rem;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`;

const Label = styled.label`
  display: block;
  color: ${props => props.theme['--text-color']};
  font-weight: 500;
  font-size: 0.95rem;
  margin-bottom: 0.5rem;
`;

const InputWrapper = styled.div`
  position: relative;
  width: 100%;
`;

const Input = styled.input`
  width: 100%;
  padding: 0.9rem 1rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.5rem;
  background-color: ${props => props.theme['--card-bg']};
  color: ${props => props.theme['--text-color']};
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;

  &:focus {
    outline: none;
    border-color: #d95550;
    box-shadow: 0 0 0 3px rgba(217, 85, 80, 0.15);
  }

  &::placeholder {
    color: #aaa;
  }
`;

const TermsText = styled.p`
  font-size: 0.85rem;
  color: ${props => props.theme['--text-secondary'] || '#666'};
  margin-bottom: 1.5rem;
  line-height: 1.5;

  a {
    color: ${props => props.theme['--primary-color'] || '#d95550'};
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
`;

const RegisterButton = styled.button`
  width: 100%;
  padding: 0.9rem;
  background: linear-gradient(135deg, #d95550 0%, #eb6b56 100%);
  color: white;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.2s;
  display: flex;
  justify-content: center;
  align-items: center;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(217, 85, 80, 0.25);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    background: linear-gradient(135deg, #e0a0a0 0%, #e8b5b0 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

const ButtonSpinner = styled.div`
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: ${spin} 0.8s linear infinite;
  margin-right: 8px;
`;

const LoginLink = styled.div`
  margin-top: 1.5rem;
  text-align: center;
  font-size: 0.95rem;
  color: ${props => props.theme['--text-color']};

  a {
    color: ${props => props.theme['--primary-color'] || '#d95550'};
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }
`;

const ImageContainer = styled.div`
  flex: 1;
  background: linear-gradient(135deg, #d95550 0%, #eb6b56 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('https://images.unsplash.com/photo-1434030216411-0b793f4b4173?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80');
    background-size: cover;
    background-position: center;
    opacity: 0.2;
  }

  @media (max-width: 768px) {
    display: none;
  }
`;

const OverlayText = styled.div`
  position: relative;
  color: white;
  text-align: center;
  padding: 2rem;
  max-width: 80%;
`;

const OverlayTitle = styled.h2`
  font-size: 2.5rem;
  margin-bottom: 1rem;
  font-weight: 700;
`;

const OverlayDescription = styled.p`
  font-size: 1.2rem;
  opacity: 0.9;
`;



const ErrorMessage = styled.div`
  padding: 0.9rem;
  margin-bottom: 1.5rem;
  background-color: rgba(198, 40, 40, 0.08);
  color: #c62828;
  border-radius: 0.5rem;
  font-size: 0.9rem;
  border-left: 3px solid #c62828;
  animation: ${fadeIn} 0.3s ease-out;
`;

const SuccessMessageBox = styled.div`
  padding: 0.9rem;
  margin-bottom: 1.5rem;
  background-color: rgba(76, 175, 80, 0.08);
  color: #4caf50;
  border-radius: 0.5rem;
  font-size: 0.9rem;
  border-left: 3px solid #4caf50;
  animation: ${fadeIn} 0.3s ease-out;
`;

const SuccessContainer = styled.div`
  width: 100%;
  max-width: 450px;
  padding: 2rem;
  background-color: ${props => props.theme['--card-bg']};
  border-radius: 1rem;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  text-align: center;
  animation: ${fadeIn} 0.5s ease-out;
`;

const SuccessIcon = styled.div`
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  background-color: #4caf50;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
`;

const SuccessTitle = styled.h3`
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: ${props => props.theme['--text-color']};
`;

const SuccessMessage = styled.p`
  font-size: 1rem;
  margin-bottom: 1.5rem;
  color: ${props => props.theme['--text-color']};
`;

const CountdownMessage = styled.p`
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
  color: ${props => props.theme['--text-secondary'] || '#666'};
`;

const CountdownNumber = styled.span`
  font-weight: bold;
  color: #d95550;
  font-size: 1.1rem;
`;

const LoginNowButton = styled.button`
  padding: 0.8rem 1.5rem;
  background: linear-gradient(135deg, #d95550 0%, #eb6b56 100%);
  color: white;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.2s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(217, 85, 80, 0.25);
  }

  &:active {
    transform: translateY(0);
  }
`;

export default Register;
