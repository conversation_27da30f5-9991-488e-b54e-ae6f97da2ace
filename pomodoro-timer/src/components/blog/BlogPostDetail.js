import React, { useState, useEffect, useMemo } from 'react';
import { useParams, Link, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { FaCalendarAlt, FaClock, FaUser, FaTag, FaArrowLeft } from 'react-icons/fa';
import { format } from 'date-fns';
import { blogApi } from '../../services/blogApi';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';
import PageHeader from '../shared/PageHeader';
import Footer from '../shared/Footer';
import SEOHead from '../SEO/SEOHead';
import LazyImage from '../SEO/LazyImage';
import SocialShare from '../shared/SocialShare';
import MetaTagsTest from '../test/MetaTagsTest';
import { createBlogPostStructuredData, createBreadcrumbStructuredData } from '../../utils/structuredData';
import { addDebugButton } from '../../utils/socialMediaDebug';
import { parseTags, tagToSlug } from '../../utils/tagsUtils';

const BlogPostDetail = () => {
  const { slug } = useParams();
  const navigate = useNavigate();

  const [post, setPost] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [relatedPosts, setRelatedPosts] = useState([]);

  // Memoize ReactMarkdown components for better performance
  const markdownComponents = useMemo(() => ({
    // Custom component for links to open external links in new tab
    a: ({ href, children, ...props }) => {
      const isExternal = href && (href.startsWith('http') || href.startsWith('https'));
      return (
        <a
          href={href}
          target={isExternal ? '_blank' : undefined}
          rel={isExternal ? 'noopener noreferrer' : undefined}
          {...props}
        >
          {children}
        </a>
      );
    },
    // Custom component for code blocks
    pre: ({ children, ...props }) => (
      <pre {...props}>
        {children}
      </pre>
    ),
    // Custom component for inline code
    code: ({ inline, children, ...props }) => (
      <code {...props}>
        {children}
      </code>
    )
  }), []);

  // Fetch blog post
  useEffect(() => {
    const fetchPost = async () => {
      try {
        setLoading(true);
        const data = await blogApi.getBlogPostBySlug(slug);
        setPost(data);

        // Set document title and meta description for SEO
        document.title = `${data.seoTitle || data.title} | AI Pomo Blog`;

        const metaDescription = document.querySelector('meta[name="description"]');
        if (metaDescription) {
          metaDescription.setAttribute('content', data.seoDescription || data.excerpt);
        }

        setLoading(false);

        // Add debug button in development
        if (process.env.NODE_ENV === 'development') {
          setTimeout(() => addDebugButton(), 1000);
        }

        // Fetch related posts
        fetchRelatedPosts(data.category, data._id);
      } catch (err) {
        console.error('Error fetching blog post:', err);
        setError('Failed to load the article. Please try again later.');
        setLoading(false);
      }
    };

    fetchPost();

    // Cleanup
    return () => {
      // Reset document title
      document.title = 'AI Pomo - AI-Enhanced Pomodoro Timer';

      // Reset meta description
      const metaDescription = document.querySelector('meta[name="description"]');
      if (metaDescription) {
        metaDescription.setAttribute('content', 'AI Pomo combines the Pomodoro technique with AI-powered task management to help you focus, track progress, and achieve your goals efficiently.');
      }
    };
  }, [slug]);

  // Fetch related posts
  const fetchRelatedPosts = async (category, postId) => {
    try {
      const data = await blogApi.getBlogPosts(1, 3, category);
      // Filter out the current post and limit to 3 posts
      const filtered = data.posts
        .filter(p => p._id !== postId)
        .slice(0, 3);

      setRelatedPosts(filtered);
    } catch (err) {
      console.error('Error fetching related posts:', err);
    }
  };

  if (loading) {
    return <LoadingMessage>Loading article...</LoadingMessage>;
  }

  if (error) {
    return <ErrorMessage>{error}</ErrorMessage>;
  }

  if (!post) {
    return <ErrorMessage>Article not found.</ErrorMessage>;
  }

  // Create SEO data
  const breadcrumbs = [
    { name: 'Home', url: 'https://www.ai-pomo.com' },
    { name: 'Blog', url: 'https://www.ai-pomo.com/blog' },
    { name: post.title, url: `https://www.ai-pomo.com/blog/${post.slug}` }
  ];

  const structuredData = [
    createBlogPostStructuredData(post),
    createBreadcrumbStructuredData(breadcrumbs)
  ];

  // Ensure absolute URL for cover image
  const absoluteCoverImage = post.coverImage && post.coverImage.startsWith('http')
    ? post.coverImage
    : `https://www.ai-pomo.com${post.coverImage || '/ai-pomo.png'}`;

  return (
    <>
      <SEOHead
        title={post.seoTitle || post.title}
        description={post.seoDescription || post.excerpt}
        keywords={post.seoKeywords || (() => {
          const tags = parseTags(post.tags);
          return tags.length > 0 ? tags.join(', ') : 'productivity, pomodoro, time management';
        })()}
        image={absoluteCoverImage}
        url={`https://www.ai-pomo.com/blog/${post.slug}`}
        type="article"
        author={post.author?.name || 'AI Pomo Team'}
        publishedTime={post.createdAt}
        modifiedTime={post.updatedAt || post.createdAt}
        section={post.category}
        tags={parseTags(post.tags)}
        structuredData={structuredData}
      />
      <PageHeader />
      <PostContainer>
        <BackLink onClick={() => navigate(-1)}>
          <FaArrowLeft />
          <span>Back to Blog</span>
        </BackLink>

      <PostHeader>
        <PostTitle>{post.title}</PostTitle>

        <SocialShare
          title={post.title}
          excerpt={post.excerpt}
          url={`https://www.ai-pomo.com/blog/${post.slug}`}
          tags={post.tags || []}
        />

        <PostMeta>
          <MetaItem>
            <FaCalendarAlt />
            <span>{format(new Date(post.createdAt), 'MMMM d, yyyy')}</span>
          </MetaItem>
          <MetaItem>
            <FaClock />
            <span>{post.readTime} min read</span>
          </MetaItem>
          <MetaItem>
            <FaUser />
            <span>
              {post.author && post.author.name && post.author.name !== 'Admin User'
                ? post.author.name
                : 'AI Pomo Editorial Team'}
            </span>
          </MetaItem>
        </PostMeta>
      </PostHeader>

      <LazyImage
        src={post.coverImage}
        alt={post.title}
        style={{
          width: '100%',
          height: '450px',
          objectFit: 'cover',
          borderRadius: '12px',
          marginBottom: '3rem',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)'
        }}
      />

      <PostContent>
        <ReactMarkdown
          remarkPlugins={[remarkGfm, remarkBreaks]}
          components={markdownComponents}
        >
          {post.content}
        </ReactMarkdown>
      </PostContent>

      {post.tags && post.tags.length > 0 && (
        <PostTags>
          <TagsIcon>
            <FaTag />
          </TagsIcon>
          {parseTags(post.tags).slice(0, 5).map((tag, index) => (
            <TagLink
              key={index}
              to={`/tags/${tagToSlug(tag)}`}
              rel="tag"
            >
              {tag}
            </TagLink>
          ))}
          {parseTags(post.tags).length > 5 && (
            <TagLink style={{ backgroundColor: '#f3f4f6', color: '#6b7280', cursor: 'default' }}>
              +{parseTags(post.tags).length - 5} more
            </TagLink>
          )}
        </PostTags>
      )}

      {relatedPosts.length > 0 && (
        <RelatedPostsSection>
          <SectionTitle>Related Articles</SectionTitle>

          <RelatedPostsGrid>
            {relatedPosts.map(relatedPost => (
              <RelatedPostCard key={relatedPost._id}>
                <RelatedPostImageLink to={`/blog/${relatedPost.slug}`}>
                  <LazyImage
                    src={relatedPost.coverImage}
                    alt={relatedPost.title}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                  />
                </RelatedPostImageLink>

                <RelatedPostContent>
                  <RelatedPostTitle>
                    <Link to={`/blog/${relatedPost.slug}`}>{relatedPost.title}</Link>
                  </RelatedPostTitle>

                  <RelatedPostMeta>
                    <MetaItem>
                      <FaCalendarAlt />
                      <span>{format(new Date(relatedPost.createdAt), 'MMM d, yyyy')}</span>
                    </MetaItem>
                    <MetaItem>
                      <FaClock />
                      <span>{relatedPost.readTime} min read</span>
                    </MetaItem>
                  </RelatedPostMeta>
                </RelatedPostContent>
              </RelatedPostCard>
            ))}
          </RelatedPostsGrid>
        </RelatedPostsSection>
      )}


    </PostContainer>
    <Footer />
    <MetaTagsTest />
    </>
  );
};

// Styled components
const PostContainer = styled.article`
  max-width: 900px;
  margin: 0 auto;
  padding: 9rem 2rem 4rem;
  background-color: ${props => props.theme['--bg-color']};

  @media (max-width: 1024px) {
    max-width: 100%;
    padding: 8rem 1.5rem 3rem;
  }

  @media (max-width: 768px) {
    padding: 6rem 1rem 2rem;
  }
`;

const BackLink = styled.button`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: ${props => props.theme['--text-secondary']};
  font-size: 0.9rem;
  cursor: pointer;
  margin-bottom: 2rem;
  padding: 0;
  transition: color 0.2s;

  &:hover {
    color: ${props => props.theme['--primary-color']};
  }
`;

const PostHeader = styled.header`
  text-align: center;
  margin-bottom: 4rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid ${props => props.theme['--border-color'] || '#e1e4e8'};
`;

const PostCategory = styled.div`
  display: inline-block;
  background: linear-gradient(135deg, ${props => props.theme['--primary-color']}, ${props => props.theme['--primary-color-dark'] || '#c44a45'});
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.6rem 1.2rem;
  border-radius: 25px;
  margin-bottom: 2rem;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  box-shadow: 0 2px 8px rgba(217, 85, 80, 0.3);
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(217, 85, 80, 0.4);
  }
`;

const PostTitle = styled.h1`
  font-size: 3rem;
  font-weight: 800;
  line-height: 1.1;
  color: ${props => props.theme['--text-color']};
  margin-bottom: 2rem;
  background: linear-gradient(135deg, ${props => props.theme['--text-color']}, ${props => props.theme['--text-secondary']});
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  @media (max-width: 768px) {
    font-size: 2.25rem;
    line-height: 1.2;
  }

  @media (max-width: 480px) {
    font-size: 1.875rem;
  }
`;

const PostMeta = styled.div`
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 2rem;
  color: ${props => props.theme['--text-secondary']};
  font-size: 0.95rem;
  font-weight: 500;

  @media (max-width: 640px) {
    gap: 1rem;
    font-size: 0.875rem;
  }
`;

const MetaItem = styled.div`
  display: flex;
  align-items: center;
  gap: 0.6rem;
  padding: 0.5rem 0.75rem;
  background-color: ${props => props.theme['--card-bg'] || '#f8f9fa'};
  border-radius: 8px;
  transition: all 0.2s ease;

  svg {
    color: ${props => props.theme['--primary-color']};
    font-size: 1rem;
  }

  &:hover {
    background-color: ${props => props.theme['--primary-color']};
    color: white;
    transform: translateY(-1px);

    svg {
      color: white;
    }
  }
`;

const PostCoverImage = styled.img`
  width: 100%;
  height: 450px;
  object-fit: cover;
  border-radius: 12px;
  margin-bottom: 3rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: scale(1.01);
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.18);
  }

  @media (max-width: 768px) {
    height: 300px;
    margin-bottom: 2rem;
  }

  @media (max-width: 480px) {
    height: 250px;
    border-radius: 8px;
  }
`;

const PostContent = styled.div`
  font-size: 1.125rem;
  line-height: 1.75;
  color: ${props => props.theme['--text-color']};
  margin-bottom: 3rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

  /* Headings */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.3;
    color: ${props => props.theme['--text-color']};
    margin-top: 2.5rem;
    margin-bottom: 1rem;
    scroll-margin-top: 100px; /* For anchor links */
  }

  h1 {
    font-size: 2.25rem;
    border-bottom: 2px solid ${props => props.theme['--border-color']};
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
  }

  h2 {
    font-size: 1.875rem;
    border-bottom: 1px solid ${props => props.theme['--border-color']};
    padding-bottom: 0.3rem;
    margin-bottom: 1.25rem;
  }

  h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  h4 {
    font-size: 1.25rem;
    margin-bottom: 0.875rem;
  }

  h5 {
    font-size: 1.125rem;
    margin-bottom: 0.75rem;
  }

  h6 {
    font-size: 1rem;
    margin-bottom: 0.75rem;
    color: ${props => props.theme['--text-secondary']};
  }

  /* Paragraphs */
  p {
    margin-bottom: 1.5rem;
    text-align: justify;
    hyphens: auto;
  }

  /* Lists */
  ul, ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
  }

  ul {
    list-style-type: disc;
  }

  ol {
    list-style-type: decimal;
  }

  li {
    margin-bottom: 0.75rem;
    line-height: 1.6;
  }

  li > ul, li > ol {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }

  /* Task lists */
  li[data-task] {
    list-style: none;
    margin-left: -1.5rem;
  }

  /* Blockquotes */
  blockquote {
    border-left: 4px solid ${props => props.theme['--primary-color']};
    background-color: ${props => props.theme['--card-bg'] || '#f8f9fa'};
    padding: 1.25rem 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    color: ${props => props.theme['--text-secondary']};
    border-radius: 0 8px 8px 0;
    position: relative;

    &::before {
      content: '"';
      font-size: 4rem;
      color: ${props => props.theme['--primary-color']};
      position: absolute;
      top: -0.5rem;
      left: 0.5rem;
      opacity: 0.3;
      font-family: Georgia, serif;
    }

    p:last-child {
      margin-bottom: 0;
    }
  }

  /* Code */
  code {
    color: ${props => props.theme['--text-color']};
    font-family: inherit;
    font-size: 0.95em;
    font-weight: 600;
  }

  /* Code blocks */
  pre {
    background-color: ${props => props.theme['--code-bg'] || '#f6f8fa'};
    border: 1px solid ${props => props.theme['--border-color'] || '#e1e4e8'};
    padding: 1.5rem;
    border-radius: 8px;
    overflow-x: auto;
    margin: 2rem 0;
    position: relative;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    code {
      background-color: transparent;
      color: ${props => props.theme['--text-color']};
      padding: 0;
      border-radius: 0;
      font-size: 0.875rem;
      line-height: 1.5;
    }

    /* Add language label for code blocks */
    &[data-language]::before {
      content: attr(data-language);
      position: absolute;
      top: 0.5rem;
      right: 0.75rem;
      font-size: 0.75rem;
      color: ${props => props.theme['--text-tertiary']};
      text-transform: uppercase;
      font-weight: 600;
      letter-spacing: 0.5px;
    }
  }

  /* Tables */
  table {
    width: 100%;
    border-collapse: collapse;
    margin: 2rem 0;
    background-color: ${props => props.theme['--card-bg'] || '#fff'};
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  th, td {
    padding: 0.875rem 1rem;
    text-align: left;
    border-bottom: 1px solid ${props => props.theme['--border-color'] || '#e1e4e8'};
  }

  th {
    background-color: ${props => props.theme['--table-header-bg'] || '#f6f8fa'};
    font-weight: 600;
    color: ${props => props.theme['--text-color']};
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  tr:hover {
    background-color: ${props => props.theme['--table-row-hover'] || '#f8f9fa'};
  }

  /* Images */
  img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 2rem auto;
    display: block;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
      transform: scale(1.02);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }
  }

  /* Links */
  a {
    color: ${props => props.theme['--primary-color']};
    text-decoration: none;
    font-weight: 500;
    border-bottom: 1px solid transparent;
    transition: all 0.2s ease;

    &:hover {
      border-bottom-color: ${props => props.theme['--primary-color']};
      background-color: ${props => props.theme['--link-hover-bg'] || 'rgba(217, 85, 80, 0.05)'};
      padding: 0 2px;
      border-radius: 2px;
    }
  }

  /* Horizontal rules */
  hr {
    border: none;
    height: 2px;
    background: linear-gradient(
      90deg,
      transparent,
      ${props => props.theme['--border-color'] || '#e1e4e8'},
      transparent
    );
    margin: 3rem 0;
  }

  /* Strong and emphasis */
  strong {
    font-weight: 700;
    color: ${props => props.theme['--text-color']};
  }

  em {
    font-style: italic;
    color: ${props => props.theme['--text-secondary']};
  }

  /* Strikethrough */
  del {
    text-decoration: line-through;
    color: ${props => props.theme['--text-tertiary']};
  }

  /* Mark/highlight */
  mark {
    background-color: ${props => props.theme['--highlight-bg'] || '#fff3cd'};
    color: ${props => props.theme['--highlight-color'] || '#856404'};
    padding: 0.1rem 0.2rem;
    border-radius: 2px;
  }

  /* Keyboard keys */
  kbd {
    background-color: ${props => props.theme['--kbd-bg'] || '#f1f3f4'};
    border: 1px solid ${props => props.theme['--kbd-border'] || '#c6cbd1'};
    border-radius: 3px;
    box-shadow: inset 0 -1px 0 ${props => props.theme['--kbd-shadow'] || '#959da5'};
    color: ${props => props.theme['--kbd-color'] || '#444d56'};
    display: inline-block;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 0.75rem;
    line-height: 1;
    padding: 0.25rem 0.375rem;
    vertical-align: middle;
  }

  /* Responsive design */
  @media (max-width: 768px) {
    font-size: 1rem;
    line-height: 1.6;

    h1 { font-size: 1.875rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.25rem; }
    h4 { font-size: 1.125rem; }

    pre {
      padding: 1rem;
      margin: 1.5rem -1rem;
      border-radius: 0;
    }

    table {
      font-size: 0.875rem;
    }

    th, td {
      padding: 0.5rem 0.75rem;
    }
  }
`;

const PostTags = styled.div`
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 1rem;
  margin-bottom: 4rem;
  padding: 2rem 0;
  border-top: 2px solid ${props => props.theme['--border-color'] || '#e1e4e8'};
  border-bottom: 2px solid ${props => props.theme['--border-color'] || '#e1e4e8'};
`;

const TagsIcon = styled.span`
  color: ${props => props.theme['--text-secondary']};
  font-size: 1rem;
  font-weight: 600;
  margin-right: 0.5rem;
`;

const TagLink = styled(Link)`
  display: inline-flex;
  align-items: center;
  background: linear-gradient(135deg, ${props => props.theme['--tag-bg'] || '#f1f3f4'}, ${props => props.theme['--tag-bg-light'] || '#f8f9fa'});
  color: ${props => props.theme['--text-secondary']};
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 1px solid ${props => props.theme['--border-color'] || '#e1e4e8'};
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  &:hover {
    background: linear-gradient(135deg, ${props => props.theme['--primary-color']}, ${props => props.theme['--primary-color-dark'] || '#c44a45'});
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(217, 85, 80, 0.3);
    border-color: ${props => props.theme['--primary-color']};
  }

  &::before {
    content: '#';
    margin-right: 0.25rem;
    opacity: 0.7;
  }
`;

const RelatedPostsSection = styled.section`
  margin-top: 4rem;
  margin-bottom: 4rem;
`;

const SectionTitle = styled.h2`
  font-size: 1.75rem;
  margin-bottom: 1.5rem;
  color: ${props => props.theme['--text-color']};
`;

const RelatedPostsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;

  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 640px) {
    grid-template-columns: 1fr;
  }
`;

const RelatedPostCard = styled.article`
  background-color: ${props => props.theme['--card-bg']};
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s, box-shadow 0.3s;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;

const RelatedPostImageLink = styled(Link)`
  display: block;
  height: 150px;
  overflow: hidden;
`;

const RelatedPostImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s;

  ${RelatedPostImageLink}:hover & {
    transform: scale(1.05);
  }
`;

const RelatedPostContent = styled.div`
  padding: 1rem;
`;

const RelatedPostTitle = styled.h3`
  font-size: 1.1rem;
  margin-bottom: 0.75rem;
  line-height: 1.4;

  a {
    color: ${props => props.theme['--text-color']};
    text-decoration: none;
    transition: color 0.2s;

    &:hover {
      color: ${props => props.theme['--primary-color']};
    }
  }
`;

const RelatedPostMeta = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  font-size: 0.8rem;
  color: ${props => props.theme['--text-tertiary']};
`;

const LoadingMessage = styled.div`
  text-align: center;
  padding: 4rem 0;
  font-size: 1.1rem;
  color: ${props => props.theme['--text-secondary']};
`;

const ErrorMessage = styled.div`
  text-align: center;
  padding: 2rem;
  background-color: #fff0f0;
  border-radius: 4px;
  color: #d32f2f;
  margin: 2rem auto;
  max-width: 800px;
`;



export default BlogPostDetail;
