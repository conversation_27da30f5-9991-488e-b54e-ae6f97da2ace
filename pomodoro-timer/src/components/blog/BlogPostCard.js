import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { FaCalendarAlt, FaClock } from 'react-icons/fa';
import { format } from 'date-fns';
import LazyImage from '../SEO/LazyImage';

const BlogPostCard = ({ post }) => {
  return (
    <CardContainer>
      <CardImageLink to={`/blog/${post.slug}`}>
        <LazyImage
          src={post.coverImage}
          alt={post.title}
          style={{ width: '100%', height: '100%', objectFit: 'cover' }}
        />
      </CardImageLink>

      <CardContent>
        <CardCategory>{post.category}</CardCategory>
        <CardTitle>
          <Link to={`/blog/${post.slug}`}>{post.title}</Link>
        </CardTitle>
        <CardExcerpt>{post.excerpt}</CardExcerpt>

        <CardMeta>
          <MetaItem>
            <FaCalendarAlt />
            <span>{format(new Date(post.createdAt), 'MMM d, yyyy')}</span>
          </MetaItem>
          <MetaItem>
            <FaClock />
            <span>{post.readTime} min read</span>
          </MetaItem>
        </CardMeta>

        <ReadMoreLink to={`/blog/${post.slug}`}>
          Read more →
        </ReadMoreLink>
      </CardContent>
    </CardContainer>
  );
};

// Styled components
const CardContainer = styled.article`
  background-color: ${props => props.theme['--card-bg']};
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  margin: 0 auto;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
  }
`;

const CardImageLink = styled(Link)`
  display: block;
  height: 180px;
  overflow: hidden;
  position: relative;
`;



const CardContent = styled.div`
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
`;

const CardCategory = styled.div`
  display: inline-block;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  color: ${props => props.theme['--primary-color']};
  margin-bottom: 0.75rem;
  letter-spacing: 0.5px;
  background-color: rgba(217, 85, 80, 0.1);
  padding: 0.25rem 0.6rem;
  border-radius: 4px;
  align-self: flex-start;
`;

const CardTitle = styled.h2`
  font-size: 1.15rem;
  margin-bottom: 0.75rem;
  line-height: 1.4;
  font-weight: 700;

  a {
    color: ${props => props.theme['--text-color']};
    text-decoration: none;
    transition: color 0.2s ease;

    &:hover {
      color: ${props => props.theme['--primary-color']};
    }
  }
`;

const CardExcerpt = styled.p`
  font-size: 0.9rem;
  color: ${props => props.theme['--text-secondary']};
  margin-bottom: 1rem;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const CardMeta = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  font-size: 0.8rem;
  color: ${props => props.theme['--text-tertiary']};
  margin-bottom: 1rem;
  margin-top: auto;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
`;

const MetaItem = styled.div`
  display: flex;
  align-items: center;

  svg {
    margin-right: 0.35rem;
    font-size: 0.8rem;
    color: ${props => props.theme['--primary-color']};
    opacity: 0.9;
  }
`;

const ReadMoreLink = styled(Link)`
  display: inline-flex;
  align-items: center;
  color: ${props => props.theme['--primary-color']};
  font-weight: 600;
  font-size: 0.85rem;
  text-decoration: none;
  transition: all 0.3s ease;
  padding: 0.5rem 0;
  position: relative;

  &:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: ${props => props.theme['--primary-color']};
    transition: width 0.3s ease;
  }

  &:hover {
    color: ${props => props.theme['--primary-color-dark']};

    &:after {
      width: 100%;
    }
  }
`;

export default BlogPostCard;
