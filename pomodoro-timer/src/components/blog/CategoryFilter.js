import React from 'react';
import styled from 'styled-components';
import { FaFilter, FaLayerGroup, FaClock, FaBrain, FaRobot, FaTasks, FaBalanceScale, FaGraduationCap } from 'react-icons/fa';

const CategoryFilter = ({ categories, selectedCategory, onCategoryChange }) => {
  // Category icon mapping
  const getCategoryIcon = (categorySlug) => {
    const iconMap = {
      'all': <FaLayerGroup />,
      'pomodoro-technique': <FaClock />,
      'time-management': <FaTasks />,
      'adhd-productivity': <FaBrain />,
      'ai-productivity': <FaRobot />,
      'ai-tools': <FaRobot />,
      'productivity': <FaGraduationCap />,
      'work-life-balance': <FaBalanceScale />
    };
    return iconMap[categorySlug] || <FaLayerGroup />;
  };

  // Get category count (you might want to pass this as a prop)
  const getCategoryCount = (categorySlug) => {
    // This would ideally come from your API
    const counts = {
      'pomodoro-technique': 8,
      'time-management': 12,
      'adhd-productivity': 6,
      'ai-productivity': 10,
      'ai-tools': 15,
      'productivity': 20,
      'work-life-balance': 7
    };
    return counts[categorySlug] || 0;
  };

  const totalArticles = categories.reduce((sum, cat) => sum + getCategoryCount(cat.slug), 0);

  return (
    <FilterSection>
      <FilterHeader>
        <FilterHeading>
          <FilterIcon>
            <FaFilter />
          </FilterIcon>
          Browse by Category
        </FilterHeading>
        {selectedCategory && (
          <ActiveFilterBadge>
            Filtered by: <strong>{categories.find(cat => cat.slug === selectedCategory)?.name}</strong>
            <ClearFilterButton onClick={() => onCategoryChange('')}>✕</ClearFilterButton>
          </ActiveFilterBadge>
        )}
      </FilterHeader>

      <FilterContainer>
        <FilterButton
          $isActive={!selectedCategory}
          $category="all"
          onClick={() => onCategoryChange('')}
        >
          <ButtonIcon $isActive={!selectedCategory}>
            {getCategoryIcon('all')}
          </ButtonIcon>
          <ButtonLabel>All Topics</ButtonLabel>
        </FilterButton>

        {categories.map(category => (
          <FilterButton
            key={category._id}
            $isActive={selectedCategory === category.slug}
            $category={category.slug}
            onClick={() => onCategoryChange(category.slug)}
          >
            <ButtonIcon $isActive={selectedCategory === category.slug}>
              {getCategoryIcon(category.slug)}
            </ButtonIcon>
            <ButtonLabel>{category.name}</ButtonLabel>
          </FilterButton>
        ))}
      </FilterContainer>
    </FilterSection>
  );
};

// Compact Styled Components
const FilterSection = styled.div`
  margin-bottom: 2rem;
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
`;

const FilterHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.25rem;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
`;

const FilterHeading = styled.h2`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  color: #111827;
`;

const FilterIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem;
  height: 1.75rem;
  background: linear-gradient(135deg, #d95550 0%, #c73e39 100%);
  color: white;
  border-radius: 0.375rem;
  font-size: 0.75rem;
`;

const ActiveFilterBadge = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f0f9ff;
  color: #0c4a6e;
  padding: 0.375rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid #0ea5e9;

  strong {
    color: #0369a1;
  }
`;

const FilterContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  justify-content: center;

  @media (max-width: 768px) {
    gap: 0.5rem;
  }
`;

const FilterButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 9999px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;

  ${props => props.$isActive && `
    background: linear-gradient(135deg, #d95550 0%, #c73e39 100%);
    border-color: #d95550;
    color: white;
    box-shadow: 0 2px 8px rgba(217, 85, 80, 0.25);
  `}

  ${props => {
    const categoryColors = {
      'pomodoro-technique': '#fef3c7',
      'time-management': '#dbeafe',
      'adhd-productivity': '#fce7f3',
      'ai-productivity': '#d1fae5',
      'ai-tools': '#e0e7ff',
      'productivity': '#f3e8ff',
      'work-life-balance': '#fef3c7'
    };

    if (!props.$isActive) {
      return `
        &:hover {
          background: ${categoryColors[props.$category] || '#f9fafb'};
          border-color: #d95550;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
      `;
    }

    return `
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 16px rgba(217, 85, 80, 0.3);
      }
    `;
  }}
`;

const ButtonIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  font-size: 0.75rem;
  flex-shrink: 0;
  color: ${props => props.$isActive ? 'white' : '#d95550'};
  transition: all 0.2s ease;
`;

const ButtonLabel = styled.span`
  font-weight: 600;
  line-height: 1;
`;

const ButtonCount = styled.span`
  font-size: 0.75rem;
  opacity: 0.7;
  font-weight: 500;
  margin-left: 0.25rem;
`;

const ClearFilterButton = styled.button`
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  font-size: 0.75rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;

  &:hover {
    background: rgba(107, 114, 128, 0.1);
    color: #374151;
  }
`;

export default CategoryFilter;
