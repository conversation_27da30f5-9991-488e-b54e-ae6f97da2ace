import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { FaTag, FaSearch, FaArrowLeft } from 'react-icons/fa';
import { blogApi } from '../../services/blogApi';
import PageHeader from '../shared/PageHeader';
import Footer from '../shared/Footer';
import SEOHead from '../SEO/SEOHead';
import { parseTags } from '../../utils/tagsUtils';

const TagsListPage = () => {
  const [allTags, setAllTags] = useState([]);
  const [filteredTags, setFilteredTags] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchAllTags();
  }, []);

  useEffect(() => {
    // Filter tags based on search term
    if (searchTerm.trim() === '') {
      setFilteredTags(allTags);
    } else {
      const filtered = allTags.filter(tagData =>
        tagData.tag.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredTags(filtered);
    }
  }, [searchTerm, allTags]);

  const fetchAllTags = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch all blog posts to extract tags
      const response = await blogApi.getBlogPosts(1, 200); // Get more articles to ensure we capture all posts
      
      // Extract and count all tags
      const tagCounts = {};
      response.posts.forEach(post => {
        if (post.tags) {
          const tagsArray = parseTags(post.tags);
          
          // Count each tag
          tagsArray.forEach(tag => {
            if (tag) {
              tagCounts[tag] = (tagCounts[tag] || 0) + 1;
            }
          });
        }
      });

      // Convert to array and sort by count (descending) then alphabetically
      const tagsArray = Object.entries(tagCounts)
        .map(([tag, count]) => ({
          tag,
          count,
          slug: tag.toLowerCase().replace(/\s+/g, '-')
        }))
        .sort((a, b) => {
          // Sort by count first (descending), then by name (ascending)
          if (b.count !== a.count) {
            return b.count - a.count;
          }
          return a.tag.localeCompare(b.tag);
        });

      setAllTags(tagsArray);
      setFilteredTags(tagsArray);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching tags:', err);
      setError('Failed to load tags. Please try again later.');
      setLoading(false);
    }
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  if (loading) {
    return (
      <>
        <PageHeader />
        <Container>
          <LoadingMessage>Loading tags...</LoadingMessage>
        </Container>
        <Footer />
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageHeader />
        <Container>
          <ErrorMessage>{error}</ErrorMessage>
        </Container>
        <Footer />
      </>
    );
  }

  return (
    <>
      <SEOHead
        title="All Tags | AI Pomo Blog"
        description="Explore all topics and tags on the AI Pomo blog. Find articles about productivity, time management, Pomodoro technique, ADHD, AI, and more."
        keywords="tags, topics, productivity, time management, pomodoro technique, ADHD, AI, focus, blog"
        url="https://www.ai-pomo.com/tags"
        type="website"
      />
      <PageHeader />
      <Container>
        <BackLink as={Link} to="/blog">
          <FaArrowLeft />
          <span>Back to Blog</span>
        </BackLink>

        <Header>
          <HeaderIcon>
            <FaTag />
          </HeaderIcon>
          <Title>Explore All Topics</Title>
          <Description>
            Discover articles by topic. We have {allTags.length} different tags covering various aspects of productivity and time management.
          </Description>
        </Header>

        <SearchSection>
          <SearchContainer>
            <SearchIcon>
              <FaSearch />
            </SearchIcon>
            <SearchInput
              type="text"
              placeholder="Search tags..."
              value={searchTerm}
              onChange={handleSearchChange}
            />
          </SearchContainer>
        </SearchSection>

        {filteredTags.length === 0 ? (
          <NoResultsMessage>
            <h3>No tags found</h3>
            <p>
              {searchTerm 
                ? `No tags match "${searchTerm}". Try a different search term.`
                : "No tags available at the moment."
              }
            </p>
          </NoResultsMessage>
        ) : (
          <>
            <TagsStats>
              Showing {filteredTags.length} of {allTags.length} tags
            </TagsStats>

            <TagsGrid>
              {filteredTags.map((tagData, index) => (
                <TagCard key={tagData.slug} $size={getTagSize(tagData.count, allTags)}>
                  <TagLink to={`/tags/${tagData.slug}`}>
                    <TagName>{tagData.tag}</TagName>
                    <TagCount>
                      {tagData.count} article{tagData.count !== 1 ? 's' : ''}
                    </TagCount>
                  </TagLink>
                </TagCard>
              ))}
            </TagsGrid>

            <PopularTagsSection>
              <SectionTitle>Most Popular Tags</SectionTitle>
              <PopularTagsList>
                {allTags.slice(0, 8).map((tagData) => (
                  <PopularTag key={tagData.slug} as={Link} to={`/tags/${tagData.slug}`}>
                    <TagName>{tagData.tag}</TagName>
                    <TagBadge>{tagData.count}</TagBadge>
                  </PopularTag>
                ))}
              </PopularTagsList>
            </PopularTagsSection>
          </>
        )}
      </Container>
      <Footer />
    </>
  );
};

// Helper function to determine tag size based on article count
const getTagSize = (count, allTags) => {
  if (allTags.length === 0) return 'medium';
  
  const maxCount = Math.max(...allTags.map(t => t.count));
  const minCount = Math.min(...allTags.map(t => t.count));
  const range = maxCount - minCount;
  
  if (range === 0) return 'medium';
  
  const percentage = (count - minCount) / range;
  
  if (percentage >= 0.8) return 'large';
  if (percentage >= 0.5) return 'medium';
  return 'small';
};

// Styled Components
const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 7rem 2rem 4rem;

  @media (max-width: 1024px) {
    padding: 6rem 1.5rem 3rem;
  }

  @media (max-width: 768px) {
    padding: 5rem 1rem 2rem;
  }
`;

const BackLink = styled.a`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  text-decoration: none;
  font-size: 0.9rem;
  margin-bottom: 2rem;
  transition: color 0.2s;

  &:hover {
    color: #d95550;
  }
`;

const Header = styled.header`
  text-align: center;
  margin-bottom: 4rem;
`;

const HeaderIcon = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #d95550, #c44a45);
  color: white;
  border-radius: 50%;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 12px rgba(217, 85, 80, 0.3);
`;

const Title = styled.h1`
  font-size: 3rem;
  font-weight: 800;
  color: #111827;
  margin-bottom: 1rem;
  
  @media (max-width: 768px) {
    font-size: 2.25rem;
  }
`;

const Description = styled.p`
  font-size: 1.125rem;
  color: #6b7280;
  margin: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
`;

const SearchSection = styled.div`
  margin-bottom: 3rem;
  display: flex;
  justify-content: center;
`;

const SearchContainer = styled.div`
  position: relative;
  max-width: 400px;
  width: 100%;
`;

const SearchIcon = styled.div`
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 0.875rem;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 25px;
  font-size: 1rem;
  background-color: white;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #d95550;
  }

  &::placeholder {
    color: #9ca3af;
  }
`;

const TagsStats = styled.div`
  text-align: center;
  color: #6b7280;
  font-size: 0.9rem;
  margin-bottom: 2rem;
`;

const TagsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 4rem;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.75rem;
  }
`;

const TagCard = styled.div`
  background: white;
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
  overflow: hidden;

  ${props => {
    switch (props.$size) {
      case 'large':
        return `
          grid-column: span 2;
          grid-row: span 2;
          
          @media (max-width: 768px) {
            grid-column: span 1;
            grid-row: span 1;
          }
        `;
      case 'medium':
        return ``;
      case 'small':
      default:
        return ``;
    }
  }}

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(217, 85, 80, 0.15);
    border-color: #d95550;
  }
`;

const TagLink = styled(Link)`
  display: block;
  padding: 2rem 1.5rem;
  text-decoration: none;
  text-align: center;
  height: 100%;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, rgba(217, 85, 80, 0.05), rgba(196, 74, 69, 0.05));
  }
`;

const TagName = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
  transition: color 0.2s;

  ${TagLink}:hover & {
    color: #d95550;
  }
`;

const TagCount = styled.p`
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
`;

const PopularTagsSection = styled.section`
  margin-top: 4rem;
  padding-top: 3rem;
  border-top: 2px solid #e5e7eb;
`;

const SectionTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2rem;
  text-align: center;
`;

const PopularTagsList = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const PopularTag = styled.a`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #f8f9fa, #f1f3f4);
  border-radius: 12px;
  text-decoration: none;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, #d95550, #c44a45);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(217, 85, 80, 0.3);

    ${TagName} {
      color: white;
    }
  }
`;

const TagBadge = styled.span`
  background-color: #d95550;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
`;

const NoResultsMessage = styled.div`
  text-align: center;
  padding: 4rem 2rem;
  background-color: #f9fafb;
  border-radius: 12px;

  h3 {
    color: #111827;
    margin-bottom: 1rem;
  }

  p {
    color: #6b7280;
    margin: 0;
  }
`;

const LoadingMessage = styled.div`
  text-align: center;
  padding: 4rem 0;
  font-size: 1.1rem;
  color: #6b7280;
`;

const ErrorMessage = styled.div`
  text-align: center;
  padding: 2rem;
  background-color: #fef2f2;
  border-radius: 8px;
  color: #dc2626;
  margin: 2rem auto;
  max-width: 600px;
`;

export default TagsListPage;