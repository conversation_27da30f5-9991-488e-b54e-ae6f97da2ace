import React, { useState, useEffect } from 'react';
import { useParams, Link } from 'react-router-dom';
import styled from 'styled-components';
import { FaCalendarAlt, FaClock, FaUser, FaTag, FaArrowLeft } from 'react-icons/fa';
import { format } from 'date-fns';
import { blogApi } from '../../services/blogApi';
import PageHeader from '../shared/PageHeader';
import Footer from '../shared/Footer';
import SEOHead from '../SEO/SEOHead';
import LazyImage from '../SEO/LazyImage';
import { parseTags, tagToSlug, slugToTag, postHasTag } from '../../utils/tagsUtils';

const TagPage = () => {
  const { tagSlug } = useParams();
  const [articles, setArticles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalArticles, setTotalArticles] = useState(0);

  // Convert tag slug back to readable format
  const tagName = slugToTag(tagSlug);

  useEffect(() => {
    fetchTagArticles();
  }, [tagSlug, currentPage]);

  const fetchTagArticles = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Fetch ALL articles to properly filter by tag
      // We need to get all articles first, then filter, then paginate
      const response = await blogApi.getBlogPosts(1, 200); // Get more articles to ensure we capture all
      
      // Filter articles that contain this tag (case insensitive)
      const allFilteredArticles = response.posts.filter(post => postHasTag(post, tagSlug));
      
      // Calculate pagination based on filtered results
      const totalFilteredCount = allFilteredArticles.length;
      setTotalArticles(totalFilteredCount);
      setTotalPages(Math.ceil(totalFilteredCount / 12));
      
      // Get articles for current page
      const startIndex = (currentPage - 1) * 12;
      const endIndex = startIndex + 12;
      const paginatedArticles = allFilteredArticles.slice(startIndex, endIndex);
      
      setArticles(paginatedArticles);
      
      setLoading(false);
    } catch (err) {
      console.error('Error fetching tag articles:', err);
      setError('Failed to load articles for this tag. Please try again later.');
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <>
        <PageHeader />
        <Container>
          <LoadingMessage>Loading articles...</LoadingMessage>
        </Container>
        <Footer />
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageHeader />
        <Container>
          <ErrorMessage>{error}</ErrorMessage>
        </Container>
        <Footer />
      </>
    );
  }

  return (
    <>
      <SEOHead
        title={`${tagName} Articles${currentPage > 1 ? ` - Page ${currentPage}` : ''} | AI Pomo Blog`}
        description={`Explore all articles about ${tagName}. Discover insights, tips, and strategies related to ${tagName} for productivity and time management.${currentPage > 1 ? ` Page ${currentPage} of ${totalPages}.` : ''}`}
        keywords={`${tagName}, productivity, time management, pomodoro technique, articles`}
        url={`https://www.ai-pomo.com/tags/${tagSlug}${currentPage > 1 ? `?page=${currentPage}` : ''}`}
        type="website"
        prev={currentPage > 1 ? `https://www.ai-pomo.com/tags/${tagSlug}${currentPage > 2 ? `?page=${currentPage - 1}` : ''}` : null}
        next={currentPage < totalPages ? `https://www.ai-pomo.com/tags/${tagSlug}?page=${currentPage + 1}` : null}
      />
      <PageHeader />
      <Container>
        <BackLink as={Link} to="/blog">
          <FaArrowLeft />
          <span>Back to Blog</span>
        </BackLink>

        <TagHeader>
          <TagIcon>
            <FaTag />
          </TagIcon>
          <TagTitle>{tagName}</TagTitle>
          <TagDescription>
            {totalArticles} article{totalArticles !== 1 ? 's' : ''} tagged with "{tagName}"
            {totalPages > 1 && (
              <> (Page {currentPage} of {totalPages})</>
            )}
          </TagDescription>
        </TagHeader>

        {articles.length === 0 ? (
          <NoArticlesMessage>
            <h3>No articles found</h3>
            <p>We couldn't find any articles with the tag "{tagName}". Check out our <Link to="/blog">latest articles</Link> instead.</p>
          </NoArticlesMessage>
        ) : (
          <ArticlesGrid>
            {articles.map(article => (
              <ArticleCard key={article._id}>
                <ArticleImageLink to={`/blog/${article.slug}`}>
                  <LazyImage
                    src={article.coverImage}
                    alt={article.title}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                  />
                </ArticleImageLink>

                <ArticleContent>
                  <ArticleTitle>
                    <Link to={`/blog/${article.slug}`}>{article.title}</Link>
                  </ArticleTitle>

                  <ArticleExcerpt>{article.excerpt}</ArticleExcerpt>

                  <ArticleMeta>
                    <MetaItem>
                      <FaCalendarAlt />
                      <span>{format(new Date(article.createdAt), 'MMM d, yyyy')}</span>
                    </MetaItem>
                    <MetaItem>
                      <FaClock />
                      <span>{article.readTime} min read</span>
                    </MetaItem>
                    <MetaItem>
                      <FaUser />
                      <span>
                        {article.author && article.author.name && article.author.name !== 'Admin User'
                          ? article.author.name
                          : 'AI Pomo Editorial Team'
                        }
                      </span>
                    </MetaItem>
                  </ArticleMeta>

                  {article.tags && article.tags.length > 0 && (
                    <ArticleTags>
                      {(() => {
                        const tagsArray = parseTags(article.tags);
                        
                        return (
                          <>
                            {tagsArray.slice(0, 3).map((tag, index) => (
                              <TagBadge
                                key={index}
                                as={Link}
                                to={`/tags/${tagToSlug(tag)}`}
                                $isCurrentTag={tagToSlug(tag) === tagSlug}
                              >
                                {tag}
                              </TagBadge>
                            ))}
                            {tagsArray.length > 3 && (
                              <TagBadge $isMore>+{tagsArray.length - 3} more</TagBadge>
                            )}
                          </>
                        );
                      })()}
                    </ArticleTags>
                  )}
                </ArticleContent>
              </ArticleCard>
            ))}
          </ArticlesGrid>
        )}

        {totalPages > 1 && (
          <PaginationSection>
            <PaginationNav>
              {currentPage > 1 && (
                <PaginationButton
                  onClick={() => setCurrentPage(currentPage - 1)}
                >
                  Previous
                </PaginationButton>
              )}
              
              <PaginationInfo>
                Page {currentPage} of {totalPages}
              </PaginationInfo>
              
              {currentPage < totalPages && (
                <PaginationButton
                  onClick={() => setCurrentPage(currentPage + 1)}
                >
                  Next
                </PaginationButton>
              )}
            </PaginationNav>
          </PaginationSection>
        )}

        <RelatedTagsSection>
          <SectionTitle>Explore Other Topics</SectionTitle>
          <RelatedTagsGrid>
            <TagLink as={Link} to="/tags/productivity" rel="nofollow">Productivity</TagLink>
            <TagLink as={Link} to="/tags/time-management" rel="nofollow">Time Management</TagLink>
            <TagLink as={Link} to="/tags/pomodoro-technique" rel="nofollow">Pomodoro Technique</TagLink>
            <TagLink as={Link} to="/tags/focus" rel="nofollow">Focus</TagLink>
            <TagLink as={Link} to="/tags/adhd" rel="nofollow">ADHD</TagLink>
            <TagLink as={Link} to="/tags/ai" rel="nofollow">AI</TagLink>
          </RelatedTagsGrid>
        </RelatedTagsSection>
      </Container>
      <Footer />
    </>
  );
};

// Styled Components
const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 7rem 2rem 4rem;

  @media (max-width: 1024px) {
    padding: 6rem 1.5rem 3rem;
  }

  @media (max-width: 768px) {
    padding: 5rem 1rem 2rem;
  }
`;

const BackLink = styled.a`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  text-decoration: none;
  font-size: 0.9rem;
  margin-bottom: 2rem;
  transition: color 0.2s;

  &:hover {
    color: #d95550;
  }
`;

const TagHeader = styled.header`
  text-align: center;
  margin-bottom: 4rem;
  padding-bottom: 2rem;
  border-bottom: 2px solid #e5e7eb;
`;

const TagIcon = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #d95550, #c44a45);
  color: white;
  border-radius: 50%;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 12px rgba(217, 85, 80, 0.3);
`;

const TagTitle = styled.h1`
  font-size: 3rem;
  font-weight: 800;
  color: #111827;
  margin-bottom: 1rem;
  
  @media (max-width: 768px) {
    font-size: 2.25rem;
  }
`;

const TagDescription = styled.p`
  font-size: 1.125rem;
  color: #6b7280;
  margin: 0;
`;

const ArticlesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
`;

const ArticleCard = styled.article`
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
  }
`;

const ArticleImageLink = styled(Link)`
  display: block;
  height: 200px;
  overflow: hidden;
`;

const ArticleContent = styled.div`
  padding: 1.5rem;
`;

const ArticleTitle = styled.h2`
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  line-height: 1.4;

  a {
    color: #111827;
    text-decoration: none;
    transition: color 0.2s;

    &:hover {
      color: #d95550;
    }
  }
`;

const ArticleExcerpt = styled.p`
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.6;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const ArticleMeta = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
  font-size: 0.8rem;
  color: #9ca3af;
`;

const MetaItem = styled.div`
  display: flex;
  align-items: center;
  gap: 0.25rem;

  svg {
    color: #d95550;
    font-size: 0.75rem;
  }
`;

const ArticleTags = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
`;

const TagBadge = styled.a`
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background-color: ${props => props.$isCurrentTag ? '#d95550' : '#f3f4f6'};
  color: ${props => props.$isCurrentTag ? 'white' : '#6b7280'};
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s;
  border: ${props => props.$isCurrentTag ? '1px solid #d95550' : '1px solid #e5e7eb'};

  ${props => !props.$isMore && !props.$isCurrentTag && `
    &:hover {
      background-color: #d95550;
      color: white;
      border-color: #d95550;
    }
  `}

  ${props => props.$isMore && `
    background-color: #e5e7eb;
    color: #9ca3af;
    cursor: default;
  `}
`;

const NoArticlesMessage = styled.div`
  text-align: center;
  padding: 4rem 2rem;
  background-color: #f9fafb;
  border-radius: 12px;
  margin-bottom: 4rem;

  h3 {
    color: #111827;
    margin-bottom: 1rem;
  }

  p {
    color: #6b7280;
    margin: 0;
  }

  a {
    color: #d95550;
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }
`;

const RelatedTagsSection = styled.section`
  margin-top: 4rem;
  padding-top: 3rem;
  border-top: 2px solid #e5e7eb;
`;

const SectionTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1.5rem;
  text-align: center;
`;

const RelatedTagsGrid = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
`;

const TagLink = styled.a`
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #f3f4f6, #f8f9fa);
  color: #6b7280;
  font-size: 0.9rem;
  font-weight: 500;
  border-radius: 25px;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;

  &:hover {
    background: linear-gradient(135deg, #d95550, #c44a45);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(217, 85, 80, 0.3);
  }

  &::before {
    content: '#';
    margin-right: 0.25rem;
    opacity: 0.7;
  }
`;

const LoadingMessage = styled.div`
  text-align: center;
  padding: 4rem 0;
  font-size: 1.1rem;
  color: #6b7280;
`;

const ErrorMessage = styled.div`
  text-align: center;
  padding: 2rem;
  background-color: #fef2f2;
  border-radius: 8px;
  color: #dc2626;
  margin: 2rem auto;
  max-width: 600px;
`;

const PaginationSection = styled.div`
  margin: 3rem 0;
  display: flex;
  justify-content: center;
`;

const PaginationNav = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  background: white;
  padding: 1rem 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
`;

const PaginationButton = styled.button`
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #d95550, #c44a45);
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(217, 85, 80, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
`;

const PaginationInfo = styled.span`
  color: #6b7280;
  font-weight: 500;
  font-size: 0.9rem;
`;

export default TagPage;