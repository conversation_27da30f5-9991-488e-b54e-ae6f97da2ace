import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';

const BlogHeader = () => {
  return (
    <HeaderContainer>
      <HeaderContent>
        <LeftSection>
          <Logo to="/">
            <LogoIcon>🍅</LogoIcon>
            <LogoText>AI Pomo</LogoText>
          </Logo>

          <NavLinks>
            <BlogLink to="/features">Features</BlogLink>
            <BlogLink to="/pricing">Pricing</BlogLink>
          </NavLinks>
        </LeftSection>

        <RightSection>
          <BlogLink to="/blog" className="active">Blog</BlogLink>
          <AuthButtons>
            <LoginButton to="/login">Log in</LoginButton>
            <StartButton to="/register">Start for free</StartButton>
          </AuthButtons>
        </RightSection>
      </HeaderContent>
    </HeaderContainer>
  );
};

// Styled components
const HeaderContainer = styled.header`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
  z-index: 1000;
  padding: 1rem 0;
  backdrop-filter: blur(5px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
`;

const HeaderContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;

  @media (max-width: 768px) {
    padding: 0 1rem;
  }
`;

const Logo = styled(Link)`
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #333;
  transition: transform 0.2s ease;

  &:hover {
    transform: translateY(-1px);
  }
`;

const LogoIcon = styled.span`
  font-size: 1.75rem;
  margin-right: 0.5rem;
`;

const LogoText = styled.span`
  font-size: 1.4rem;
  font-weight: 700;
  color: #333;
  letter-spacing: -0.5px;
`;

const LeftSection = styled.div`
  display: flex;
  align-items: center;
  gap: 3rem;

  @media (max-width: 768px) {
    gap: 1.5rem;
  }
`;

const RightSection = styled.div`
  display: flex;
  align-items: center;
  gap: 2rem;

  @media (max-width: 768px) {
    gap: 1rem;
  }
`;

const NavLinks = styled.div`
  display: flex;
  align-items: center;
  gap: 1.5rem;

  @media (max-width: 768px) {
    display: none;
  }
`;

const AuthButtons = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const BlogLink = styled(Link)`
  color: #555;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  padding: 0.5rem 0.25rem;
  position: relative;
  transition: color 0.2s ease;

  &:hover, &.active {
    color: #d95550;
  }

  &.active:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #d95550;
    border-radius: 2px;
  }
`;

const LoginButton = styled(Link)`
  color: #555;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  transition: all 0.2s ease;

  &:hover {
    color: #d95550;
    background-color: rgba(217, 85, 80, 0.05);
  }
`;

const StartButton = styled(Link)`
  background-color: #d95550;
  color: white;
  text-decoration: none;
  padding: 0.6rem 1.2rem;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(217, 85, 80, 0.2);

  &:hover {
    background-color: #c04540;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(217, 85, 80, 0.25);
  }
`;

export default BlogHeader;
