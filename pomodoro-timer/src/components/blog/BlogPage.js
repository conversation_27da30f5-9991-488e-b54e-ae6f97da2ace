import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { FaCalendarAlt, FaClock } from 'react-icons/fa';
import { blogApi } from '../../services/blogApi';
import BlogPostCard from './BlogPostCard';
import Pagination from './Pagination';
import CategoryFilter from './CategoryFilter';
import PageHeader from '../shared/PageHeader';
import Footer from '../shared/Footer';
import SEOHead from '../SEO/SEOHead';
import { createWebsiteStructuredData, createBreadcrumbStructuredData } from '../../utils/structuredData';

const BlogPage = () => {
  const [posts, setPosts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedCategory, setSelectedCategory] = useState('');

  const location = useLocation();
  const navigate = useNavigate();

  // Parse query parameters
  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const page = parseInt(queryParams.get('page')) || 1;
    const category = queryParams.get('category') || '';

    setCurrentPage(page);
    setSelectedCategory(category);
  }, [location.search]);

  // Fetch blog posts
  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setLoading(true);
        setError(null); // Clear previous errors
        const data = await blogApi.getBlogPosts(
          currentPage,
          9,
          selectedCategory,
          '',
          ''
        );
        setPosts(data.posts);
        setTotalPages(data.totalPages);
      } catch (err) {
        console.error('Error fetching blog posts:', err);
        setError('Failed to load blog posts. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();
  }, [currentPage, selectedCategory]);

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const data = await blogApi.getCategories();
        setCategories(data);
      } catch (err) {
        console.error('Error fetching categories:', err);
      }
    };

    fetchCategories();
  }, []);

  // Handle category change
  const handleCategoryChange = (category) => {
    const queryParams = new URLSearchParams();
    if (category) queryParams.set('category', category);
    queryParams.set('page', '1');

    navigate(`/blog?${queryParams.toString()}`);
  };

  // Handle pagination change
  const handlePageChange = (page) => {
    const queryParams = new URLSearchParams(location.search);
    queryParams.set('page', page.toString());

    navigate(`/blog?${queryParams.toString()}`);
  };

  // Create SEO data
  const pageTitle = selectedCategory
    ? `${selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)} Articles | AI Pomo Blog`
    : 'AI Pomodoro Timer Blog | Productivity Tips, Time Management & Focus Strategies';

  const pageDescription = selectedCategory
    ? `Discover ${selectedCategory} articles about AI Pomodoro techniques, productivity tips, and time management strategies for better focus.`
    : 'Expert productivity tips, AI Pomodoro timer strategies, time management insights, and focus techniques. Learn how to boost productivity, manage ADHD, and achieve your goals with proven methods.';

  const breadcrumbs = [
    { name: 'Home', url: 'https://www.ai-pomo.com' },
    { name: 'Blog', url: 'https://www.ai-pomo.com/blog' }
  ];

  if (selectedCategory) {
    breadcrumbs.push({
      name: selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1),
      url: `https://www.ai-pomo.com/blog?category=${selectedCategory}`
    });
  }

  const structuredData = [
    createWebsiteStructuredData(),
    createBreadcrumbStructuredData(breadcrumbs)
  ];

  return (
    <>
      <SEOHead
        title={pageTitle}
        description={pageDescription}
        keywords="pomodoro timer, productivity blog, time management, focus techniques, productivity tips, task management"
        url={`https://www.ai-pomo.com/blog${selectedCategory ? `?category=${selectedCategory}` : ''}`}
        structuredData={structuredData}
      />
      <PageHeader />
      <HeroSection>
        <HeroContent>
          <HeroTitle>AI Pomo Blog</HeroTitle>
          <HeroSubtitle>Productivity tips, time management strategies, and Pomodoro technique insights</HeroSubtitle>
        </HeroContent>
      </HeroSection>

      <BlogContainer>
        <ContentSection>
          <CategoryFilter
            categories={categories}
            selectedCategory={selectedCategory}
            onCategoryChange={handleCategoryChange}
          />

          {loading ? (
            <LoadingMessage>
              <LoadingSpinner />
              <span>Loading articles...</span>
            </LoadingMessage>
          ) : error ? (
            <ErrorMessage>{error}</ErrorMessage>
          ) : (
            <>
              <BlogGrid>
                {posts.length > 0 ? (
                  posts.map(post => (
                    <BlogPostCard key={post._id} post={post} />
                  ))
                ) : (
                  <NoPostsMessage>
                    <NoResultsIcon>🔍</NoResultsIcon>
                    <h3>No articles found</h3>
                    <p>Try adjusting your filters.</p>
                  </NoPostsMessage>
                )}
              </BlogGrid>

              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </>
          )}
        </ContentSection>
      </BlogContainer>
      <Footer />
    </>
  );
};

// Styled components
const HeroSection = styled.div`
  background: linear-gradient(135deg, #f0f4f8 0%, #d7e3f0 100%);
  padding: 7rem 2rem 3rem;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 20% 30%, rgba(217, 85, 80, 0.08) 0%, transparent 25%),
      radial-gradient(circle at 80% 70%, rgba(217, 85, 80, 0.08) 0%, transparent 25%);
    z-index: 1;
  }
`;

const HeroContent = styled.div`
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 2;
`;

const HeroTitle = styled.h1`
  font-size: 3.2rem;
  font-weight: 800;
  margin-bottom: 1.2rem;
  color: #2a3f5f;
  letter-spacing: -0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const HeroSubtitle = styled.p`
  font-size: 1.3rem;
  color: #4a5568;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
  font-weight: 400;
`;

const BlogContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem 4rem;
  background-color: #ffffff;
`;

const ContentSection = styled.div`
  margin-bottom: 4rem;
`;

const BlogGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin: 2rem 0 3rem;

  @media (max-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }

  @media (max-width: 640px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
`;

const LoadingMessage = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 5rem 0;
  font-size: 1.1rem;
  color: ${props => props.theme['--text-secondary']};
  gap: 1rem;
`;

const LoadingSpinner = styled.div`
  width: 40px;
  height: 40px;
  border: 3px solid rgba(217, 85, 80, 0.1);
  border-radius: 50%;
  border-top-color: ${props => props.theme['--primary-color']};
  animation: spin 1s ease-in-out infinite;

  @keyframes spin {
    to { transform: rotate(360deg); }
  }
`;

const ErrorMessage = styled.div`
  text-align: center;
  padding: 2rem;
  background-color: rgba(211, 47, 47, 0.05);
  border-radius: 8px;
  color: #d32f2f;
  margin: 2rem 0;
  border: 1px solid rgba(211, 47, 47, 0.1);
`;

const NoPostsMessage = styled.div`
  text-align: center;
  padding: 3rem 1.5rem;
  color: ${props => props.theme['--text-secondary']};
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.03);

  h3 {
    font-size: 1.25rem;
    margin: 0.75rem 0 0.5rem;
    color: ${props => props.theme['--text-color']};
  }

  p {
    font-size: 1rem;
    max-width: 350px;
    margin: 0 auto;
  }
`;

const NoResultsIcon = styled.div`
  font-size: 2.25rem;
  margin-bottom: 0.75rem;
`;

export default BlogPage;
