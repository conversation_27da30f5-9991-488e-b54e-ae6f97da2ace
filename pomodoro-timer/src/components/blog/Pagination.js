import React from 'react';
import styled from 'styled-components';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';

const Pagination = ({ currentPage, totalPages, onPageChange }) => {
  // Generate page numbers to display
  const getPageNumbers = () => {
    const pages = [];

    // Always show first page
    pages.push(1);

    // Calculate range around current page
    let rangeStart = Math.max(2, currentPage - 1);
    let rangeEnd = Math.min(totalPages - 1, currentPage + 1);

    // Adjust range to always show 3 pages if possible
    if (rangeEnd - rangeStart < 2) {
      if (rangeStart === 2) {
        rangeEnd = Math.min(totalPages - 1, rangeEnd + 1);
      } else if (rangeEnd === totalPages - 1) {
        rangeStart = Math.max(2, rangeStart - 1);
      }
    }

    // Add ellipsis before range if needed
    if (rangeStart > 2) {
      pages.push('...');
    }

    // Add range pages
    for (let i = rangeStart; i <= rangeEnd; i++) {
      pages.push(i);
    }

    // Add ellipsis after range if needed
    if (rangeEnd < totalPages - 1) {
      pages.push('...');
    }

    // Always show last page if more than 1 page
    if (totalPages > 1) {
      pages.push(totalPages);
    }

    return pages;
  };

  if (totalPages <= 1) return null;

  return (
    <PaginationContainer>
      <PaginationButton
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
      >
        <FaChevronLeft />
        <span>Previous</span>
      </PaginationButton>

      <PageNumbers>
        {getPageNumbers().map((page, index) => (
          page === '...' ? (
            <Ellipsis key={`ellipsis-${index}`}>...</Ellipsis>
          ) : (
            <PageNumber
              key={page}
              $isActive={page === currentPage}
              onClick={() => onPageChange(page)}
            >
              {page}
            </PageNumber>
          )
        ))}
      </PageNumbers>

      <PaginationButton
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
      >
        <span>Next</span>
        <FaChevronRight />
      </PaginationButton>
    </PaginationContainer>
  );
};

// Styled components
const PaginationContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 2rem 0 3rem;

  @media (max-width: 640px) {
    flex-direction: column;
    gap: 1.25rem;
  }
`;

const PaginationButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: white;
  color: ${props => props.theme['--text-color']};
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 6px;
  padding: 0.6rem 1.2rem;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);

  svg {
    font-size: 0.8rem;
    color: ${props => props.theme['--primary-color']};
  }

  &:hover:not(:disabled) {
    background-color: rgba(217, 85, 80, 0.05);
    border-color: ${props => props.theme['--primary-color']};
    color: ${props => props.theme['--primary-color']};
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.06);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

const PageNumbers = styled.div`
  display: flex;
  align-items: center;
  margin: 0 1.5rem;

  @media (max-width: 640px) {
    margin: 0;
  }
`;

const PageNumber = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  margin: 0 0.3rem;
  background-color: ${props => props.$isActive ? props.theme['--primary-color'] : 'white'};
  color: ${props => props.$isActive ? 'white' : props.theme['--text-color']};
  border: 1px solid ${props => props.$isActive ? props.theme['--primary-color'] : 'rgba(0, 0, 0, 0.08)'};
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: ${props => props.$isActive ? '600' : '500'};
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: ${props => props.$isActive ? '0 2px 8px rgba(217, 85, 80, 0.2)' : '0 1px 3px rgba(0, 0, 0, 0.03)'};

  &:hover:not(:disabled) {
    background-color: ${props => props.$isActive ? props.theme['--primary-color-dark'] : 'rgba(217, 85, 80, 0.05)'};
    border-color: ${props => props.$isActive ? props.theme['--primary-color-dark'] : props.theme['--primary-color']};
    color: ${props => props.$isActive ? 'white' : props.theme['--primary-color']};
    transform: translateY(-1px);
    box-shadow: ${props => props.$isActive ? '0 4px 12px rgba(217, 85, 80, 0.25)' : '0 3px 6px rgba(0, 0, 0, 0.06)'};
  }
`;

const Ellipsis = styled.span`
  margin: 0 0.3rem;
  color: ${props => props.theme['--text-tertiary']};
  font-weight: 500;
`;

export default Pagination;
