import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';

const LazyImage = ({ src, alt, width, height, ...props }) => {
  const [loaded, setLoaded] = useState(false);
  const [inView, setInView] = useState(false);
  const imgRef = useRef();

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <ImageContainer ref={imgRef} width={width} height={height} {...props}>
      {inView && (
        <StyledImage
          src={src}
          alt={alt}
          loaded={loaded}
          onLoad={() => setLoaded(true)}
          loading="lazy"
          decoding="async"
        />
      )}
      {!loaded && inView && <Placeholder>Loading...</Placeholder>}
    </ImageContainer>
  );
};

const ImageContainer = styled.div`
  position: relative;
  width: ${props => props.width || '100%'};
  height: ${props => props.height || 'auto'};
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const StyledImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: ${props => props.loaded ? 1 : 0};
  transition: opacity 0.3s ease;
`;

const Placeholder = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #666;
  font-size: 0.9rem;
`;

export default LazyImage;