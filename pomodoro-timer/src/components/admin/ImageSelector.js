import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import {
  FaImage, FaSearch, FaTags, FaHeart, FaClock, FaDownload,
  <PERSON>a<PERSON>ye, FaSpinner, FaCheck, FaTimes, FaFilter, FaStar
} from 'react-icons/fa';
import { api } from '../../services/apiService';

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
`;

const ModalContent = styled.div`
  background: white;
  width: 95vw;
  height: 90vh;
  border-radius: 10px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const Title = styled.h2`
  margin: 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 10px;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
`;

const Controls = styled.div`
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  align-items: center;
`;

const SearchInput = styled.input`
  flex: 1;
  min-width: 300px;
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
`;

const Select = styled.select`
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  min-width: 150px;
`;

const Content = styled.div`
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const TagsBar = styled.div`
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  max-height: 100px;
  overflow-y: auto;
`;

const TagButton = styled.button`
  background: ${props => props.active ? '#3498db' : 'white'};
  color: ${props => props.active ? 'white' : '#666'};
  border: 1px solid #ddd;
  padding: 5px 10px;
  border-radius: 15px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;

  &:hover {
    background: #3498db;
    color: white;
  }
`;

const ImageGrid = styled.div`
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
`;

const ImageCard = styled.div`
  background: white;
  border: 2px solid ${props => props.selected ? '#3498db' : '#eee'};
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-color: #3498db;
  }
`;

const ImageContainer = styled.div`
  position: relative;
  width: 100%;
  height: 150px;
  overflow: hidden;
`;

const Image = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const ImageOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: ${props => props.selected ? 1 : 0};
  transition: opacity 0.3s ease;

  ${ImageCard}:hover & {
    opacity: 1;
  }
`;

const CheckIcon = styled(FaCheck)`
  color: white;
  font-size: 24px;
`;

const ImageInfo = styled.div`
  padding: 10px;
`;

const ImageTitle = styled.div`
  font-size: 12px;
  color: #333;
  margin-bottom: 5px;
  line-height: 1.3;
  height: 32px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
`;

const ImageStats = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #666;
`;

const StatItem = styled.span`
  display: flex;
  align-items: center;
  gap: 3px;
`;

const LoadingContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #666;
`;

const Footer = styled.div`
  padding: 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const SelectedInfo = styled.div`
  color: #666;
  font-size: 14px;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 10px;
`;

const Button = styled.button`
  background: ${props => 
    props.variant === 'primary' ? '#3498db' : 
    props.variant === 'success' ? '#27ae60' : 'transparent'
  };
  color: ${props => 
    props.variant === 'primary' || props.variant === 'success' ? 'white' : '#666'
  };
  border: 1px solid ${props => 
    props.variant === 'primary' ? '#3498db' : 
    props.variant === 'success' ? '#27ae60' : '#ddd'
  };
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &:disabled {
    background: #95a5a6;
    border-color: #95a5a6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

const Pagination = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #eee;
`;

const PageButton = styled.button`
  background: ${props => props.active ? '#3498db' : 'white'};
  color: ${props => props.active ? 'white' : '#3498db'};
  border: 1px solid #3498db;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;

  &:hover {
    background: #3498db;
    color: white;
  }

  &:disabled {
    background: #f8f9fa;
    color: #ccc;
    border-color: #ddd;
    cursor: not-allowed;
  }
`;

const ImageSelector = ({ isOpen, onClose, onSelect, currentImage = null }) => {
  const [images, setImages] = useState([]);
  const [tags, setTags] = useState([]);
  const [selectedTag, setSelectedTag] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('downloads');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);

  useEffect(() => {
    if (isOpen) {
      loadTags();
      loadImages();
    }
  }, [isOpen, selectedTag, searchTerm, sortBy, currentPage]);

  // Find and set the selected image based on currentImage URL
  useEffect(() => {
    if (currentImage && images.length > 0) {
      // If currentImage is a string (URL), find the matching image
      if (typeof currentImage === 'string') {
        const matchingImage = images.find(img =>
          img.urls.large === currentImage ||
          img.urls.small === currentImage ||
          img.urls.medium === currentImage
        );
        setSelectedImage(matchingImage || null);
      } else {
        // If currentImage is an object, use it directly
        setSelectedImage(currentImage);
      }
    } else {
      setSelectedImage(null);
    }
  }, [currentImage, images]);

  const loadTags = async () => {
    try {
      const response = await api.get('/images/tags');
      if (response.data.success) {
        setTags(['all', ...response.data.data.tags]);
      }
    } catch (error) {
      console.error('Error loading tags:', error);
    }
  };

  const loadImages = async () => {
    try {
      setLoading(true);
      
      let response;
      if (searchTerm) {
        response = await api.get('/images/search', {
          params: {
            q: searchTerm,
            tag: selectedTag !== 'all' ? selectedTag : undefined,
            page: currentPage,
            limit: 20
          }
        });
      } else {
        response = await api.get(`/images/tag/${selectedTag}`, {
          params: {
            page: currentPage,
            limit: 20,
            sortBy
          }
        });
      }

      if (response.data.success) {
        setImages(response.data.data.images);
        setTotalPages(response.data.data.pagination.pages);
      }
    } catch (error) {
      console.error('Error loading images:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleImageClick = (image) => {
    setSelectedImage(selectedImage?._id === image._id ? null : image);
  };

  const handleSelectImage = () => {
    if (selectedImage) {
      // Record usage
      api.post(`/images/${selectedImage._id}/usage`).catch(console.error);
      
      onSelect({
        id: selectedImage._id,
        url: selectedImage.urls.large,
        title: selectedImage.title,
        alt: selectedImage.title,
        user: selectedImage.user,
        pageUrl: selectedImage.pageUrl
      });
      onClose();
    }
  };

  const handleTagChange = (tag) => {
    setSelectedTag(tag);
    setCurrentPage(1);
    setSearchTerm('');
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  if (!isOpen) return null;

  return (
    <Modal onClick={onClose}>
      <ModalContent onClick={(e) => e.stopPropagation()}>
        <Header>
          <Title>
            <FaImage />
            Select Image
          </Title>
          <CloseButton onClick={onClose}>
            <FaTimes />
          </CloseButton>
        </Header>

        <Controls>
          <SearchInput
            type="text"
            placeholder="Search images..."
            value={searchTerm}
            onChange={handleSearch}
          />
          
          <Select value={sortBy} onChange={(e) => setSortBy(e.target.value)}>
            <option value="downloads">Most Downloaded</option>
            <option value="views">Most Viewed</option>
            <option value="recent">Recently Added</option>
            <option value="popular">Most Used</option>
          </Select>
        </Controls>

        <Content>
          <TagsBar>
            {tags.map(tag => (
              <TagButton
                key={tag}
                active={selectedTag === tag}
                onClick={() => handleTagChange(tag)}
              >
                {tag === 'all' ? 'All Images' : tag}
              </TagButton>
            ))}
          </TagsBar>

          {loading ? (
            <LoadingContainer>
              <FaSpinner style={{ animation: 'spin 1s linear infinite', marginRight: '10px' }} />
              Loading images...
            </LoadingContainer>
          ) : (
            <ImageGrid>
              {images.map(image => (
                <ImageCard
                  key={image._id}
                  selected={selectedImage?._id === image._id}
                  onClick={() => handleImageClick(image)}
                >
                  <ImageContainer>
                    <Image src={image.urls.small} alt={image.title} />
                    <ImageOverlay selected={selectedImage?._id === image._id}>
                      <CheckIcon />
                    </ImageOverlay>
                  </ImageContainer>
                  <ImageInfo>
                    <ImageTitle>{image.title}</ImageTitle>
                    <ImageStats>
                      <StatItem>
                        <FaDownload />
                        {image.downloads.toLocaleString()}
                      </StatItem>
                      <StatItem>
                        <FaEye />
                        {image.views.toLocaleString()}
                      </StatItem>
                    </ImageStats>
                  </ImageInfo>
                </ImageCard>
              ))}
            </ImageGrid>
          )}

          {totalPages > 1 && (
            <Pagination>
              <PageButton
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                Previous
              </PageButton>
              
              {[...Array(Math.min(totalPages, 5))].map((_, index) => {
                const page = index + 1;
                return (
                  <PageButton
                    key={page}
                    active={currentPage === page}
                    onClick={() => setCurrentPage(page)}
                  >
                    {page}
                  </PageButton>
                );
              })}
              
              <PageButton
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
              >
                Next
              </PageButton>
            </Pagination>
          )}
        </Content>

        <Footer>
          <SelectedInfo>
            {selectedImage ? (
              `Selected: ${selectedImage.title} by ${selectedImage.user}`
            ) : (
              'No image selected'
            )}
          </SelectedInfo>
          
          <ActionButtons>
            <Button onClick={onClose}>
              Cancel
            </Button>
            <Button 
              variant="success" 
              onClick={handleSelectImage}
              disabled={!selectedImage}
            >
              <FaCheck />
              Select Image
            </Button>
          </ActionButtons>
        </Footer>
      </ModalContent>
    </Modal>
  );
};

export default ImageSelector;