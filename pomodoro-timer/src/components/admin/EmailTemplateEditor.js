import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FaSave, FaTimes, FaEye, FaCode, FaEdit } from 'react-icons/fa';
import { api } from '../../services/apiService';

const EditorContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 80vh;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
`;

const EditorHeader = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
  flex-wrap: wrap;
  gap: 15px;
`;

const EditorTitle = styled.h2`
  color: #2c3e50;
  margin: 0;
  flex: 1;
`;

const EditorActions = styled.div`
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
`;

const Button = styled.button`
  background: ${props => props.variant === 'danger' ? '#e74c3c' : props.variant === 'success' ? '#27ae60' : '#3498db'};
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.9;
    transform: translateY(-1px);
  }

  &:disabled {
    background: #95a5a6;
    cursor: not-allowed;
    transform: none;
  }
`;

const TabContainer = styled.div`
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #ddd;
`;

const Tab = styled.button`
  background: ${props => props.active ? 'white' : 'transparent'};
  border: none;
  padding: 12px 20px;
  cursor: pointer;
  border-bottom: ${props => props.active ? '2px solid #3498db' : 'none'};
  color: ${props => props.active ? '#3498db' : '#666'};
  font-weight: ${props => props.active ? '600' : '400'};
  display: flex;
  align-items: center;
  gap: 8px;

  &:hover {
    background: ${props => props.active ? 'white' : '#f0f0f0'};
  }
`;

const EditorContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

const FormGroup = styled.div`
  margin-bottom: 20px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2c3e50;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 300px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  font-family: ${props => props.codeMode ? 'Monaco, "Courier New", monospace' : 'inherit'};
  resize: vertical;
  line-height: 1.5;
`;

const PreviewContainer = styled.div`
  padding: 20px;
  overflow-y: auto;
  flex: 1;
  background: #f9f9f9;
`;

const PreviewFrame = styled.div`
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
`;

const SaveStatus = styled.div`
  padding: 10px 20px;
  background: ${props => props.type === 'success' ? '#d4edda' : props.type === 'error' ? '#f8d7da' : '#fff3cd'};
  color: ${props => props.type === 'success' ? '#155724' : props.type === 'error' ? '#721c24' : '#856404'};
  border-bottom: 1px solid #eee;
  font-size: 14px;
`;

const EmailTemplateEditor = ({ universityId, onClose, onSave }) => {
  const [template, setTemplate] = useState({
    subject: '',
    textContent: '',
    htmlContent: ''
  });
  const [activeTab, setActiveTab] = useState('edit');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState(null);
  const [university, setUniversity] = useState(null);

  useEffect(() => {
    loadTemplate();
  }, [universityId]);

  const loadTemplate = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/email-outreach/template/${universityId}`);
      
      if (response.data && response.data.success) {
        const { university: uni, template: temp } = response.data.data;
        setUniversity(uni);
        setTemplate({
          subject: temp.subject || '',
          textContent: temp.text || '',
          htmlContent: temp.html || ''
        });
      }
    } catch (error) {
      console.error('Error loading template:', error);
      setSaveStatus({ type: 'error', message: 'Failed to load template' });
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setSaveStatus({ type: 'info', message: 'Saving...' });

      const response = await api.put(`/email-outreach/template/${universityId}`, {
        subject: template.subject,
        textContent: template.textContent,
        htmlContent: template.htmlContent
      });

      if (response.data && response.data.success) {
        setSaveStatus({ type: 'success', message: 'Template saved successfully!' });
        if (onSave) onSave();
      } else {
        setSaveStatus({ type: 'error', message: 'Failed to save template' });
      }
    } catch (error) {
      console.error('Error saving template:', error);
      setSaveStatus({ 
        type: 'error', 
        message: error.response?.data?.message || 'Failed to save template' 
      });
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field, value) => {
    setTemplate(prev => ({ ...prev, [field]: value }));
    setSaveStatus(null);
  };

  if (loading) {
    return (
      <EditorContainer>
        <div style={{ padding: '50px', textAlign: 'center' }}>
          <p>Loading template...</p>
        </div>
      </EditorContainer>
    );
  }

  return (
    <EditorContainer>
      <EditorHeader>
        <EditorTitle>
          Edit Template: {university?.name}
        </EditorTitle>
        <EditorActions>
          <Button variant="success" onClick={handleSave} disabled={saving}>
            <FaSave />
            {saving ? 'Saving...' : 'Save'}
          </Button>
          <Button variant="danger" onClick={onClose}>
            <FaTimes />
            Close
          </Button>
        </EditorActions>
      </EditorHeader>

      {saveStatus && (
        <SaveStatus type={saveStatus.type}>
          {saveStatus.message}
        </SaveStatus>
      )}

      <TabContainer>
        <Tab active={activeTab === 'edit'} onClick={() => setActiveTab('edit')}>
          <FaEdit />
          Edit
        </Tab>
        <Tab active={activeTab === 'preview'} onClick={() => setActiveTab('preview')}>
          <FaEye />
          Preview
        </Tab>
        <Tab active={activeTab === 'html'} onClick={() => setActiveTab('html')}>
          <FaCode />
          HTML Code
        </Tab>
      </TabContainer>

      <EditorContent>
        {activeTab === 'edit' && (
          <div style={{ padding: '20px', overflow: 'auto' }}>
            <FormGroup>
              <Label>Email Subject</Label>
              <Input
                type="text"
                value={template.subject}
                onChange={(e) => handleInputChange('subject', e.target.value)}
                placeholder="Enter email subject..."
              />
            </FormGroup>

            <FormGroup>
              <Label>Text Content</Label>
              <TextArea
                value={template.textContent}
                onChange={(e) => handleInputChange('textContent', e.target.value)}
                placeholder="Enter plain text email content..."
              />
            </FormGroup>

            <FormGroup>
              <Label>HTML Content</Label>
              <TextArea
                codeMode
                value={template.htmlContent}
                onChange={(e) => handleInputChange('htmlContent', e.target.value)}
                placeholder="Enter HTML email content..."
              />
            </FormGroup>
          </div>
        )}

        {activeTab === 'preview' && (
          <PreviewContainer>
            <PreviewFrame>
              <div style={{ padding: '20px', borderBottom: '1px solid #eee' }}>
                <strong>Subject:</strong> {template.subject}
              </div>
              <div 
                dangerouslySetInnerHTML={{ __html: template.htmlContent }}
                style={{ padding: '20px' }}
              />
            </PreviewFrame>
          </PreviewContainer>
        )}

        {activeTab === 'html' && (
          <div style={{ padding: '20px', overflow: 'auto' }}>
            <TextArea
              codeMode
              style={{ minHeight: '500px' }}
              value={template.htmlContent}
              onChange={(e) => handleInputChange('htmlContent', e.target.value)}
              placeholder="Enter HTML email content..."
            />
          </div>
        )}
      </EditorContent>
    </EditorContainer>
  );
};

export default EmailTemplateEditor;