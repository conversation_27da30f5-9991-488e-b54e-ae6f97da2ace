import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { FaPlus, FaEdit, FaTrash, FaEye, FaCheck, FaTimes, FaStar } from 'react-icons/fa';
import { format } from 'date-fns';
import { blogApi } from '../../services/blogApi';
import { toast } from 'react-toastify';

const BlogPostList = () => {
  const navigate = useNavigate();

  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [confirmDelete, setConfirmDelete] = useState(null);

  // Fetch blog posts
  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setLoading(true);
        // Get all posts including unpublished ones (admin view)
        const data = await blogApi.getAllBlogPosts();
        setPosts(data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching blog posts:', err);
        setError('Failed to load blog posts');
        setLoading(false);
      }
    };

    fetchPosts();
  }, []);

  // Handle delete confirmation
  const handleDeleteClick = (postId) => {
    setConfirmDelete(postId);
  };

  // Cancel delete
  const cancelDelete = () => {
    setConfirmDelete(null);
  };

  // Confirm delete
  const confirmDeletePost = async (postId) => {
    try {
      await blogApi.deleteBlogPost(postId);
      setPosts(posts.filter(post => post._id !== postId));
      toast.success('Blog post deleted successfully');
      setConfirmDelete(null);
    } catch (err) {
      console.error('Error deleting post:', err);
      toast.error('Failed to delete blog post');
    }
  };

  // Toggle post published status
  const togglePublished = async (post) => {
    try {
      const updatedPost = { ...post, published: !post.published };
      await blogApi.updateBlogPost(post._id, updatedPost);

      // Update local state
      setPosts(posts.map(p =>
        p._id === post._id ? { ...p, published: !p.published } : p
      ));

      toast.success(`Post ${updatedPost.published ? 'published' : 'unpublished'} successfully`);
    } catch (err) {
      console.error('Error updating post:', err);
      toast.error('Failed to update post status');
    }
  };

  // Toggle post featured status
  const toggleFeatured = async (post) => {
    try {
      const updatedPost = { ...post, featured: !post.featured };
      await blogApi.updateBlogPost(post._id, updatedPost);

      // Update local state
      setPosts(posts.map(p =>
        p._id === post._id ? { ...p, featured: !p.featured } : p
      ));

      toast.success(`Post ${updatedPost.featured ? 'featured' : 'unfeatured'} successfully`);
    } catch (err) {
      console.error('Error updating post:', err);
      toast.error('Failed to update featured status');
    }
  };

  return (
    <ListContainer>
      <ListHeader>
        <h1>Blog Posts</h1>

        <CreateButton onClick={() => navigate('/admin/blog/new')}>
          <FaPlus />
          <span>Create New Post</span>
        </CreateButton>
      </ListHeader>

      {loading ? (
        <LoadingMessage>Loading blog posts...</LoadingMessage>
      ) : error ? (
        <ErrorMessage>{error}</ErrorMessage>
      ) : posts.length === 0 ? (
        <EmptyState>
          <p>No blog posts found. Create your first post to get started.</p>
          <CreateButton onClick={() => navigate('/admin/blog/new')}>
            <FaPlus />
            <span>Create New Post</span>
          </CreateButton>
        </EmptyState>
      ) : (
        <PostsTable>
          <thead>
            <tr>
              <TableHeader>Title</TableHeader>
              <TableHeader>Category</TableHeader>
              <TableHeader>Date</TableHeader>
              <TableHeader>Status</TableHeader>
              <TableHeader>Featured</TableHeader>
              <TableHeader>Actions</TableHeader>
            </tr>
          </thead>
          <tbody>
            {posts.map(post => (
              <TableRow key={post._id}>
                <TableCell>
                  <PostTitle>{post.title}</PostTitle>
                </TableCell>
                <TableCell>
                  <CategoryBadge>{post.category}</CategoryBadge>
                </TableCell>
                <TableCell>
                  <DateInfo>
                    {format(new Date(post.createdAt), 'MMM d, yyyy')}
                  </DateInfo>
                </TableCell>
                <TableCell>
                  <StatusToggle
                    onClick={() => togglePublished(post)}
                    $isPublished={post.published}
                    title={post.published ? 'Unpublish post' : 'Publish post'}
                  >
                    {post.published ? (
                      <>
                        <FaCheck />
                        <span>Published</span>
                      </>
                    ) : (
                      <>
                        <FaTimes />
                        <span>Draft</span>
                      </>
                    )}
                  </StatusToggle>
                </TableCell>
                <TableCell>
                  <FeaturedToggle
                    onClick={() => toggleFeatured(post)}
                    $isFeatured={post.featured}
                    title={post.featured ? 'Remove from featured' : 'Add to featured'}
                  >
                    <FaStar />
                  </FeaturedToggle>
                </TableCell>
                <TableCell>
                  <ActionButtons>
                    <ActionButton
                      onClick={() => window.open(`/blog/${post.slug}`, '_blank')}
                      title="View post"
                    >
                      <FaEye />
                    </ActionButton>
                    <ActionButton
                      onClick={() => navigate(`/admin/blog/edit/${post._id}`)}
                      title="Edit post"
                    >
                      <FaEdit />
                    </ActionButton>
                    {confirmDelete === post._id ? (
                      <ConfirmDeleteContainer>
                        <ConfirmText>Are you sure?</ConfirmText>
                        <ConfirmButton onClick={() => confirmDeletePost(post._id)}>
                          Yes
                        </ConfirmButton>
                        <CancelButton onClick={cancelDelete}>
                          No
                        </CancelButton>
                      </ConfirmDeleteContainer>
                    ) : (
                      <DeleteButton
                        onClick={() => handleDeleteClick(post._id)}
                        title="Delete post"
                      >
                        <FaTrash />
                      </DeleteButton>
                    )}
                  </ActionButtons>
                </TableCell>
              </TableRow>
            ))}
          </tbody>
        </PostsTable>
      )}
    </ListContainer>
  );
};

// Styled components
const ListContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2.5rem 1.5rem;
`;

const ListHeader = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid ${props => props.theme['--border-color']};

  h1 {
    font-size: 1.85rem;
    color: ${props => props.theme['--text-color']};
    margin: 0;
    font-weight: 700;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
`;

const CreateButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.6rem;
  background-color: ${props => props.theme['--primary-color']};
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.7rem 1.5rem;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(217, 85, 80, 0.2);

  &:hover {
    background-color: ${props => props.theme['--primary-color-dark']};
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(217, 85, 80, 0.3);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(217, 85, 80, 0.2);
  }
`;

const LoadingMessage = styled.div`
  text-align: center;
  padding: 3rem 0;
  font-size: 1.1rem;
  color: ${props => props.theme['--text-secondary']};
`;

const ErrorMessage = styled.div`
  text-align: center;
  padding: 2rem;
  background-color: #fff0f0;
  border-radius: 4px;
  color: #d32f2f;
  margin-bottom: 2rem;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem 0;

  p {
    font-size: 1.1rem;
    color: ${props => props.theme['--text-secondary']};
    margin-bottom: 1.5rem;
  }
`;

const PostsTable = styled.table`
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background-color: ${props => props.theme['--card-bg']};
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
`;

const TableHeader = styled.th`
  text-align: left;
  padding: 1.2rem 1rem;
  font-size: 0.95rem;
  font-weight: 600;
  color: ${props => props.theme['--text-secondary']};
  background-color: ${props => props.theme['--table-header-bg'] || '#f8f9fa'};
  border-bottom: 1px solid ${props => props.theme['--border-color']};

  &:first-child {
    padding-left: 1.5rem;
  }

  &:last-child {
    padding-right: 1.5rem;
    text-align: center;
  }
`;

const TableRow = styled.tr`
  &:not(:last-child) {
    border-bottom: 1px solid ${props => props.theme['--border-color']};
  }

  &:hover {
    background-color: ${props => props.theme['--hover-bg']};
  }

  &:last-child td:first-child {
    border-bottom-left-radius: 12px;
  }

  &:last-child td:last-child {
    border-bottom-right-radius: 12px;
  }
`;

const TableCell = styled.td`
  padding: 1rem;
  vertical-align: middle;

  &:first-child {
    padding-left: 1.5rem;
  }

  &:last-child {
    padding-right: 1.5rem;
  }
`;

const PostTitle = styled.div`
  font-weight: 600;
  color: ${props => props.theme['--text-color']};
`;

const CategoryBadge = styled.span`
  display: inline-block;
  background-color: ${props => props.theme['--tag-bg'] || '#f0f0f0'};
  color: ${props => props.theme['--text-secondary']};
  font-size: 0.8rem;
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
`;

const DateInfo = styled.span`
  font-size: 0.9rem;
  color: ${props => props.theme['--text-tertiary']};
`;

const StatusToggle = styled.button`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background-color: ${props => props.$isPublished ? '#e6f7e6' : '#f7e6e6'};
  color: ${props => props.$isPublished ? '#2e7d32' : '#c62828'};
  border: none;
  border-radius: 4px;
  padding: 0.4rem 0.8rem;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${props => props.$isPublished ? '#d4f0d4' : '#f0d4d4'};
  }
`;

const FeaturedToggle = styled.button`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background-color: transparent;
  color: ${props => props.$isFeatured ? '#f9a825' : props.theme['--text-tertiary']};
  border: 1px solid ${props => props.$isFeatured ? '#f9a825' : props.theme['--border-color']};
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${props => props.theme['--hover-bg']};
    border-color: ${props => props.$isFeatured ? '#f9a825' : props.theme['--text-tertiary']};
  }
`;

const ActionButtons = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const ActionButton = styled.button`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background-color: transparent;
  color: ${props => props.theme['--text-tertiary']};
  border: 1px solid ${props => props.theme['--border-color']};
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${props => props.theme['--hover-bg']};
    border-color: ${props => props.theme['--text-tertiary']};
    color: ${props => props.theme['--text-color']};
  }
`;

const DeleteButton = styled(ActionButton)`
  &:hover {
    background-color: #fff0f0;
    border-color: #d32f2f;
    color: #d32f2f;
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(211, 47, 47, 0.2);
  }
`;

const ConfirmDeleteContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.6rem;
  background-color: #fff8f8;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  border: 1px solid rgba(211, 47, 47, 0.2);
`;

const ConfirmText = styled.span`
  font-size: 0.85rem;
  font-weight: 500;
  color: #d32f2f;
`;

const ConfirmButton = styled.button`
  background-color: #d32f2f;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.35rem 0.7rem;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(211, 47, 47, 0.2);

  &:hover {
    background-color: #b71c1c;
    transform: translateY(-1px);
    box-shadow: 0 3px 5px rgba(211, 47, 47, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
`;

const CancelButton = styled.button`
  background-color: white;
  color: #666;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0.35rem 0.7rem;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f5f5f5;
    color: #333;
    border-color: #ccc;
  }
`;

export default BlogPostList;
