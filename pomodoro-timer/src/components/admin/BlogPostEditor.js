import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { FaSave, FaImage, FaTimes, FaPlus, FaEye, FaImages } from 'react-icons/fa';
import { blogApi } from '../../services/blogApi';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';
import { toast } from 'react-toastify';
import BlogHeader from '../blog/BlogHeader';
import ImageSelector from './ImageSelector';

const BlogPostEditor = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEditing = Boolean(id);

  const [post, setPost] = useState({
    title: '',
    content: '',
    excerpt: '',
    coverImage: '',
    tags: [],
    category: '',
    readTime: 5,
    published: false,
    featured: false,
    seoTitle: '',
    seoDescription: '',
    seoKeywords: ''
  });

  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [tagInput, setTagInput] = useState('');
  const [previewMode, setPreviewMode] = useState(false);
  const [showImageSelector, setShowImageSelector] = useState(false);

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const data = await blogApi.getCategories();
        setCategories(data);

        // Set default category if available and creating new post
        if (data.length > 0 && !isEditing) {
          setPost(prev => ({ ...prev, category: data[0].slug }));
        }
      } catch (err) {
        console.error('Error fetching categories:', err);
        toast.error('Failed to load categories');
      }
    };

    fetchCategories();
  }, [isEditing]);

  // Fetch post data if editing
  useEffect(() => {
    if (isEditing) {
      const fetchPost = async () => {
        try {
          setLoading(true);
          const data = await blogApi.getBlogPostById(id);
          setPost(data);
          setLoading(false);
        } catch (err) {
          console.error('Error fetching post:', err);
          toast.error('Failed to load post data');
          setLoading(false);
          navigate('/admin/blog');
        }
      };

      fetchPost();
    }
  }, [id, isEditing, navigate]);

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setPost(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Handle tag input
  const handleTagInputChange = (e) => {
    setTagInput(e.target.value);
  };

  // Add tag
  const addTag = () => {
    if (tagInput.trim() && !post.tags.includes(tagInput.trim())) {
      setPost(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  // Remove tag
  const removeTag = (tagToRemove) => {
    setPost(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  // Handle tag input keydown
  const handleTagKeyDown = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);

      // Validate form
      if (!post.title.trim()) {
        toast.error('Title is required');
        setLoading(false);
        return;
      }

      if (!post.content.trim()) {
        toast.error('Content is required');
        setLoading(false);
        return;
      }

      if (!post.excerpt.trim()) {
        toast.error('Excerpt is required');
        setLoading(false);
        return;
      }

      if (!post.coverImage.trim()) {
        toast.error('Cover image URL is required');
        setLoading(false);
        return;
      }

      if (!post.category) {
        toast.error('Category is required');
        setLoading(false);
        return;
      }

      // Create or update post
      if (isEditing) {
        await blogApi.updateBlogPost(id, post);
        toast.success('Blog post updated successfully');
      } else {
        await blogApi.createBlogPost(post);
        toast.success('Blog post created successfully');
      }

      setLoading(false);
      navigate('/admin/blog');
    } catch (err) {
      console.error('Error saving post:', err);
      toast.error('Failed to save blog post');
      setLoading(false);
    }
  };

  // Toggle preview mode
  const togglePreview = () => {
    setPreviewMode(!previewMode);
  };

  // Calculate estimated read time
  const calculateReadTime = () => {
    const wordCount = post.content.trim().split(/\s+/).length;
    const readTime = Math.max(1, Math.ceil(wordCount / 200)); // Assuming 200 words per minute

    setPost(prev => ({
      ...prev,
      readTime
    }));
  };

  return (
    <>
      <BlogHeader />
      <EditorContainer>
        <EditorHeader>
          <h1>{isEditing ? 'Edit Blog Post' : 'Create New Blog Post'}</h1>

        <EditorActions>
          <ActionButton
            type="button"
            onClick={togglePreview}
            title={previewMode ? 'Edit mode' : 'Preview mode'}
          >
            <FaEye />
            <span>{previewMode ? 'Edit' : 'Preview'}</span>
          </ActionButton>

          <PrimaryButton
            type="button"
            onClick={handleSubmit}
            disabled={loading}
          >
            <FaSave />
            <span>{loading ? 'Saving...' : 'Save Post'}</span>
          </PrimaryButton>
        </EditorActions>
      </EditorHeader>

      {previewMode ? (
        <PreviewContainer>
          <PreviewHeader>
            <PreviewCategory>{
              categories.find(c => c.slug === post.category)?.name || post.category
            }</PreviewCategory>
            <PreviewTitle>{post.title}</PreviewTitle>
            <PreviewMeta>
              <span>{post.readTime} min read</span>
              {post.tags.length > 0 && (
                <span>Tags: {post.tags.join(', ')}</span>
              )}
            </PreviewMeta>
          </PreviewHeader>

          {post.coverImage && (
            <PreviewCoverImage src={post.coverImage} alt={post.title} />
          )}

          <PreviewContent>
            <ReactMarkdown
              remarkPlugins={[remarkGfm, remarkBreaks]}
              components={{
                // Custom component for links to open external links in new tab
                a: ({ href, children, ...props }) => {
                  const isExternal = href && (href.startsWith('http') || href.startsWith('https'));
                  return (
                    <a
                      href={href}
                      target={isExternal ? '_blank' : undefined}
                      rel={isExternal ? 'noopener noreferrer' : undefined}
                      {...props}
                    >
                      {children}
                    </a>
                  );
                },
                // Custom component for code blocks
                pre: ({ children, ...props }) => (
                  <pre {...props}>
                    {children}
                  </pre>
                ),
                // Custom component for inline code
                code: ({ inline, children, ...props }) => (
                  <code {...props}>
                    {children}
                  </code>
                )
              }}
            >
              {post.content}
            </ReactMarkdown>
          </PreviewContent>
        </PreviewContainer>
      ) : (
        <EditorForm onSubmit={handleSubmit}>
          <FormRow>
            <FormGroup>
              <Label htmlFor="title">Title *</Label>
              <Input
                type="text"
                id="title"
                name="title"
                value={post.title}
                onChange={handleChange}
                placeholder="Enter post title"
                required
              />
            </FormGroup>
          </FormRow>

          <FormRow>
            <FormGroup>
              <Label htmlFor="excerpt">Excerpt * (Short summary for blog listing)</Label>
              <Textarea
                id="excerpt"
                name="excerpt"
                value={post.excerpt}
                onChange={handleChange}
                placeholder="Enter a brief summary of the post"
                rows={3}
                required
              />
            </FormGroup>
          </FormRow>

          <FormRow>
            <FormGroup>
              <Label htmlFor="coverImage">Cover Image URL *</Label>
              <ImageInputContainer>
                <InputWithIcon>
                  <Input
                    type="text"
                    id="coverImage"
                    name="coverImage"
                    value={post.coverImage}
                    onChange={handleChange}
                    placeholder="Enter image URL or select from gallery"
                    required
                  />
                  <InputIcon>
                    <FaImage />
                  </InputIcon>
                </InputWithIcon>
                <ImageSelectorButton
                  type="button"
                  onClick={() => setShowImageSelector(true)}
                  title="Select from image gallery"
                >
                  <FaImages />
                </ImageSelectorButton>
              </ImageInputContainer>
              {post.coverImage && (
                <ImagePreview>
                  <img src={post.coverImage} alt="Cover preview" />
                </ImagePreview>
              )}
            </FormGroup>
          </FormRow>

          <FormRow>
            <FormGroup>
              <Label htmlFor="content">Content * (Markdown format)</Label>
              <Textarea
                id="content"
                name="content"
                value={post.content}
                onChange={handleChange}
                placeholder="Write your post content in Markdown format"
                rows={15}
                required
                onBlur={calculateReadTime}
              />
              <FormHelp>
                Markdown formatting supported. You can use # for headings, * for lists, etc.
              </FormHelp>
            </FormGroup>
          </FormRow>

          <FormRow>
            <FormGroup>
              <Label htmlFor="category">Category *</Label>
              <Select
                id="category"
                name="category"
                value={post.category}
                onChange={handleChange}
                required
              >
                <option value="">Select a category</option>
                {categories.map(category => (
                  <option key={category._id} value={category.slug}>
                    {category.name}
                  </option>
                ))}
              </Select>
            </FormGroup>

            <FormGroup>
              <Label htmlFor="readTime">Read Time (minutes)</Label>
              <Input
                type="number"
                id="readTime"
                name="readTime"
                value={post.readTime}
                onChange={handleChange}
                min="1"
                max="60"
              />
              <FormHelp>
                Auto-calculated based on content length, but you can adjust manually.
              </FormHelp>
            </FormGroup>
          </FormRow>

          <FormRow>
            <FormGroup>
              <Label>Tags</Label>
              <TagInputContainer>
                <TagInput
                  type="text"
                  value={tagInput}
                  onChange={handleTagInputChange}
                  onKeyDown={handleTagKeyDown}
                  placeholder="Add a tag and press Enter"
                />
                <TagAddButton type="button" onClick={addTag}>
                  <FaPlus />
                </TagAddButton>
              </TagInputContainer>

              {post.tags.length > 0 && (
                <TagList>
                  {post.tags.map((tag, index) => (
                    <Tag key={index}>
                      <span>{tag}</span>
                      <TagRemoveButton type="button" onClick={() => removeTag(tag)}>
                        <FaTimes />
                      </TagRemoveButton>
                    </Tag>
                  ))}
                </TagList>
              )}
            </FormGroup>
          </FormRow>

          <FormRow>
            <CheckboxGroup>
              <CheckboxLabel>
                <Checkbox
                  type="checkbox"
                  name="published"
                  checked={post.published}
                  onChange={handleChange}
                />
                <span>Publish post (will be visible on the blog)</span>
              </CheckboxLabel>
            </CheckboxGroup>

            <CheckboxGroup>
              <CheckboxLabel>
                <Checkbox
                  type="checkbox"
                  name="featured"
                  checked={post.featured}
                  onChange={handleChange}
                />
                <span>Feature this post (highlight on blog homepage)</span>
              </CheckboxLabel>
            </CheckboxGroup>
          </FormRow>

          <SeoSection>
            <SeoHeader>
              <h3>SEO Settings</h3>
              <p>Optimize your post for search engines</p>
            </SeoHeader>

            <FormRow>
              <FormGroup>
                <Label htmlFor="seoTitle">SEO Title</Label>
                <Input
                  type="text"
                  id="seoTitle"
                  name="seoTitle"
                  value={post.seoTitle}
                  onChange={handleChange}
                  placeholder="Enter SEO title (defaults to post title if empty)"
                />
                <FormHelp>
                  Recommended length: 50-60 characters
                </FormHelp>
              </FormGroup>
            </FormRow>

            <FormRow>
              <FormGroup>
                <Label htmlFor="seoDescription">Meta Description</Label>
                <Textarea
                  id="seoDescription"
                  name="seoDescription"
                  value={post.seoDescription}
                  onChange={handleChange}
                  placeholder="Enter meta description (defaults to excerpt if empty)"
                  rows={2}
                />
                <FormHelp>
                  Recommended length: 150-160 characters
                </FormHelp>
              </FormGroup>
            </FormRow>

            <FormRow>
              <FormGroup>
                <Label htmlFor="seoKeywords">Meta Keywords</Label>
                <Input
                  type="text"
                  id="seoKeywords"
                  name="seoKeywords"
                  value={post.seoKeywords}
                  onChange={handleChange}
                  placeholder="Enter keywords separated by commas (defaults to tags if empty)"
                />
              </FormGroup>
            </FormRow>
          </SeoSection>
        </EditorForm>
      )}

      {/* Image Selector Modal */}
      <ImageSelector
        isOpen={showImageSelector}
        onClose={() => setShowImageSelector(false)}
        onSelect={(imageData) => {
          setPost(prev => ({ ...prev, coverImage: imageData.url }));
        }}
        currentImage={post.coverImage}
      />
    </EditorContainer>
    </>
  );
};

// Styled components
const EditorContainer = styled.div`
  max-width: 1000px;
  margin: 0 auto;
  padding: 5rem 1rem 2rem;
`;

const EditorHeader = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;

  h1 {
    font-size: 1.75rem;
    color: ${props => props.theme['--text-color']};
    margin: 0;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
`;

const EditorActions = styled.div`
  display: flex;
  gap: 1rem;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: transparent;
  color: ${props => props.theme['--text-color']};
  border: 1px solid ${props => props.theme['--border-color']};
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${props => props.theme['--hover-bg']};
    border-color: ${props => props.theme['--hover-bg']};
  }
`;

const PrimaryButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: ${props => props.theme['--primary-color']};
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1.25rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover:not(:disabled) {
    background-color: ${props => props.theme['--primary-color-dark']};
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
`;

const EditorForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const FormRow = styled.div`
  display: flex;
  gap: 1.5rem;

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const FormGroup = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
`;

const Label = styled.label`
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: ${props => props.theme['--text-color']};
`;

const Input = styled.input`
  padding: 0.75rem;
  border: 1px solid ${props => props.theme['--border-color']};
  border-radius: 4px;
  font-size: 1rem;
  background-color: ${props => props.theme['--input-bg'] || '#fff'};
  color: ${props => props.theme['--text-color']};

  &:focus {
    outline: none;
    border-color: ${props => props.theme['--primary-color']};
  }
`;

const Textarea = styled.textarea`
  padding: 0.75rem;
  border: 1px solid ${props => props.theme['--border-color']};
  border-radius: 4px;
  font-size: 1rem;
  background-color: ${props => props.theme['--input-bg'] || '#fff'};
  color: ${props => props.theme['--text-color']};
  font-family: inherit;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: ${props => props.theme['--primary-color']};
  }
`;

const Select = styled.select`
  padding: 0.75rem;
  border: 1px solid ${props => props.theme['--border-color']};
  border-radius: 4px;
  font-size: 1rem;
  background-color: ${props => props.theme['--input-bg'] || '#fff'};
  color: ${props => props.theme['--text-color']};

  &:focus {
    outline: none;
    border-color: ${props => props.theme['--primary-color']};
  }
`;

const FormHelp = styled.small`
  font-size: 0.8rem;
  color: ${props => props.theme['--text-tertiary']};
  margin-top: 0.4rem;
`;

const InputWithIcon = styled.div`
  position: relative;

  input {
    padding-right: 2.5rem;
  }
`;

const InputIcon = styled.span`
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: ${props => props.theme['--text-tertiary']};
`;

const ImageInputContainer = styled.div`
  display: flex;
  gap: 0.5rem;
  align-items: flex-start;
`;

const ImageSelectorButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  background: ${props => props.theme['--primary-color']};
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 48px;
  height: 48px;

  &:hover {
    background: ${props => props.theme['--primary-hover']};
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  svg {
    font-size: 1.1rem;
  }
`;

const ImagePreview = styled.div`
  margin-top: 0.75rem;
  border-radius: 4px;
  overflow: hidden;
  max-width: 300px;

  img {
    width: 100%;
    height: auto;
    display: block;
  }
`;

const TagInputContainer = styled.div`
  display: flex;
`;

const TagInput = styled.input`
  flex: 1;
  padding: 0.75rem;
  border: 1px solid ${props => props.theme['--border-color']};
  border-right: none;
  border-radius: 4px 0 0 4px;
  font-size: 1rem;
  background-color: ${props => props.theme['--input-bg'] || '#fff'};
  color: ${props => props.theme['--text-color']};

  &:focus {
    outline: none;
    border-color: ${props => props.theme['--primary-color']};
  }
`;

const TagAddButton = styled.button`
  background-color: ${props => props.theme['--primary-color']};
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  padding: 0 1rem;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: ${props => props.theme['--primary-color-dark']};
  }
`;

const TagList = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.75rem;
`;

const Tag = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: ${props => props.theme['--tag-bg'] || '#f0f0f0'};
  color: ${props => props.theme['--text-color']};
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  font-size: 0.85rem;
`;

const TagRemoveButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme['--text-tertiary']};
  cursor: pointer;
  padding: 0;
  font-size: 0.8rem;
  display: flex;
  align-items: center;

  &:hover {
    color: ${props => props.theme['--danger-color'] || '#d32f2f'};
  }
`;

const CheckboxGroup = styled.div`
  flex: 1;
`;

const CheckboxLabel = styled.label`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;

  span {
    font-size: 0.9rem;
    color: ${props => props.theme['--text-color']};
  }
`;

const Checkbox = styled.input`
  cursor: pointer;
`;

const SeoSection = styled.section`
  margin-top: 1rem;
  padding-top: 1.5rem;
  border-top: 1px solid ${props => props.theme['--border-color']};
`;

const SeoHeader = styled.div`
  margin-bottom: 1.5rem;

  h3 {
    font-size: 1.2rem;
    margin: 0 0 0.5rem;
    color: ${props => props.theme['--text-color']};
  }

  p {
    font-size: 0.9rem;
    color: ${props => props.theme['--text-secondary']};
    margin: 0;
  }
`;

const PreviewContainer = styled.div`
  background-color: ${props => props.theme['--card-bg'] || '#fff'};
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
`;

const PreviewHeader = styled.header`
  margin-bottom: 2rem;
`;

const PreviewCategory = styled.div`
  display: inline-block;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  color: ${props => props.theme['--primary-color']};
  margin-bottom: 1rem;
  letter-spacing: 0.5px;
`;

const PreviewTitle = styled.h1`
  font-size: 2.5rem;
  line-height: 1.3;
  margin-bottom: 1.5rem;
  color: ${props => props.theme['--text-color']};

  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const PreviewMeta = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  font-size: 0.9rem;
  color: ${props => props.theme['--text-tertiary']};
`;

const PreviewCoverImage = styled.img`
  width: 100%;
  height: auto;
  border-radius: 8px;
  margin-bottom: 2.5rem;
`;

const PreviewContent = styled.div`
  font-size: 1.1rem;
  line-height: 1.8;
  color: ${props => props.theme['--text-color']};

  h2 {
    font-size: 1.8rem;
    margin: 2rem 0 1rem;
  }

  h3 {
    font-size: 1.5rem;
    margin: 1.75rem 0 1rem;
  }

  p {
    margin-bottom: 1.5rem;
  }

  ul, ol {
    margin-bottom: 1.5rem;
    padding-left: 1.5rem;
  }

  li {
    margin-bottom: 0.5rem;
  }

  blockquote {
    border-left: 4px solid ${props => props.theme['--primary-color']};
    padding-left: 1.5rem;
    margin: 1.5rem 0;
    font-style: italic;
    color: ${props => props.theme['--text-secondary']};
  }

  code {
    background-color: ${props => props.theme['--code-bg'] || '#f5f5f5'};
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-family: monospace;
    font-size: 0.9em;
  }

  pre {
    background-color: ${props => props.theme['--code-bg'] || '#f5f5f5'};
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
    margin-bottom: 1.5rem;

    code {
      background-color: transparent;
      padding: 0;
    }
  }

  img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 1.5rem 0;
  }

  a {
    color: ${props => props.theme['--primary-color']};
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
`;

export default BlogPostEditor;
