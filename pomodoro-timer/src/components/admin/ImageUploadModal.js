import React, { useState, useRef } from 'react';
import styled from 'styled-components';
import { FaUpload, FaTimes, FaImage, FaSpinner } from 'react-icons/fa';
import { blogImageApi } from '../../services/blogImageApi';

const ImageUploadModal = ({ isOpen, onClose, onImageInsert }) => {
  const [uploading, setUploading] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [altText, setAltText] = useState('');
  const [alignment, setAlignment] = useState('center');
  const [size, setSize] = useState('medium');
  const fileInputRef = useRef(null);

  const handleFileSelect = async (file) => {
    if (!file || !file.type.startsWith('image/')) {
      alert('Please select a valid image file');
      return;
    }

    try {
      setUploading(true);
      const uploadedImage = await blogImageApi.uploadImage(file);
      setImageUrl(uploadedImage.url);
      setAltText(file.name.replace(/\.[^/.]+$/, "")); // Remove extension for alt text
    } catch (error) {
      console.error('Error uploading image:', error);
      alert('Failed to upload image. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleFileInputChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleInsertImage = () => {
    if (!imageUrl) {
      alert('Please upload an image or enter an image URL');
      return;
    }

    const sizeClasses = {
      small: 'width: 300px; max-width: 100%;',
      medium: 'width: 500px; max-width: 100%;',
      large: 'width: 100%; max-width: 800px;',
      full: 'width: 100%;'
    };

    const alignmentClasses = {
      left: 'float: left; margin: 0 1rem 1rem 0;',
      center: 'display: block; margin: 1rem auto;',
      right: 'float: right; margin: 0 0 1rem 1rem;'
    };

    const imageMarkdown = `<img src="${imageUrl}" alt="${altText}" style="${sizeClasses[size]} ${alignmentClasses[alignment]} border-radius: 0.5rem;" />`;
    
    onImageInsert(imageMarkdown);
    handleClose();
  };

  const handleClose = () => {
    setImageUrl('');
    setAltText('');
    setAlignment('center');
    setSize('medium');
    setUploading(false);
    setDragOver(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <ModalOverlay onClick={handleClose}>
      <ModalContent onClick={(e) => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>
            <FaImage />
            Insert Image
          </ModalTitle>
          <CloseButton onClick={handleClose}>
            <FaTimes />
          </CloseButton>
        </ModalHeader>

        <ModalBody>
          {/* Upload Section */}
          <Section>
            <SectionTitle>Upload Image</SectionTitle>
            <UploadArea
              $dragOver={dragOver}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onClick={() => fileInputRef.current?.click()}
            >
              {uploading ? (
                <UploadContent>
                  <FaSpinner className="spinning" />
                  <p>Uploading...</p>
                </UploadContent>
              ) : (
                <UploadContent>
                  <FaUpload />
                  <p>Click to upload or drag & drop an image</p>
                  <small>Supports JPG, PNG, GIF, WebP</small>
                </UploadContent>
              )}
            </UploadArea>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileInputChange}
              style={{ display: 'none' }}
            />
          </Section>

          {/* URL Section */}
          <Section>
            <SectionTitle>Or Enter Image URL</SectionTitle>
            <Input
              type="url"
              placeholder="https://example.com/image.jpg"
              value={imageUrl}
              onChange={(e) => setImageUrl(e.target.value)}
            />
          </Section>

          {/* Image Settings */}
          {imageUrl && (
            <>
              <Section>
                <SectionTitle>Image Settings</SectionTitle>
                <SettingsGrid>
                  <SettingGroup>
                    <Label>Alt Text</Label>
                    <Input
                      type="text"
                      placeholder="Describe the image"
                      value={altText}
                      onChange={(e) => setAltText(e.target.value)}
                    />
                  </SettingGroup>

                  <SettingGroup>
                    <Label>Size</Label>
                    <Select value={size} onChange={(e) => setSize(e.target.value)}>
                      <option value="small">Small (300px)</option>
                      <option value="medium">Medium (500px)</option>
                      <option value="large">Large (800px)</option>
                      <option value="full">Full Width</option>
                    </Select>
                  </SettingGroup>

                  <SettingGroup>
                    <Label>Alignment</Label>
                    <Select value={alignment} onChange={(e) => setAlignment(e.target.value)}>
                      <option value="left">Left</option>
                      <option value="center">Center</option>
                      <option value="right">Right</option>
                    </Select>
                  </SettingGroup>
                </SettingsGrid>
              </Section>

              {/* Preview */}
              <Section>
                <SectionTitle>Preview</SectionTitle>
                <PreviewContainer>
                  <PreviewImage
                    src={imageUrl}
                    alt={altText}
                    $size={size}
                    $alignment={alignment}
                  />
                </PreviewContainer>
              </Section>
            </>
          )}
        </ModalBody>

        <ModalFooter>
          <CancelButton onClick={handleClose}>Cancel</CancelButton>
          <InsertButton 
            onClick={handleInsertImage}
            disabled={!imageUrl || uploading}
          >
            Insert Image
          </InsertButton>
        </ModalFooter>
      </ModalContent>
    </ModalOverlay>
  );
};

// Styled Components
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: white;
  border-radius: 0.75rem;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
`;

const ModalTitle = styled.h2`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  color: #111827;
  font-size: 1.25rem;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: color 0.2s;

  &:hover {
    color: #374151;
  }
`;

const ModalBody = styled.div`
  padding: 1.5rem;
`;

const Section = styled.div`
  margin-bottom: 2rem;

  &:last-child {
    margin-bottom: 0;
  }
`;

const SectionTitle = styled.h3`
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
`;

const UploadArea = styled.div`
  border: 2px dashed ${props => props.$dragOver ? '#d95550' : '#d1d5db'};
  border-radius: 0.5rem;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  background: ${props => props.$dragOver ? 'rgba(217, 85, 80, 0.05)' : '#f9fafb'};

  &:hover {
    border-color: #d95550;
    background: rgba(217, 85, 80, 0.05);
  }
`;

const UploadContent = styled.div`
  color: #6b7280;

  svg {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #9ca3af;

    &.spinning {
      animation: spin 1s linear infinite;
    }
  }

  p {
    margin: 0.5rem 0;
    font-weight: 500;
  }

  small {
    color: #9ca3af;
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
`;

const Input = styled.input`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;

  &:focus {
    outline: none;
    border-color: #d95550;
    box-shadow: 0 0 0 3px rgba(217, 85, 80, 0.1);
  }
`;

const SettingsGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const SettingGroup = styled.div``;

const Label = styled.label`
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
`;

const Select = styled.select`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  background: white;

  &:focus {
    outline: none;
    border-color: #d95550;
    box-shadow: 0 0 0 3px rgba(217, 85, 80, 0.1);
  }
`;

const PreviewContainer = styled.div`
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  background: #f9fafb;
  text-align: center;
`;

const PreviewImage = styled.img`
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  ${props => {
    const sizes = {
      small: 'width: 150px;',
      medium: 'width: 250px;',
      large: 'width: 100%; max-width: 400px;',
      full: 'width: 100%;'
    };
    return sizes[props.$size] || sizes.medium;
  }}
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
`;

const CancelButton = styled.button`
  padding: 0.75rem 1.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  background: white;
  color: #374151;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: #f9fafb;
  }
`;

const InsertButton = styled.button`
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  background: #d95550;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover:not(:disabled) {
    background: #c73e39;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

export default ImageUploadModal;
