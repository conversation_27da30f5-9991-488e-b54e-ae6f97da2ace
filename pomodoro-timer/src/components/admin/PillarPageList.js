import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { FaPlus, FaEdit, FaTrash, FaEye, FaStar, FaToggleOn, FaToggleOff } from 'react-icons/fa';
import { format } from 'date-fns';
import { pillarPageApi } from '../../services/pillarPageApi';

const PillarPageList = () => {
  const navigate = useNavigate();

  const [pillarPages, setPillarPages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [confirmDelete, setConfirmDelete] = useState(null);

  // Fetch pillar pages
  useEffect(() => {
    const fetchPillarPages = async () => {
      try {
        setLoading(true);
        // Get all pillar pages including unpublished ones (admin view)
        const data = await pillarPageApi.getAllPillarPages();
        setPillarPages(data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching pillar pages:', err);
        setError('Failed to load pillar pages');
        setLoading(false);
      }
    };

    fetchPillarPages();
  }, []);

  // Handle delete confirmation
  const handleDeleteClick = (pageId) => {
    setConfirmDelete(pageId);
  };

  const cancelDelete = () => {
    setConfirmDelete(null);
  };

  const confirmDeletePage = async (pageId) => {
    try {
      await pillarPageApi.deletePillarPage(pageId);
      setPillarPages(prev => prev.filter(page => page._id !== pageId));
      setConfirmDelete(null);
    } catch (err) {
      console.error('Error deleting pillar page:', err);
      setError('Failed to delete pillar page');
    }
  };

  // Toggle published status
  const togglePublished = async (page) => {
    try {
      const updatedPage = await pillarPageApi.updatePillarPage(page._id, {
        ...page,
        published: !page.published
      });
      
      setPillarPages(prev => 
        prev.map(p => p._id === page._id ? updatedPage : p)
      );
    } catch (err) {
      console.error('Error updating pillar page:', err);
      setError('Failed to update pillar page');
    }
  };

  // Toggle featured status
  const toggleFeatured = async (page) => {
    try {
      const updatedPage = await pillarPageApi.updatePillarPage(page._id, {
        ...page,
        featured: !page.featured
      });
      
      setPillarPages(prev => 
        prev.map(p => p._id === page._id ? updatedPage : p)
      );
    } catch (err) {
      console.error('Error updating pillar page:', err);
      setError('Failed to update pillar page');
    }
  };

  const getCategoryLabel = (category) => {
    const categoryMap = {
      'pomodoro-technique': 'The Pomodoro Technique',
      'time-management': 'Time Management',
      'adhd-productivity': 'ADHD & Productivity',
      'ai-productivity': 'AI & Productivity'
    };
    return categoryMap[category] || category;
  };

  return (
    <ListContainer>
      <ListHeader>
        <HeaderContent>
          <h1>Pillar Page Management</h1>
          <p>Manage your comprehensive resource guides and their cluster associations</p>
        </HeaderContent>
        <CreateButton onClick={() => navigate('/admin/pillar-pages/new')}>
          <FaPlus />
          <span>Create New Pillar Page</span>
        </CreateButton>
      </ListHeader>

      {error && <ErrorMessage>{error}</ErrorMessage>}

      {loading ? (
        <LoadingMessage>Loading pillar pages...</LoadingMessage>
      ) : error ? (
        <ErrorMessage>{error}</ErrorMessage>
      ) : pillarPages.length === 0 ? (
        <EmptyState>
          <p>No pillar pages found. Create your first pillar page to get started.</p>
          <CreateButton onClick={() => navigate('/admin/pillar-pages/new')}>
            <FaPlus />
            <span>Create New Pillar Page</span>
          </CreateButton>
        </EmptyState>
      ) : (
        <PagesTable>
          <thead>
            <tr>
              <TableHeader>Title</TableHeader>
              <TableHeader>Category</TableHeader>
              <TableHeader>Date</TableHeader>
              <TableHeader>Status</TableHeader>
              <TableHeader>Featured</TableHeader>
              <TableHeader>Menu Order</TableHeader>
              <TableHeader>Cluster Pages</TableHeader>
              <TableHeader>Actions</TableHeader>
            </tr>
          </thead>
          <tbody>
            {pillarPages.map(page => (
              <TableRow key={page._id}>
                <TableCell>
                  <PageTitle>{page.title}</PageTitle>
                </TableCell>
                <TableCell>
                  <CategoryBadge $category={page.category}>
                    {getCategoryLabel(page.category)}
                  </CategoryBadge>
                </TableCell>
                <TableCell>
                  <DateText>{format(new Date(page.createdAt), 'MMM d, yyyy')}</DateText>
                </TableCell>
                <TableCell>
                  <StatusToggle
                    onClick={() => togglePublished(page)}
                    $isPublished={page.published}
                    title={page.published ? 'Click to unpublish' : 'Click to publish'}
                  >
                    {page.published ? <FaToggleOn /> : <FaToggleOff />}
                    <span>{page.published ? 'Published' : 'Draft'}</span>
                  </StatusToggle>
                </TableCell>
                <TableCell>
                  <FeaturedToggle
                    onClick={() => toggleFeatured(page)}
                    $isFeatured={page.featured}
                    title={page.featured ? 'Remove from featured' : 'Add to featured'}
                  >
                    <FaStar />
                  </FeaturedToggle>
                </TableCell>
                <TableCell>
                  <MenuOrder>{page.menuOrder}</MenuOrder>
                </TableCell>
                <TableCell>
                  <ClusterCount>
                    {page.clusterPages ? page.clusterPages.length : 0} articles
                  </ClusterCount>
                </TableCell>
                <TableCell>
                  <ActionButtons>
                    <ActionButton
                      onClick={() => window.open(`/resources/${page.slug}`, '_blank')}
                      title="View pillar page"
                    >
                      <FaEye />
                    </ActionButton>
                    <ActionButton
                      onClick={() => navigate(`/admin/pillar-pages/edit/${page._id}`)}
                      title="Edit pillar page"
                    >
                      <FaEdit />
                    </ActionButton>
                    {confirmDelete === page._id ? (
                      <ConfirmDeleteContainer>
                        <ConfirmText>Are you sure?</ConfirmText>
                        <ConfirmButton onClick={() => confirmDeletePage(page._id)}>
                          Yes
                        </ConfirmButton>
                        <CancelButton onClick={cancelDelete}>
                          No
                        </CancelButton>
                      </ConfirmDeleteContainer>
                    ) : (
                      <ActionButton
                        onClick={() => handleDeleteClick(page._id)}
                        title="Delete pillar page"
                        $danger
                      >
                        <FaTrash />
                      </ActionButton>
                    )}
                  </ActionButtons>
                </TableCell>
              </TableRow>
            ))}
          </tbody>
        </PagesTable>
      )}
    </ListContainer>
  );
};

// Styled Components
const ListContainer = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem 1rem;
`;

const ListHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
`;

const HeaderContent = styled.div`
  h1 {
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
    color: #111827;
  }

  p {
    color: #6b7280;
    margin: 0;
  }
`;

const CreateButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #d95550;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  white-space: nowrap;

  &:hover {
    background: #c73e39;
  }

  @media (max-width: 768px) {
    justify-content: center;
  }
`;

const ErrorMessage = styled.div`
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 2rem;
`;

const LoadingMessage = styled.div`
  text-align: center;
  color: #6b7280;
  font-size: 1.1rem;
  padding: 4rem 2rem;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 4rem 2rem;
  color: #6b7280;

  p {
    margin-bottom: 2rem;
    font-size: 1.1rem;
  }
`;

const PagesTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const TableHeader = styled.th`
  background: #f8fafc;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875rem;
`;

const TableRow = styled.tr`
  &:hover {
    background: #f9fafb;
  }

  &:not(:last-child) {
    border-bottom: 1px solid #f3f4f6;
  }
`;

const TableCell = styled.td`
  padding: 1rem;
  vertical-align: middle;
`;

const PageTitle = styled.div`
  font-weight: 600;
  color: #111827;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const CategoryBadge = styled.span`
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
  background: ${props => {
    switch (props.$category) {
      case 'pomodoro-technique': return 'rgba(217, 85, 80, 0.1)';
      case 'time-management': return 'rgba(59, 130, 246, 0.1)';
      case 'adhd-productivity': return 'rgba(16, 185, 129, 0.1)';
      case 'ai-productivity': return 'rgba(139, 92, 246, 0.1)';
      default: return 'rgba(107, 114, 128, 0.1)';
    }
  }};
  color: ${props => {
    switch (props.$category) {
      case 'pomodoro-technique': return '#d95550';
      case 'time-management': return '#3b82f6';
      case 'adhd-productivity': return '#10b981';
      case 'ai-productivity': return '#8b5cf6';
      default: return '#6b7280';
    }
  }};
`;

const DateText = styled.span`
  color: #6b7280;
  font-size: 0.875rem;
`;

const StatusToggle = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  cursor: pointer;
  color: ${props => props.$isPublished ? '#10b981' : '#6b7280'};
  font-size: 0.875rem;
  transition: color 0.2s;

  &:hover {
    color: ${props => props.$isPublished ? '#059669' : '#4b5563'};
  }

  svg {
    font-size: 1.25rem;
  }
`;

const FeaturedToggle = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: ${props => props.$isFeatured ? '#f59e0b' : '#d1d5db'};
  font-size: 1.25rem;
  transition: color 0.2s;

  &:hover {
    color: ${props => props.$isFeatured ? '#d97706' : '#9ca3af'};
  }
`;

const MenuOrder = styled.span`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: #f3f4f6;
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.875rem;
  color: #374151;
`;

const ClusterCount = styled.span`
  color: #6b7280;
  font-size: 0.875rem;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 0.5rem;
  align-items: center;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: ${props => props.$danger ? '#fef2f2' : '#f3f4f6'};
  color: ${props => props.$danger ? '#dc2626' : '#6b7280'};
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: ${props => props.$danger ? '#fee2e2' : '#e5e7eb'};
    color: ${props => props.$danger ? '#b91c1c' : '#374151'};
  }
`;

const ConfirmDeleteContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #fef2f2;
  padding: 0.5rem;
  border-radius: 0.375rem;
  border: 1px solid #fecaca;
`;

const ConfirmText = styled.span`
  font-size: 0.75rem;
  color: #dc2626;
  white-space: nowrap;
`;

const ConfirmButton = styled.button`
  background: #dc2626;
  color: white;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background: #b91c1c;
  }
`;

const CancelButton = styled.button`
  background: #6b7280;
  color: white;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background: #4b5563;
  }
`;

export default PillarPageList;
