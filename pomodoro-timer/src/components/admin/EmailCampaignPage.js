import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import {
  FaEnvelope, FaPlus, FaEdit, FaTrash, FaPlay, FaPause, FaStop, FaEye,
  FaSpinner, FaSearch, FaClock, FaPaperPlane, FaUsers, FaChartLine,
  FaFileAlt, FaTags, FaCalendarAlt
} from 'react-icons/fa';
import { api } from '../../services/apiService';

const Container = styled.div`
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 20px;
`;

const Title = styled.h1`
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
`;

const Button = styled.button`
  background: ${props => 
    props.variant === 'danger' ? '#e74c3c' : 
    props.variant === 'success' ? '#27ae60' : 
    props.variant === 'warning' ? '#f39c12' :
    props.variant === 'outline' ? 'transparent' : '#3498db'};
  color: ${props => props.variant === 'outline' ? '#3498db' : 'white'};
  border: ${props => props.variant === 'outline' ? '1px solid #3498db' : 'none'};
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    background: ${props => props.variant === 'outline' ? '#3498db' : ''};
    color: ${props => props.variant === 'outline' ? 'white' : ''};
  }

  &:disabled {
    background: #95a5a6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

const FilterSection = styled.div`
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
  align-items: center;
`;

const SearchInput = styled.input`
  flex: 1;
  min-width: 300px;
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
`;

const Select = styled.select`
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background: white;
`;

const CampaignGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`;

const CampaignCard = styled.div`
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-left: 4px solid ${props => 
    props.status === 'sent' ? '#27ae60' :
    props.status === 'sending' ? '#f39c12' :
    props.status === 'scheduled' ? '#3498db' :
    props.status === 'paused' ? '#e74c3c' :
    '#95a5a6'
  };
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }
`;

const CampaignHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
`;

const CampaignName = styled.h3`
  color: #2c3e50;
  margin: 0;
  font-size: 18px;
  max-width: 70%;
`;

const StatusBadge = styled.span`
  background: ${props => 
    props.status === 'sent' ? '#27ae60' :
    props.status === 'sending' ? '#f39c12' :
    props.status === 'scheduled' ? '#3498db' :
    props.status === 'paused' ? '#e74c3c' :
    '#95a5a6'
  };
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
`;

const CampaignActions = styled.div`
  display: flex;
  gap: 5px;
  margin-top: 10px;
`;

const SmallButton = styled.button`
  background: ${props => 
    props.variant === 'danger' ? '#e74c3c' :
    props.variant === 'success' ? '#27ae60' :
    props.variant === 'warning' ? '#f39c12' :
    'transparent'
  };
  color: ${props => 
    props.variant === 'danger' ? 'white' :
    props.variant === 'success' ? 'white' :
    props.variant === 'warning' ? 'white' :
    '#3498db'
  };
  border: ${props => 
    props.variant === 'danger' || props.variant === 'success' || props.variant === 'warning' ? 
    'none' : '1px solid #3498db'
  };
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  transition: all 0.3s ease;

  &:hover {
    background: ${props => 
      props.variant === 'danger' ? '#c0392b' :
      props.variant === 'success' ? '#219a52' :
      props.variant === 'warning' ? '#d68910' :
      '#3498db'
    };
    color: white;
  }

  &:disabled {
    background: #bdc3c7;
    color: #7f8c8d;
    cursor: not-allowed;
  }
`;

const CampaignStats = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin: 15px 0;
`;

const StatItem = styled.div`
  text-align: center;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
`;

const StatValue = styled.div`
  font-size: 16px;
  font-weight: bold;
  color: #2c3e50;
`;

const StatLabel = styled.div`
  font-size: 11px;
  color: #666;
  margin-top: 4px;
`;

const CampaignDescription = styled.p`
  color: #666;
  font-size: 14px;
  margin: 10px 0;
  line-height: 1.4;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 6px;
  background: #ecf0f1;
  border-radius: 3px;
  margin: 10px 0;
  overflow: hidden;
`;

const ProgressFill = styled.div`
  height: 100%;
  background: #3498db;
  width: ${props => props.progress}%;
  transition: width 0.3s ease;
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: white;
  max-width: 800px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  border-radius: 10px;
  padding: 30px;
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
`;

const ModalTitle = styled.h2`
  color: #2c3e50;
  margin: 0;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
`;

const FormGroup = styled.div`
  margin-bottom: 20px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2c3e50;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 100px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  resize: vertical;
`;

const EmailCampaignPage = () => {
  const [campaigns, setCampaigns] = useState([]);
  const [emailLists, setEmailLists] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingCampaign, setEditingCampaign] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    subject: '',
    textContent: '',
    htmlContent: '',
    fromName: 'AI-Pomo Team',
    fromEmail: '<EMAIL>',
    replyToEmail: '<EMAIL>',
    emailLists: [],
    tags: '',
    excludeTags: '',
    batchSize: 100,
    delayBetweenBatches: 60000
  });

  useEffect(() => {
    loadCampaigns();
    loadEmailLists();
  }, [currentPage, searchTerm, statusFilter]);

  const loadCampaigns = async () => {
    try {
      setLoading(true);
      const response = await api.get('/email-campaigns', {
        params: {
          page: currentPage,
          limit: 12,
          search: searchTerm,
          status: statusFilter !== 'all' ? statusFilter : undefined
        }
      });

      if (response.data && response.data.success) {
        setCampaigns(response.data.data.campaigns);
        setTotalPages(response.data.data.pagination.pages);
      }
    } catch (error) {
      console.error('Error loading campaigns:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadEmailLists = async () => {
    try {
      const response = await api.get('/email-lists');
      if (response.data && response.data.success) {
        setEmailLists(response.data.data.lists);
      }
    } catch (error) {
      console.error('Error loading email lists:', error);
    }
  };

  const handleCreateCampaign = async () => {
    try {
      const campaignData = {
        ...formData,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
        excludeTags: formData.excludeTags.split(',').map(tag => tag.trim()).filter(tag => tag)
      };

      const response = await api.post('/email-campaigns', campaignData);

      if (response.data && response.data.success) {
        setShowCreateModal(false);
        resetForm();
        loadCampaigns();
      }
    } catch (error) {
      console.error('Error creating campaign:', error);
      alert(error.response?.data?.message || 'Failed to create campaign');
    }
  };

  const handleUpdateCampaign = async () => {
    try {
      const campaignData = {
        ...formData,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
        excludeTags: formData.excludeTags.split(',').map(tag => tag.trim()).filter(tag => tag)
      };

      const response = await api.put(`/email-campaigns/${editingCampaign._id}`, campaignData);

      if (response.data && response.data.success) {
        setEditingCampaign(null);
        resetForm();
        loadCampaigns();
      }
    } catch (error) {
      console.error('Error updating campaign:', error);
      alert(error.response?.data?.message || 'Failed to update campaign');
    }
  };

  const handleDeleteCampaign = async (campaignId, campaignName) => {
    if (!window.confirm(`Are you sure you want to delete "${campaignName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await api.delete(`/email-campaigns/${campaignId}`);
      if (response.data && response.data.success) {
        loadCampaigns();
      }
    } catch (error) {
      console.error('Error deleting campaign:', error);
      alert(error.response?.data?.message || 'Failed to delete campaign');
    }
  };

  const handleSendCampaign = async (campaignId) => {
    if (!window.confirm('Are you sure you want to send this campaign? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await api.post(`/email-campaigns/${campaignId}/send`);
      if (response.data && response.data.success) {
        loadCampaigns();
        alert('Campaign sending started successfully!');
      }
    } catch (error) {
      console.error('Error sending campaign:', error);
      alert(error.response?.data?.message || 'Failed to send campaign');
    }
  };

  const handlePauseCampaign = async (campaignId) => {
    try {
      const response = await api.post(`/email-campaigns/${campaignId}/pause`);
      if (response.data && response.data.success) {
        loadCampaigns();
        alert('Campaign paused successfully');
      }
    } catch (error) {
      console.error('Error pausing campaign:', error);
      alert(error.response?.data?.message || 'Failed to pause campaign');
    }
  };

  const handleEditCampaign = (campaign) => {
    setEditingCampaign(campaign);
    setFormData({
      name: campaign.name || '',
      description: campaign.description || '',
      subject: campaign.subject || '',
      textContent: campaign.textContent || '',
      htmlContent: campaign.htmlContent || '',
      fromName: campaign.fromName || 'AI-Pomo Team',
      fromEmail: campaign.fromEmail || '<EMAIL>',
      replyToEmail: campaign.replyToEmail || '<EMAIL>',
      emailLists: campaign.emailLists || [],
      tags: campaign.tags ? campaign.tags.join(', ') : '',
      excludeTags: campaign.excludeTags ? campaign.excludeTags.join(', ') : '',
      batchSize: campaign.batchSize || 100,
      delayBetweenBatches: campaign.delayBetweenBatches || 60000
    });
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      subject: '',
      textContent: '',
      htmlContent: '',
      fromName: 'AI-Pomo Team',
      fromEmail: '<EMAIL>',
      replyToEmail: '<EMAIL>',
      emailLists: [],
      tags: '',
      excludeTags: '',
      batchSize: 100,
      delayBetweenBatches: 60000
    });
  };

  const handleCloseModal = () => {
    setShowCreateModal(false);
    setEditingCampaign(null);
    resetForm();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'sent': return '#27ae60';
      case 'sending': return '#f39c12';
      case 'scheduled': return '#3498db';
      case 'paused': return '#e74c3c';
      default: return '#95a5a6';
    }
  };

  const getProgressPercentage = (campaign) => {
    if (!campaign.sendingProgress || !campaign.sendingProgress.total) return 0;
    return (campaign.sendingProgress.current / campaign.sendingProgress.total) * 100;
  };

  if (loading && campaigns.length === 0) {
    return (
      <Container>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <FaSpinner style={{ animation: 'spin 1s linear infinite', fontSize: '24px', color: '#3498db' }} />
          <p>Loading campaigns...</p>
        </div>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <Title>
          <FaEnvelope />
          Email Campaigns
        </Title>
        <ActionButtons>
          <Button variant="success" onClick={() => setShowCreateModal(true)}>
            <FaPlus />
            Create Campaign
          </Button>
        </ActionButtons>
      </Header>

      <FilterSection>
        <SearchInput
          type="text"
          placeholder="Search campaigns..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        <Select value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)}>
          <option value="all">All Status</option>
          <option value="draft">Draft</option>
          <option value="scheduled">Scheduled</option>
          <option value="sending">Sending</option>
          <option value="sent">Sent</option>
          <option value="paused">Paused</option>
        </Select>
        <Button variant="outline">
          <FaSearch />
          Search
        </Button>
      </FilterSection>

      <CampaignGrid>
        {campaigns.map(campaign => (
          <CampaignCard key={campaign._id} status={campaign.status}>
            <CampaignHeader>
              <CampaignName>{campaign.name}</CampaignName>
              <StatusBadge status={campaign.status}>{campaign.status}</StatusBadge>
            </CampaignHeader>

            <CampaignStats>
              <StatItem>
                <StatValue>{campaign.totalRecipients || 0}</StatValue>
                <StatLabel>Recipients</StatLabel>
              </StatItem>
              <StatItem>
                <StatValue>{campaign.emailsSent || 0}</StatValue>
                <StatLabel>Sent</StatLabel>
              </StatItem>
              <StatItem>
                <StatValue>{campaign.emailLists?.length || 0}</StatValue>
                <StatLabel>Lists</StatLabel>
              </StatItem>
            </CampaignStats>

            {campaign.status === 'sending' && (
              <div>
                <ProgressBar>
                  <ProgressFill progress={getProgressPercentage(campaign)} />
                </ProgressBar>
                <div style={{ fontSize: '12px', color: '#666', textAlign: 'center' }}>
                  {campaign.sendingProgress?.current || 0} / {campaign.sendingProgress?.total || 0} sent
                </div>
              </div>
            )}

            {campaign.description && (
              <CampaignDescription>{campaign.description}</CampaignDescription>
            )}

            <CampaignActions>
              {campaign.status === 'draft' && (
                <>
                  <SmallButton onClick={() => handleEditCampaign(campaign)} title="Edit Campaign">
                    <FaEdit />
                  </SmallButton>
                  <SmallButton variant="success" onClick={() => handleSendCampaign(campaign._id)} title="Send Campaign">
                    <FaPlay />
                  </SmallButton>
                </>
              )}
              
              {campaign.status === 'scheduled' && (
                <>
                  <SmallButton variant="success" onClick={() => handleSendCampaign(campaign._id)} title="Send Now">
                    <FaPlay />
                  </SmallButton>
                  <SmallButton variant="warning" onClick={() => handlePauseCampaign(campaign._id)} title="Pause">
                    <FaPause />
                  </SmallButton>
                </>
              )}
              
              {campaign.status === 'sending' && (
                <SmallButton variant="warning" onClick={() => handlePauseCampaign(campaign._id)} title="Pause">
                  <FaPause />
                </SmallButton>
              )}
              
              {(campaign.status === 'draft' || campaign.status === 'paused') && (
                <SmallButton 
                  variant="danger" 
                  onClick={() => handleDeleteCampaign(campaign._id, campaign.name)} 
                  title="Delete Campaign"
                >
                  <FaTrash />
                </SmallButton>
              )}
            </CampaignActions>
          </CampaignCard>
        ))}
      </CampaignGrid>

      {campaigns.length === 0 && !loading && (
        <div style={{ textAlign: 'center', padding: '50px', color: '#666' }}>
          <FaEnvelope style={{ fontSize: '48px', marginBottom: '20px', opacity: 0.3 }} />
          <h3>No Email Campaigns Found</h3>
          <p>Create your first email campaign to start reaching your audience.</p>
          <Button variant="success" onClick={() => setShowCreateModal(true)}>
            <FaPlus />
            Create Your First Campaign
          </Button>
        </div>
      )}

      {(showCreateModal || editingCampaign) && (
        <Modal onClick={handleCloseModal}>
          <ModalContent onClick={(e) => e.stopPropagation()}>
            <ModalHeader>
              <ModalTitle>
                {editingCampaign ? 'Edit Email Campaign' : 'Create New Email Campaign'}
              </ModalTitle>
              <CloseButton onClick={handleCloseModal}>×</CloseButton>
            </ModalHeader>

            <FormGroup>
              <Label>Campaign Name *</Label>
              <Input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter campaign name..."
                required
              />
            </FormGroup>

            <FormGroup>
              <Label>Description</Label>
              <TextArea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Enter campaign description..."
              />
            </FormGroup>

            <FormGroup>
              <Label>Email Subject *</Label>
              <Input
                type="text"
                value={formData.subject}
                onChange={(e) => setFormData(prev => ({ ...prev, subject: e.target.value }))}
                placeholder="Enter email subject..."
                required
              />
            </FormGroup>

            <FormGroup>
              <Label>Text Content *</Label>
              <TextArea
                value={formData.textContent}
                onChange={(e) => setFormData(prev => ({ ...prev, textContent: e.target.value }))}
                placeholder="Enter plain text email content..."
                required
                style={{ minHeight: '100px' }}
              />
            </FormGroup>

            <FormGroup>
              <Label>HTML Content *</Label>
              <TextArea
                value={formData.htmlContent}
                onChange={(e) => setFormData(prev => ({ ...prev, htmlContent: e.target.value }))}
                placeholder="Enter HTML email content..."
                required
                style={{ minHeight: '150px' }}
              />
            </FormGroup>

            <FormGroup>
              <Label>Email Lists *</Label>
              <Select
                multiple
                value={formData.emailLists}
                onChange={(e) => {
                  const selectedLists = Array.from(e.target.selectedOptions, option => option.value);
                  setFormData(prev => ({ ...prev, emailLists: selectedLists }));
                }}
                style={{ minHeight: '100px' }}
                required
              >
                {emailLists.map(list => (
                  <option key={list._id} value={list._id}>
                    {list.name} ({list.totalContacts || 0} contacts)
                  </option>
                ))}
              </Select>
              <small style={{ color: '#666', fontSize: '12px' }}>
                Hold Ctrl/Cmd to select multiple lists
              </small>
            </FormGroup>

            <ActionButtons>
              <Button variant="outline" onClick={handleCloseModal}>
                Cancel
              </Button>
              <Button 
                variant="success" 
                onClick={editingCampaign ? handleUpdateCampaign : handleCreateCampaign}
                disabled={!formData.name.trim() || !formData.subject.trim() || !formData.textContent.trim() || !formData.htmlContent.trim() || formData.emailLists.length === 0}
              >
                {editingCampaign ? 'Update Campaign' : 'Create Campaign'}
              </Button>
            </ActionButtons>
          </ModalContent>
        </Modal>
      )}
    </Container>
  );
};

export default EmailCampaignPage;