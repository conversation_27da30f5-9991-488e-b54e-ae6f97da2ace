import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import {
  FaUniversity, FaEnvelope, FaPaperPlane, FaEye, FaEdit,
  FaUsers, FaCheckCircle, FaExclamationTriangle, FaSpinner,
  FaPlay, FaStop, FaClock, FaChartLine, FaDownload, FaCog, FaDatabase
} from 'react-icons/fa';
import { api } from '../../services/apiService';
import EmailTemplateEditor from './EmailTemplateEditor';

const Container = styled.div`
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 20px;
`;

const Title = styled.h1`
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
`;

const StatsRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`;

const StatCard = styled.div`
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 15px;
`;

const StatIcon = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  background: ${props => props.color || '#3498db'};
`;

const StatContent = styled.div`
  flex: 1;
`;

const StatLabel = styled.div`
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
`;

const StatValue = styled.div`
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
  flex-wrap: wrap;
`;

const Button = styled.button`
  background: ${props => props.variant === 'danger' ? '#e74c3c' : props.variant === 'success' ? '#27ae60' : '#3498db'};
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }

  &:disabled {
    background: #95a5a6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

const UniversityGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`;

const UniversityCard = styled.div`
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-left: 4px solid ${props => props.sent ? '#27ae60' : '#3498db'};
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }
`;

const UniversityHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
`;

const UniversityName = styled.h3`
  color: #2c3e50;
  margin: 0;
  font-size: 18px;
`;

const UniversityStatus = styled.div`
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: ${props => props.sent ? '#27ae60' : '#666'};
  background: ${props => props.sent ? '#d5f4e6' : '#f8f9fa'};
  padding: 4px 8px;
  border-radius: 12px;
`;

const UniversityInfo = styled.div`
  margin-bottom: 15px;
`;

const InfoRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
`;

const InfoLabel = styled.span`
  color: #666;
`;

const InfoValue = styled.span`
  font-weight: 500;
  color: #2c3e50;
`;

const UniversityActions = styled.div`
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
`;

const SmallButton = styled.button`
  background: ${props => props.variant === 'outline' ? 'transparent' : '#3498db'};
  color: ${props => props.variant === 'outline' ? '#3498db' : 'white'};
  border: ${props => props.variant === 'outline' ? '1px solid #3498db' : 'none'};
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  transition: all 0.3s ease;

  &:hover {
    background: #3498db;
    color: white;
  }

  &:disabled {
    background: #95a5a6;
    cursor: not-allowed;
  }
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: white;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  border-radius: 10px;
  padding: 30px;
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
`;

const ModalTitle = styled.h2`
  color: #2c3e50;
  margin: 0;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
`;

const FormGroup = styled.div`
  margin-bottom: 20px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2c3e50;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 200px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  font-family: monospace;
  resize: vertical;
`;

const CheckboxGroup = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
`;

const Checkbox = styled.input`
  width: 16px;
  height: 16px;
`;

const BulkActions = styled.div`
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
`;

const SelectedCount = styled.div`
  color: #666;
  margin-bottom: 15px;
  font-size: 14px;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
  margin: 10px 0;
`;

const ProgressFill = styled.div`
  height: 100%;
  background: #3498db;
  width: ${props => props.progress}%;
  transition: width 0.3s ease;
`;

const ProgressText = styled.div`
  font-size: 12px;
  color: #666;
  text-align: center;
`;

const LogSection = styled.div`
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
`;

const LogTitle = styled.h3`
  color: #2c3e50;
  margin-bottom: 15px;
`;

const LogEntry = styled.div`
  padding: 10px;
  border-bottom: 1px solid #eee;
  font-size: 13px;

  &:last-child {
    border-bottom: none;
  }
`;

const LogTime = styled.span`
  color: #666;
  margin-right: 10px;
`;

const LogMessage = styled.span`
  color: ${props => props.type === 'error' ? '#e74c3c' : props.type === 'success' ? '#27ae60' : '#2c3e50'};
`;

const EmailOutreachPage = () => {
  const [universities, setUniversities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedUniversities, setSelectedUniversities] = useState([]);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [previewEmail, setPreviewEmail] = useState(null);
  const [testMode, setTestMode] = useState(true);
  const [bulkSending, setBulkSending] = useState(false);
  const [sendProgress, setSendProgress] = useState(0);
  const [activityLog, setActivityLog] = useState([]);
  const [customSubject, setCustomSubject] = useState('');
  const [customContent, setCustomContent] = useState('');
  const [showTemplateEditor, setShowTemplateEditor] = useState(false);
  const [editingUniversityId, setEditingUniversityId] = useState(null);
  const [initializing, setInitializing] = useState(false);

  const stats = {
    totalUniversities: universities.length,
    emailsSent: activityLog.filter(log => log.type === 'success').length,
    emailsFailed: activityLog.filter(log => log.type === 'error').length,
    emailsPending: universities.length - activityLog.filter(log => log.type === 'success').length
  };

  useEffect(() => {
    loadUniversities();
  }, []);

  const initializeTemplates = async () => {
    try {
      setInitializing(true);
      addLogEntry('Initializing email templates in database...', 'info');
      
      const response = await api.post('/email-outreach/templates/initialize');
      
      if (response.data && response.data.success) {
        const { created, updated } = response.data.data;
        addLogEntry(`Templates initialized: ${created} created, ${updated} updated`, 'success');
      } else {
        addLogEntry('Failed to initialize templates', 'error');
      }
    } catch (error) {
      console.error('Error initializing templates:', error);
      const errorMessage = error.response?.data?.message || 'Failed to initialize templates';
      addLogEntry(`Error initializing templates: ${errorMessage}`, 'error');
    } finally {
      setInitializing(false);
    }
  };

  const handleEditTemplate = (universityId) => {
    setEditingUniversityId(universityId);
    setShowTemplateEditor(true);
  };

  const handleTemplateEditorClose = () => {
    setShowTemplateEditor(false);
    setEditingUniversityId(null);
  };

  const handleTemplateSaved = () => {
    addLogEntry('Email template updated successfully', 'success');
  };

  const loadUniversities = async () => {
    try {
      setLoading(true);
      console.log('Loading universities from API...');
      const response = await api.get('/email-outreach/universities');
      console.log('Universities response:', response);
      
      if (response.data && response.data.success) {
        setUniversities(response.data.data);
        addLogEntry(`Loaded ${response.data.data.length} universities`, 'success');
      } else {
        addLogEntry('Failed to load universities: Invalid response', 'error');
      }
    } catch (error) {
      console.error('Error loading universities:', error);
      
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);
        const errorMessage = error.response.data?.message || error.response.statusText || 'Server error';
        addLogEntry(`Failed to load universities: ${errorMessage}`, 'error');
      } else if (error.request) {
        console.error('No response received:', error.request);
        addLogEntry('Failed to load universities: Network error', 'error');
      } else {
        addLogEntry(`Failed to load universities: ${error.message}`, 'error');
      }
    } finally {
      setLoading(false);
    }
  };

  const addLogEntry = (message, type = 'info') => {
    const entry = {
      id: Date.now(),
      timestamp: new Date().toLocaleTimeString(),
      message,
      type
    };
    setActivityLog(prev => [entry, ...prev.slice(0, 49)]); // Keep last 50 entries
  };

  const handlePreviewEmail = async (universityId) => {
    try {
      console.log(`Loading email template for ${universityId}`);
      const response = await api.get(`/email-outreach/template/${universityId}`);
      console.log('Template response:', response);
      
      if (response.data && response.data.success) {
        setPreviewEmail(response.data.data);
        setShowPreviewModal(true);
      } else {
        const errorMessage = response.data?.message || 'Invalid response';
        addLogEntry(`Failed to load template for ${universityId}: ${errorMessage}`, 'error');
      }
    } catch (error) {
      console.error('Error loading email template:', error);
      
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);
        const errorMessage = error.response.data?.message || error.response.statusText || 'Server error';
        addLogEntry(`Failed to load template for ${universityId}: ${errorMessage}`, 'error');
      } else {
        addLogEntry(`Failed to load template for ${universityId}: ${error.message}`, 'error');
      }
    }
  };

  const handleSendSingleEmail = async (universityId) => {
    try {
      const university = universities.find(u => u.id === universityId);
      addLogEntry(`Sending email to ${university.name}...`, 'info');

      console.log('Sending email request:', {
        universityId,
        testMode,
        customSubject: customSubject || undefined,
        customContent: customContent || undefined
      });

      const response = await api.post('/email-outreach/send', {
        universityId,
        testMode,
        customSubject: customSubject || undefined,
        customContent: customContent || undefined
      });

      console.log('Email send response:', response);

      if (response.data && response.data.success) {
        addLogEntry(`✅ Email sent to ${university.name}`, 'success');
      } else {
        const errorMessage = response.data?.message || 'Unknown error';
        addLogEntry(`❌ Failed to send to ${university.name}: ${errorMessage}`, 'error');
      }
    } catch (error) {
      console.error('Error sending email:', error);
      
      // More detailed error logging
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);
        const errorMessage = error.response.data?.message || error.response.statusText || 'Server error';
        addLogEntry(`❌ Error sending to ${universityId}: ${errorMessage}`, 'error');
      } else if (error.request) {
        console.error('No response received:', error.request);
        addLogEntry(`❌ Network error sending to ${universityId}: No response from server`, 'error');
      } else {
        addLogEntry(`❌ Error sending to ${universityId}: ${error.message}`, 'error');
      }
    }
  };

  const handleBulkSend = async () => {
    if (selectedUniversities.length === 0) {
      alert('Please select universities to send emails to');
      return;
    }

    if (!window.confirm(`Are you sure you want to send emails to ${selectedUniversities.length} universities? ${testMode ? '(Test Mode)' : '(Live Send)'}`)) {
      return;
    }

    try {
      setBulkSending(true);
      setSendProgress(0);
      addLogEntry(`Starting bulk email send to ${selectedUniversities.length} universities...`, 'info');

      const response = await api.post('/email-outreach/send-bulk', {
        universityIds: selectedUniversities,
        testMode,
        delay: 5000 // 5 seconds delay between emails
      });

      if (response.data.success) {
        const { results, summary } = response.data.data;
        addLogEntry(`✅ Bulk send completed: ${summary.success} successful, ${summary.failure} failed`, 'success');
        
        results.forEach(result => {
          if (result.success) {
            addLogEntry(`✅ ${result.universityName}: Sent successfully`, 'success');
          } else {
            addLogEntry(`❌ ${result.universityId}: ${result.error}`, 'error');
          }
        });

        setSelectedUniversities([]);
      } else {
        addLogEntry(`❌ Bulk send failed: ${response.data.message}`, 'error');
      }
    } catch (error) {
      console.error('Error sending bulk emails:', error);
      addLogEntry(`❌ Bulk send error: ${error.message}`, 'error');
    } finally {
      setBulkSending(false);
      setSendProgress(0);
    }
  };

  const handleSelectAll = () => {
    if (selectedUniversities.length === universities.length) {
      setSelectedUniversities([]);
    } else {
      setSelectedUniversities(universities.map(u => u.id));
    }
  };

  const handleSelectUniversity = (universityId) => {
    setSelectedUniversities(prev => {
      if (prev.includes(universityId)) {
        return prev.filter(id => id !== universityId);
      } else {
        return [...prev, universityId];
      }
    });
  };

  if (loading) {
    return (
      <Container>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <FaSpinner style={{ animation: 'spin 1s linear infinite', fontSize: '24px', color: '#3498db' }} />
          <p>Loading university outreach data...</p>
        </div>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <Title>
          <FaUniversity />
University Email Outreach
        </Title>
      </Header>

      <StatsRow>
        <StatCard>
          <StatIcon color="#3498db">
            <FaUniversity />
          </StatIcon>
          <StatContent>
            <StatLabel>Target Universities</StatLabel>
            <StatValue>{stats.totalUniversities}</StatValue>
          </StatContent>
        </StatCard>

        <StatCard>
          <StatIcon color="#27ae60">
            <FaCheckCircle />
          </StatIcon>
          <StatContent>
            <StatLabel>Emails Sent</StatLabel>
            <StatValue>{stats.emailsSent}</StatValue>
          </StatContent>
        </StatCard>

        <StatCard>
          <StatIcon color="#e74c3c">
            <FaExclamationTriangle />
          </StatIcon>
          <StatContent>
            <StatLabel>Send Failed</StatLabel>
            <StatValue>{stats.emailsFailed}</StatValue>
          </StatContent>
        </StatCard>

        <StatCard>
          <StatIcon color="#f39c12">
            <FaClock />
          </StatIcon>
          <StatContent>
            <StatLabel>Pending Send</StatLabel>
            <StatValue>{stats.emailsPending}</StatValue>
          </StatContent>
        </StatCard>
      </StatsRow>

      <ActionButtons>
        <Button onClick={handleSelectAll}>
          <FaUsers />
          {selectedUniversities.length === universities.length ? 'Deselect All' : 'Select All Universities'}
        </Button>
        <Button 
          variant="success" 
          onClick={handleBulkSend}
          disabled={selectedUniversities.length === 0 || bulkSending}
        >
          {bulkSending ? <FaSpinner style={{ animation: 'spin 1s linear infinite' }} /> : <FaPaperPlane />}
          Bulk Send ({selectedUniversities.length})
        </Button>
        <Button onClick={initializeTemplates} disabled={initializing}>
          {initializing ? <FaSpinner style={{ animation: 'spin 1s linear infinite' }} /> : <FaDatabase />}
          Initialize Templates
        </Button>
        <CheckboxGroup>
          <Checkbox
            type="checkbox"
            checked={testMode}
            onChange={(e) => setTestMode(e.target.checked)}
          />
          <label>Test Mode (<NAME_EMAIL>)</label>
        </CheckboxGroup>
      </ActionButtons>

      {selectedUniversities.length > 0 && (
        <BulkActions>
          <h3>Bulk Actions</h3>
          <SelectedCount>Selected {selectedUniversities.length} universities</SelectedCount>
          
          <FormGroup>
            <Label>Custom Email Subject (Optional)</Label>
            <Input
              type="text"
              value={customSubject}
              onChange={(e) => setCustomSubject(e.target.value)}
              placeholder="Leave empty to use default template subject"
            />
          </FormGroup>

          <FormGroup>
            <Label>Custom Email Content (Optional)</Label>
            <TextArea
              value={customContent}
              onChange={(e) => setCustomContent(e.target.value)}
              placeholder="Leave empty to use default template content..."
            />
          </FormGroup>

          {bulkSending && (
            <div>
              <ProgressBar>
                <ProgressFill progress={sendProgress} />
              </ProgressBar>
              <ProgressText>Send Progress: {sendProgress}%</ProgressText>
            </div>
          )}
        </BulkActions>
      )}

      <UniversityGrid>
        {universities.map(university => (
          <UniversityCard key={university.id}>
            <UniversityHeader>
              <div>
                <UniversityName>{university.name}</UniversityName>
                <div style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>
                  {university.email}
                </div>
              </div>
              <UniversityStatus sent={false}>
                <FaClock />
                Pending
              </UniversityStatus>
            </UniversityHeader>

            <UniversityInfo>
              <InfoRow>
                <InfoLabel>Authority Score:</InfoLabel>
                <InfoValue>{university.authorityScore}</InfoValue>
              </InfoRow>
              <InfoRow>
                <InfoLabel>Existing Backlinks:</InfoLabel>
                <InfoValue>{university.backlinks} links</InfoValue>
              </InfoRow>
              <InfoRow>
                <InfoLabel>Strategy:</InfoLabel>
                <InfoValue>{university.strategy}</InfoValue>
              </InfoRow>
              {university.existingPage && (
                <InfoRow>
                  <InfoLabel>Existing Page:</InfoLabel>
                  <InfoValue>
                    <a href={university.existingPage} target="_blank" rel="noopener noreferrer">
                      View Page
                    </a>
                  </InfoValue>
                </InfoRow>
              )}
            </UniversityInfo>

            <UniversityActions>
              <CheckboxGroup>
                <Checkbox
                  type="checkbox"
                  checked={selectedUniversities.includes(university.id)}
                  onChange={() => handleSelectUniversity(university.id)}
                />
                <span style={{ fontSize: '12px' }}>Select</span>
              </CheckboxGroup>
              
              <SmallButton 
                variant="outline"
                onClick={() => handlePreviewEmail(university.id)}
              >
                <FaEye />
                Preview
              </SmallButton>
              
              <SmallButton 
                variant="outline"
                onClick={() => handleEditTemplate(university.id)}
              >
                <FaEdit />
                Edit Template
              </SmallButton>
              
              <SmallButton onClick={() => handleSendSingleEmail(university.id)}>
                <FaPaperPlane />
                Send
              </SmallButton>
            </UniversityActions>
          </UniversityCard>
        ))}
      </UniversityGrid>

      <LogSection>
        <LogTitle>Activity Log</LogTitle>
        {activityLog.length === 0 ? (
          <p style={{ color: '#666', textAlign: 'center' }}>No activity records yet</p>
        ) : (
          activityLog.map(entry => (
            <LogEntry key={entry.id}>
              <LogTime>{entry.timestamp}</LogTime>
              <LogMessage type={entry.type}>{entry.message}</LogMessage>
            </LogEntry>
          ))
        )}
      </LogSection>

      {showPreviewModal && previewEmail && (
        <Modal onClick={() => setShowPreviewModal(false)}>
          <ModalContent onClick={(e) => e.stopPropagation()}>
            <ModalHeader>
              <ModalTitle>Email Preview - {previewEmail.university.name}</ModalTitle>
              <CloseButton onClick={() => setShowPreviewModal(false)}>×</CloseButton>
            </ModalHeader>
            
            <FormGroup>
              <Label>To:</Label>
              <Input value={previewEmail.university.email} readOnly />
            </FormGroup>

            <FormGroup>
              <Label>Subject:</Label>
              <Input value={previewEmail.template.subject} readOnly />
            </FormGroup>

            <FormGroup>
              <Label>Content:</Label>
              <TextArea value={previewEmail.template.text || previewEmail.template.content} readOnly />
            </FormGroup>

            <ActionButtons>
              <Button onClick={() => setShowPreviewModal(false)}>
                Close
              </Button>
              <Button 
                variant="success"
                onClick={() => {
                  handleSendSingleEmail(previewEmail.university.id);
                  setShowPreviewModal(false);
                }}
              >
                <FaPaperPlane />
                Send This Email
              </Button>
            </ActionButtons>
          </ModalContent>
        </Modal>
      )}

      {showTemplateEditor && editingUniversityId && (
        <Modal onClick={() => setShowTemplateEditor(false)}>
          <ModalContent onClick={(e) => e.stopPropagation()} style={{ maxWidth: '95vw', width: '1200px', maxHeight: '95vh' }}>
            <EmailTemplateEditor
              universityId={editingUniversityId}
              onClose={handleTemplateEditorClose}
              onSave={handleTemplateSaved}
            />
          </ModalContent>
        </Modal>
      )}
    </Container>
  );
};

export default EmailOutreachPage;