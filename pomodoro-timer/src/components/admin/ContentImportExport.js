import React, { useState } from 'react';
import styled from 'styled-components';
import { exportContent, importContent, downloadBlob } from '../../services/contentExportService';
import { api } from '../../services/apiService';

const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
`;

const Section = styled.div`
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const SectionTitle = styled.h2`
  margin: 0 0 16px 0;
  color: #333;
  font-size: 1.5rem;
`;

const Description = styled.p`
  color: #666;
  margin-bottom: 20px;
  line-height: 1.5;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  flex-wrap: wrap;
`;

const Button = styled.button`
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const PrimaryButton = styled(Button)`
  background: #007bff;
  color: white;
  
  &:hover:not(:disabled) {
    background: #0056b3;
  }
`;

const SecondaryButton = styled(Button)`
  background: #6c757d;
  color: white;
  
  &:hover:not(:disabled) {
    background: #545b62;
  }
`;

const SuccessButton = styled(Button)`
  background: #28a745;
  color: white;
  
  &:hover:not(:disabled) {
    background: #1e7e34;
  }
`;

const FileInput = styled.input`
  margin-bottom: 16px;
  padding: 8px;
  border: 2px dashed #ddd;
  border-radius: 6px;
  width: 100%;
  
  &:focus {
    outline: none;
    border-color: #007bff;
  }
`;

const Select = styled.select`
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-right: 12px;
  margin-bottom: 12px;
`;

const StatusMessage = styled.div`
  padding: 12px;
  border-radius: 6px;
  margin-top: 16px;
  
  &.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }
  
  &.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }
  
  &.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
  }
`;

const ResultsContainer = styled.div`
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
`;

const ContentImportExport = () => {
  const [exportLoading, setExportLoading] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  const [importFile, setImportFile] = useState(null);
  const [importMode, setImportMode] = useState('update');
  const [message, setMessage] = useState(null);
  const [importResults, setImportResults] = useState(null);

  const handleExport = async (type) => {
    setExportLoading(true);
    setMessage(null);
    
    try {
      const blob = await exportContent(type);
      const filename = `content-export-${type}-${new Date().toISOString().split('T')[0]}.zip`;
      downloadBlob(blob, filename);
      
      setMessage({
        type: 'success',
        text: `Successfully exported ${type} content as ${filename}`
      });
    } catch (error) {
      setMessage({
        type: 'error',
        text: `Failed to export content: ${error.response?.data?.message || error.message}`
      });
    } finally {
      setExportLoading(false);
    }
  };

  const handleImport = async () => {
    console.log('Import file:', importFile); // Debug log

    if (!importFile) {
      setMessage({
        type: 'error',
        text: 'Please select a ZIP file to import'
      });
      return;
    }

    // Validate file type
    if (!importFile.name.toLowerCase().endsWith('.zip')) {
      setMessage({
        type: 'error',
        text: 'Please select a valid ZIP file'
      });
      return;
    }

    setImportLoading(true);
    setMessage(null);
    setImportResults(null);

    try {
      console.log('Starting import with file:', importFile.name, 'Mode:', importMode); // Debug log
      const results = await importContent(importFile, importMode);

      setImportResults(results.results);
      setMessage({
        type: 'success',
        text: 'Import completed successfully!'
      });

      // Clear the file input
      setImportFile(null);
      const fileInput = document.getElementById('importFile');
      if (fileInput) {
        fileInput.value = '';
      }

    } catch (error) {
      console.error('Import error:', error); // Debug log
      setMessage({
        type: 'error',
        text: `Failed to import content: ${error.response?.data?.message || error.message}`
      });
    } finally {
      setImportLoading(false);
    }
  };

  const renderImportResults = () => {
    if (!importResults) return null;

    return (
      <ResultsContainer>
        <h4>Import Results:</h4>
        
        <div style={{ marginBottom: '12px' }}>
          <strong>Blog Posts:</strong>
          <ul>
            <li>Created: {importResults.blogPosts.created}</li>
            <li>Updated: {importResults.blogPosts.updated}</li>
            <li>Errors: {importResults.blogPosts.errors.length}</li>
          </ul>
        </div>
        
        <div style={{ marginBottom: '12px' }}>
          <strong>Pillar Pages:</strong>
          <ul>
            <li>Created: {importResults.pillarPages.created}</li>
            <li>Updated: {importResults.pillarPages.updated}</li>
            <li>Errors: {importResults.pillarPages.errors.length}</li>
          </ul>
        </div>
        
        {(importResults.blogPosts.errors.length > 0 || importResults.pillarPages.errors.length > 0) && (
          <div>
            <strong>Errors:</strong>
            <ul>
              {importResults.blogPosts.errors.map((error, index) => (
                <li key={`blog-${index}`} style={{ color: '#dc3545' }}>
                  {error.file}: {error.error}
                </li>
              ))}
              {importResults.pillarPages.errors.map((error, index) => (
                <li key={`pillar-${index}`} style={{ color: '#dc3545' }}>
                  {error.file}: {error.error}
                </li>
              ))}
            </ul>
          </div>
        )}
      </ResultsContainer>
    );
  };

  return (
    <Container>
      <h1>Content Import/Export</h1>
      
      {/* Export Section */}
      <Section>
        <SectionTitle>📤 Export Content</SectionTitle>
        <Description>
          Export your blog posts and pillar pages as Markdown files in a ZIP archive. 
          This allows you to edit content offline and maintain backups.
        </Description>
        
        <ButtonGroup>
          <PrimaryButton 
            onClick={() => handleExport('all')} 
            disabled={exportLoading}
          >
            {exportLoading ? 'Exporting...' : 'Export All Content'}
          </PrimaryButton>
          
          <SecondaryButton 
            onClick={() => handleExport('blog')} 
            disabled={exportLoading}
          >
            Export Blog Posts Only
          </SecondaryButton>
          
          <SecondaryButton 
            onClick={() => handleExport('pillar')} 
            disabled={exportLoading}
          >
            Export Pillar Pages Only
          </SecondaryButton>
        </ButtonGroup>
      </Section>

      {/* Import Section */}
      <Section>
        <SectionTitle>📥 Import Content</SectionTitle>
        <Description>
          Import blog posts and pillar pages from a ZIP file containing Markdown files. 
          The ZIP should have the same structure as exported files.
        </Description>
        
        <div>
          <FileInput
            id="importFile"
            type="file"
            accept=".zip"
            onChange={(e) => {
              const file = e.target.files[0];
              console.log('File selected:', file); // Debug log
              console.log('File details:', {
                name: file?.name,
                size: file?.size,
                type: file?.type,
                lastModified: file?.lastModified
              });
              setImportFile(file);
              // Clear any previous error messages when a new file is selected
              if (file && message?.type === 'error') {
                setMessage(null);
              }
            }}
          />

          {/* Debug info */}
          {importFile && (
            <div style={{
              fontSize: '12px',
              color: '#666',
              marginTop: '8px',
              padding: '8px',
              backgroundColor: '#f8f9fa',
              borderRadius: '4px'
            }}>
              <strong>Selected file:</strong> {importFile.name} ({(importFile.size / 1024).toFixed(1)} KB)
            </div>
          )}
          
          <div style={{ marginBottom: '16px' }}>
            <label>Import Mode: </label>
            <Select 
              value={importMode} 
              onChange={(e) => setImportMode(e.target.value)}
            >
              <option value="update">Update existing content</option>
              <option value="skip">Skip existing content</option>
            </Select>
          </div>
          
          <SuccessButton
            onClick={handleImport}
            disabled={importLoading || !importFile}
          >
            {importLoading ? 'Importing...' : 'Import Content'}
          </SuccessButton>

          {/* Test button for debugging */}
          <SecondaryButton
            onClick={async () => {
              if (!importFile) {
                alert('Please select a file first');
                return;
              }

              try {
                const formData = new FormData();
                formData.append('importFile', importFile);

                // Create a custom config for this request to override the default transformRequest
                const config = {
                  headers: {
                    // Don't set Content-Type, let the browser set it with boundary
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                  },
                  transformRequest: [(data) => data] // Don't transform FormData
                };

                const response = await api.post('/content-export/test-upload', formData, config);
                console.log('Test response:', response.data);
                alert('Test successful! Check console for details.');
              } catch (error) {
                console.error('Test error:', error);
                alert('Test failed! Check console for details.');
              }
            }}
            disabled={!importFile}
            style={{ marginLeft: '12px' }}
          >
            Test Upload
          </SecondaryButton>
        </div>
        
        {renderImportResults()}
      </Section>

      {/* Status Messages */}
      {message && (
        <StatusMessage className={message.type}>
          {message.text}
        </StatusMessage>
      )}
    </Container>
  );
};

export default ContentImportExport;
