import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import {
  FaList, FaPlus, FaEdit, FaTrash, FaUsers, FaUpload, FaDownload,
  <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON><PERSON>ner, FaSearch, FaFilter, FaFileImport, FaUserPlus
} from 'react-icons/fa';
import { api } from '../../services/apiService';

const Container = styled.div`
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 20px;
`;

const Title = styled.h1`
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
`;

const Button = styled.button`
  background: ${props => 
    props.variant === 'danger' ? '#e74c3c' : 
    props.variant === 'success' ? '#27ae60' : 
    props.variant === 'outline' ? 'transparent' : '#3498db'};
  color: ${props => props.variant === 'outline' ? '#3498db' : 'white'};
  border: ${props => props.variant === 'outline' ? '1px solid #3498db' : 'none'};
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    background: ${props => props.variant === 'outline' ? '#3498db' : ''};
    color: ${props => props.variant === 'outline' ? 'white' : ''};
  }

  &:disabled {
    background: #95a5a6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

const SearchSection = styled.div`
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
  align-items: center;
`;

const SearchInput = styled.input`
  flex: 1;
  min-width: 300px;
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
`;

const ListGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`;

const ListCard = styled.div`
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #3498db;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }
`;

const ListHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
`;

const ListName = styled.h3`
  color: #2c3e50;
  margin: 0;
  font-size: 18px;
`;

const ListActions = styled.div`
  display: flex;
  gap: 5px;
`;

const SmallButton = styled.button`
  background: ${props => props.variant === 'outline' ? 'transparent' : '#3498db'};
  color: ${props => props.variant === 'outline' ? '#3498db' : 'white'};
  border: ${props => props.variant === 'outline' ? '1px solid #3498db' : 'none'};
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  transition: all 0.3s ease;

  &:hover {
    background: #3498db;
    color: white;
  }
`;

const ListStats = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin-bottom: 15px;
`;

const StatItem = styled.div`
  text-align: center;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
`;

const StatValue = styled.div`
  font-size: 18px;
  font-weight: bold;
  color: #2c3e50;
`;

const StatLabel = styled.div`
  font-size: 12px;
  color: #666;
  margin-top: 4px;
`;

const ListDescription = styled.p`
  color: #666;
  font-size: 14px;
  margin: 10px 0;
`;

const TagContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 10px;
`;

const Tag = styled.span`
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: white;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  border-radius: 10px;
  padding: 30px;
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
`;

const ModalTitle = styled.h2`
  color: #2c3e50;
  margin: 0;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
`;

const FormGroup = styled.div`
  margin-bottom: 20px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2c3e50;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 100px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  resize: vertical;
`;

const Pagination = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 30px;
`;

const PageButton = styled.button`
  background: ${props => props.active ? '#3498db' : 'white'};
  color: ${props => props.active ? 'white' : '#3498db'};
  border: 1px solid #3498db;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;

  &:hover {
    background: #3498db;
    color: white;
  }

  &:disabled {
    background: #f8f9fa;
    color: #ccc;
    border-color: #ddd;
    cursor: not-allowed;
  }
`;

const EmailListPage = () => {
  const navigate = useNavigate();
  const [lists, setLists] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingList, setEditingList] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    tags: ''
  });

  useEffect(() => {
    loadEmailLists();
  }, [currentPage, searchTerm]);

  const loadEmailLists = async () => {
    try {
      setLoading(true);
      const response = await api.get('/email-lists', {
        params: {
          page: currentPage,
          limit: 12,
          search: searchTerm
        }
      });

      if (response.data && response.data.success) {
        setLists(response.data.data.lists);
        setTotalPages(response.data.data.pagination.pages);
      }
    } catch (error) {
      console.error('Error loading email lists:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateList = async () => {
    try {
      const response = await api.post('/email-lists', {
        name: formData.name,
        description: formData.description,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
      });

      if (response.data && response.data.success) {
        setShowCreateModal(false);
        setFormData({ name: '', description: '', tags: '' });
        loadEmailLists();
      }
    } catch (error) {
      console.error('Error creating list:', error);
      alert(error.response?.data?.message || 'Failed to create list');
    }
  };

  const handleUpdateList = async () => {
    try {
      const response = await api.put(`/email-lists/${editingList._id}`, {
        name: formData.name,
        description: formData.description,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
      });

      if (response.data && response.data.success) {
        setEditingList(null);
        setFormData({ name: '', description: '', tags: '' });
        loadEmailLists();
      }
    } catch (error) {
      console.error('Error updating list:', error);
      alert(error.response?.data?.message || 'Failed to update list');
    }
  };

  const handleDeleteList = async (listId, listName) => {
    if (!window.confirm(`Are you sure you want to delete "${listName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await api.delete(`/email-lists/${listId}`);
      if (response.data && response.data.success) {
        loadEmailLists();
      }
    } catch (error) {
      console.error('Error deleting list:', error);
      alert(error.response?.data?.message || 'Failed to delete list');
    }
  };

  const handleEditList = (list) => {
    setEditingList(list);
    setFormData({
      name: list.name,
      description: list.description || '',
      tags: list.tags ? list.tags.join(', ') : ''
    });
  };

  const handleCloseModal = () => {
    setShowCreateModal(false);
    setEditingList(null);
    setFormData({ name: '', description: '', tags: '' });
  };

  if (loading && lists.length === 0) {
    return (
      <Container>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <FaSpinner style={{ animation: 'spin 1s linear infinite', fontSize: '24px', color: '#3498db' }} />
          <p>Loading email lists...</p>
        </div>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <Title>
          <FaList />
          Email Lists
        </Title>
        <ActionButtons>
          <Button variant="success" onClick={() => setShowCreateModal(true)}>
            <FaPlus />
            Create List
          </Button>
        </ActionButtons>
      </Header>

      <SearchSection>
        <SearchInput
          type="text"
          placeholder="Search lists..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        <Button variant="outline">
          <FaSearch />
          Search
        </Button>
      </SearchSection>

      <ListGrid>
        {lists.map(list => (
          <ListCard key={list._id}>
            <ListHeader>
              <ListName>{list.name}</ListName>
              <ListActions>
                <SmallButton 
                  variant="outline"
                  onClick={() => handleEditList(list)}
                  title="Edit List"
                >
                  <FaEdit />
                </SmallButton>
                <SmallButton 
                  variant="outline"
                  onClick={() => navigate(`/admin/email-lists/${list._id}/contacts`)}
                  title="View Contacts"
                >
                  <FaEye />
                </SmallButton>
                <SmallButton 
                  variant="outline"
                  onClick={() => handleDeleteList(list._id, list.name)}
                  title="Delete List"
                  style={{ color: '#e74c3c', borderColor: '#e74c3c' }}
                >
                  <FaTrash />
                </SmallButton>
              </ListActions>
            </ListHeader>

            <ListStats>
              <StatItem>
                <StatValue>{list.totalContacts || 0}</StatValue>
                <StatLabel>Total</StatLabel>
              </StatItem>
              <StatItem>
                <StatValue>{list.activeContacts || 0}</StatValue>
                <StatLabel>Active</StatLabel>
              </StatItem>
              <StatItem>
                <StatValue>{list.unsubscribedContacts || 0}</StatValue>
                <StatLabel>Unsubscribed</StatLabel>
              </StatItem>
            </ListStats>

            {list.description && (
              <ListDescription>{list.description}</ListDescription>
            )}

            {list.tags && list.tags.length > 0 && (
              <TagContainer>
                {list.tags.map((tag, index) => (
                  <Tag key={index}>{tag}</Tag>
                ))}
              </TagContainer>
            )}
          </ListCard>
        ))}
      </ListGrid>

      {lists.length === 0 && !loading && (
        <div style={{ textAlign: 'center', padding: '50px', color: '#666' }}>
          <FaList style={{ fontSize: '48px', marginBottom: '20px', opacity: 0.3 }} />
          <h3>No Email Lists Found</h3>
          <p>Create your first email list to start building your subscriber base.</p>
          <Button variant="success" onClick={() => setShowCreateModal(true)}>
            <FaPlus />
            Create Your First List
          </Button>
        </div>
      )}

      {totalPages > 1 && (
        <Pagination>
          <PageButton
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            Previous
          </PageButton>
          
          {[...Array(totalPages)].map((_, index) => (
            <PageButton
              key={index + 1}
              active={currentPage === index + 1}
              onClick={() => setCurrentPage(index + 1)}
            >
              {index + 1}
            </PageButton>
          ))}
          
          <PageButton
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
          >
            Next
          </PageButton>
        </Pagination>
      )}

      {(showCreateModal || editingList) && (
        <Modal onClick={handleCloseModal}>
          <ModalContent onClick={(e) => e.stopPropagation()}>
            <ModalHeader>
              <ModalTitle>
                {editingList ? 'Edit Email List' : 'Create New Email List'}
              </ModalTitle>
              <CloseButton onClick={handleCloseModal}>×</CloseButton>
            </ModalHeader>

            <FormGroup>
              <Label>List Name *</Label>
              <Input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter list name..."
                required
              />
            </FormGroup>

            <FormGroup>
              <Label>Description</Label>
              <TextArea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Enter list description..."
              />
            </FormGroup>

            <FormGroup>
              <Label>Tags (comma separated)</Label>
              <Input
                type="text"
                value={formData.tags}
                onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
                placeholder="e.g. newsletter, customers, prospects"
              />
            </FormGroup>

            <ActionButtons>
              <Button variant="outline" onClick={handleCloseModal}>
                Cancel
              </Button>
              <Button 
                variant="success" 
                onClick={editingList ? handleUpdateList : handleCreateList}
                disabled={!formData.name.trim()}
              >
                {editingList ? 'Update List' : 'Create List'}
              </Button>
            </ActionButtons>
          </ModalContent>
        </Modal>
      )}
    </Container>
  );
};

export default EmailListPage;