import React, { useState, useRef } from 'react';
import styled from 'styled-components';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';
import {
  FaBold, FaItalic, FaUnderline, FaHeading, FaListUl, FaListOl,
  FaLink, FaImage, FaCode, FaQuoteLeft, FaEye, FaEdit, FaYoutube,
  FaStrikethrough, FaTable, FaAlignLeft, FaAlignCenter, FaAlignRight
} from 'react-icons/fa';
import ImageUploadModal from './ImageUploadModal';
import VideoEmbedModal from './VideoEmbedModal';

const EnhancedTextEditor = ({ value, onChange, placeholder, rows = 20 }) => {
  const [showPreview, setShowPreview] = useState(false);
  const [showImageModal, setShowImageModal] = useState(false);
  const [showVideoModal, setShowVideoModal] = useState(false);
  const textareaRef = useRef(null);
  const [cursorPosition, setCursorPosition] = useState({ start: 0, end: 0 });

  // Track cursor position changes
  const handleSelectionChange = () => {
    if (textareaRef.current) {
      setCursorPosition({
        start: textareaRef.current.selectionStart,
        end: textareaRef.current.selectionEnd
      });
    }
  };

  // Enhanced onChange handler that preserves cursor position
  const handleTextChange = (e) => {
    const newValue = e.target.value;
    const currentStart = e.target.selectionStart;
    const currentEnd = e.target.selectionEnd;

    // Call the parent onChange
    onChange(e);

    // Restore cursor position after React re-render
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.setSelectionRange(currentStart, currentEnd);
      }
    }, 0);
  };

  const insertText = (before, after = '') => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);

    const newText = value.substring(0, start) + before + selectedText + after + value.substring(end);

    // Calculate new cursor position
    const newCursorStart = start + before.length;
    const newCursorEnd = newCursorStart + selectedText.length;

    // Update the value
    onChange({ target: { value: newText } });

    // Restore cursor position with multiple attempts to ensure it works
    const restoreCursor = () => {
      if (textarea && document.activeElement === textarea) {
        textarea.setSelectionRange(newCursorStart, newCursorEnd);
      }
    };

    // Multiple timeouts to handle different React render cycles
    setTimeout(restoreCursor, 0);
    setTimeout(restoreCursor, 10);
    setTimeout(() => {
      textarea.focus();
      restoreCursor();
    }, 20);
  };

  const formatButtons = [
    // Text formatting
    { icon: FaHeading, label: 'Heading', action: () => insertText('## ', '') },
    { icon: FaBold, label: 'Bold', action: () => insertText('**', '**') },
    { icon: FaItalic, label: 'Italic', action: () => insertText('*', '*') },
    { icon: FaUnderline, label: 'Underline', action: () => insertText('<u>', '</u>') },
    { icon: FaStrikethrough, label: 'Strikethrough', action: () => insertText('~~', '~~') },

    // Lists and structure
    { icon: FaListUl, label: 'Bullet List', action: () => insertText('- ', '') },
    { icon: FaListOl, label: 'Numbered List', action: () => insertText('1. ', '') },
    { icon: FaQuoteLeft, label: 'Quote', action: () => insertText('> ', '') },
    { icon: FaCode, label: 'Inline Code', action: () => insertText('`', '`') },
    { icon: FaTable, label: 'Table', action: () => insertTable() },

    // Media and links
    { icon: FaLink, label: 'Link', action: () => insertText('[', '](URL)') },
    { icon: FaImage, label: 'Image', action: () => setShowImageModal(true) },
    { icon: FaYoutube, label: 'YouTube Video', action: () => setShowVideoModal(true) },
  ];

  const insertTable = () => {
    const tableMarkdown = `
| Column 1 | Column 2 | Column 3 |
|----------|----------|----------|
| Row 1    | Data     | Data     |
| Row 2    | Data     | Data     |
`;
    insertText(tableMarkdown, '');
  };

  const handleImageInsert = (imageMarkdown) => {
    insertText(imageMarkdown, '');
  };

  const handleVideoInsert = (videoMarkdown) => {
    insertText(videoMarkdown, '');
  };

  const renderPreview = () => {
    return (
      <PreviewContent>
        <ReactMarkdown
          remarkPlugins={[remarkGfm, remarkBreaks]}
          components={{
            // Custom component for links to open external links in new tab
            a: ({ href, children, ...props }) => {
              const isExternal = href && (href.startsWith('http') || href.startsWith('https'));
              return (
                <a
                  href={href}
                  target={isExternal ? '_blank' : undefined}
                  rel={isExternal ? 'noopener noreferrer' : undefined}
                  {...props}
                >
                  {children}
                </a>
              );
            },
            // Custom component for images with proper styling
            img: ({ src, alt, ...props }) => (
              <img
                src={src}
                alt={alt}
                style={{ maxWidth: '100%', height: 'auto', borderRadius: '0.5rem' }}
                {...props}
              />
            ),
          }}
        >
          {value}
        </ReactMarkdown>
      </PreviewContent>
    );
  };

  return (
    <EditorContainer>
      <EditorHeader>
        <ToolbarSection>
          {/* Text Formatting Group */}
          <ToolbarGroup>
            {formatButtons.slice(0, 5).map((button, index) => (
              <ToolbarButton
                key={index}
                onClick={button.action}
                title={button.label}
                type="button"
              >
                <button.icon />
              </ToolbarButton>
            ))}
          </ToolbarGroup>

          {/* Lists and Structure Group */}
          <ToolbarGroup>
            {formatButtons.slice(5, 10).map((button, index) => (
              <ToolbarButton
                key={index + 5}
                onClick={button.action}
                title={button.label}
                type="button"
              >
                <button.icon />
              </ToolbarButton>
            ))}
          </ToolbarGroup>

          {/* Media and Links Group */}
          <ToolbarGroup>
            {formatButtons.slice(10).map((button, index) => (
              <ToolbarButton
                key={index + 10}
                onClick={button.action}
                title={button.label}
                type="button"
                $isMedia={button.label === 'Image' || button.label === 'YouTube Video'}
              >
                <button.icon />
              </ToolbarButton>
            ))}
          </ToolbarGroup>
        </ToolbarSection>
        
        <ViewToggle>
          <ToggleButton
            $active={!showPreview}
            onClick={() => setShowPreview(false)}
            type="button"
          >
            <FaEdit /> Edit
          </ToggleButton>
          <ToggleButton
            $active={showPreview}
            onClick={() => setShowPreview(true)}
            type="button"
          >
            <FaEye /> Preview
          </ToggleButton>
        </ViewToggle>
      </EditorHeader>

      <EditorContent>
        {showPreview ? (
          <PreviewContainer>
            {value ? renderPreview() : <EmptyPreview>Nothing to preview yet...</EmptyPreview>}
          </PreviewContainer>
        ) : (
          <EditorTextarea
            ref={textareaRef}
            value={value}
            onChange={handleTextChange}
            onSelect={handleSelectionChange}
            onKeyUp={handleSelectionChange}
            onMouseUp={handleSelectionChange}
            placeholder={placeholder}
            rows={rows}
          />
        )}
      </EditorContent>

      <EditorFooter>
        <FooterText>
          💡 <strong>Tips:</strong> Use Markdown syntax for formatting. Click the buttons above to insert common formatting. You can also use HTML tags if needed.
        </FooterText>
      </EditorFooter>

      {/* Modals */}
      <ImageUploadModal
        isOpen={showImageModal}
        onClose={() => setShowImageModal(false)}
        onImageInsert={handleImageInsert}
      />

      <VideoEmbedModal
        isOpen={showVideoModal}
        onClose={() => setShowVideoModal(false)}
        onVideoInsert={handleVideoInsert}
      />
    </EditorContainer>
  );
};

// Styled Components
const EditorContainer = styled.div`
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  overflow: hidden;
  background: white;
`;

const EditorHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  flex-wrap: wrap;
  gap: 1rem;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
`;

const ToolbarSection = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
`;

const ToolbarGroup = styled.div`
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem;
  background: white;
  border-radius: 0.375rem;
  border: 1px solid #e5e7eb;
`;

const ToolbarButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: none;
  border: none;
  border-radius: 0.25rem;
  color: ${props => props.$isMedia ? '#d95550' : '#6b7280'};
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: ${props => props.$isMedia ? 'rgba(217, 85, 80, 0.1)' : '#f3f4f6'};
    color: #d95550;
  }

  &:active {
    background: #e5e7eb;
  }

  svg {
    ${props => props.$isMedia && props.title === 'YouTube Video' && 'color: #ff0000;'}
  }
`;

const ViewToggle = styled.div`
  display: flex;
  background: white;
  border-radius: 0.375rem;
  border: 1px solid #e5e7eb;
  overflow: hidden;
`;

const ToggleButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: ${props => props.$active ? '#d95550' : 'white'};
  color: ${props => props.$active ? 'white' : '#6b7280'};
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;

  &:hover {
    background: ${props => props.$active ? '#c73e39' : '#f9fafb'};
  }
`;

const EditorContent = styled.div`
  min-height: 400px;
`;

const EditorTextarea = styled.textarea`
  width: 100%;
  min-height: 400px;
  padding: 1rem;
  border: none;
  outline: none;
  font-size: 0.875rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  line-height: 1.6;
  resize: vertical;
  background: white;

  &::placeholder {
    color: #9ca3af;
  }
`;

const PreviewContainer = styled.div`
  min-height: 400px;
  padding: 1rem;
  background: white;
`;

const PreviewContent = styled.div`
  font-size: 1rem;
  line-height: 1.6;
  color: #374151;

  h1, h2, h3, h4, h5, h6 {
    color: #111827;
    font-weight: 700;
    margin: 1.5rem 0 1rem;
    line-height: 1.3;
  }

  h1 { font-size: 2rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }
  h4 { font-size: 1.125rem; }

  p {
    margin-bottom: 1rem;
  }

  ul, ol {
    margin-bottom: 1rem;
    padding-left: 2rem;
  }

  li {
    margin-bottom: 0.5rem;
  }

  blockquote {
    border-left: 4px solid #d95550;
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
    color: #6b7280;
  }

  code {
    background-color: #f3f4f6;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875em;
  }

  img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 1rem 0;
  }

  a {
    color: #d95550;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  strong {
    font-weight: 700;
  }

  em {
    font-style: italic;
  }

  u {
    text-decoration: underline;
  }
`;

const EmptyPreview = styled.div`
  color: #9ca3af;
  font-style: italic;
  text-align: center;
  padding: 2rem;
`;

const EditorFooter = styled.div`
  padding: 0.75rem;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
`;

const FooterText = styled.div`
  font-size: 0.75rem;
  color: #6b7280;
  line-height: 1.4;
`;

export default EnhancedTextEditor;
