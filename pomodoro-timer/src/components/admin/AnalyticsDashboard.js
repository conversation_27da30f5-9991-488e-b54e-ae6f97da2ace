import React, { useState, useEffect, useCallback } from 'react';
import styled from 'styled-components';
import { analyticsApi } from '../../services/apiService';

const AnalyticsDashboard = () => {
  const [analyticsData, setAnalyticsData] = useState(null);
  const [realTimeData, setRealTimeData] = useState(null);
  const [timeRange, setTimeRange] = useState('7d');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch analytics data
  const fetchAnalytics = useCallback(async () => {
    try {
      setLoading(true);
      const data = await analyticsApi.getDashboard(timeRange);
      setAnalyticsData(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [timeRange]);

  // Fetch real-time data
  const fetchRealTime = useCallback(async () => {
    try {
      const data = await analyticsApi.getRealTime();
      setRealTimeData(data);
    } catch (err) {
      console.error('Error fetching real-time data:', err);
    }
  }, []);

  useEffect(() => {
    fetchAnalytics();
  }, [fetchAnalytics]);

  useEffect(() => {
    fetchRealTime();

    // Update real-time data every 30 seconds
    const interval = setInterval(fetchRealTime, 30000);
    return () => clearInterval(interval);
  }, [fetchRealTime]);

  if (loading) {
    return (
      <Container>
        <LoadingSpinner>Loading analytics...</LoadingSpinner>
      </Container>
    );
  }

  if (error) {
    return (
      <Container>
        <ErrorMessage>Error: {error}</ErrorMessage>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <Title>Website Analytics</Title>
        <TimeRangeSelector>
          <select value={timeRange} onChange={(e) => setTimeRange(e.target.value)}>
            <option value="1d">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
        </TimeRangeSelector>
      </Header>

      {/* Real-time Stats */}
      {realTimeData && (
        <RealTimeSection>
          <SectionTitle>Real-time</SectionTitle>
          <StatsGrid>
            <StatCard>
              <StatValue>{realTimeData.activeVisitors}</StatValue>
              <StatLabel>Active Visitors</StatLabel>
            </StatCard>
          </StatsGrid>
        </RealTimeSection>
      )}

      {/* Summary Stats */}
      {analyticsData && (
        <>
          <SummarySection>
            <SectionTitle>Summary ({timeRange})</SectionTitle>
            <StatsGrid>
              <StatCard>
                <StatValue>{analyticsData.summary.totalPageViews.toLocaleString()}</StatValue>
                <StatLabel>Page Views</StatLabel>
              </StatCard>
              <StatCard>
                <StatValue>{analyticsData.summary.uniqueVisitors.toLocaleString()}</StatValue>
                <StatLabel>Unique Visitors</StatLabel>
              </StatCard>
              <StatCard>
                <StatValue>{analyticsData.summary.totalSessions.toLocaleString()}</StatValue>
                <StatLabel>Total Sessions</StatLabel>
              </StatCard>
              <StatCard>
                <StatValue>{analyticsData.summary.newSessions.toLocaleString()}</StatValue>
                <StatLabel>New Sessions</StatLabel>
              </StatCard>
              <StatCard>
                <StatValue>{Math.floor(analyticsData.summary.avgSessionDuration / 60)}m {analyticsData.summary.avgSessionDuration % 60}s</StatValue>
                <StatLabel>Avg Session Duration</StatLabel>
              </StatCard>
              <StatCard>
                <StatValue>{analyticsData.summary.bounceRate}%</StatValue>
                <StatLabel>Bounce Rate</StatLabel>
              </StatCard>
            </StatsGrid>
          </SummarySection>

          {/* Charts Section */}
          <ChartsSection>
            <ChartContainer>
              <ChartTitle>Daily Traffic</ChartTitle>
              <SimpleChart>
                {analyticsData.dailyTraffic.map((day, index) => (
                  <ChartBar key={index}>
                    <BarLabel>{new Date(day.date).toLocaleDateString()}</BarLabel>
                    <Bar height={Math.max(day.pageViews / Math.max(...analyticsData.dailyTraffic.map(d => d.pageViews)) * 100, 5)}>
                      {day.pageViews}
                    </Bar>
                  </ChartBar>
                ))}
              </SimpleChart>
            </ChartContainer>
          </ChartsSection>

          {/* Top Pages */}
          <DataSection>
            <DataContainer>
              <DataTitle>Top Pages</DataTitle>
              <DataTable>
                <thead>
                  <tr>
                    <th>Page</th>
                    <th>Views</th>
                    <th>Unique Visitors</th>
                  </tr>
                </thead>
                <tbody>
                  {analyticsData.topPages.map((page, index) => (
                    <tr key={index}>
                      <td>{page.path}</td>
                      <td>{page.views}</td>
                      <td>{page.uniqueVisitors}</td>
                    </tr>
                  ))}
                </tbody>
              </DataTable>
            </DataContainer>

            {/* Device Breakdown */}
            <DataContainer>
              <DataTitle>Device Types</DataTitle>
              <DataList>
                {analyticsData.deviceBreakdown.map((device, index) => (
                  <DataItem key={index}>
                    <span>{device.device || 'Unknown'}</span>
                    <span>{device.count}</span>
                  </DataItem>
                ))}
              </DataList>
            </DataContainer>
          </DataSection>

          {/* Browser and Country Data */}
          <DataSection>
            <DataContainer>
              <DataTitle>Top Browsers</DataTitle>
              <DataList>
                {analyticsData.browserBreakdown.map((browser, index) => (
                  <DataItem key={index}>
                    <span>{browser.browser || 'Unknown'}</span>
                    <span>{browser.count}</span>
                  </DataItem>
                ))}
              </DataList>
            </DataContainer>

            <DataContainer>
              <DataTitle>Top Countries</DataTitle>
              <DataList>
                {analyticsData.countryBreakdown.map((country, index) => (
                  <DataItem key={index}>
                    <span>{country.country || 'Unknown'}</span>
                    <span>{country.count}</span>
                  </DataItem>
                ))}
              </DataList>
            </DataContainer>
          </DataSection>
        </>
      )}
    </Container>
  );
};

// Styled Components
const Container = styled.div`
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
`;

const TimeRangeSelector = styled.div`
  select {
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    background-color: white;
    font-size: 0.875rem;
  }
`;

const RealTimeSection = styled.div`
  margin-bottom: 2rem;
`;

const SummarySection = styled.div`
  margin-bottom: 2rem;
`;

const SectionTitle = styled.h2`
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
`;

const StatCard = styled.div`
  background: white;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  text-align: center;
`;

const StatValue = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
`;

const StatLabel = styled.div`
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
`;

const ChartsSection = styled.div`
  margin-bottom: 2rem;
`;

const ChartContainer = styled.div`
  background: white;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const ChartTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
`;

const SimpleChart = styled.div`
  display: flex;
  align-items: end;
  gap: 0.5rem;
  height: 200px;
  overflow-x: auto;
`;

const ChartBar = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60px;
`;

const Bar = styled.div`
  background: #3b82f6;
  color: white;
  padding: 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  text-align: center;
  min-height: 20px;
  height: ${props => props.height}%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
`;

const BarLabel = styled.div`
  font-size: 0.75rem;
  color: #6b7280;
  transform: rotate(-45deg);
  white-space: nowrap;
`;

const DataSection = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
`;

const DataContainer = styled.div`
  background: white;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const DataTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
`;

const DataTable = styled.table`
  width: 100%;
  border-collapse: collapse;

  th, td {
    padding: 0.5rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
  }

  th {
    font-weight: 600;
    color: #374151;
  }

  td {
    color: #6b7280;
  }
`;

const DataList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const DataItem = styled.div`
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f3f4f6;

  &:last-child {
    border-bottom: none;
  }
`;

const LoadingSpinner = styled.div`
  text-align: center;
  padding: 2rem;
  color: #6b7280;
`;

const ErrorMessage = styled.div`
  text-align: center;
  padding: 2rem;
  color: #dc2626;
`;

export default AnalyticsDashboard;
