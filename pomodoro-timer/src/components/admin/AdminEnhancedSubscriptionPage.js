import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import {
  FaCheck, FaTimes, FaSpinner, FaPaypal, FaEthereum,
  FaCalendarAlt, FaFilter, FaSort, FaSearch, FaExclamationTriangle,
  FaEnvelope, FaUser, FaInfoCircle, FaFileInvoiceDollar, FaHistory
} from 'react-icons/fa';
// eslint-disable-next-line no-unused-vars
import { adminApi } from '../../services/apiService';

const AdminEnhancedSubscriptionPage = () => {
  const [pendingSubscriptions, setPendingSubscriptions] = useState([]);
  const [allSubscriptions, setAllSubscriptions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('pending');
  const [selectedSubscription, setSelectedSubscription] = useState(null);
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const [rejectModalOpen, setRejectModalOpen] = useState(false);
  const [userDetailsModalOpen, setUserDetailsModalOpen] = useState(false);
  const [expiryDate, setExpiryDate] = useState('');
  const [adminNotes, setAdminNotes] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');
  const [actionLoading, setActionLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredSubscriptions, setFilteredSubscriptions] = useState([]);
  const [sortField, setSortField] = useState('createdAt');
  const [sortDirection, setSortDirection] = useState('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedSubscriptions, setSelectedSubscriptions] = useState([]);
  const [batchActionModalOpen, setBatchActionModalOpen] = useState(false);
  const [batchAction, setBatchAction] = useState('');
  const [paymentHistory, setPaymentHistory] = useState([]);
  const [paymentHistoryModalOpen, setPaymentHistoryModalOpen] = useState(false);
  const [userStats, setUserStats] = useState(null);
  const [manualUpdateModalOpen, setManualUpdateModalOpen] = useState(false);
  const [manualUpdateData, setManualUpdateData] = useState({
    userId: '',
    plan: 'monthly',
    subscriptionStatus: 'active',
    paymentStatus: 'confirmed',
    paddleSubscriptionId: '',
    expiryDate: '',
    notes: ''
  });

  // Fetch subscriptions on mount and when tab changes
  useEffect(() => {
    fetchSubscriptions();
  }, [activeTab, currentPage, sortField, sortDirection]);

  // Filter subscriptions when search term changes
  useEffect(() => {
    if (searchTerm) {
      const filtered = (activeTab === 'pending' ? pendingSubscriptions : allSubscriptions).filter(sub =>
        (sub.user?.name && sub.user.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (sub.user?.email && sub.user.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (sub.payerEmail && sub.payerEmail.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (sub.payerName && sub.payerName.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredSubscriptions(filtered);
    } else {
      setFilteredSubscriptions(activeTab === 'pending' ? pendingSubscriptions : allSubscriptions);
    }
  }, [searchTerm, pendingSubscriptions, allSubscriptions, activeTab]);

  const fetchSubscriptions = async () => {
    setIsLoading(true);
    setError('');

    try {
      if (activeTab === 'pending') {
        const data = await adminApi.getPendingSubscriptions();
        setPendingSubscriptions(data);
        setFilteredSubscriptions(data);
      } else {
        const filters = {
          status: activeTab === 'all' ? undefined : activeTab,
          page: currentPage,
          sort: sortField,
          direction: sortDirection
        };
        const data = await adminApi.getAllSubscriptions(filters);
        setAllSubscriptions(data.subscriptions || []);
        setFilteredSubscriptions(data.subscriptions || []);
        setTotalPages(data.pagination?.pages || 1);
      }
    } catch (error) {
      console.error('Error fetching subscriptions:', error);
      setError('Failed to load subscriptions. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleConfirmClick = (subscription) => {
    setSelectedSubscription(subscription);

    // Set default expiry date based on plan
    if (subscription.plan === 'yearly') {
      const oneYearFromNow = new Date();
      oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
      setExpiryDate(oneYearFromNow.toISOString().split('T')[0]);
    } else {
      // For lifetime, set a far future date (e.g., 100 years)
      const farFuture = new Date();
      farFuture.setFullYear(farFuture.getFullYear() + 100);
      setExpiryDate(farFuture.toISOString().split('T')[0]);
    }

    setConfirmModalOpen(true);
  };

  const handleRejectClick = (subscription) => {
    setSelectedSubscription(subscription);
    setRejectModalOpen(true);
  };

  const handleUserDetailsClick = async (subscription) => {
    setSelectedSubscription(subscription);
    setIsLoading(true);

    try {
      // Fetch user stats and payment history
      const stats = await adminApi.getUserStats(subscription.user?._id);
      setUserStats(stats);

      const history = await adminApi.getUserPaymentHistory(subscription.user?._id);
      setPaymentHistory(history);

      setUserDetailsModalOpen(true);
    } catch (error) {
      console.error('Error fetching user details:', error);
      setError('Failed to load user details. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePaymentHistoryClick = async (subscription) => {
    setSelectedSubscription(subscription);
    setIsLoading(true);

    try {
      const history = await adminApi.getUserPaymentHistory(subscription.user?._id);
      setPaymentHistory(history);
      setPaymentHistoryModalOpen(true);
    } catch (error) {
      console.error('Error fetching payment history:', error);
      setError('Failed to load payment history. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const confirmSubscription = async () => {
    if (!selectedSubscription) return;

    setActionLoading(true);

    try {
      await adminApi.confirmSubscription(selectedSubscription._id, {
        expiryDate,
        notes: adminNotes
      });

      // Refresh subscriptions
      fetchSubscriptions();

      // Close modal and reset state
      setConfirmModalOpen(false);
      setSelectedSubscription(null);
      setExpiryDate('');
      setAdminNotes('');
    } catch (error) {
      console.error('Error confirming subscription:', error);
      setError('Failed to confirm subscription. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  const rejectSubscription = async () => {
    if (!selectedSubscription) return;

    setActionLoading(true);

    try {
      await adminApi.rejectSubscription(selectedSubscription._id, rejectionReason);

      // Refresh subscriptions
      fetchSubscriptions();

      // Close modal and reset state
      setRejectModalOpen(false);
      setSelectedSubscription(null);
      setRejectionReason('');
    } catch (error) {
      console.error('Error rejecting subscription:', error);
      setError('Failed to reject subscription. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  const handleBatchAction = (action) => {
    if (selectedSubscriptions.length === 0) return;

    setBatchAction(action);
    setBatchActionModalOpen(true);
  };

  const executeBatchAction = async () => {
    if (selectedSubscriptions.length === 0 || !batchAction) return;

    setActionLoading(true);

    try {
      switch (batchAction) {
        case 'confirm':
          await Promise.all(selectedSubscriptions.map(id =>
            adminApi.confirmSubscription(id, {
              expiryDate: calculateExpiryDate(
                allSubscriptions.find(sub => sub._id === id)?.plan || 'yearly'
              ),
              notes: 'Batch confirmed'
            })
          ));
          break;
        case 'reject':
          await Promise.all(selectedSubscriptions.map(id =>
            adminApi.rejectSubscription(id, 'Batch rejected')
          ));
          break;
        default:
          throw new Error('Invalid batch action');
      }

      // Refresh subscriptions
      fetchSubscriptions();

      // Close modal and reset state
      setBatchActionModalOpen(false);
      setSelectedSubscriptions([]);
      setBatchAction('');
    } catch (error) {
      console.error('Error executing batch action:', error);
      setError(`Failed to execute batch action: ${error.message}`);
    } finally {
      setActionLoading(false);
    }
  };

  const calculateExpiryDate = (plan) => {
    if (plan === 'yearly') {
      const oneYearFromNow = new Date();
      oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
      return oneYearFromNow.toISOString().split('T')[0];
    } else {
      // For lifetime, set a far future date (e.g., 100 years)
      const farFuture = new Date();
      farFuture.setFullYear(farFuture.getFullYear() + 100);
      return farFuture.toISOString().split('T')[0];
    }
  };

  const handleSelectSubscription = (subscriptionId) => {
    setSelectedSubscriptions(prev => {
      if (prev.includes(subscriptionId)) {
        return prev.filter(id => id !== subscriptionId);
      } else {
        return [...prev, subscriptionId];
      }
    });
  };

  const handleSelectAllSubscriptions = () => {
    if (selectedSubscriptions.length === filteredSubscriptions.length) {
      setSelectedSubscriptions([]);
    } else {
      setSelectedSubscriptions(filteredSubscriptions.map(sub => sub._id));
    }
  };

  const handleManualUpdate = async () => {
    if (!manualUpdateData.userId) {
      setError('请输入用户ID或邮箱');
      return;
    }

    setActionLoading(true);
    setError('');

    try {
      const response = await adminApi.manualUpdateSubscription(manualUpdateData);
      
      if (response.success) {
        setManualUpdateModalOpen(false);
        setManualUpdateData({
          userId: '',
          plan: 'monthly',
          subscriptionStatus: 'active',
          paymentStatus: 'confirmed',
          paddleSubscriptionId: '',
          expiryDate: '',
          notes: ''
        });
        
        // Refresh data
        fetchSubscriptions();
        
        alert('用户订阅状态更新成功！');
      } else {
        throw new Error(response.message || '更新失败');
      }
    } catch (error) {
      console.error('Manual update error:', error);
      setError(`更新失败: ${error.message}`);
    } finally {
      setActionLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  const getPaymentMethodIcon = (method) => {
    switch (method) {
      case 'paypal':
        return <FaPaypal />;
      case 'usdt':
        return <FaEthereum />;
      default:
        return null;
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'pending':
        return <StatusBadge $status="pending">Pending</StatusBadge>;
      case 'confirmed':
        return <StatusBadge $status="confirmed">Confirmed</StatusBadge>;
      case 'rejected':
        return <StatusBadge $status="rejected">Rejected</StatusBadge>;
      default:
        return <StatusBadge $status="unknown">Unknown</StatusBadge>;
    }
  };

  const renderSubscriptionTable = (subscriptions) => {
    if (subscriptions.length === 0) {
      return <EmptyState>No subscriptions found</EmptyState>;
    }

    return (
      <SubscriptionTable>
        <thead>
          <tr>
            {activeTab === 'pending' && (
              <th>
                <Checkbox
                  type="checkbox"
                  checked={selectedSubscriptions.length === filteredSubscriptions.length && filteredSubscriptions.length > 0}
                  onChange={handleSelectAllSubscriptions}
                />
              </th>
            )}
            <th onClick={() => handleSort('user.name')}>
              User {sortField === 'user.name' && (
                <SortIcon direction={sortDirection}>
                  <FaSort />
                </SortIcon>
              )}
            </th>
            <th onClick={() => handleSort('plan')}>
              Plan {sortField === 'plan' && (
                <SortIcon direction={sortDirection}>
                  <FaSort />
                </SortIcon>
              )}
            </th>
            <th onClick={() => handleSort('paymentMethod')}>
              Payment {sortField === 'paymentMethod' && (
                <SortIcon direction={sortDirection}>
                  <FaSort />
                </SortIcon>
              )}
            </th>
            <th onClick={() => handleSort('paymentAmount')}>
              Amount {sortField === 'paymentAmount' && (
                <SortIcon direction={sortDirection}>
                  <FaSort />
                </SortIcon>
              )}
            </th>
            <th onClick={() => handleSort('paymentStatus')}>
              Status {sortField === 'paymentStatus' && (
                <SortIcon direction={sortDirection}>
                  <FaSort />
                </SortIcon>
              )}
            </th>
            <th onClick={() => handleSort('createdAt')}>
              Date {sortField === 'createdAt' && (
                <SortIcon direction={sortDirection}>
                  <FaSort />
                </SortIcon>
              )}
            </th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {subscriptions.map((subscription) => (
            <tr key={subscription._id}>
              {activeTab === 'pending' && (
                <td>
                  <Checkbox
                    type="checkbox"
                    checked={selectedSubscriptions.includes(subscription._id)}
                    onChange={() => handleSelectSubscription(subscription._id)}
                  />
                </td>
              )}
              <td>
                <UserInfo>
                  <div>{subscription.user?.name || subscription.payerName || 'Unknown'}</div>
                  <UserEmail>{subscription.user?.email || subscription.payerEmail || 'No email'}</UserEmail>
                </UserInfo>
              </td>
              <td>{subscription.plan.charAt(0).toUpperCase() + subscription.plan.slice(1)}</td>
              <td>
                <PaymentMethod>
                  {getPaymentMethodIcon(subscription.paymentMethod)}
                  <span>{subscription.paymentMethod?.toUpperCase()}</span>
                </PaymentMethod>
              </td>
              <td>${subscription.paymentAmount}</td>
              <td>{getStatusBadge(subscription.paymentStatus)}</td>
              <td>{formatDate(subscription.createdAt)}</td>
              <td>
                <ActionButtons>
                  {subscription.paymentStatus === 'pending' && (
                    <>
                      <ActionButton title="Confirm Payment" onClick={() => handleConfirmClick(subscription)}>
                        <FaCheck />
                      </ActionButton>
                      <ActionButton title="Reject Payment" onClick={() => handleRejectClick(subscription)}>
                        <FaTimes />
                      </ActionButton>
                    </>
                  )}
                  <ActionButton title="User Details" onClick={() => handleUserDetailsClick(subscription)}>
                    <FaUser />
                  </ActionButton>
                  <ActionButton title="Payment History" onClick={() => handlePaymentHistoryClick(subscription)}>
                    <FaHistory />
                  </ActionButton>
                </ActionButtons>
              </td>
            </tr>
          ))}
        </tbody>
      </SubscriptionTable>
    );
  };

  return (
    <PageContainer>
      <PageHeader>
        <h1>Enhanced Subscription Management</h1>
        <p>Manage user subscription payments and requests with advanced features</p>
      </PageHeader>

      {error && <ErrorMessage><FaExclamationTriangle /> {error}</ErrorMessage>}

      <TabsContainer>
        <TabButton
          $isActive={activeTab === 'pending'}
          onClick={() => setActiveTab('pending')}
        >
          Pending Requests
        </TabButton>
        <TabButton
          $isActive={activeTab === 'all'}
          onClick={() => setActiveTab('all')}
        >
          All Subscriptions
        </TabButton>
        <TabButton
          $isActive={activeTab === 'confirmed'}
          onClick={() => setActiveTab('confirmed')}
        >
          Confirmed
        </TabButton>
        <TabButton
          $isActive={activeTab === 'rejected'}
          onClick={() => setActiveTab('rejected')}
        >
          Rejected
        </TabButton>
        <TabButton
          $isActive={activeTab === 'manage'}
          onClick={() => setActiveTab('manage')}
        >
          手动管理
        </TabButton>
      </TabsContainer>

      <ControlsContainer>
        <SearchContainer>
          <SearchIcon>
            <FaSearch />
          </SearchIcon>
          <SearchInput
            type="text"
            placeholder="Search by user name or email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </SearchContainer>

        {activeTab === 'pending' && selectedSubscriptions.length > 0 && (
          <BatchActionsContainer>
            <BatchActionButton onClick={() => handleBatchAction('confirm')}>
              <FaCheck /> Confirm Selected ({selectedSubscriptions.length})
            </BatchActionButton>
            <BatchActionButton $danger onClick={() => handleBatchAction('reject')}>
              <FaTimes /> Reject Selected ({selectedSubscriptions.length})
            </BatchActionButton>
          </BatchActionsContainer>
        )}
        
        {activeTab === 'manage' && (
          <ManualManagementContainer>
            <ManualActionButton onClick={() => setManualUpdateModalOpen(true)}>
              <FaUser /> 手动更新用户订阅状态
            </ManualActionButton>
          </ManualManagementContainer>
        )}
      </ControlsContainer>

      <ContentContainer>
        {isLoading ? (
          <LoadingState>
            <FaSpinner className="spinner" />
            <span>Loading subscriptions...</span>
          </LoadingState>
        ) : activeTab === 'manage' ? (
          <ManagementPanel>
            <h3>手动管理用户订阅</h3>
            <p>使用此面板来处理Paddle支付成功但系统状态不一致的情况。</p>
            <ManualActionButton onClick={() => setManualUpdateModalOpen(true)}>
              <FaUser /> 手动更新用户订阅状态
            </ManualActionButton>
          </ManagementPanel>
        ) : (
          renderSubscriptionTable(filteredSubscriptions)
        )}
      </ContentContainer>

      {activeTab !== 'pending' && (
        <Pagination>
          <PaginationButton
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            Previous
          </PaginationButton>
          <PageInfo>
            Page {currentPage} of {totalPages}
          </PageInfo>
          <PaginationButton
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
          >
            Next
          </PaginationButton>
        </Pagination>
      )}

      {/* Confirm Subscription Modal */}
      {confirmModalOpen && selectedSubscription && (
        <ModalOverlay>
          <ModalContent>
            <ModalHeader>
              <h2>Confirm Subscription Payment</h2>
              <CloseButton onClick={() => setConfirmModalOpen(false)}>×</CloseButton>
            </ModalHeader>
            <ModalBody>
              <SubscriptionDetails>
                <DetailItem>
                  <DetailLabel>User:</DetailLabel>
                  <DetailValue>{selectedSubscription.user?.name || selectedSubscription.payerName} ({selectedSubscription.user?.email || selectedSubscription.payerEmail})</DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Plan:</DetailLabel>
                  <DetailValue>{selectedSubscription.plan.charAt(0).toUpperCase() + selectedSubscription.plan.slice(1)}</DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Payment Method:</DetailLabel>
                  <DetailValue>
                    {getPaymentMethodIcon(selectedSubscription.paymentMethod)}
                    <span>{selectedSubscription.paymentMethod?.toUpperCase()}</span>
                  </DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Amount:</DetailLabel>
                  <DetailValue>${selectedSubscription.paymentAmount}</DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Payment Date:</DetailLabel>
                  <DetailValue>{formatDate(selectedSubscription.paymentDate)}</DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Additional Info:</DetailLabel>
                  <DetailValue>{selectedSubscription.additionalInfo || 'None provided'}</DetailValue>
                </DetailItem>
              </SubscriptionDetails>

              <FormGroup>
                <Label>
                  <FaCalendarAlt /> Expiry Date
                </Label>
                <Input
                  type="date"
                  value={expiryDate}
                  onChange={(e) => setExpiryDate(e.target.value)}
                  disabled={selectedSubscription.paymentStatus !== 'pending'}
                />
              </FormGroup>

              <FormGroup>
                <Label>Admin Notes</Label>
                <Textarea
                  value={adminNotes}
                  onChange={(e) => setAdminNotes(e.target.value)}
                  placeholder="Add any notes about this subscription"
                  rows={3}
                  disabled={selectedSubscription.paymentStatus !== 'pending'}
                />
              </FormGroup>

              {selectedSubscription.paymentStatus === 'pending' && (
                <ButtonGroup>
                  <ConfirmButton
                    onClick={confirmSubscription}
                    disabled={actionLoading}
                  >
                    {actionLoading ? <FaSpinner className="spinner" /> : <FaCheck />}
                    Confirm Payment
                  </ConfirmButton>
                  <CancelButton onClick={() => setConfirmModalOpen(false)}>
                    Cancel
                  </CancelButton>
                </ButtonGroup>
              )}
            </ModalBody>
          </ModalContent>
        </ModalOverlay>
      )}

      {/* Manual Update Modal */}
      {manualUpdateModalOpen && (
        <ModalOverlay>
          <ModalContent>
            <ModalHeader>
              <h2>手动更新用户订阅状态</h2>
              <CloseButton onClick={() => setManualUpdateModalOpen(false)}>×</CloseButton>
            </ModalHeader>
            <ModalBody>
              <FormGroup>
                <Label>用户ID或邮箱</Label>
                <Input
                  type="text"
                  value={manualUpdateData.userId}
                  onChange={(e) => setManualUpdateData({...manualUpdateData, userId: e.target.value})}
                  placeholder="输入用户ID或邮箱地址"
                />
              </FormGroup>
              
              <FormGroup>
                <Label>订阅计划</Label>
                <Select
                  value={manualUpdateData.plan}
                  onChange={(e) => setManualUpdateData({...manualUpdateData, plan: e.target.value})}
                >
                  <option value="monthly">月度订阅</option>
                  <option value="yearly">年度订阅</option>
                  <option value="lifetime">终身订阅</option>
                </Select>
              </FormGroup>
              
              <FormGroup>
                <Label>订阅状态</Label>
                <Select
                  value={manualUpdateData.subscriptionStatus}
                  onChange={(e) => setManualUpdateData({...manualUpdateData, subscriptionStatus: e.target.value})}
                >
                  <option value="active">激活</option>
                  <option value="trialing">试用中</option>
                  <option value="cancelled">已取消</option>
                  <option value="expired">已过期</option>
                </Select>
              </FormGroup>
              
              <FormGroup>
                <Label>支付状态</Label>
                <Select
                  value={manualUpdateData.paymentStatus}
                  onChange={(e) => setManualUpdateData({...manualUpdateData, paymentStatus: e.target.value})}
                >
                  <option value="confirmed">已确认</option>
                  <option value="trial">试用中</option>
                  <option value="failed">失败</option>
                  <option value="cancelled">已取消</option>
                </Select>
              </FormGroup>
              
              <FormGroup>
                <Label>Paddle订阅ID（可选）</Label>
                <Input
                  type="text"
                  value={manualUpdateData.paddleSubscriptionId}
                  onChange={(e) => setManualUpdateData({...manualUpdateData, paddleSubscriptionId: e.target.value})}
                  placeholder="Paddle订阅ID"
                />
              </FormGroup>
              
              <FormGroup>
                <Label>过期日期</Label>
                <Input
                  type="date"
                  value={manualUpdateData.expiryDate}
                  onChange={(e) => setManualUpdateData({...manualUpdateData, expiryDate: e.target.value})}
                />
              </FormGroup>
              
              <FormGroup>
                <Label>管理员备注</Label>
                <Textarea
                  value={manualUpdateData.notes}
                  onChange={(e) => setManualUpdateData({...manualUpdateData, notes: e.target.value})}
                  placeholder="记录更新原因和相关信息"
                  rows={3}
                />
              </FormGroup>
              
              <ButtonGroup>
                <ConfirmButton
                  onClick={handleManualUpdate}
                  disabled={actionLoading || !manualUpdateData.userId}
                >
                  {actionLoading ? <FaSpinner className="spinner" /> : <FaCheck />}
                  更新订阅状态
                </ConfirmButton>
                <CancelButton onClick={() => setManualUpdateModalOpen(false)}>
                  取消
                </CancelButton>
              </ButtonGroup>
            </ModalBody>
          </ModalContent>
        </ModalOverlay>
      )}
    </PageContainer>
  );
};

// Styled Components
const PageContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
`;

const PageHeader = styled.div`
  margin-bottom: 2rem;

  h1 {
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
    color: ${props => props.theme['--text-color'] || '#333'};
  }

  p {
    color: ${props => props.theme['--text-secondary'] || '#666'};
  }
`;

const TabsContainer = styled.div`
  display: flex;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
`;

const TabButton = styled.button`
  padding: 0.75rem 1.25rem;
  background: none;
  border: none;
  border-bottom: 2px solid ${props => props.$isActive ? props.theme['--primary-color'] || '#d95550' : 'transparent'};
  color: ${props => props.$isActive ? props.theme['--primary-color'] || '#d95550' : props.theme['--text-secondary'] || '#666'};
  font-weight: ${props => props.$isActive ? '600' : '400'};
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    color: ${props => props.theme['--primary-color'] || '#d95550'};
  }
`;

const ControlsContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
`;

const SearchContainer = styled.div`
  position: relative;
  flex: 1;
  min-width: 250px;
`;

const SearchIcon = styled.div`
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: ${props => props.theme['--text-secondary'] || '#666'};
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: ${props => props.theme['--primary-color'] || '#d95550'};
  }
`;

const BatchActionsContainer = styled.div`
  display: flex;
  gap: 1rem;
`;

const BatchActionButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 600;
  background-color: ${props => props.$danger ? '#F44336' : '#4CAF50'};
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${props => props.$danger ? '#D32F2F' : '#388E3C'};
  }
`;

const ContentContainer = styled.div`
  background-color: ${props => props.theme['--card-bg'] || '#fff'};
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
`;

const SubscriptionTable = styled.table`
  width: 100%;
  border-collapse: collapse;

  th, td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #eee;
  }

  th {
    font-weight: 600;
    color: ${props => props.theme['--text-color'] || '#333'};
    background-color: ${props => props.theme['--bg-color'] || '#f5f5f5'};
    cursor: pointer;
    user-select: none;
    position: relative;
  }

  tbody tr:hover {
    background-color: ${props => props.theme['--bg-color'] || '#f5f5f5'};
  }
`;

const SortIcon = styled.span`
  display: inline-block;
  margin-left: 0.5rem;
  transform: ${props => props.direction === 'asc' ? 'rotate(0deg)' : 'rotate(180deg)'};
  transition: transform 0.2s;
`;

const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const UserEmail = styled.span`
  font-size: 0.8rem;
  color: ${props => props.theme['--text-secondary'] || '#666'};
`;

const PaymentMethod = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const StatusBadge = styled.span`
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;

  ${props => {
    switch (props.$status) {
      case 'confirmed':
        return `
          background-color: rgba(76, 175, 80, 0.1);
          color: #4CAF50;
        `;
      case 'pending':
        return `
          background-color: rgba(255, 152, 0, 0.1);
          color: #FF9800;
        `;
      case 'rejected':
        return `
          background-color: rgba(244, 67, 54, 0.1);
          color: #F44336;
        `;
      default:
        return `
          background-color: rgba(158, 158, 158, 0.1);
          color: #9E9E9E;
        `;
    }
  }}
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background: none;
  border: 1px solid #ddd;
  color: ${props => props.theme['--text-secondary'] || '#666'};
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${props => props.theme['--bg-color'] || '#f5f5f5'};
    color: ${props => props.theme['--primary-color'] || '#d95550'};
  }
`;

const EmptyState = styled.div`
  padding: 3rem;
  text-align: center;
  color: ${props => props.theme['--text-secondary'] || '#666'};
`;

const LoadingState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: ${props => props.theme['--text-secondary'] || '#666'};

  .spinner {
    font-size: 2rem;
    margin-bottom: 1rem;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ErrorMessage = styled.div`
  background-color: rgba(244, 67, 54, 0.1);
  color: #F44336;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: ${props => props.theme['--card-bg'] || '#fff'};
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #eee;

  h2 {
    margin: 0;
    font-size: 1.25rem;
    color: ${props => props.theme['--text-color'] || '#333'};
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: ${props => props.theme['--text-secondary'] || '#666'};

  &:hover {
    color: ${props => props.theme['--text-color'] || '#333'};
  }
`;

const ModalBody = styled.div`
  padding: 1.5rem;
`;

const SubscriptionDetails = styled.div`
  background-color: ${props => props.theme['--bg-color'] || '#f5f5f5'};
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1.5rem;
`;

const DetailItem = styled.div`
  display: flex;
  margin-bottom: 0.5rem;

  &:last-child {
    margin-bottom: 0;
  }
`;

const DetailLabel = styled.span`
  font-weight: 600;
  width: 150px;
  color: ${props => props.theme['--text-color'] || '#333'};
`;

const DetailValue = styled.span`
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: ${props => props.theme['--text-secondary'] || '#666'};
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`;

const Label = styled.label`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: ${props => props.theme['--text-color'] || '#333'};
`;

const Input = styled.input`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: ${props => props.theme['--primary-color'] || '#d95550'};
  }

  &:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
  }
`;

const Textarea = styled.textarea`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: ${props => props.theme['--primary-color'] || '#d95550'};
  }

  &:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .spinner {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ConfirmButton = styled(Button)`
  background-color: #4CAF50;
  color: white;
  border: none;

  &:hover:not(:disabled) {
    background-color: #388E3C;
  }
`;

const CancelButton = styled(Button)`
  background-color: transparent;
  color: ${props => props.theme['--text-secondary'] || '#666'};
  border: 1px solid #ddd;

  &:hover:not(:disabled) {
    background-color: #f5f5f5;
  }
`;

const Checkbox = styled.input`
  cursor: pointer;
  width: 16px;
  height: 16px;
`;

const Pagination = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 1.5rem;
  gap: 1rem;
`;

const PaginationButton = styled.button`
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s;

  &:hover:not(:disabled) {
    background-color: ${props => props.theme['--bg-color'] || '#f5f5f5'};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const PageInfo = styled.div`
  color: ${props => props.theme['--text-secondary'] || '#666'};
`;

const ManualManagementContainer = styled.div`
  display: flex;
  gap: 1rem;
`;

const ManualActionButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 600;
  background-color: ${props => props.theme['--primary-color'] || '#d95550'};
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${props => props.theme['--primary-color-dark'] || '#c74440'};
  }
`;

const ManagementPanel = styled.div`
  padding: 2rem;
  text-align: center;
  
  h3 {
    color: ${props => props.theme['--text-color'] || '#333'};
    margin-bottom: 1rem;
  }
  
  p {
    color: ${props => props.theme['--text-secondary'] || '#666'};
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;

  &:focus {
    outline: none;
    border-color: ${props => props.theme['--primary-color'] || '#d95550'};
  }
`;

export default AdminEnhancedSubscriptionPage;
