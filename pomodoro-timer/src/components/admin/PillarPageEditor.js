import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { FaSave, FaArrowLeft, FaPlus, FaTimes, FaGripVertical } from 'react-icons/fa';
import { pillarPageApi } from '../../services/pillarPageApi';
import { blogApi } from '../../services/blogApi';
import EnhancedTextEditor from './EnhancedTextEditor';

const PillarPageEditor = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEditing = Boolean(id);

  const [formData, setFormData] = useState({
    title: '',
    content: '',
    excerpt: '',
    coverImage: '',
    category: 'pomodoro-technique',
    readTime: 5,
    published: false,
    featured: false,
    seoTitle: '',
    seoDescription: '',
    seoKeywords: '',
    clusterPages: [],
    menuOrder: 0
  });

  const [availableBlogPosts, setAvailableBlogPosts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [editingCluster, setEditingCluster] = useState(null);

  const categoryOptions = [
    { value: 'pomodoro-technique', label: 'The Pomodoro Technique' },
    { value: 'time-management', label: 'Time Management' },
    { value: 'adhd-productivity', label: 'ADHD & Productivity' },
    { value: 'ai-productivity', label: 'AI & Productivity' }
  ];

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch available blog posts for cluster association
        try {
          // Try the admin endpoint first
          const blogPosts = await blogApi.getAllBlogPosts();
          console.log('Loaded blog posts (admin):', blogPosts);
          setAvailableBlogPosts(blogPosts);
        } catch (adminError) {
          console.log('Admin endpoint failed, trying public endpoint:', adminError);
          // Fallback to public endpoint
          const blogData = await blogApi.getBlogPosts(1, 100); // Get up to 100 posts
          console.log('Loaded blog posts (public):', blogData);
          setAvailableBlogPosts(blogData.posts);
        }

        // If editing, fetch the pillar page data
        if (isEditing) {
          const pillarPage = await pillarPageApi.getPillarPageById(id);
          console.log('Loaded pillar page:', pillarPage);
          console.log('Cluster pages:', pillarPage.clusterPages);

          // Clean up cluster pages - remove any without blogPost
          const validClusterPages = (pillarPage.clusterPages || []).filter(cluster =>
            cluster.blogPost && (cluster.blogPost._id || cluster.blogPost)
          );
          console.log('Valid cluster pages:', validClusterPages);

          setFormData({
            title: pillarPage.title || '',
            content: pillarPage.content || '',
            excerpt: pillarPage.excerpt || '',
            coverImage: pillarPage.coverImage || '',
            category: pillarPage.category || 'pomodoro-technique',
            readTime: pillarPage.readTime || 5,
            published: pillarPage.published || false,
            featured: pillarPage.featured || false,
            seoTitle: pillarPage.seoTitle || '',
            seoDescription: pillarPage.seoDescription || '',
            seoKeywords: pillarPage.seoKeywords || '',
            clusterPages: validClusterPages,
            menuOrder: pillarPage.menuOrder || 0
          });
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id, isEditing]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleContentChange = (e) => {
    const newValue = typeof e === 'string' ? e : e.target.value;
    setFormData(prev => ({
      ...prev,
      content: newValue
    }));
  };

  const handleAddClusterPage = () => {
    const newIndex = formData.clusterPages.length;
    setFormData(prev => ({
      ...prev,
      clusterPages: [
        ...prev.clusterPages,
        {
          blogPost: '',
          displayOrder: newIndex,
          customTitle: '',
          customDescription: ''
        }
      ]
    }));
    setEditingCluster(newIndex);
  };

  const handleRemoveClusterPage = (index) => {
    setFormData(prev => ({
      ...prev,
      clusterPages: prev.clusterPages.filter((_, i) => i !== index)
    }));
  };

  const moveClusterPage = (fromIndex, toIndex) => {
    setFormData(prev => {
      const newClusterPages = [...prev.clusterPages];
      const [movedItem] = newClusterPages.splice(fromIndex, 1);
      newClusterPages.splice(toIndex, 0, movedItem);

      // Update display order
      const updatedClusterPages = newClusterPages.map((cluster, index) => ({
        ...cluster,
        displayOrder: index
      }));

      return {
        ...prev,
        clusterPages: updatedClusterPages
      };
    });
  };

  const handleClusterPageChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      clusterPages: prev.clusterPages.map((cluster, i) =>
        i === index ? { ...cluster, [field]: value } : cluster
      )
    }));
  };

  const renderClusterEditForm = () => {
    const index = editingCluster;
    if (index === null || !formData.clusterPages[index]) return null;

    const cluster = formData.clusterPages[index];
    // Handle both cases: blogPost as ID or as populated object
    const selectedPost = cluster.blogPost && cluster.blogPost._id
      ? cluster.blogPost // Already populated
      : availableBlogPosts.find(post => post._id === cluster.blogPost);

    // Filter out already selected blog posts (except the current one being edited)
    const getSelectedBlogPostIds = () => {
      return formData.clusterPages
        .map((cp, i) => {
          if (i === index) return null; // Don't exclude the current one being edited
          return cp.blogPost && cp.blogPost._id ? cp.blogPost._id : cp.blogPost;
        })
        .filter(Boolean);
    };

    const selectedIds = getSelectedBlogPostIds();
    const availableForSelection = availableBlogPosts.filter(post =>
      !selectedIds.includes(post._id)
    );

    return (
      <ClusterEditContent>
        <FormRow>
          <FormGroup>
            <Label>Select Blog Post *</Label>
            <Select
              value={cluster.blogPost && cluster.blogPost._id ? cluster.blogPost._id : cluster.blogPost}
              onChange={(e) => handleClusterPageChange(index, 'blogPost', e.target.value)}
              required
            >
              <option value="">Choose an article...</option>
              {/* Show currently selected post even if it would be filtered out */}
              {selectedPost && selectedIds.includes(selectedPost._id) && (
                <option key={selectedPost._id} value={selectedPost._id}>
                  {selectedPost.title} ({selectedPost.category}) - Currently Selected
                </option>
              )}
              {availableForSelection.map(post => (
                <option key={post._id} value={post._id}>
                  {post.title} ({post.category})
                </option>
              ))}
              {availableForSelection.length === 0 && !selectedPost && (
                <option value="" disabled>No more articles available</option>
              )}
            </Select>
          </FormGroup>
        </FormRow>

        {selectedPost && (
          <ArticlePreview>
            <PreviewImage src={selectedPost.coverImage} alt={selectedPost.title} />
            <PreviewContent>
              <PreviewTitle>{selectedPost.title}</PreviewTitle>
              <PreviewMeta>
                Category: {selectedPost.category} • {selectedPost.readTime} min read
              </PreviewMeta>
              <PreviewExcerpt>{selectedPost.excerpt}</PreviewExcerpt>
            </PreviewContent>
          </ArticlePreview>
        )}

        <FormRow>
          <FormGroup>
            <Label>Custom Title (optional)</Label>
            <Input
              type="text"
              value={cluster.customTitle}
              onChange={(e) => handleClusterPageChange(index, 'customTitle', e.target.value)}
              placeholder="Override the article title for this context"
            />
          </FormGroup>
        </FormRow>

        <FormGroup>
          <Label>Custom Description (optional)</Label>
          <Textarea
            value={cluster.customDescription}
            onChange={(e) => handleClusterPageChange(index, 'customDescription', e.target.value)}
            rows={3}
            placeholder="Override the article excerpt for this context"
          />
        </FormGroup>

        <ClusterEditActions>
          <SaveClusterButton
            type="button"
            onClick={() => setEditingCluster(null)}
            disabled={!(cluster.blogPost && (cluster.blogPost._id || cluster.blogPost))}
          >
            Done
          </SaveClusterButton>
          <CancelClusterButton
            type="button"
            onClick={() => {
              if (!(cluster.blogPost && (cluster.blogPost._id || cluster.blogPost))) {
                handleRemoveClusterPage(index);
              }
              setEditingCluster(null);
            }}
          >
            Cancel
          </CancelClusterButton>
        </ClusterEditActions>
      </ClusterEditContent>
    );
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.title || !formData.content || !formData.excerpt) {
      setError('Please fill in all required fields');
      return;
    }

    try {
      setSaving(true);
      setError(null);

      // Filter out cluster pages without blog post selection
      const validClusterPages = formData.clusterPages.filter(cluster =>
        cluster.blogPost && (cluster.blogPost._id || cluster.blogPost)
      );

      const submitData = {
        ...formData,
        clusterPages: validClusterPages
      };

      if (isEditing) {
        await pillarPageApi.updatePillarPage(id, submitData);
      } else {
        await pillarPageApi.createPillarPage(submitData);
      }

      navigate('/app', { state: { activeTab: 'admin', adminSection: 'pillarPages' } });
    } catch (err) {
      console.error('Error saving pillar page:', err);
      setError('Failed to save pillar page');
    } finally {
      setSaving(false);
    }
  };



  if (loading) {
    return (
      <LoadingContainer>
        <LoadingSpinner />
        <p>Loading...</p>
      </LoadingContainer>
    );
  }

  return (
    <EditorContainer>
      <EditorHeader>
        <BackButton onClick={() => navigate('/app', { state: { activeTab: 'admin', adminSection: 'pillarPages' } })}>
          <FaArrowLeft />
          Back to Pillar Pages
        </BackButton>
        <EditorTitle>
          {isEditing ? 'Edit Pillar Page' : 'Create New Pillar Page'}
        </EditorTitle>
      </EditorHeader>

      {error && <ErrorMessage>{error}</ErrorMessage>}

      <EditorForm onSubmit={handleSubmit}>
        <FormSection>
          <SectionTitle>Basic Information</SectionTitle>
          
          <FormRow>
            <FormGroup>
              <Label htmlFor="title">Title *</Label>
              <Input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                required
              />
            </FormGroup>

            <FormGroup>
              <Label htmlFor="category">Category *</Label>
              <Select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                required
              >
                {categoryOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </Select>
            </FormGroup>
          </FormRow>

          <FormGroup>
            <Label htmlFor="excerpt">Excerpt *</Label>
            <Textarea
              id="excerpt"
              name="excerpt"
              value={formData.excerpt}
              onChange={handleInputChange}
              rows={3}
              required
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="coverImage">Cover Image URL *</Label>
            <Input
              type="url"
              id="coverImage"
              name="coverImage"
              value={formData.coverImage}
              onChange={handleInputChange}
              required
            />
          </FormGroup>

          <FormRow>
            <FormGroup>
              <Label htmlFor="readTime">Read Time (minutes)</Label>
              <Input
                type="number"
                id="readTime"
                name="readTime"
                value={formData.readTime}
                onChange={handleInputChange}
                min="1"
                max="120"
              />
            </FormGroup>

            <FormGroup>
              <Label htmlFor="menuOrder">Menu Order</Label>
              <Input
                type="number"
                id="menuOrder"
                name="menuOrder"
                value={formData.menuOrder}
                onChange={handleInputChange}
                min="0"
              />
            </FormGroup>
          </FormRow>

          <FormRow>
            <CheckboxGroup>
              <Checkbox
                type="checkbox"
                id="published"
                name="published"
                checked={formData.published}
                onChange={handleInputChange}
              />
              <Label htmlFor="published">Published</Label>
            </CheckboxGroup>

            <CheckboxGroup>
              <Checkbox
                type="checkbox"
                id="featured"
                name="featured"
                checked={formData.featured}
                onChange={handleInputChange}
              />
              <Label htmlFor="featured">Featured</Label>
            </CheckboxGroup>
          </FormRow>
        </FormSection>

        <FormSection>
          <SectionTitle>Content</SectionTitle>
          <FormGroup>
            <Label htmlFor="content">Content *</Label>
            <EnhancedTextEditor
              value={formData.content}
              onChange={(e) => {
                const newValue = typeof e === 'string' ? e : e.target.value;
                setFormData(prev => ({
                  ...prev,
                  content: newValue
                }));
              }}
              placeholder="Enter your pillar page content here. Use the toolbar buttons to add formatting, or write HTML directly."
              rows={25}
            />
          </FormGroup>
        </FormSection>

        <FormSection>
          <SectionTitle>SEO Settings</SectionTitle>
          
          <FormGroup>
            <Label htmlFor="seoTitle">SEO Title</Label>
            <Input
              type="text"
              id="seoTitle"
              name="seoTitle"
              value={formData.seoTitle}
              onChange={handleInputChange}
              placeholder="Leave empty to use title"
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="seoDescription">SEO Description</Label>
            <Textarea
              id="seoDescription"
              name="seoDescription"
              value={formData.seoDescription}
              onChange={handleInputChange}
              rows={2}
              placeholder="Leave empty to use excerpt"
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="seoKeywords">SEO Keywords</Label>
            <Input
              type="text"
              id="seoKeywords"
              name="seoKeywords"
              value={formData.seoKeywords}
              onChange={handleInputChange}
              placeholder="Comma-separated keywords"
            />
          </FormGroup>
        </FormSection>

        <FormSection>
          <SectionTitle>Related Articles (Cluster Pages)</SectionTitle>

          <ClusterDescription>
            Associate existing blog posts with this pillar page. These articles will appear in the "Dive Deeper" section to help readers explore related topics.
          </ClusterDescription>

          {/* Current Cluster Pages List */}
          {formData.clusterPages.length > 0 && (
            <CurrentClusterSection>
              <CurrentClusterHeader>
                <CurrentClusterTitle>
                  Related Articles ({formData.clusterPages.length}) - NEW TABLE VIEW
                </CurrentClusterTitle>
                <ClusterHeaderActions>
                  <BulkActionsText>
                    {formData.clusterPages.length} article{formData.clusterPages.length !== 1 ? 's' : ''} associated
                  </BulkActionsText>
                  <AddButton type="button" onClick={handleAddClusterPage}>
                    <FaPlus />
                    Add Article
                  </AddButton>
                </ClusterHeaderActions>
              </CurrentClusterHeader>

              <ClusterTable>
                <ClusterTableHeader>
                  <ClusterTableRow $isHeader>
                    <ClusterTableCell $width="60px">#</ClusterTableCell>
                    <ClusterTableCell $width="80px">Image</ClusterTableCell>
                    <ClusterTableCell>Article Details</ClusterTableCell>
                    <ClusterTableCell $width="120px">Category</ClusterTableCell>
                    <ClusterTableCell $width="80px">Read Time</ClusterTableCell>
                    <ClusterTableCell $width="100px">Status</ClusterTableCell>
                    <ClusterTableCell $width="140px">Actions</ClusterTableCell>
                  </ClusterTableRow>
                </ClusterTableHeader>

                <ClusterTableBody>
                  {formData.clusterPages.map((cluster, index) => {
                    // Handle both cases: blogPost as ID or as populated object
                    const selectedPost = cluster.blogPost && cluster.blogPost._id
                      ? cluster.blogPost // Already populated
                      : availableBlogPosts.find(post => post._id === cluster.blogPost);

                    return (
                      <ClusterTableRow key={index} $isEven={index % 2 === 0}>
                        <ClusterTableCell>
                          <OrderBadge>{index + 1}</OrderBadge>
                        </ClusterTableCell>

                        <ClusterTableCell>
                          {selectedPost && (
                            <ArticleThumbnail
                              src={selectedPost.coverImage}
                              alt={selectedPost.title}
                            />
                          )}
                        </ClusterTableCell>

                        <ClusterTableCell>
                          {selectedPost ? (
                            <ArticleInfo>
                              <ArticleTitle>
                                {cluster.customTitle || selectedPost.title}
                                {cluster.customTitle && <CustomBadge>Custom Title</CustomBadge>}
                              </ArticleTitle>
                              <ArticleExcerpt>
                                {cluster.customDescription || selectedPost.excerpt}
                                {cluster.customDescription && <CustomBadge>Custom Description</CustomBadge>}
                              </ArticleExcerpt>
                            </ArticleInfo>
                          ) : (
                            <ErrorState>⚠️ Article not found</ErrorState>
                          )}
                        </ClusterTableCell>

                        <ClusterTableCell>
                          {selectedPost && (
                            <CategoryBadge $category={selectedPost.category}>
                              {selectedPost.category}
                            </CategoryBadge>
                          )}
                        </ClusterTableCell>

                        <ClusterTableCell>
                          {selectedPost && (
                            <ReadTimeBadge>{selectedPost.readTime} min</ReadTimeBadge>
                          )}
                        </ClusterTableCell>

                        <ClusterTableCell>
                          <StatusIndicator $hasCustomizations={!!(cluster.customTitle || cluster.customDescription)}>
                            {cluster.customTitle || cluster.customDescription ? 'Customized' : 'Default'}
                          </StatusIndicator>
                        </ClusterTableCell>

                        <ClusterTableCell>
                          <TableActions>
                            <TableActionButton
                              type="button"
                              onClick={() => setEditingCluster(index)}
                              title="Edit article"
                              $variant="edit"
                            >
                              ✏️
                            </TableActionButton>

                            {index > 0 && (
                              <TableActionButton
                                type="button"
                                onClick={() => moveClusterPage(index, index - 1)}
                                title="Move up"
                                $variant="move"
                              >
                                ↑
                              </TableActionButton>
                            )}

                            {index < formData.clusterPages.length - 1 && (
                              <TableActionButton
                                type="button"
                                onClick={() => moveClusterPage(index, index + 1)}
                                title="Move down"
                                $variant="move"
                              >
                                ↓
                              </TableActionButton>
                            )}

                            <TableActionButton
                              type="button"
                              onClick={() => handleRemoveClusterPage(index)}
                              title="Remove article"
                              $variant="delete"
                            >
                              🗑️
                            </TableActionButton>
                          </TableActions>
                        </ClusterTableCell>
                      </ClusterTableRow>
                    );
                  })}
                </ClusterTableBody>
              </ClusterTable>
            </CurrentClusterSection>
          )}

          {/* Empty State */}
          {formData.clusterPages.length === 0 && (
            <EmptyClusterState>
              <EmptyIcon>📚</EmptyIcon>
              <EmptyTitle>No related articles yet</EmptyTitle>
              <EmptyDescription>
                Add related blog posts to create a comprehensive content cluster around this pillar page topic.
              </EmptyDescription>
              <AddButton type="button" onClick={handleAddClusterPage}>
                <FaPlus />
                Add Your First Article
              </AddButton>
            </EmptyClusterState>
          )}

          {/* Edit/Add Form */}
          {(editingCluster !== null || formData.clusterPages.length === 0) && editingCluster !== -1 && (
            <ClusterEditForm>
              <ClusterEditHeader>
                <ClusterEditTitle>
                  {editingCluster !== null ? `Edit Article #${editingCluster + 1}` : 'Add New Related Article'}
                </ClusterEditTitle>
                {editingCluster !== null && (
                  <CloseEditButton type="button" onClick={() => setEditingCluster(null)}>
                    <FaTimes />
                  </CloseEditButton>
                )}
              </ClusterEditHeader>

              {renderClusterEditForm()}
            </ClusterEditForm>
          )}
        </FormSection>

        <FormActions>
          <SaveButton type="submit" disabled={saving}>
            <FaSave />
            {saving ? 'Saving...' : 'Save Pillar Page'}
          </SaveButton>
        </FormActions>
      </EditorForm>
    </EditorContainer>
  );
};

// Styled Components
const EditorContainer = styled.div`
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
`;

const EditorHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
`;

const BackButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f3f4f6;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  color: #374151;
  text-decoration: none;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background: #e5e7eb;
  }
`;

const EditorTitle = styled.h1`
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: #6b7280;
`;

const LoadingSpinner = styled.div`
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #d95550;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ErrorMessage = styled.div`
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 2rem;
`;

const EditorForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const FormSection = styled.div`
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  padding: 2rem;
`;

const SectionTitle = styled.h2`
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`;

const Label = styled.label`
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
`;

const Input = styled.input`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #d95550;
    box-shadow: 0 0 0 3px rgba(217, 85, 80, 0.1);
  }
`;

const Textarea = styled.textarea`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  resize: vertical;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #d95550;
    box-shadow: 0 0 0 3px rgba(217, 85, 80, 0.1);
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  background: white;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #d95550;
    box-shadow: 0 0 0 3px rgba(217, 85, 80, 0.1);
  }
`;

const CheckboxGroup = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const Checkbox = styled.input`
  width: 1rem;
  height: 1rem;
  accent-color: #d95550;
`;



const AddButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #d95550;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background: #c73e39;
  }
`;

const ClusterPageItem = styled.div`
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1rem;
  background: #f9fafb;
`;

const ClusterPageHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: #6b7280;
  font-weight: 500;

  svg:first-child {
    cursor: grab;
  }

  span {
    flex: 1;
  }
`;

const RemoveButton = styled.button`
  background: #ef4444;
  color: white;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background: #dc2626;
  }
`;

const FormActions = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 2rem 0;
`;

const SaveButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #d95550;
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover:not(:disabled) {
    background: #c73e39;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

// Enhanced Cluster Page Styled Components
const ClusterDescription = styled.p`
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
  line-height: 1.5;
`;

const EmptyClusterState = styled.div`
  text-align: center;
  padding: 3rem 2rem;
  border: 2px dashed #d1d5db;
  border-radius: 0.75rem;
  background: #f9fafb;
`;

const EmptyIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const EmptyTitle = styled.h3`
  color: #374151;
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
`;

const EmptyDescription = styled.p`
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 2rem;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
`;

const ClusterList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const ClusterOrder = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: #d95550;
  color: white;
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
`;

const ClusterTitle = styled.h4`
  flex: 1;
  margin: 0;
  color: #374151;
  font-size: 1rem;
  font-weight: 500;
  padding: 0 1rem;
`;

const ClusterActions = styled.div`
  display: flex;
  align-items: center;
  gap: 0.25rem;
`;

const MoveButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem;
  height: 1.75rem;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  color: #6b7280;
  cursor: pointer;
  font-size: 0.75rem;
  font-weight: 600;
  transition: all 0.2s;

  &:hover {
    background: #e5e7eb;
    color: #374151;
  }
`;

const ClusterContent = styled.div`
  padding: 1rem;
  background: #f9fafb;
  border-radius: 0 0 0.5rem 0.5rem;
`;

const ArticlePreview = styled.div`
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const PreviewImage = styled.img`
  width: 120px;
  height: 80px;
  object-fit: cover;
  border-radius: 0.375rem;
  flex-shrink: 0;

  @media (max-width: 768px) {
    width: 100%;
    height: 150px;
  }
`;

const PreviewContent = styled.div`
  flex: 1;
`;

const PreviewTitle = styled.h5`
  margin: 0 0 0.5rem 0;
  color: #111827;
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1.3;
`;

const PreviewMeta = styled.div`
  color: #6b7280;
  font-size: 0.75rem;
  margin-bottom: 0.5rem;
`;

const PreviewExcerpt = styled.p`
  margin: 0;
  color: #6b7280;
  font-size: 0.75rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

// Enhanced Cluster List Styled Components
const CurrentClusterSection = styled.div`
  margin-bottom: 2rem;
`;

const CurrentClusterHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #e5e7eb;
`;

const CurrentClusterTitle = styled.h3`
  margin: 0;
  color: #111827;
  font-size: 1.125rem;
  font-weight: 600;
`;

const ClusterHeaderActions = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const BulkActionsText = styled.span`
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
`;

// Table Components
const ClusterTable = styled.div`
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const ClusterTableHeader = styled.div`
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
`;

const ClusterTableBody = styled.div``;

const ClusterTableRow = styled.div`
  display: grid;
  grid-template-columns: 60px 80px 1fr 120px 80px 100px 140px;
  align-items: center;
  min-height: 60px;
  padding: 0.75rem 1rem;

  ${props => props.$isHeader && `
    background: #f9fafb;
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    min-height: 48px;
    border-bottom: 1px solid #e5e7eb;
  `}

  ${props => !props.$isHeader && props.$isEven && `
    background: #f9fafb;
  `}

  ${props => !props.$isHeader && `
    border-bottom: 1px solid #f3f4f6;
    transition: background-color 0.2s;

    &:hover {
      background: #f0f9ff;
    }

    &:last-child {
      border-bottom: none;
    }
  `}

  @media (max-width: 1200px) {
    grid-template-columns: 50px 70px 1fr 100px 70px 90px 120px;
    font-size: 0.875rem;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    padding: 1rem;

    ${props => !props.$isHeader && `
      border: 1px solid #e5e7eb;
      border-radius: 0.5rem;
      margin-bottom: 0.75rem;
      background: white !important;

      &:hover {
        background: #f9fafb !important;
      }
    `}
  }
`;

const ClusterTableCell = styled.div`
  display: flex;
  align-items: center;
  padding: 0.25rem 0.5rem;

  ${props => props.$width && `
    width: ${props.$width};
    flex-shrink: 0;
  `}

  @media (max-width: 768px) {
    width: 100% !important;
    justify-content: flex-start;
    padding: 0.25rem 0;

    &:before {
      content: attr(data-label);
      font-weight: 600;
      color: #6b7280;
      min-width: 100px;
      margin-right: 1rem;
      font-size: 0.75rem;
    }
  }
`;

// Badge and Status Components
const OrderBadge = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: #d95550;
  color: white;
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: 600;
`;

const ArticleThumbnail = styled.img`
  width: 50px;
  height: 35px;
  object-fit: cover;
  border-radius: 0.375rem;
  border: 1px solid #e5e7eb;
`;

const ArticleInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const ArticleTitle = styled.div`
  font-weight: 600;
  color: #111827;
  font-size: 0.875rem;
  line-height: 1.3;
  margin-bottom: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
`;

const ArticleExcerpt = styled.div`
  color: #6b7280;
  font-size: 0.75rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
`;

const CustomBadge = styled.span`
  display: inline-flex;
  align-items: center;
  padding: 0.125rem 0.375rem;
  background: #dbeafe;
  color: #1e40af;
  border-radius: 0.25rem;
  font-size: 0.625rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;

const CategoryBadge = styled.div`
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;

  ${props => {
    const colors = {
      'pomodoro-technique': 'background: #fef3c7; color: #92400e;',
      'time-management': 'background: #dbeafe; color: #1e40af;',
      'adhd-productivity': 'background: #fce7f3; color: #be185d;',
      'ai-productivity': 'background: #d1fae5; color: #065f46;',
      'ai-tools': 'background: #e0e7ff; color: #3730a3;',
      'productivity': 'background: #f3e8ff; color: #6b21a8;'
    };
    return colors[props.$category] || 'background: #f3f4f6; color: #374151;';
  }}
`;

const ReadTimeBadge = styled.div`
  padding: 0.25rem 0.5rem;
  background: #f3f4f6;
  color: #374151;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;
`;

const StatusIndicator = styled.div`
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;

  ${props => props.$hasCustomizations
    ? 'background: #fef3c7; color: #92400e;'
    : 'background: #f3f4f6; color: #6b7280;'
  }
`;

const ErrorState = styled.div`
  color: #dc2626;
  font-size: 0.875rem;
  font-weight: 500;
`;

// Action Components
const TableActions = styled.div`
  display: flex;
  align-items: center;
  gap: 0.25rem;
  justify-content: flex-end;
`;

const TableActionButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem;
  height: 1.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 0.75rem;
  transition: all 0.2s;

  ${props => {
    switch (props.$variant) {
      case 'edit':
        return `
          background: #f0f9ff;
          color: #0369a1;
          &:hover { background: #e0f2fe; border-color: #0369a1; }
        `;
      case 'move':
        return `
          background: #f9fafb;
          color: #6b7280;
          font-weight: 600;
          &:hover { background: #f3f4f6; border-color: #9ca3af; }
        `;
      case 'delete':
        return `
          background: #fef2f2;
          color: #dc2626;
          &:hover { background: #fee2e2; border-color: #dc2626; }
        `;
      default:
        return `
          background: white;
          color: #6b7280;
          &:hover { background: #f9fafb; }
        `;
    }
  }}
`;

const ClusterListContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const ClusterListItem = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  transition: all 0.2s;

  &:hover {
    border-color: #d1d5db;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }
`;

const ClusterItemContent = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
`;

const ClusterItemOrder = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: #d95550;
  color: white;
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
`;

const ClusterItemPreview = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
`;

const ClusterItemImage = styled.img`
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 0.25rem;
  flex-shrink: 0;
`;

const ClusterItemDetails = styled.div`
  flex: 1;
  min-width: 0;
`;

const ClusterItemTitle = styled.h4`
  margin: 0 0 0.25rem 0;
  color: #111827;
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const ClusterItemMeta = styled.div`
  color: #6b7280;
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
`;

const ClusterItemExcerpt = styled.p`
  margin: 0;
  color: #6b7280;
  font-size: 0.75rem;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const ClusterItemPlaceholder = styled.div`
  flex: 1;
  padding: 1rem;
  text-align: center;
  color: #ef4444;
  font-size: 0.875rem;
  background: #fef2f2;
  border: 1px dashed #fecaca;
  border-radius: 0.375rem;
`;

const ClusterItemActions = styled.div`
  display: flex;
  align-items: center;
  gap: 0.25rem;
  flex-shrink: 0;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;

  ${props => {
    switch (props.$variant) {
      case 'edit':
        return `
          background: #f0f9ff;
          color: #0369a1;
          &:hover { background: #e0f2fe; border-color: #0369a1; }
        `;
      case 'move':
        return `
          background: #f9fafb;
          color: #6b7280;
          &:hover { background: #f3f4f6; border-color: #9ca3af; }
        `;
      case 'delete':
        return `
          background: #fef2f2;
          color: #dc2626;
          &:hover { background: #fee2e2; border-color: #dc2626; }
        `;
      default:
        return `
          background: white;
          color: #6b7280;
          &:hover { background: #f9fafb; }
        `;
    }
  }}
`;

const ClusterEditForm = styled.div`
  margin-top: 2rem;
  padding: 1.5rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
`;

const ClusterEditHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
`;

const ClusterEditTitle = styled.h3`
  margin: 0;
  color: #1e293b;
  font-size: 1.125rem;
  font-weight: 600;
`;

const CloseEditButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: #f1f5f9;
  border: 1px solid #cbd5e1;
  border-radius: 0.375rem;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: #e2e8f0;
    color: #475569;
  }
`;

const ClusterEditContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const ClusterEditActions = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
`;

const SaveClusterButton = styled.button`
  padding: 0.5rem 1.5rem;
  background: #d95550;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover:not(:disabled) {
    background: #c73e39;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const CancelClusterButton = styled.button`
  padding: 0.5rem 1.5rem;
  background: white;
  color: #6b7280;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: #f9fafb;
    border-color: #9ca3af;
  }
`;

export default PillarPageEditor;
