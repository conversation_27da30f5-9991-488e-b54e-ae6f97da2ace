import React, { useState } from 'react';
import styled from 'styled-components';
import { FaYoutube, FaTimes, FaPlay } from 'react-icons/fa';

const VideoEmbedModal = ({ isOpen, onClose, onVideoInsert }) => {
  const [videoUrl, setVideoUrl] = useState('');
  const [videoId, setVideoId] = useState('');
  const [title, setTitle] = useState('');
  const [size, setSize] = useState('medium');
  const [alignment, setAlignment] = useState('center');

  const extractYouTubeId = (url) => {
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
      /youtube\.com\/watch\?.*v=([^&\n?#]+)/
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) {
        return match[1];
      }
    }
    return null;
  };

  const handleUrlChange = (e) => {
    const url = e.target.value;
    setVideoUrl(url);
    
    const id = extractYouTubeId(url);
    setVideoId(id || '');
    
    // Auto-generate title if not set
    if (id && !title) {
      setTitle('YouTube Video');
    }
  };

  const handleInsertVideo = () => {
    if (!videoId) {
      alert('Please enter a valid YouTube URL');
      return;
    }

    const sizeSettings = {
      small: { width: 400, height: 225 },
      medium: { width: 560, height: 315 },
      large: { width: 800, height: 450 },
      full: { width: '100%', height: 450 }
    };

    const { width, height } = sizeSettings[size];

    const alignmentStyles = {
      left: 'margin: 1rem 1rem 1rem 0; float: left;',
      center: 'margin: 1rem auto; display: block;',
      right: 'margin: 1rem 0 1rem 1rem; float: right;'
    };

    const videoEmbed = `
<div style="${alignmentStyles[alignment]} max-width: 100%;">
  <iframe 
    width="${width}" 
    height="${height}" 
    src="https://www.youtube.com/embed/${videoId}" 
    title="${title}"
    frameborder="0" 
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
    allowfullscreen
    style="border-radius: 0.5rem; max-width: 100%;"
  ></iframe>
</div>`;

    onVideoInsert(videoEmbed);
    handleClose();
  };

  const handleClose = () => {
    setVideoUrl('');
    setVideoId('');
    setTitle('');
    setSize('medium');
    setAlignment('center');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <ModalOverlay onClick={handleClose}>
      <ModalContent onClick={(e) => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>
            <FaYoutube />
            Embed YouTube Video
          </ModalTitle>
          <CloseButton onClick={handleClose}>
            <FaTimes />
          </CloseButton>
        </ModalHeader>

        <ModalBody>
          {/* URL Input */}
          <Section>
            <SectionTitle>YouTube Video URL</SectionTitle>
            <Input
              type="url"
              placeholder="https://www.youtube.com/watch?v=... or https://youtu.be/..."
              value={videoUrl}
              onChange={handleUrlChange}
            />
            <HelpText>
              Paste any YouTube URL format (youtube.com/watch?v=..., youtu.be/..., or youtube.com/embed/...)
            </HelpText>
          </Section>

          {/* Video Settings */}
          {videoId && (
            <>
              <Section>
                <SectionTitle>Video Settings</SectionTitle>
                <SettingsGrid>
                  <SettingGroup>
                    <Label>Title</Label>
                    <Input
                      type="text"
                      placeholder="Video title for accessibility"
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                    />
                  </SettingGroup>

                  <SettingGroup>
                    <Label>Size</Label>
                    <Select value={size} onChange={(e) => setSize(e.target.value)}>
                      <option value="small">Small (400x225)</option>
                      <option value="medium">Medium (560x315)</option>
                      <option value="large">Large (800x450)</option>
                      <option value="full">Full Width</option>
                    </Select>
                  </SettingGroup>

                  <SettingGroup>
                    <Label>Alignment</Label>
                    <Select value={alignment} onChange={(e) => setAlignment(e.target.value)}>
                      <option value="left">Left</option>
                      <option value="center">Center</option>
                      <option value="right">Right</option>
                    </Select>
                  </SettingGroup>
                </SettingsGrid>
              </Section>

              {/* Preview */}
              <Section>
                <SectionTitle>Preview</SectionTitle>
                <PreviewContainer>
                  <VideoPreview>
                    <VideoThumbnail>
                      <img 
                        src={`https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`}
                        alt="Video thumbnail"
                        onError={(e) => {
                          e.target.src = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
                        }}
                      />
                      <PlayOverlay>
                        <FaPlay />
                      </PlayOverlay>
                    </VideoThumbnail>
                    <VideoInfo>
                      <VideoTitle>{title || 'YouTube Video'}</VideoTitle>
                      <VideoDetails>
                        Size: {size} • Alignment: {alignment}
                      </VideoDetails>
                    </VideoInfo>
                  </VideoPreview>
                </PreviewContainer>
              </Section>
            </>
          )}
        </ModalBody>

        <ModalFooter>
          <CancelButton onClick={handleClose}>Cancel</CancelButton>
          <InsertButton 
            onClick={handleInsertVideo}
            disabled={!videoId}
          >
            Embed Video
          </InsertButton>
        </ModalFooter>
      </ModalContent>
    </ModalOverlay>
  );
};

// Styled Components
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: white;
  border-radius: 0.75rem;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
`;

const ModalTitle = styled.h2`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  color: #111827;
  font-size: 1.25rem;

  svg {
    color: #ff0000;
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: color 0.2s;

  &:hover {
    color: #374151;
  }
`;

const ModalBody = styled.div`
  padding: 1.5rem;
`;

const Section = styled.div`
  margin-bottom: 2rem;

  &:last-child {
    margin-bottom: 0;
  }
`;

const SectionTitle = styled.h3`
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
`;

const Input = styled.input`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;

  &:focus {
    outline: none;
    border-color: #d95550;
    box-shadow: 0 0 0 3px rgba(217, 85, 80, 0.1);
  }
`;

const HelpText = styled.p`
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
  font-style: italic;
`;

const SettingsGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const SettingGroup = styled.div``;

const Label = styled.label`
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
`;

const Select = styled.select`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  background: white;

  &:focus {
    outline: none;
    border-color: #d95550;
    box-shadow: 0 0 0 3px rgba(217, 85, 80, 0.1);
  }
`;

const PreviewContainer = styled.div`
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  background: #f9fafb;
`;

const VideoPreview = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const VideoThumbnail = styled.div`
  position: relative;
  flex-shrink: 0;
  border-radius: 0.5rem;
  overflow: hidden;
  width: 160px;
  height: 90px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const PlayOverlay = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
`;

const VideoInfo = styled.div`
  flex: 1;
`;

const VideoTitle = styled.h4`
  margin: 0 0 0.5rem 0;
  color: #111827;
  font-size: 1rem;
`;

const VideoDetails = styled.p`
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
`;

const CancelButton = styled.button`
  padding: 0.75rem 1.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  background: white;
  color: #374151;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: #f9fafb;
  }
`;

const InsertButton = styled.button`
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  background: #d95550;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover:not(:disabled) {
    background: #c73e39;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

export default VideoEmbedModal;
