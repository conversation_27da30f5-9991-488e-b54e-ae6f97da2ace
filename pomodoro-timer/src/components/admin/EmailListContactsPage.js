import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import {
  FaUsers, FaPlus, FaEdit, FaTrash, FaArrowLeft, FaUpload, FaDownload,
  FaSpinner, FaSearch, FaFilter, FaUserPlus, FaFileImport, FaEnvelope,
  FaTags, FaCalendarAlt, FaEye, FaEyeSlash
} from 'react-icons/fa';
import { api } from '../../services/apiService';

const Container = styled.div`
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 20px;
`;

const BackButton = styled.button`
  background: transparent;
  border: 1px solid #3498db;
  color: #3498db;
  padding: 10px 15px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  transition: all 0.3s ease;

  &:hover {
    background: #3498db;
    color: white;
  }
`;

const TitleSection = styled.div`
  flex: 1;
`;

const Title = styled.h1`
  color: #2c3e50;
  margin: 0 0 5px 0;
  display: flex;
  align-items: center;
  gap: 10px;
`;

const Subtitle = styled.p`
  color: #666;
  margin: 0;
  font-size: 14px;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
`;

const Button = styled.button`
  background: ${props => 
    props.variant === 'danger' ? '#e74c3c' : 
    props.variant === 'success' ? '#27ae60' : 
    props.variant === 'outline' ? 'transparent' : '#3498db'};
  color: ${props => props.variant === 'outline' ? '#3498db' : 'white'};
  border: ${props => props.variant === 'outline' ? '1px solid #3498db' : 'none'};
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    background: ${props => props.variant === 'outline' ? '#3498db' : ''};
    color: ${props => props.variant === 'outline' ? 'white' : ''};
  }

  &:disabled {
    background: #95a5a6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

const StatsBar = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`;

const StatCard = styled.div`
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
`;

const StatValue = styled.div`
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
`;

const StatLabel = styled.div`
  font-size: 14px;
  color: #666;
`;

const FilterSection = styled.div`
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
  align-items: center;
`;

const SearchInput = styled.input`
  flex: 1;
  min-width: 300px;
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
`;

const Select = styled.select`
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background: white;
`;

const ContactsTable = styled.div`
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
`;

const TableHeader = styled.div`
  display: grid;
  grid-template-columns: 1fr 200px 150px 120px 150px 120px;
  background: #f8f9fa;
  padding: 15px 20px;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 1px solid #dee2e6;
`;

const TableRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 200px 150px 120px 150px 120px;
  padding: 15px 20px;
  border-bottom: 1px solid #f1f3f4;
  transition: background 0.2s ease;

  &:hover {
    background: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }
`;

const EmailCell = styled.div`
  display: flex;
  flex-direction: column;
`;

const ContactEmail = styled.span`
  font-weight: 500;
  color: #2c3e50;
`;

const ContactName = styled.span`
  font-size: 12px;
  color: #666;
  margin-top: 2px;
`;

const StatusBadge = styled.span`
  background: ${props => 
    props.status === 'active' ? '#27ae60' :
    props.status === 'unsubscribed' ? '#e74c3c' :
    '#95a5a6'
  };
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  display: inline-block;
`;

const TagsCell = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
`;

const Tag = styled.span`
  background: #e3f2fd;
  color: #1976d2;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
`;

const ActionsCell = styled.div`
  display: flex;
  gap: 5px;
`;

const SmallButton = styled.button`
  background: ${props => 
    props.variant === 'danger' ? '#e74c3c' : 'transparent'
  };
  color: ${props => 
    props.variant === 'danger' ? 'white' : '#3498db'
  };
  border: ${props => 
    props.variant === 'danger' ? 'none' : '1px solid #3498db'
  };
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 11px;
  transition: all 0.3s ease;

  &:hover {
    background: ${props => 
      props.variant === 'danger' ? '#c0392b' : '#3498db'
    };
    color: white;
  }
`;

const DateCell = styled.span`
  font-size: 12px;
  color: #666;
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: white;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  border-radius: 10px;
  padding: 30px;
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
`;

const ModalTitle = styled.h2`
  color: #2c3e50;
  margin: 0;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
`;

const FormGroup = styled.div`
  margin-bottom: 20px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2c3e50;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
`;

const FileInput = styled.input`
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
`;

const Pagination = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 30px;
`;

const PageButton = styled.button`
  background: ${props => props.active ? '#3498db' : 'white'};
  color: ${props => props.active ? 'white' : '#3498db'};
  border: 1px solid #3498db;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;

  &:hover {
    background: #3498db;
    color: white;
  }

  &:disabled {
    background: #f8f9fa;
    color: #ccc;
    border-color: #ddd;
    cursor: not-allowed;
  }
`;

const EmailListContactsPage = () => {
  const { listId } = useParams();
  const navigate = useNavigate();
  const [list, setList] = useState(null);
  const [contacts, setContacts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    unsubscribed: 0
  });
  const [formData, setFormData] = useState({
    email: '',
    firstName: '',
    lastName: '',
    tags: ''
  });
  const [importFile, setImportFile] = useState(null);

  useEffect(() => {
    if (listId) {
      loadEmailList();
      loadContacts();
    }
  }, [listId, currentPage, searchTerm, statusFilter]);

  const loadEmailList = async () => {
    try {
      const response = await api.get(`/email-lists/${listId}`);
      if (response.data && response.data.success) {
        setList(response.data.data);
        setStats({
          total: response.data.data.totalContacts || 0,
          active: response.data.data.activeContacts || 0,
          unsubscribed: response.data.data.unsubscribedContacts || 0
        });
      }
    } catch (error) {
      console.error('Error loading email list:', error);
    }
  };

  const loadContacts = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/email-lists/${listId}`, {
        params: {
          page: currentPage,
          limit: 20,
          search: searchTerm,
          status: statusFilter !== 'all' ? statusFilter : undefined
        }
      });

      if (response.data && response.data.success) {
        setContacts(response.data.data.contacts || []);
        setTotalPages(response.data.data.pagination?.pages || 1);
      }
    } catch (error) {
      console.error('Error loading contacts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddContact = async () => {
    try {
      const contactData = {
        ...formData,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
      };

      const response = await api.post(`/email-lists/${listId}/contacts`, contactData);

      if (response.data && response.data.success) {
        setShowAddModal(false);
        setFormData({ email: '', firstName: '', lastName: '', tags: '' });
        loadContacts();
        loadEmailList();
      }
    } catch (error) {
      console.error('Error adding contact:', error);
      alert(error.response?.data?.message || 'Failed to add contact');
    }
  };

  const handleImportContacts = async () => {
    if (!importFile) {
      alert('Please select a CSV file to import');
      return;
    }

    const formData = new FormData();
    formData.append('csvFile', importFile);

    try {
      const response = await api.post(`/email-lists/${listId}/import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data && response.data.success) {
        setShowImportModal(false);
        setImportFile(null);
        loadContacts();
        loadEmailList();
        alert(`Successfully imported ${response.data.data.imported} contacts`);
      }
    } catch (error) {
      console.error('Error importing contacts:', error);
      alert(error.response?.data?.message || 'Failed to import contacts');
    }
  };

  const handleRemoveContact = async (contactId, contactEmail) => {
    if (!window.confirm(`Are you sure you want to remove ${contactEmail} from this list?`)) {
      return;
    }

    try {
      const response = await api.delete(`/email-lists/${listId}/contacts/${contactId}`);
      if (response.data && response.data.success) {
        loadContacts();
        loadEmailList();
      }
    } catch (error) {
      console.error('Error removing contact:', error);
      alert(error.response?.data?.message || 'Failed to remove contact');
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  if (loading && !list) {
    return (
      <Container>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <FaSpinner style={{ animation: 'spin 1s linear infinite', fontSize: '24px', color: '#3498db' }} />
          <p>Loading email list...</p>
        </div>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
          <BackButton onClick={() => navigate('/admin/email-lists')}>
            <FaArrowLeft />
            Back to Lists
          </BackButton>
          <TitleSection>
            <Title>
              <FaUsers />
              {list?.name || 'Email List Contacts'}
            </Title>
            <Subtitle>{list?.description || 'Manage contacts in this email list'}</Subtitle>
          </TitleSection>
        </div>
        <ActionButtons>
          <Button onClick={() => setShowAddModal(true)}>
            <FaPlus />
            Add Contact
          </Button>
          <Button variant="outline" onClick={() => setShowImportModal(true)}>
            <FaFileImport />
            Import CSV
          </Button>
        </ActionButtons>
      </Header>

      <StatsBar>
        <StatCard>
          <StatValue>{stats.total}</StatValue>
          <StatLabel>Total Contacts</StatLabel>
        </StatCard>
        <StatCard>
          <StatValue>{stats.active}</StatValue>
          <StatLabel>Active Contacts</StatLabel>
        </StatCard>
        <StatCard>
          <StatValue>{stats.unsubscribed}</StatValue>
          <StatLabel>Unsubscribed</StatLabel>
        </StatCard>
      </StatsBar>

      <FilterSection>
        <SearchInput
          type="text"
          placeholder="Search contacts..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        <Select value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)}>
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="unsubscribed">Unsubscribed</option>
        </Select>
        <Button variant="outline">
          <FaSearch />
          Search
        </Button>
      </FilterSection>

      <ContactsTable>
        <TableHeader>
          <div>Email & Name</div>
          <div>Status</div>
          <div>Tags</div>
          <div>Added Date</div>
          <div>Last Email</div>
          <div>Actions</div>
        </TableHeader>

        {contacts.map(contact => (
          <TableRow key={contact._id}>
            <EmailCell>
              <ContactEmail>{contact.email}</ContactEmail>
              {(contact.firstName || contact.lastName) && (
                <ContactName>
                  {[contact.firstName, contact.lastName].filter(Boolean).join(' ')}
                </ContactName>
              )}
            </EmailCell>
            <div>
              <StatusBadge status={contact.status}>
                {contact.status}
              </StatusBadge>
            </div>
            <TagsCell>
              {contact.tags?.map((tag, index) => (
                <Tag key={index}>{tag}</Tag>
              ))}
            </TagsCell>
            <DateCell>{formatDate(contact.createdAt)}</DateCell>
            <DateCell>{formatDate(contact.lastEmailSent)}</DateCell>
            <ActionsCell>
              <SmallButton 
                variant="danger"
                onClick={() => handleRemoveContact(contact._id, contact.email)}
                title="Remove from list"
              >
                <FaTrash />
              </SmallButton>
            </ActionsCell>
          </TableRow>
        ))}
      </ContactsTable>

      {contacts.length === 0 && !loading && (
        <div style={{ textAlign: 'center', padding: '50px', color: '#666' }}>
          <FaUsers style={{ fontSize: '48px', marginBottom: '20px', opacity: 0.3 }} />
          <h3>No Contacts Found</h3>
          <p>Add contacts to this email list to start building your audience.</p>
          <Button onClick={() => setShowAddModal(true)}>
            <FaPlus />
            Add Your First Contact
          </Button>
        </div>
      )}

      {totalPages > 1 && (
        <Pagination>
          <PageButton
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            Previous
          </PageButton>
          
          {[...Array(totalPages)].map((_, index) => (
            <PageButton
              key={index + 1}
              active={currentPage === index + 1}
              onClick={() => setCurrentPage(index + 1)}
            >
              {index + 1}
            </PageButton>
          ))}
          
          <PageButton
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
          >
            Next
          </PageButton>
        </Pagination>
      )}

      {/* Add Contact Modal */}
      {showAddModal && (
        <Modal onClick={() => setShowAddModal(false)}>
          <ModalContent onClick={(e) => e.stopPropagation()}>
            <ModalHeader>
              <ModalTitle>Add New Contact</ModalTitle>
              <CloseButton onClick={() => setShowAddModal(false)}>×</CloseButton>
            </ModalHeader>

            <FormGroup>
              <Label>Email Address *</Label>
              <Input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder="Enter email address..."
                required
              />
            </FormGroup>

            <FormGroup>
              <Label>First Name</Label>
              <Input
                type="text"
                value={formData.firstName}
                onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                placeholder="Enter first name..."
              />
            </FormGroup>

            <FormGroup>
              <Label>Last Name</Label>
              <Input
                type="text"
                value={formData.lastName}
                onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                placeholder="Enter last name..."
              />
            </FormGroup>

            <FormGroup>
              <Label>Tags (comma separated)</Label>
              <Input
                type="text"
                value={formData.tags}
                onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
                placeholder="e.g. premium, newsletter, beta"
              />
            </FormGroup>

            <ActionButtons>
              <Button variant="outline" onClick={() => setShowAddModal(false)}>
                Cancel
              </Button>
              <Button 
                variant="success" 
                onClick={handleAddContact}
                disabled={!formData.email.trim()}
              >
                Add Contact
              </Button>
            </ActionButtons>
          </ModalContent>
        </Modal>
      )}

      {/* Import CSV Modal */}
      {showImportModal && (
        <Modal onClick={() => setShowImportModal(false)}>
          <ModalContent onClick={(e) => e.stopPropagation()}>
            <ModalHeader>
              <ModalTitle>Import Contacts from CSV</ModalTitle>
              <CloseButton onClick={() => setShowImportModal(false)}>×</CloseButton>
            </ModalHeader>

            <FormGroup>
              <Label>CSV File *</Label>
              <FileInput
                type="file"
                accept=".csv"
                onChange={(e) => setImportFile(e.target.files[0])}
                required
              />
              <small style={{ color: '#666', fontSize: '12px', marginTop: '5px', display: 'block' }}>
                CSV should have columns: email, firstName, lastName, tags (optional)
              </small>
            </FormGroup>

            <ActionButtons>
              <Button variant="outline" onClick={() => setShowImportModal(false)}>
                Cancel
              </Button>
              <Button 
                variant="success" 
                onClick={handleImportContacts}
                disabled={!importFile}
              >
                <FaUpload />
                Import Contacts
              </Button>
            </ActionButtons>
          </ModalContent>
        </Modal>
      )}
    </Container>
  );
};

export default EmailListContactsPage;