import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import styled from 'styled-components';
import {
  FaChartLine, FaUsers, FaMoneyBillWave, FaCog,
  FaTachometerAlt, FaUserCog, FaFileInvoiceDollar, FaSpinner,
  FaDatabase, FaBook, FaChartBar, FaColumns, FaFileExport, FaUniversity,
  FaList, FaEnvelope
} from 'react-icons/fa';
import AdminDashboard from './AdminDashboard';
import AdminUserManagement from './AdminUserManagement';
import AdminEnhancedSubscriptionPage from './AdminEnhancedSubscriptionPage';
import AdminBackupPage from './AdminBackupPage';
import BlogPostList from './BlogPostList';
import PillarPageList from './PillarPageList';
import AnalyticsDashboard from './AnalyticsDashboard';
import ContentImportExport from './ContentImportExport';
import EmailOutreachPage from './EmailOutreachPage';
import EmailListPage from './EmailListPage';
import EmailCampaignPage from './EmailCampaignPage';
import { adminApi } from '../../services/apiService';

const AdminPage = () => {
  const location = useLocation();
  const [activeSection, setActiveSection] = useState('dashboard');
  const [stats, setStats] = useState({
    totalUsers: '--',
    totalRevenue: '--',
    growth: '--'
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchQuickStats();
  }, []);

  // Check for adminSection in location state and set active section
  useEffect(() => {
    if (location.state && location.state.adminSection) {
      setActiveSection(location.state.adminSection);
    }
  }, [location.state]);

  const fetchQuickStats = async () => {
    try {
      const quickStats = await adminApi.getQuickStats();
      setStats({
        totalUsers: quickStats.totalUsers || '--',
        totalRevenue: quickStats.totalRevenue ? `$${quickStats.totalRevenue}` : '--',
        growth: quickStats.growth ? `${quickStats.growth}%` : '--'
      });
    } catch (error) {
      console.error('Error fetching quick stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const renderSection = () => {
    switch (activeSection) {
      case 'dashboard':
        return <AdminDashboard />;
      case 'analytics':
        return <AnalyticsDashboard />;
      case 'users':
        return <AdminUserManagement />;
      case 'subscriptions':
        return <AdminEnhancedSubscriptionPage />;
      case 'backups':
        return <AdminBackupPage />;
      case 'blog':
        return <BlogPostList />;
      case 'pillarPages':
        return <PillarPageList />;
      case 'importExport':
        return <ContentImportExport />;
      case 'emailOutreach':
        return <EmailOutreachPage />;
      case 'emailLists':
        return <EmailListPage />;
      case 'emailCampaigns':
        return <EmailCampaignPage />;
      default:
        return <AdminDashboard />;
    }
  };

  return (
    <AdminContainer>
      <Sidebar>
        <SidebarHeader>
          <FaCog />
          <h2>Admin Panel</h2>
        </SidebarHeader>

        <SidebarMenu>
          <SidebarMenuItem
            $isActive={activeSection === 'dashboard'}
            onClick={() => setActiveSection('dashboard')}
          >
            <FaTachometerAlt />
            <span>Dashboard</span>
          </SidebarMenuItem>

          <SidebarMenuItem
            $isActive={activeSection === 'analytics'}
            onClick={() => setActiveSection('analytics')}
          >
            <FaChartBar />
            <span>Website Analytics</span>
          </SidebarMenuItem>

          <SidebarMenuItem
            $isActive={activeSection === 'users'}
            onClick={() => setActiveSection('users')}
          >
            <FaUserCog />
            <span>User Management</span>
          </SidebarMenuItem>

          <SidebarMenuItem
            $isActive={activeSection === 'subscriptions'}
            onClick={() => setActiveSection('subscriptions')}
          >
            <FaFileInvoiceDollar />
            <span>Subscriptions</span>
          </SidebarMenuItem>

          <SidebarMenuItem
            $isActive={activeSection === 'backups'}
            onClick={() => setActiveSection('backups')}
          >
            <FaDatabase />
            <span>Database Backup</span>
          </SidebarMenuItem>

          <SidebarMenuItem
            $isActive={activeSection === 'blog'}
            onClick={() => setActiveSection('blog')}
          >
            <FaBook />
            <span>Blog Management</span>
          </SidebarMenuItem>

          <SidebarMenuItem
            $isActive={activeSection === 'pillarPages'}
            onClick={() => setActiveSection('pillarPages')}
          >
            <FaColumns />
            <span>Pillar Pages</span>
          </SidebarMenuItem>

          <SidebarMenuItem
            $isActive={activeSection === 'importExport'}
            onClick={() => setActiveSection('importExport')}
          >
            <FaFileExport />
            <span>Import/Export</span>
          </SidebarMenuItem>

          <SidebarMenuItem
            $isActive={activeSection === 'emailOutreach'}
            onClick={() => setActiveSection('emailOutreach')}
          >
            <FaUniversity />
            <span>Email Outreach</span>
          </SidebarMenuItem>

          <SidebarMenuItem
            $isActive={activeSection === 'emailLists'}
            onClick={() => setActiveSection('emailLists')}
          >
            <FaList />
            <span>Email Lists</span>
          </SidebarMenuItem>

          <SidebarMenuItem
            $isActive={activeSection === 'emailCampaigns'}
            onClick={() => setActiveSection('emailCampaigns')}
          >
            <FaEnvelope />
            <span>Email Campaigns</span>
          </SidebarMenuItem>
        </SidebarMenu>

        <SidebarFooter>
          <AdminStats>
            {isLoading ? (
              <LoadingStats>
                <FaSpinner className="spinner" />
                <span>Loading stats...</span>
              </LoadingStats>
            ) : (
              <>
                <StatItem>
                  <FaUsers />
                  <span>Users</span>
                  <StatValue>{stats.totalUsers}</StatValue>
                </StatItem>
                <StatItem>
                  <FaMoneyBillWave />
                  <span>Revenue</span>
                  <StatValue>{stats.totalRevenue}</StatValue>
                </StatItem>
                <StatItem>
                  <FaChartLine />
                  <span>Growth</span>
                  <StatValue>{stats.growth}</StatValue>
                </StatItem>
              </>
            )}
          </AdminStats>
        </SidebarFooter>
      </Sidebar>

      <MainContent>
        {renderSection()}
      </MainContent>
    </AdminContainer>
  );
};

// Styled Components
const AdminContainer = styled.div`
  display: flex;
  min-height: calc(100vh - 60px); /* Adjust based on your header height */
`;

const Sidebar = styled.div`
  width: 250px;
  background-color: ${props => props.theme['--card-bg'] || '#fff'};
  border-right: 1px solid #eee;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
`;

const SidebarHeader = styled.div`
  padding: 1.5rem;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  gap: 0.75rem;

  h2 {
    margin: 0;
    font-size: 1.25rem;
    color: ${props => props.theme['--text-color'] || '#333'};
  }

  svg {
    color: ${props => props.theme['--primary-color'] || '#d95550'};
    font-size: 1.25rem;
  }
`;

const SidebarMenu = styled.nav`
  flex: 1;
  padding: 1.5rem 0;
`;

const SidebarMenuItem = styled.button`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem 1.5rem;
  background: ${props => props.$isActive ? 'rgba(217, 85, 80, 0.08)' : 'transparent'};
  border: none;
  text-align: left;
  color: ${props => props.$isActive ? props.theme['--primary-color'] || '#d95550' : props.theme['--text-color'] || '#333'};
  font-weight: ${props => props.$isActive ? '600' : '400'};
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: rgba(217, 85, 80, 0.05);
  }

  svg {
    font-size: 1.1rem;
  }
`;

const SidebarFooter = styled.div`
  padding: 1.5rem;
  border-top: 1px solid #eee;
`;

const AdminStats = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const StatItem = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: ${props => props.theme['--text-secondary'] || '#666'};
  font-size: 0.875rem;

  svg {
    color: ${props => props.theme['--primary-color'] || '#d95550'};
  }
`;

const StatValue = styled.span`
  margin-left: auto;
  font-weight: 600;
  color: ${props => props.theme['--text-color'] || '#333'};
`;

const MainContent = styled.main`
  flex: 1;
  padding: 0;
  background-color: ${props => props.theme['--bg-color'] || '#f5f5f5'};
  overflow-y: auto;
`;

const LoadingStats = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem 0;
  color: ${props => props.theme['--text-secondary'] || '#666'};
  font-size: 0.875rem;

  .spinner {
    margin-bottom: 0.5rem;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

export default AdminPage;
