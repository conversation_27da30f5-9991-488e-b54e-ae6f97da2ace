import React, { useState } from 'react';
import styled from 'styled-components';
import { <PERSON>a<PERSON><PERSON><PERSON>, FaCheck, FaTimes, FaPaypal, FaEthereum } from 'react-icons/fa';
import { subscriptionApi } from '../services/apiService';

const PaymentConfirmationModal = ({
  isOpen,
  onClose,
  selectedPlan,
  paymentMethod,
  onSuccess
}) => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [additionalInfo, setAdditionalInfo] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);

  if (!isOpen) return null;

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    if (!name.trim() || !email.trim()) {
      setError('Please provide your name and email');
      return;
    }

    // Clear previous errors
    setError('');
    setIsSubmitting(true);

    try {
      // Send payment confirmation to backend
      console.log('Submitting payment confirmation with data:', {
        plan: selectedPlan,
        paymentMethod,
        payerName: name,
        payerEmail: email,
        additionalInfo
      });

      const response = await subscriptionApi.createSubscriptionRequest({
        plan: selectedPlan,
        paymentMethod,
        payerName: name,
        payerEmail: email,
        additionalInfo
      });

      console.log('Payment confirmation submitted successfully:', response);

      // Show success message
      setShowSuccess(true);

      // Reset form fields but don't close modal automatically
      setName('');
      setEmail('');
      setAdditionalInfo('');
    } catch (error) {
      console.error('Error submitting payment confirmation:', error);

      // Provide more detailed error information
      let errorMessage = 'Failed to submit payment confirmation. Please try again.';

      if (error.response) {
        console.error('Error response:', error.response);
        errorMessage = error.response.data?.message || errorMessage;

        // Check for authentication errors
        if (error.response.status === 401) {
          errorMessage = 'Authentication error. Please log out and log back in, then try again.';
        }
      } else if (error.request) {
        console.error('No response received:', error.request);
        errorMessage = 'No response received from server. Please check your internet connection.';
      } else {
        console.error('Error message:', error.message);
        errorMessage = `Error: ${error.message}`;
      }

      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ModalOverlay>
      <ModalContent>
        <ModalHeader>
          <h2>{showSuccess ? 'Payment Information Sent' : 'Confirm Your Payment'}</h2>
          <CloseButton onClick={() => {
            if (showSuccess && onSuccess) {
              onSuccess();
            }
            onClose();
          }}>×</CloseButton>
        </ModalHeader>

        <ModalBody>
          {showSuccess ? (
            <SuccessMessage>
              <SuccessIcon><FaCheck /></SuccessIcon>
              <h3>Payment Information Sent!</h3>
              <p>Thank you for your payment. Your payment information has been successfully submitted to our system.</p>
              <SuccessDetails>
                <p>Our team will verify your payment and activate your subscription as soon as possible, usually within 24 hours.</p>
                <p>You'll receive an email notification once your subscription is activated.</p>
                <p>If you have any questions, please contact us at <strong><EMAIL></strong>.</p>
              </SuccessDetails>
              <SuccessNote>
                You can check the status of your subscription in the "Subscription History" section below.
              </SuccessNote>
              <CloseSuccessButton onClick={() => {
                setShowSuccess(false);
                if (onSuccess) onSuccess();
                onClose();
              }}>
                Close
              </CloseSuccessButton>
            </SuccessMessage>
          ) : (
            <>
              <PaymentSummary>
                <SummaryItem>
                  <SummaryLabel>Plan:</SummaryLabel>
                  <SummaryValue>{selectedPlan === 'yearly' ? 'Yearly ($20)' : 'Lifetime ($50)'}</SummaryValue>
                </SummaryItem>
                <SummaryItem>
                  <SummaryLabel>Payment Method:</SummaryLabel>
                  <SummaryValue>
                    {paymentMethod === 'paypal' ? (
                      <><FaPaypal /> PayPal</>
                    ) : (
                      <><FaEthereum /> USDT (TRC20)</>
                    )}
                  </SummaryValue>
                </SummaryItem>
              </PaymentSummary>

              <PaymentInstructions>
                {paymentMethod === 'paypal' ? (
                  <>
                    <h3>PayPal Payment Instructions</h3>
                    <p>Please send your payment to:</p>
                    <PaymentLink href="https://paypal.me/hijerry1206" target="_blank" rel="noopener noreferrer">
                      paypal.me/hijerry1206
                    </PaymentLink>
                    <p>After sending payment, fill out the form below to confirm your payment.</p>
                  </>
                ) : (
                  <>
                    <h3>USDT (TRC20) Payment Instructions</h3>
                    <p>Please send your payment to this wallet address:</p>
                    <WalletAddress>TCwiem6nAkbinFsydgHLPANGNZF7C5ccvv</WalletAddress>
                    <p>After sending payment, fill out the form below to confirm your payment.</p>
                  </>
                )}
              </PaymentInstructions>

              {error && <ErrorMessage>{error}</ErrorMessage>}

              <ConfirmationForm onSubmit={handleSubmit}>
                <FormGroup>
                  <Label htmlFor="name">Your Name</Label>
                  <Input
                    type="text"
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    required
                    placeholder="Enter your full name"
                  />
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="email">Your Email</Label>
                  <Input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    placeholder="Enter your email address"
                  />
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="additionalInfo">Additional Information (Optional)</Label>
                  <Textarea
                    id="additionalInfo"
                    value={additionalInfo}
                    onChange={(e) => setAdditionalInfo(e.target.value)}
                    placeholder="Transaction ID, payment date, or any other relevant information"
                    rows={3}
                  />
                </FormGroup>

                <SubmitButton type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <FaSpinner className="spinner" />
                      <span>Submitting...</span>
                    </>
                  ) : (
                    <span>Confirm Payment</span>
                  )}
                </SubmitButton>
              </ConfirmationForm>
            </>
          )}
        </ModalBody>
      </ModalContent>
    </ModalOverlay>
  );
};

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: ${props => props.theme['--card-bg'] || '#fff'};
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #eee;

  h2 {
    margin: 0;
    font-size: 1.25rem;
    color: ${props => props.theme['--text-color'] || '#333'};
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: ${props => props.theme['--text-secondary'] || '#666'};

  &:hover {
    color: ${props => props.theme['--text-color'] || '#333'};
  }
`;

const ModalBody = styled.div`
  padding: 1.5rem;
`;

const PaymentSummary = styled.div`
  background-color: ${props => props.theme['--bg-color'] || '#f5f5f5'};
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1.5rem;
`;

const SummaryItem = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;

  &:last-child {
    margin-bottom: 0;
  }
`;

const SummaryLabel = styled.span`
  font-weight: 600;
  color: ${props => props.theme['--text-color'] || '#333'};
`;

const SummaryValue = styled.span`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: ${props => props.theme['--text-color'] || '#333'};
`;

const PaymentInstructions = styled.div`
  margin-bottom: 1.5rem;

  h3 {
    font-size: 1rem;
    margin-bottom: 0.75rem;
    color: ${props => props.theme['--text-color'] || '#333'};
  }

  p {
    margin-bottom: 0.75rem;
    color: ${props => props.theme['--text-secondary'] || '#666'};
  }
`;

const PaymentLink = styled.a`
  display: block;
  padding: 0.75rem;
  background-color: #0070ba;
  color: white;
  text-align: center;
  border-radius: 6px;
  font-weight: 600;
  margin: 0.75rem 0;
  text-decoration: none;

  &:hover {
    background-color: #005ea6;
  }
`;

const WalletAddress = styled.div`
  padding: 0.75rem;
  background-color: ${props => props.theme['--bg-color'] || '#f5f5f5'};
  border: 1px dashed #ccc;
  border-radius: 6px;
  font-family: monospace;
  word-break: break-all;
  margin: 0.75rem 0;
  text-align: center;
`;

const ErrorMessage = styled.div`
  color: #f44336;
  background-color: rgba(244, 67, 54, 0.1);
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
`;

const ConfirmationForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  font-size: 0.875rem;
  font-weight: 600;
  color: ${props => props.theme['--text-color'] || '#333'};
`;

const Input = styled.input`
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: #1a73e8;
  }
`;

const Textarea = styled.textarea`
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: #1a73e8;
  }
`;

const SubmitButton = styled.button`
  padding: 0.75rem;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  &:hover {
    background-color: #388E3C;
  }

  &:disabled {
    background-color: #9E9E9E;
    cursor: not-allowed;
  }

  .spinner {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const SuccessMessage = styled.div`
  text-align: center;
  padding: 2rem 1rem;

  h3 {
    margin: 1rem 0;
    color: #4CAF50;
    font-size: 1.5rem;
  }

  > p {
    color: ${props => props.theme['--text-color'] || '#333'};
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
  }
`;

const SuccessDetails = styled.div`
  text-align: left;
  background-color: ${props => props.theme['--bg-color'] || '#f5f5f5'};
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;

  p {
    color: ${props => props.theme['--text-secondary'] || '#666'};
    margin-bottom: 0.75rem;
    position: relative;
    padding-left: 1.5rem;

    &:before {
      content: "•";
      position: absolute;
      left: 0.5rem;
      color: #4CAF50;
      font-weight: bold;
    }

    &:last-child {
      margin-bottom: 0;
    }

    strong {
      color: ${props => props.theme['--text-color'] || '#333'};
    }
  }
`;

const SuccessNote = styled.div`
  margin-top: 1rem;
  font-style: italic;
  color: ${props => props.theme['--text-secondary'] || '#666'};
  font-size: 0.9rem;
`;

const CloseSuccessButton = styled.button`
  margin-top: 1.5rem;
  padding: 0.75rem 2.5rem;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);

  &:hover {
    background-color: #388E3C;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
  }

  &:active {
    transform: translateY(1px);
  }
`;

const SuccessIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 70px;
  background-color: #4CAF50;
  color: white;
  border-radius: 50%;
  font-size: 1.75rem;
  margin: 0 auto;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
`;

export default PaymentConfirmationModal;
