const CACHE_NAME = 'ai-pomo-lcp-v1.0.2';
const STATIC_CACHE = 'ai-pomo-static-v1.0.2';
const DYNAMIC_CACHE = 'ai-pomo-dynamic-v1.0.2';

const urlsToCache = [
  '/',
  '/static/css/main.css',
  '/manifest.json',
  '/favicon.ico'
];

// Optimized install event for desktop performance
self.addEventListener('install', event => {
  event.waitUntil(
    Promise.all([
      caches.open(STATIC_CACHE).then(cache => cache.addAll(urlsToCache)),
      caches.open(DYNAMIC_CACHE)
    ])
  );
  self.skipWaiting();
});

// Advanced fetch strategy for desktop optimization
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);

  // 优化关键资源加载策略
  if (request.destination === 'script' || request.destination === 'style') {
    // 对于关键资源，优先使用缓存但快速失效处理
    event.respondWith(
      caches.match(request).then(response => {
        if (response) {
          // 后台更新策略
          fetch(request).then(fetchResponse => {
            if (fetchResponse.status === 200) {
              caches.open(STATIC_CACHE).then(cache => {
                cache.put(request, fetchResponse.clone());
              });
            }
          }).catch(() => {}); // 静默处理错误
          return response;
        }
        return fetch(request).then(fetchResponse => {
          if (fetchResponse.status === 200) {
            const responseClone = fetchResponse.clone();
            caches.open(STATIC_CACHE).then(cache => {
              cache.put(request, responseClone);
            });
          }
          return fetchResponse;
        });
      })
    );
  } else if (request.destination === 'image') {
    // Stale while revalidate for images
    event.respondWith(
      caches.match(request).then(response => {
        const fetchPromise = fetch(request).then(fetchResponse => {
          const responseClone = fetchResponse.clone();
          caches.open(DYNAMIC_CACHE).then(cache => {
            cache.put(request, responseClone);
          });
          return fetchResponse;
        });
        return response || fetchPromise;
      })
    );
  } else if (url.pathname.startsWith('/api/')) {
    // Network first for API calls
    event.respondWith(
      fetch(request).catch(() => {
        return caches.match(request);
      })
    );
  } else {
    // Default cache strategy
    event.respondWith(
      caches.match(request).then(response => {
        return response || fetch(request);
      })
    );
  }
});

// Activate event
self.addEventListener('activate', event => {
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  // Claim all clients
  self.clients.claim();
});