<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <!-- Favicon configuration -->
    <link rel="icon" type="image/x-icon" href="%PUBLIC_URL%/favicon.ico?v=2" />
    <link rel="shortcut icon" type="image/x-icon" href="%PUBLIC_URL%/favicon.ico?v=2" />
    <link rel="icon" type="image/png" sizes="32x32" href="%PUBLIC_URL%/favicon.ico?v=2" />
    <link rel="icon" type="image/png" sizes="16x16" href="%PUBLIC_URL%/favicon.ico?v=2" />
    <link rel="icon" type="image/png" sizes="192x192" href="%PUBLIC_URL%/ai-pomo.png?v=2" />
    <link rel="icon" type="image/png" sizes="512x512" href="%PUBLIC_URL%/ai-pomo.png?v=2" />

    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#d95550" />

    <!-- 关键资源立即加载优化 -->
    <link rel="preload" href="%PUBLIC_URL%/static/css/main.css" as="style">
    <link rel="stylesheet" href="%PUBLIC_URL%/static/css/main.css">
    <link rel="preload" href="%PUBLIC_URL%/static/js/main.js" as="script">
    <link rel="preload" href="https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuDyfAZ9hiA.woff2" as="font" type="font/woff2" crossorigin>
    
    <!-- DNS prefetch and preconnect for faster connection setup -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//api.openai.com">
    <link rel="dns-prefetch" href="//vercel.app">
    
    <!-- Preconnect to critical third-party origins -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="preconnect" href="https://api.openai.com" crossorigin>

    <link rel="apple-touch-icon" href="%PUBLIC_URL%/ai-pomo.png?v=2" />
    <link rel="apple-touch-icon" sizes="192x192" href="%PUBLIC_URL%/ai-pomo.png?v=2" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <title>AI Pomo - Boost Your Productivity with AI-Enhanced Pomodoro Timer</title>
    <meta name="description" content="AI Pomo combines the Pomodoro technique with AI-powered task management to help you focus, track progress, and achieve your goals efficiently." />
    <meta name="keywords" content="pomodoro timer, AI productivity, task management, time management, focus timer, productivity app" />
    
    <!-- Optimization for desktop Core Web Vitals -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <!-- 关键CSS内联 - 减少LCP渲染延迟 -->
    <style>
      /* 基础重置和字体 */
      * { box-sizing: border-box; margin: 0; padding: 0; }
      html { font-size: 16px; }
      body { 
        margin: 0; 
        font-family: 'Inter', system-ui, -apple-system, sans-serif; 
        line-height: 1.6; color: #333; background: #fff;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      
      /* 根容器优化 */
      #root { 
        min-height: 100vh; 
        display: flex; 
        flex-direction: column; 
        contain: layout style paint;
      }
      
      /* 首屏关键样式 */
      .loading-container {
        display: flex; justify-content: center; align-items: center;
        min-height: 100vh; flex-direction: column;
      }
      
      /* Header关键样式 */
      header { background: #fff; border-bottom: 1px solid #e5e7eb; }
      .header-container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
      
      /* Hero区域关键样式 */
      .hero-section { 
        padding: 5rem 2rem 0; text-align: center; background: #fff;
        min-height: 60vh; display: flex; align-items: center; justify-content: center;
      }
      .hero-title { 
        font-size: 2.75rem; font-weight: 800; line-height: 1.2; margin-bottom: 0.75rem;
        background: linear-gradient(to right, #d95550, #eb6b56);
        -webkit-background-clip: text; -webkit-text-fill-color: transparent;
      }
      
      /* 防止CLS */
      img { max-width: 100%; height: auto; }
      .stable-layout { min-height: 600px; width: 100%; }
      
      @media (max-width: 768px) {
        .hero-title { font-size: 2rem; }
        .hero-section { padding: 4rem 1rem 0; min-height: 50vh; }
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
