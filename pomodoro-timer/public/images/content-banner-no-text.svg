<svg width="1200" height="400" viewBox="0 0 1200 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Main gradient background -->
    <linearGradient id="mainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    
    <!-- Grid pattern -->
    <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
      <path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    </pattern>
    
    <!-- Glow effect -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="400" fill="url(#mainGradient)"/>
  
  <!-- Grid overlay -->
  <rect width="1200" height="400" fill="url(#grid)" opacity="0.3"/>
  
  <!-- Geometric shapes with animations -->
  <g opacity="0.1">
    <!-- Floating circle -->
    <circle cx="120" cy="80" r="50" fill="white">
      <animateTransform attributeName="transform" type="translate" 
                        values="0,0; 0,-10; 0,0" dur="6s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Floating square -->
    <rect x="960" y="40" width="80" height="80" fill="white" transform="rotate(45 1000 80)">
      <animateTransform attributeName="transform" type="translate" 
                        values="0,0; 0,15; 0,0" dur="8s" repeatCount="indefinite"/>
    </rect>
    
    <!-- Floating triangle -->
    <polygon points="240,340 210,290 270,290" fill="white">
      <animateTransform attributeName="transform" type="translate" 
                        values="0,0; 0,-8; 0,0" dur="7s" repeatCount="indefinite"/>
    </polygon>
    
    <!-- Floating rounded rectangle -->
    <rect x="940" y="250" width="120" height="120" rx="20" fill="white">
      <animateTransform attributeName="transform" type="translate" 
                        values="0,0; 0,12; 0,0" dur="9s" repeatCount="indefinite"/>
    </rect>
    
    <!-- Additional decorative elements -->
    <circle cx="200" cy="320" r="25" fill="white">
      <animateTransform attributeName="transform" type="translate" 
                        values="0,0; 0,-5; 0,0" dur="5s" repeatCount="indefinite"/>
    </circle>
    
    <rect x="850" y="150" width="40" height="40" fill="white" transform="rotate(30 870 170)">
      <animateTransform attributeName="transform" type="translate" 
                        values="0,0; 0,8; 0,0" dur="6.5s" repeatCount="indefinite"/>
    </rect>
    
    <!-- Hexagon -->
    <polygon points="350,100 380,85 410,100 410,130 380,145 350,130" fill="white">
      <animateTransform attributeName="transform" type="translate" 
                        values="0,0; 0,-6; 0,0" dur="7.5s" repeatCount="indefinite"/>
    </polygon>
  </g>
  
  <!-- Productivity icons with subtle glow -->
  <g opacity="0.15" filter="url(#glow)">
    <!-- Clock icon -->
    <circle cx="100" cy="300" r="20" fill="none" stroke="white" stroke-width="2"/>
    <line x1="100" y1="300" x2="100" y2="285" stroke="white" stroke-width="2"/>
    <line x1="100" y1="300" x2="110" y2="300" stroke="white" stroke-width="2"/>
    
    <!-- Task checkmark -->
    <circle cx="1100" cy="100" r="18" fill="none" stroke="white" stroke-width="2"/>
    <polyline points="1092,100 1098,106 1108,94" fill="none" stroke="white" stroke-width="2"/>
    
    <!-- Brain/AI icon -->
    <path d="M 80 150 Q 70 140 80 130 Q 90 125 100 130 Q 110 125 120 130 Q 130 140 120 150 Q 115 160 105 155 Q 95 160 80 150 Z" 
          fill="white" opacity="0.8">
      <animateTransform attributeName="transform" type="scale" 
                        values="1; 1.1; 1" dur="4s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- Subtle particle effects -->
  <g opacity="0.2">
    <circle cx="300" cy="50" r="2" fill="white">
      <animate attributeName="opacity" values="0.2;0.8;0.2" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="800" cy="350" r="1.5" fill="white">
      <animate attributeName="opacity" values="0.2;0.6;0.2" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="150" cy="200" r="1" fill="white">
      <animate attributeName="opacity" values="0.2;0.7;0.2" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="1050" cy="200" r="2" fill="white">
      <animate attributeName="opacity" values="0.2;0.9;0.2" dur="3.5s" repeatCount="indefinite"/>
    </circle>
  </g>
</svg>
