<svg width="1200" height="400" viewBox="0 0 1200 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Green gradient background -->
    <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#43e97b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#38f9d7;stop-opacity:1" />
    </linearGradient>
    
    <!-- Grid pattern -->
    <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
      <path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    </pattern>
    
    <!-- Text shadow -->
    <filter id="textShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="rgba(0,0,0,0.3)"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="400" fill="url(#greenGradient)"/>
  
  <!-- Grid overlay -->
  <rect width="1200" height="400" fill="url(#grid)" opacity="0.3"/>
  
  <!-- Geometric shapes -->
  <g opacity="0.1">
    <circle cx="120" cy="80" r="50" fill="white">
      <animateTransform attributeName="transform" type="translate" 
                        values="0,0; 0,-10; 0,0" dur="6s" repeatCount="indefinite"/>
    </circle>
    
    <rect x="960" y="40" width="80" height="80" fill="white" transform="rotate(45 1000 80)">
      <animateTransform attributeName="transform" type="translate" 
                        values="0,0; 0,15; 0,0" dur="8s" repeatCount="indefinite"/>
    </rect>
    
    <polygon points="240,340 210,290 270,290" fill="white">
      <animateTransform attributeName="transform" type="translate" 
                        values="0,0; 0,-8; 0,0" dur="7s" repeatCount="indefinite"/>
    </polygon>
    
    <rect x="940" y="250" width="120" height="120" rx="20" fill="white">
      <animateTransform attributeName="transform" type="translate" 
                        values="0,0; 0,12; 0,0" dur="9s" repeatCount="indefinite"/>
    </rect>
  </g>
  
  <!-- Main content -->
  <g text-anchor="middle" filter="url(#textShadow)">
    <text x="600" y="180" font-family="Inter, Arial, sans-serif" font-size="48" font-weight="700" fill="white">
      🍅 AI Pomo
    </text>
    
    <text x="600" y="220" font-family="Inter, Arial, sans-serif" font-size="20" fill="rgba(255,255,255,0.9)">
      Revolutionize your productivity with AI-enhanced time management
    </text>
  </g>
</svg>
