# Paddle Payment Integration - Deployment Guide

## Overview
This guide will help you deploy the Paddle payment integration for AI-Pomo. The integration replaces the existing manual PayPal/USDT payment system with automated Paddle checkout.

## Prerequisites
1. **Paddle Account**: Verified Paddle account with API access
2. **Node.js Dependencies**: Add these to your production server
   ```bash
   # Backend dependencies
   npm install @paddle/paddle-node-sdk axios crypto
   
   # Frontend dependencies  
   npm install @paddle/paddle-js
   ```

## Step 1: Paddle Dashboard Setup

### 1.1 Create Products
In your Paddle Dashboard, create three products:
- **Monthly Plan**: $8/month recurring subscription with 7-day free trial
- **Yearly Plan**: $60/year recurring subscription with 7-day free trial  
- **Lifetime Plan**: $100 one-time payment

### 1.2 Get API Credentials
Collect these from your Paddle Dashboard:
- API Key (from Settings > API Keys)
- Webhook Secret (from Settings > Webhooks)
- Vendor/Seller ID (from Settings > Account)
- Product IDs and Price IDs (from Products section)

### 1.3 Configure Webhooks
Set webhook URL to: `https://ai-pomo.com/api/paddle/webhook`
Enable these events:
- `transaction.completed`
- `subscription.created`
- `subscription.updated`
- `subscription.cancelled`
- `subscription.paused`
- `subscription.resumed`

## Step 2: Environment Variables

### 2.1 Backend (.env in server directory)
```bash
# Paddle Configuration
PADDLE_API_KEY=pdl_live_apikey_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
PADDLE_WEBHOOK_SECRET=your_webhook_secret_here
PADDLE_VENDOR_ID=pro_01jxjzx642xnz9k48cwt9hv4r2
PADDLE_ENVIRONMENT=production

# Product Configuration
PADDLE_MONTHLY_PRODUCT_ID=your_monthly_product_id
PADDLE_MONTHLY_PRICE_ID=your_monthly_price_id
PADDLE_YEARLY_PRODUCT_ID=your_yearly_product_id
PADDLE_YEARLY_PRICE_ID=your_yearly_price_id
PADDLE_LIFETIME_PRODUCT_ID=your_lifetime_product_id
PADDLE_LIFETIME_PRICE_ID=your_lifetime_price_id

# Webhook URL (optional, defaults to production URL)
PADDLE_WEBHOOK_URL=https://ai-pomo.com/api/paddle/webhook
```

### 2.2 Frontend (.env in pomodoro-timer directory)
```bash
# Paddle Frontend Configuration
REACT_APP_PADDLE_VENDOR_ID=pro_01jxjzx642xnz9k48cwt9hv4r2
REACT_APP_PADDLE_ENVIRONMENT=production
REACT_APP_PADDLE_CLIENT_TOKEN=live_9c5e1bb16afca4f9f2969c86119
```

### 2.3 Update PM2 Configuration
Add Paddle environment variables to `ecosystem.config.js`:
```javascript
env: {
  // ... existing variables
  PADDLE_API_KEY: "your_paddle_api_key_here",
  PADDLE_WEBHOOK_SECRET: "your_webhook_secret_here",
  PADDLE_VENDOR_ID: "your_vendor_id_here",
  PADDLE_ENVIRONMENT: "production",
  PADDLE_MONTHLY_PRODUCT_ID: "your_monthly_product_id",
  PADDLE_MONTHLY_PRICE_ID: "your_monthly_price_id",
  PADDLE_YEARLY_PRODUCT_ID: "your_yearly_product_id",
  PADDLE_YEARLY_PRICE_ID: "your_yearly_price_id",
  PADDLE_LIFETIME_PRODUCT_ID: "your_lifetime_product_id",
  PADDLE_LIFETIME_PRICE_ID: "your_lifetime_price_id"
}
```

## Step 3: Database Migration

### 3.1 Run Migration Script
```bash
cd server
npm run migrate-paddle
```

This script will:
- Add migration notes to existing subscriptions
- Prepare database for Paddle integration
- Provide deployment summary

### 3.2 Verify Migration
Check that existing subscriptions are preserved and marked appropriately.

## Step 4: Deployment

### 4.1 Deploy Code
```bash
# From project root
git add .
git commit -m "Add Paddle payment integration"
git push origin master

# On production server
./deploy-pm2.sh
```

### 4.2 Verify Deployment
1. Check that all services started correctly
2. Verify API endpoints are accessible:
   - `GET /api/paddle/subscription/status`
   - `POST /api/paddle/checkout`
   - `POST /api/paddle/webhook`

## Step 5: Testing

### 5.1 Test Subscription Flow
1. Log into your application
2. Go to Premium page
3. Select a plan
4. Complete checkout using Paddle
5. Verify subscription activation

### 5.2 Test Webhooks
1. Use Paddle's webhook testing tool
2. Verify webhook events are processed correctly
3. Check database updates

### 5.3 Monitor Logs
```bash
# Check PM2 logs
pm2 logs

# Check for Paddle-related errors
pm2 logs | grep -i paddle
```

## Step 6: Go Live

### 6.1 Switch to Production Mode
Ensure `PADDLE_ENVIRONMENT=production` in all environments.

### 6.2 Update Frontend
The frontend will automatically use production Paddle environment.

### 6.3 Monitor Initial Transactions
Watch for the first few transactions to ensure everything works correctly.

## Troubleshooting

### Common Issues

1. **Webhook Signature Verification Fails**
   - Check `PADDLE_WEBHOOK_SECRET` matches dashboard
   - Verify webhook URL is accessible

2. **Checkout Session Creation Fails**
   - Verify API key has correct permissions
   - Check product/price IDs are correct

3. **Frontend Paddle.js Loading Issues**
   - Ensure Paddle.js script loads successfully
   - Check browser console for errors

### Debug Commands
```bash
# Check environment variables
pm2 show pomodoro-backend | grep -A 20 "env:"

# Test webhook endpoint
curl -X POST https://ai-pomo.com/api/paddle/webhook

# Check subscription status
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     https://ai-pomo.com/api/paddle/subscription/status
```

## Rollback Plan

If issues occur, you can temporarily disable Paddle integration:

1. **Frontend**: Comment out Paddle checkout in PremiumPage.js
2. **Backend**: Disable Paddle routes in index.js
3. **Database**: Existing subscriptions remain unaffected

## Support

- **Paddle Documentation**: https://developer.paddle.com/
- **Paddle Support**: Contact via Paddle Dashboard
- **Application Logs**: Check PM2 logs for detailed error information

## Security Notes

1. **Never commit API keys** to version control
2. **Use environment variables** for all sensitive data
3. **Verify webhook signatures** to prevent fraud
4. **Monitor failed transactions** for suspicious activity
5. **Regularly rotate API keys** for security

## Next Steps After Deployment

1. Monitor initial transaction volume
2. Set up Paddle Dashboard notifications
3. Configure tax collection if required
4. Plan migration strategy for legacy subscriptions
5. Update user communication about new payment system