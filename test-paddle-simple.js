// Simple test script to verify Paddle configuration
const paddleConfig = {
  environment: 'sandbox',
  vendorId: 'pro_01jxjzx642xnz9k48cwt9hv4r2',
  priceIds: {
    monthly: 'pri_01jxk01813b3zfs36kcz66x0xg',
    yearly: 'pri_01jxk02vycmv0zjfck99vcxqmt',
    lifetime: 'pri_01jxk0448wa25ssmshke9c7r1m',
  },
};

const paddleController = {
  testPaddle: () => {
    try {
      return {
        success: true,
        message: 'Paddle integration is working',
        config: {
          environment: paddleConfig.environment,
          vendorId: paddleConfig.vendorId,
          priceIds: paddleConfig.priceIds,
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  },

  createCheckout: (planType) => {
    try {
      if (!['monthly', 'yearly', 'lifetime'].includes(planType)) {
        return {
          success: false,
          error: 'Invalid plan type',
        };
      }

      const priceId = paddleConfig.priceIds[planType];
      
      return {
        success: true,
        planType,
        priceId,
        message: 'Checkout configuration ready',
        checkoutUrl: `https://checkout.paddle.com/checkout?price_id=${priceId}`,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  }
};

// Test the functions
console.log('=== Paddle Integration Test ===\n');

console.log('1. Test Paddle Configuration:');
console.log(JSON.stringify(paddleController.testPaddle(), null, 2));

console.log('\n2. Test Monthly Checkout:');
console.log(JSON.stringify(paddleController.createCheckout('monthly'), null, 2));

console.log('\n3. Test Yearly Checkout:');
console.log(JSON.stringify(paddleController.createCheckout('yearly'), null, 2));

console.log('\n4. Test Lifetime Checkout:');
console.log(JSON.stringify(paddleController.createCheckout('lifetime'), null, 2));

console.log('\n5. Test Invalid Plan:');
console.log(JSON.stringify(paddleController.createCheckout('invalid'), null, 2));

console.log('\n=== Test Complete ===');