#!/bin/bash

# Make script exit on any error
set -e

echo "Restarting services with updated configuration..."

# Reload PM2 applications
echo "Reloading PM2 applications..."
pm2 reload ecosystem.config.js --update-env

# Reload Caddy with the new configuration
echo "Reloading Caddy with new configuration..."
sudo cp Caddyfile-pm2 /etc/caddy/Caddyfile
sudo systemctl reload caddy

# Check Caddy status
echo "Checking Caddy status..."
sudo systemctl status caddy --no-pager

# Check PM2 status
echo "Checking PM2 status..."
pm2 status

echo "Services restarted successfully!"
echo "Try logging in again to see if the issue is resolved."
