# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

AI Pomo is a comprehensive Pomodoro timer application with task management and productivity tracking features. It combines the Pomodoro Technique with Getting Things Done (GTD) methodology to help users manage their tasks, projects, and time effectively.

## Key Commands

### Development

```bash
# Frontend development (from pomodoro-timer directory)
npm install --legacy-peer-deps
npm start

# Backend development (from server directory) 
npm install --legacy-peer-deps
npm run dev

# Initialize database (first time only, from server directory)
npm run init-db

# Migrate existing subscriptions to Paddle (one-time only)
npm run migrate-paddle
```

### Testing
```bash
# Backend tests (from server directory)
npm test
```

### Production Build & Deployment

#### Traditional Server Deployment (PM2/Docker)
```bash
# Build frontend (from pomodoro-timer directory)
npm run build

# Deploy with PM2 (from root directory)
./deploy-pm2.sh

# Start services with Docker Compose
docker-compose up -d

# Production deployment with PM2
pm2 reload ecosystem.config.js --update-env
```

#### Cloud Deployment (Vercel + Railway)
```bash
# Check deployment configuration
node scripts/check-deployment.js

# Migrate database (if coming from existing server)
./scripts/migrate-to-cloud.sh

# Frontend deploys automatically on Vercel when pushing to GitHub
# Backend deploys automatically on Railway when pushing to GitHub
```

**Deployment Configuration Files:**
- `pomodoro-timer/vercel.json` - Vercel frontend configuration
- `server/railway.json` - Railway backend configuration
- `VERCEL_RAILWAY_DEPLOYMENT.md` - Complete deployment guide

## Architecture Overview

### Tech Stack
- **Frontend**: React with styled-components, Context API for state management
- **Backend**: Node.js/Express REST API with JWT authentication
- **Database**: MongoDB with Mongoose ODM
- **Deployment**: Docker, PM2, Caddy server (traditional) or Vercel + Railway (cloud)
- **Authentication**: JWT tokens, Google OAuth support

### Project Structure
- `/pomodoro-timer` - React frontend application
- `/server` - Node.js/Express backend API
- `/ai-pomo` - Main project root with deployment configs

### Key Features
- User authentication with JWT and Google OAuth
- Project management (max 5 open projects)
- Task tracking with Pomodoro estimations
- Milestone timeline visualization
- Note-taking within projects
- Customizable timer settings
- Statistics and analytics tracking
- Gamification with XP and achievements
- Blog system with admin interface
- AI-powered project generation

### Database Models
- **User**: Authentication, settings, statistics
- **Project**: Contains tasks, milestones, and notes
- **Task**: Individual work items with Pomodoro tracking
- **Pomodoro**: Session tracking for work/break intervals
- **BlogPost**: Content management system
- **Subscription**: Premium features management

### API Structure
All API routes are prefixed with `/api/`:
- `/auth/*` - Authentication endpoints
- `/projects/*` - Project CRUD operations
- `/tasks/*` - Task management
- `/pomodoros/*` - Timer session tracking
- `/stats/*` - Analytics and statistics
- `/blog/*` - Blog content management
- `/admin/*` - Admin functionality

### Environment Configuration
- Development uses `.env` files in both frontend and backend
- Production config in `ecosystem.config.js` for PM2
- MongoDB credentials configured in docker-compose files
- Proxy configuration in frontend's package.json for API calls

### Paddle Payment Integration
- **Backend Environment Variables** (required in production):
  - `PADDLE_API_KEY` - Paddle API key from dashboard
  - `PADDLE_WEBHOOK_SECRET` - Webhook signature secret
  - `PADDLE_VENDOR_ID` - Your Paddle vendor/seller ID
  - `PADDLE_ENVIRONMENT` - 'sandbox' or 'production'
  - `PADDLE_MONTHLY_PRICE_ID` - pri_01jxk01813b3zfs36kcz66x0xg (monthly $8)
  - `PADDLE_YEARLY_PRICE_ID` - pri_01jxk02vycmv0zjfck99vcxqmt (yearly $60)
  - `PADDLE_LIFETIME_PRICE_ID` - pri_01jxk0448wa25ssmshke9c7r1m (lifetime $100)
  - `PADDLE_WEBHOOK_URL` - Webhook endpoint URL
- **Frontend Environment Variables**:
  - `REACT_APP_PADDLE_VENDOR_ID` - pro_01jxjzx642xnz9k48cwt9hv4r2
  - `REACT_APP_PADDLE_CLIENT_TOKEN` - Client-side token for Paddle.js
  - `REACT_APP_PADDLE_ENVIRONMENT` - 'sandbox' or 'production'
- **Webhook Configuration**: Set webhook URL to `/api/paddle/webhook`
- **Product Setup**: Create monthly ($8), yearly ($60), and lifetime ($100) products in Paddle Dashboard

### Important Notes
- Always use `--legacy-peer-deps` when installing npm packages
- Frontend runs on port 3000, backend on port 5000
- MongoDB runs on port 27017 with Mongo Express on port 8081
- Production deployment uses PM2 for process management
- Caddy server handles HTTPS and reverse proxy in production

### Security & Environment
- **CRITICAL**: Never commit actual production secrets to version control
- Use `.env` files for environment variables (excluded from git)
- Development MongoDB credentials: Use environment variables (see .env.example)
- JWT secrets and API keys should be unique per environment
- Google OAuth requires separate client IDs for dev/prod environments

### Development Workflow
- Use `npm test` in server directory to run Jest tests
- Backend tests use `--detectOpenHandles` flag for proper cleanup
- Frontend uses Create React App with proxy configuration
- Hot reload available in development mode for both frontend and backend