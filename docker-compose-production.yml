name: pomodoro-timer-production

services:
  # MongoDB service
  mongodb:
    image: mongo:latest
    container_name: pomodoro-mongodb-prod
    restart: always
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: hiruben
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD:-changeme}
      MONGO_INITDB_DATABASE: pomodoro-timer
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - pomodoro-network

  # MongoDB Express web-based admin interface
  mongo-express:
    image: mongo-express:latest
    container_name: pomodoro-mongo-express-prod
    restart: always
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: hiruben
      ME_CONFIG_MONGODB_ADMINPASSWORD: ${MONGO_PASSWORD:-changeme}
      ME_CONFIG_MONGODB_SERVER: mongodb
      ME_CONFIG_MONGODB_PORT: 27017
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: ${MONGO_ADMIN_PASSWORD:-changeme}
    depends_on:
      - mongodb
    networks:
      - pomodoro-network

volumes:
  mongodb_data:
    name: pomodoro-mongodb-data-prod

networks:
  pomodoro-network:
    driver: bridge