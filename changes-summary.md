# Changes Made to functionality-specification.md

## 1. Standalone Timer Page Section

**Added a new bullet point:**
- Using the "Set Active" button requires a confirmation if a timer is currently running, as it will reset the timer

## 2. Timer Behavior Section

**Changed from:**
- When switching between projects, the running Pomodoro timer is reset with a warning prompt
- When switching active tasks, the running timer is reset with a warning prompt

**Changed to:**
- When switching between projects, the running Pomodoro timer is reset with a warning prompt that clearly explains the consequences
- When switching active tasks, the running timer is reset with a warning prompt that informs the user the current timer will be abandoned

## 3. Active Task Management Section

**Changed from:**
- Switching active tasks during a running timer requires confirmation
- Pomodoro sessions are associated with the active task

**Changed to:**
- Switching active tasks during a running timer requires confirmation and will reset the current timer
- Pomodoro sessions are associated with the active task
- Confirmation prompts clearly warn users that changing the active task will reset the current timer

These changes ensure that the specification clearly documents the requirement for confirmation prompts when switching tasks during a running timer, with clear warnings about the consequences of these actions.
