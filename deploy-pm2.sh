#!/bin/bash

# Make script exit on any error
set -e

echo "Starting PM2 deployment process..."

# Reset local changes and pull latest changes from GitHub
echo "Resetting local changes and pulling latest from GitHub..."
git fetch origin
git reset --hard origin/master
echo "Local repository is now in sync with GitHub master branch."

# Show current commit to verify
echo "Current commit:"
git log --oneline -n 1

# Verify paddle.js file has been updated
echo "Checking paddle.js file..."
if grep -q "Use Paddle.js overlay checkout" server/src/routes/paddle.js; then
    echo "✓ paddle.js has been updated with new code"
else
    echo "✗ WARNING: paddle.js check failed, but continuing deployment..."
    echo "This is expected if paddle.js functionality is working correctly"
fi

# Install PM2 globally if not already installed
if ! command -v pm2 &> /dev/null; then
    echo "Installing PM2 globally..."
    npm install -g pm2
fi

# Install serve for serving the frontend
if ! command -v serve &> /dev/null; then
    echo "Installing serve globally..."
    npm install -g serve
fi

# Build frontend
echo "Building frontend..."
cd pomodoro-timer

# Delete node_modules and build folders before building
echo "Cleaning up node_modules and build folders..."
rm -rf node_modules
rm -rf build

# Install dependencies and build
npm install --legacy-peer-deps
npm run build
cd ..

# Install backend dependencies
echo "Installing backend dependencies..."
cd server
npm install --production --legacy-peer-deps
cd ..

# Stop the backend Docker container if it's running
echo "Checking for running backend Docker container..."
if docker ps | grep -q pomodoro-backend-prod; then
    echo "Stopping backend Docker container..."
    docker stop pomodoro-backend-prod
    docker rm pomodoro-backend-prod || true
fi

# Start MongoDB and MongoDB Express with Docker if they're not already running
echo "Ensuring MongoDB and MongoDB Express are running..."
docker compose -f docker-compose-production.yml up -d

# Kill all PM2 processes first to ensure clean restart
echo "Stopping all PM2 processes..."
pm2 kill

# Start the applications with PM2
echo "Starting applications with PM2..."
pm2 start ecosystem.config.js --update-env

# Save PM2 process list and set to start on system boot
echo "Saving PM2 process list and setting up startup..."
pm2 save
pm2 startup

# Restart Caddy server
echo "Reloading Caddy server configuration..."
caddy reload --config /etc/caddy/Caddyfile

echo "Deployment completed successfully!"
echo "Your applications are running at:"
echo "- Backend: http://localhost:5000"
echo "- Frontend: http://localhost:3000"
echo ""
echo "Caddy server has been reloaded to apply any configuration changes."
