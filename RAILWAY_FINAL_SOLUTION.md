# Railway 部署最终解决方案

## 问题根源
Git 仓库历史太大（包含已删除的大文件），导致 Railway 克隆代码很慢。

## 解决方案（按推荐顺序）

### 方案 1：创建独立的后端仓库（最推荐）
```bash
# 1. 创建新的后端仓库
cd ~/文档
mkdir ai-pomo-backend
cd ai-pomo-backend
git init

# 2. 复制后端代码
cp -r ~/文档/ai-pomo/ai-pomo/server/* .
cp ~/文档/ai-pomo/ai-pomo/.gitignore .

# 3. 初始化 Git
git add .
git commit -m "Initial backend code"

# 4. 推送到新的 GitHub 仓库
git remote add origin https://github.com/Hicruben/ai-pomo-backend.git
git push -u origin master

# 5. 在 Railway 部署这个新仓库
```

### 方案 2：使用 GitHub Actions 部署（自动化）
创建 `.github/workflows/railway-deploy.yml`:
```yaml
name: Deploy to Railway
on:
  push:
    branches: [master]
    paths:
      - 'server/**'

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          sparse-checkout: server
      
      - name: Deploy to Railway
        run: |
          npm install -g @railway/cli
          railway up --service=${{ secrets.RAILWAY_SERVICE_ID }}
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
```

### 方案 3：清理 Git 历史（风险较高）
```bash
# 警告：这会重写 Git 历史
git filter-branch --index-filter \
  'git rm -r --cached --ignore-unmatch pomodoro-timer/public/landing-videos' \
  --prune-empty --tag-name-filter cat -- --all

# 强制推送
git push --force --all
git push --force --tags
```

### 方案 4：使用其他平台
- **Render.com** - 通常更快，有免费套餐
- **Fly.io** - 性能好，部署快
- **Heroku** - 经典选择（现在收费）

## 立即可用的临时方案

在 Railway 使用 Docker 部署：
1. 在 Railway 设置中启用 "Use Dockerfile"
2. 创建简单的 Dockerfile（只复制 server 目录）

## 推荐：方案 1
创建独立的后端仓库是最干净、最快的解决方案。