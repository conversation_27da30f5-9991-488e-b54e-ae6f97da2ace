# Paddle Environment Variables Template

## ⚠️ SECURITY WARNING
**NEVER commit actual API keys to version control!**
**The API key shown in this template should be replaced with a NEW key after the old one is revoked.**

## Backend Environment Variables (server/.env)

```bash
# Paddle Configuration
PADDLE_API_KEY=pdl_live_apikey_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
PADDLE_WEBHOOK_SECRET=# Optional - leave empty if Paddle doesn't provide one
PADDLE_NOTIFICATION_SET_ID=ntfset_01jxk2ty9a4x0p7hm6sjepxqwn
PADDLE_VENDOR_ID=pro_01jxjzx642xnz9k48cwt9hv4r2
PADDLE_ENVIRONMENT=production

# Product Configuration - From your Paddle Dashboard
PADDLE_MONTHLY_PRICE_ID=pri_01jxk01813b3zfs36kcz66x0xg
PADDLE_YEARLY_PRICE_ID=pri_01jxk02vycmv0zjfck99vcxqmt
PADDLE_LIFETIME_PRICE_ID=pri_01jxk0448wa25ssmshke9c7r1m

# Webhook URL (optional, defaults to production URL)
PADDLE_WEBHOOK_URL=https://ai-pomo.com/api/paddle/webhook
```

## Frontend Environment Variables (pomodoro-timer/.env)

```bash
# Paddle Frontend Configuration
REACT_APP_PADDLE_VENDOR_ID=pro_01jxjzx642xnz9k48cwt9hv4r2
REACT_APP_PADDLE_CLIENT_TOKEN=live_9c5e1bb16afca4f9f2969c86119
REACT_APP_PADDLE_ENVIRONMENT=production
```

## PM2 Production Configuration (ecosystem.config.js)

```javascript
env: {
  NODE_ENV: "production",
  PORT: 5000,
  MONGODB_URI: process.env.MONGODB_URI,
  JWT_SECRET: "YY1sui@sz",
  CLIENT_URL: "https://ai-pomo.com",
  
  // Paddle Configuration
  PADDLE_API_KEY: "pdl_live_apikey_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  PADDLE_WEBHOOK_SECRET: "your_webhook_secret_from_paddle_dashboard",
  PADDLE_VENDOR_ID: "pro_01jxjzx642xnz9k48cwt9hv4r2",
  PADDLE_ENVIRONMENT: "production",
  PADDLE_MONTHLY_PRICE_ID: "pri_01jxk01813b3zfs36kcz66x0xg",
  PADDLE_YEARLY_PRICE_ID: "pri_01jxk02vycmv0zjfck99vcxqmt",
  PADDLE_LIFETIME_PRICE_ID: "pri_01jxk0448wa25ssmshke9c7r1m"
}
```

## Next Steps

1. **IMMEDIATELY revoke the API key**: pdl_live_apikey_01jxk0egcxqcc7vgvqr5pr72mp_PfJvaHyc3WzCrmQd5Sh0bR_Asw
2. **Generate a new API key** in your Paddle Dashboard
3. **Get your Product and Price IDs** from Paddle Dashboard for each plan
4. **Set up webhook secret** in Paddle Dashboard
5. **Configure webhook URL**: https://ai-pomo.com/api/paddle/webhook
6. **Test in sandbox mode first** before going live

## Security Checklist

- [ ] Old API key revoked
- [ ] New API key generated and secured
- [ ] Environment variables set (not committed to git)
- [ ] Webhook secret configured
- [ ] Webhook URL tested
- [ ] Product IDs verified
- [ ] Price IDs verified
- [ ] Sandbox testing completed