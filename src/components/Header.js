import React from 'react';
import styled from 'styled-components';
import { FaMoon, FaSun } from 'react-icons/fa';

const Header = ({ darkMode, toggleDarkMode }) => {
  return (
    <HeaderContainer>
      <Logo>
        <LogoIcon>🍅</LogoIcon>
        <LogoText>Pomodoro Timer</LogoText>
      </Logo>
      
      <ThemeToggle onClick={toggleDarkMode}>
        {darkMode ? <FaSun /> : <FaMoon />}
      </ThemeToggle>
    </HeaderContainer>
  );
};

// Styled components
const HeaderContainer = styled.header\`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: var(--header-bg);
  color: var(--header-text);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
\`;

const Logo = styled.div\`
  display: flex;
  align-items: center;
  font-weight: 700;
  font-size: 1.25rem;
\`;

const LogoIcon = styled.span\`
  font-size: 1.5rem;
  margin-right: 0.5rem;
\`;

const LogoText = styled.span\`
  @media (max-width: 480px) {
    display: none;
  }
\`;

const ThemeToggle = styled.button\`
  background: none;
  border: none;
  color: var(--header-text);
  font-size: 1.25rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }
\`;

export default Header;
