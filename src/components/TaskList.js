import React, { useState, useEffect } from 'react';
import styled, { css } from 'styled-components';
import { FaCheck, FaTrash, FaPlay, FaEdit, FaPlus } from 'react-icons/fa';
import { useTask } from '../context/TaskContext';
import { isAuthenticated } from '../utils/auth';
import { taskApi } from '../services/apiService';

const TaskList = () => {
  const { 
    tasks, 
    activeTaskId, 
    addTask, 
    updateTask, 
    deleteTask, 
    setActiveTask, 
    completeTask 
  } = useTask();
  
  const [newTaskTitle, setNewTaskTitle] = useState('');
  const [newTaskPomodoros, setNewTaskPomodoros] = useState(1);
  const [editingTaskId, setEditingTaskId] = useState(null);
  const [editTitle, setEditTitle] = useState('');
  const [editPomodoros, setEditPomodoros] = useState(1);
  const [localTasks, setLocalTasks] = useState(tasks);
  
  // Update local tasks when tasks change
  useEffect(() => {
    setLocalTasks(tasks);
  }, [tasks]);
  
  // Handle adding a new task
  const handleAddTask = (e) => {
    e.preventDefault();
    
    if (newTaskTitle.trim()) {
      addTask({
        title: newTaskTitle.trim(),
        estimatedPomodoros: newTaskPomodoros,
      });
      
      // Reset form
      setNewTaskTitle('');
      setNewTaskPomodoros(1);
    }
  };
  
  // Start editing a task
  const startEditing = (task) => {
    setEditingTaskId(task.id);
    setEditTitle(task.title);
    setEditPomodoros(task.estimatedPomodoros);
  };
  
  // Save task edits
  const saveTaskEdit = () => {
    if (editTitle.trim()) {
      updateTask(editingTaskId, {
        title: editTitle.trim(),
        estimatedPomodoros: editPomodoros,
      });
      
      // Exit edit mode
      setEditingTaskId(null);
    }
  };
  
  // Cancel task editing
  const cancelEditing = () => {
    setEditingTaskId(null);
  };
  
  // Handle task deletion
  const handleDeleteTask = async (taskId) => {
    try {
      if (isAuthenticated()) {
        // Delete task via API if authenticated
        await taskApi.deleteTask(taskId);
        // Update local state immediately
        setLocalTasks(prevTasks => prevTasks.filter(task => task.id !== taskId));
      }
      
      // Delete from context state
      deleteTask(taskId);
    } catch (err) {
      console.error('Error deleting task:', err);
    }
  };
  
  // Filter tasks into active and completed
  const activeTasks = localTasks.filter(task => !task.completed);
  const completedTasks = localTasks.filter(task => task.completed);
  
  return (
    <TaskListContainer>
      <h2>Tasks</h2>
      
      {/* Add task form */}
      <AddTaskForm onSubmit={handleAddTask}>
        <TaskInput
          type="text"
          placeholder="What are you working on?"
          value={newTaskTitle}
          onChange={(e) => setNewTaskTitle(e.target.value)}
        />
        
        <PomodoroInput>
          <label>
            Est. Pomodoros:
            <input
              type="number"
              min="1"
              max="10"
              value={newTaskPomodoros}
              onChange={(e) => setNewTaskPomodoros(parseInt(e.target.value) || 1)}
            />
          </label>
        </PomodoroInput>
        
        <AddButton type="submit">
          <FaPlus /> Add Task
        </AddButton>
      </AddTaskForm>
      
      {/* Active tasks */}
      <TaskSection>
        <h3>Active Tasks</h3>
        
        {activeTasks.length === 0 ? (
          <EmptyMessage>No active tasks. Add a task to get started!</EmptyMessage>
        ) : (
          <TaskItems>
            {activeTasks.map(task => (
              <TaskItem 
                key={task.id} 
                isActive={task.id === activeTaskId}
              >
                {editingTaskId === task.id ? (
                  // Edit mode
                  <EditForm>
                    <TaskInput
                      type="text"
                      value={editTitle}
                      onChange={(e) => setEditTitle(e.target.value)}
                      autoFocus
                    />
                    
                    <PomodoroInput>
                      <label>
                        Est. Pomodoros:
                        <input
                          type="number"
                          min="1"
                          max="10"
                          value={editPomodoros}
                          onChange={(e) => setEditPomodoros(parseInt(e.target.value) || 1)}
                        />
                      </label>
                    </PomodoroInput>
                    
                    <EditActions>
                      <ActionButton onClick={saveTaskEdit}>Save</ActionButton>
                      <ActionButton onClick={cancelEditing}>Cancel</ActionButton>
                    </EditActions>
                  </EditForm>
                ) : (
                  // View mode
                  <>
                    <TaskContent>
                      <TaskTitle>{task.title}</TaskTitle>
                    </TaskContent>
                    
                    <TaskActions>
                      <ActionButton 
                        onClick={() => setActiveTask(task.id)}
                        isActive={task.id === activeTaskId}
                        title="Set as active task"
                      >
                        <FaPlay />
                      </ActionButton>
                      
                      <ActionButton 
                        onClick={() => startEditing(task)}
                        title="Edit task"
                      >
                        <FaEdit />
                      </ActionButton>
                      
                      <ActionButton 
                        onClick={() => completeTask(task.id)}
                        title="Mark as completed"
                      >
                        <FaCheck />
                      </ActionButton>
                      
                      <ActionButton 
                        onClick={() => handleDeleteTask(task.id)}
                        title="Delete task"
                      >
                        <FaTrash />
                      </ActionButton>
                    </TaskActions>
                  </>
                )}
              </TaskItem>
            ))}
          </TaskItems>
        )}
      </TaskSection>
      
      {/* Completed tasks */}
      {completedTasks.length > 0 && (
        <TaskSection>
          <h3>Completed Tasks</h3>
          
          <TaskItems>
            {completedTasks.map(task => (
              <TaskItem 
                key={task.id} 
                isCompleted
              >
                <TaskContent>
                  <TaskTitle>{task.title}</TaskTitle>
                </TaskContent>
                
                <TaskActions>
                  <ActionButton 
                    onClick={() => updateTask(task.id, { completed: false })}
                    title="Mark as active"
                  >
                    <FaPlay />
                  </ActionButton>
                  
                  <ActionButton 
                    onClick={() => handleDeleteTask(task.id)}
                    title="Delete task"
                  >
                    <FaTrash />
                  </ActionButton>
                </TaskActions>
              </TaskItem>
            ))}
          </TaskItems>
        </TaskSection>
      )}
    </TaskListContainer>
  );
};

// Styled components
const taskListContainerStyles = css`
  max-width: 600px;
  margin: 2rem auto;
  padding: 1rem;
`;

const addTaskFormStyles = css`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background-color: var(--card-bg);
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const taskInputStyles = css`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 0.25rem;
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
  }
`;

const pomodoroInputStyles = css`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  input {
    width: 60px;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 0.25rem;
    font-size: 1rem;
    text-align: center;
  }
`;

const addButtonStyles = css`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0.25rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: var(--primary-dark);
  }
`;

const taskSectionStyles = css`
  margin-bottom: 2rem;
`;

const emptyMessageStyles = css`
  text-align: center;
  padding: 2rem;
  color: #666;
  font-style: italic;
`;

const taskItemsStyles = css`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const taskItemStyles = css`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: var(--card-bg);
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  opacity: ${props => props.isCompleted ? 0.6 : 1};
  border-left: ${props => props.isActive ? '4px solid var(--primary-color)' : 'none'};
`;

const taskContentStyles = css`
  flex: 1;
`;

const taskTitleStyles = css`
  font-weight: 500;
  margin-bottom: 0.25rem;
`;

const taskActionsStyles = css`
  display: flex;
  gap: 0.5rem;
`;

const actionButtonStyles = css`
  padding: 0.5rem;
  background: none;
  border: none;
  color: ${props => props.isActive ? 'var(--primary-color)' : '#666'};
  cursor: pointer;
  transition: color 0.2s;
  
  &:hover {
    color: var(--primary-color);
  }
`;

const editFormStyles = css`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const editActionsStyles = css`
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
`;

const TaskListContainer = styled.div`${taskListContainerStyles}`;
const AddTaskForm = styled.form`${addTaskFormStyles}`;
const TaskInput = styled.input`${taskInputStyles}`;
const PomodoroInput = styled.div`${pomodoroInputStyles}`;
const AddButton = styled.button`${addButtonStyles}`;
const TaskSection = styled.section`${taskSectionStyles}`;
const EmptyMessage = styled.div`${emptyMessageStyles}`;
const TaskItems = styled.div`${taskItemsStyles}`;
const TaskItem = styled.div`${taskItemStyles}`;
const TaskContent = styled.div`${taskContentStyles}`;
const TaskTitle = styled.div`${taskTitleStyles}`;
const TaskActions = styled.div`${taskActionsStyles}`;
const ActionButton = styled.button`${actionButtonStyles}`;
const EditForm = styled.div`${editFormStyles}`;
const EditActions = styled.div`${editActionsStyles}`;

export default TaskList;
