import React from 'react';
import styled from 'styled-components';
import { FaClock, FaTasks, FaChartBar } from 'react-icons/fa';

const Navigation = ({ activeTab, setActiveTab }) => {
  return (
    <NavContainer>
      <NavItem 
        isActive={activeTab === 'timer'} 
        onClick={() => setActiveTab('timer')}
      >
        <FaClock />
        <NavText>Timer</NavText>
      </NavItem>
      
      <NavItem 
        isActive={activeTab === 'tasks'} 
        onClick={() => setActiveTab('tasks')}
      >
        <FaTasks />
        <NavText>Tasks</NavText>
      </NavItem>
      
      <NavItem 
        isActive={activeTab === 'stats'} 
        onClick={() => setActiveTab('stats')}
      >
        <FaChartBar />
        <NavText>Stats</NavText>
      </NavItem>
    </NavContainer>
  );
};

// Styled components
const NavContainer = styled.nav\`
  display: flex;
  justify-content: center;
  background-color: var(--nav-bg);
  padding: 0.5rem;
  margin-bottom: 2rem;
\`;

const NavItem = styled.button\`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background: ${props => props.isActive ? 'var(--nav-active-bg)' : 'transparent'};
  color: ${props => props.isActive ? 'var(--nav-active-text)' : 'var(--nav-text)'};
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: ${props => props.isActive ? 'var(--nav-active-bg)' : 'var(--nav-hover-bg)'};
  }
  
  & + & {
    margin-left: 0.5rem;
  }
  
  @media (max-width: 480px) {
    padding: 0.75rem 1rem;
  }
\`;

const NavText = styled.span\`
  margin-top: 0.25rem;
  font-size: 0.85rem;
  
  @media (max-width: 480px) {
    font-size: 0.75rem;
  }
\`;

export default Navigation;
