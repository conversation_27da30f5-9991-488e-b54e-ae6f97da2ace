import React, { useState, useEffect } from 'react';
import styled, { css } from 'styled-components';

const SimpleTaskList = () => {
  // Load tasks from localStorage or use empty array
  const [tasks, setTasks] = useState(() => {
    const savedTasks = localStorage.getItem('pomodoroTasks');
    return savedTasks ? JSON.parse(savedTasks) : [];
  });
  
  // Current active task
  const [activeTaskId, setActiveTaskId] = useState(() => {
    const savedActiveTaskId = localStorage.getItem('pomodoroActiveTaskId');
    return savedActiveTaskId || null;
  });
  
  // New task form state
  const [newTaskTitle, setNewTaskTitle] = useState('');
  const [newTaskPomodoros, setNewTaskPomodoros] = useState(1);
  
  // Save tasks to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('pomodoroTasks', JSON.stringify(tasks));
  }, [tasks]);
  
  // Save active task ID to localStorage
  useEffect(() => {
    if (activeTaskId) {
      localStorage.setItem('pomodoroActiveTaskId', activeTaskId);
    } else {
      localStorage.removeItem('pomodoroActiveTaskId');
    }
  }, [activeTaskId]);
  
  // Add a new task
  const addTask = (e) => {
    e.preventDefault();
    
    if (newTaskTitle.trim()) {
      const newTask = {
        id: Date.now().toString(),
        title: newTaskTitle.trim(),
        completed: false,
        estimatedPomodoros: newTaskPomodoros,
        completedPomodoros: 0,
        createdAt: new Date().toISOString(),
      };
      
      setTasks(prevTasks => [...prevTasks, newTask]);
      
      // Reset form
      setNewTaskTitle('');
      setNewTaskPomodoros(1);
    }
  };
  
  // Delete a task
  const deleteTask = (taskId) => {
    setTasks(prevTasks => prevTasks.filter(task => task.id !== taskId));
    
    // If the deleted task was active, clear the active task
    if (activeTaskId === taskId) {
      setActiveTaskId(null);
    }
  };
  
  // Set the active task
  const setActiveTask = (taskId) => {
    setActiveTaskId(taskId === activeTaskId ? null : taskId);
  };
  
  // Mark a task as completed
  const completeTask = (taskId) => {
    setTasks(prevTasks => 
      prevTasks.map(task => 
        task.id === taskId ? { ...task, completed: true } : task
      )
    );
    
    // If the completed task was active, clear the active task
    if (activeTaskId === taskId) {
      setActiveTaskId(null);
    }
  };
  
  // Filter tasks into active and completed
  const activeTasks = tasks.filter(task => !task.completed);
  const completedTasks = tasks.filter(task => task.completed);
  
  return (
    <TaskListContainer>
      <h2>Tasks</h2>
      
      {/* Add task form */}
      <AddTaskForm onSubmit={addTask}>
        <TaskInput
          type="text"
          placeholder="What are you working on?"
          value={newTaskTitle}
          onChange={(e) => setNewTaskTitle(e.target.value)}
        />
        
        <PomodoroInput>
          <label>
            Est. Pomodoros:
            <input
              type="number"
              min="1"
              max="10"
              value={newTaskPomodoros}
              onChange={(e) => setNewTaskPomodoros(parseInt(e.target.value) || 1)}
            />
          </label>
        </PomodoroInput>
        
        <AddButton type="submit">
          Add Task
        </AddButton>
      </AddTaskForm>
      
      {/* Active tasks */}
      <TaskSection>
        <h3>Active Tasks</h3>
        
        {activeTasks.length === 0 ? (
          <EmptyMessage>No active tasks. Add a task to get started!</EmptyMessage>
        ) : (
          <TaskItems>
            {activeTasks.map(task => (
              <TaskItem 
                key={task.id} 
                isActive={task.id === activeTaskId}
              >
                <TaskContent>
                  <TaskTitle>{task.title}</TaskTitle>
                  <TaskProgress>
                    {task.completedPomodoros} / {task.estimatedPomodoros} pomodoros
                  </TaskProgress>
                </TaskContent>
                
                <TaskActions>
                  <ActionButton 
                    onClick={() => setActiveTask(task.id)}
                    isActive={task.id === activeTaskId}
                    title="Set as active task"
                  >
                    {task.id === activeTaskId ? 'Active' : 'Set Active'}
                  </ActionButton>
                  
                  <ActionButton 
                    onClick={() => completeTask(task.id)}
                    title="Mark as completed"
                  >
                    Complete
                  </ActionButton>
                  
                  <ActionButton 
                    onClick={() => deleteTask(task.id)}
                    title="Delete task"
                  >
                    Delete
                  </ActionButton>
                </TaskActions>
              </TaskItem>
            ))}
          </TaskItems>
        )}
      </TaskSection>
      
      {/* Completed tasks */}
      {completedTasks.length > 0 && (
        <TaskSection>
          <h3>Completed Tasks</h3>
          
          <TaskItems>
            {completedTasks.map(task => (
              <TaskItem 
                key={task.id} 
                isCompleted
              >
                <TaskContent>
                  <TaskTitle>{task.title}</TaskTitle>
                  <TaskProgress>
                    {task.completedPomodoros} / {task.estimatedPomodoros} pomodoros
                  </TaskProgress>
                </TaskContent>
                
                <TaskActions>
                  <ActionButton 
                    onClick={() => setTasks(prevTasks => 
                      prevTasks.map(t => 
                        t.id === task.id ? { ...t, completed: false } : t
                      )
                    )}
                    title="Mark as active"
                  >
                    Reactivate
                  </ActionButton>
                  
                  <ActionButton 
                    onClick={() => deleteTask(task.id)}
                    title="Delete task"
                  >
                    Delete
                  </ActionButton>
                </TaskActions>
              </TaskItem>
            ))}
          </TaskItems>
        </TaskSection>
      )}
    </TaskListContainer>
  );
};

// Styled components
const taskListContainerStyles = css`
  max-width: 600px;
  margin: 2rem auto;
  padding: 1rem;
  
  h2 {
    margin-bottom: 1.5rem;
    text-align: center;
  }
  
  h3 {
    margin: 1.5rem 0 1rem;
    font-weight: 500;
    color: #555;
  }
`;

const addTaskFormStyles = css`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background-color: var(--card-bg);
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const taskInputStyles = css`
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 0.25rem;
  font-size: 1rem;
`;

const pomodoroInputStyles = css`
  display: flex;
  align-items: center;
  
  label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #555;
  }
  
  input {
    width: 3rem;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 0.25rem;
    text-align: center;
  }
`;

const addButtonStyles = css`
  padding: 0.75rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0.25rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: var(--primary-dark);
  }
`;

const taskSectionStyles = css`
  margin-bottom: 2rem;
`;

const taskItemsStyles = css`
  list-style: none;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const taskItemStyles = css`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: ${props => 
    props.isActive ? '#fff8e6' : 
    props.isCompleted ? '#f5f5f5' : 'white'
  };
  border-left: 4px solid ${props => 
    props.isActive ? '#ffc107' : 
    props.isCompleted ? '#4caf50' : '#ddd'
  };
  border-radius: 0.25rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  opacity: ${props => props.isCompleted ? 0.7 : 1};
`;

const taskContentStyles = css`
  flex: 1;
`;

const taskTitleStyles = css`
  font-weight: 500;
  margin-bottom: 0.25rem;
`;

const taskProgressStyles = css`
  font-size: 0.85rem;
  color: #777;
`;

const taskActionsStyles = css`
  display: flex;
  gap: 0.5rem;
`;

const actionButtonStyles = css`
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.25rem;
  background-color: ${props => props.isActive ? '#ffc107' : '#f0f0f0'};
  color: ${props => props.isActive ? '#fff' : '#555'};
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: ${props => props.isActive ? '#e5ac06' : '#e0e0e0'};
  }
`;

const emptyMessageStyles = css`
  text-align: center;
  color: #777;
  font-style: italic;
  padding: 1rem;
`;

const TaskListContainer = styled.div`${taskListContainerStyles}`;
const AddTaskForm = styled.form`${addTaskFormStyles}`;
const TaskInput = styled.input`${taskInputStyles}`;
const PomodoroInput = styled.div`${pomodoroInputStyles}`;
const AddButton = styled.button`${addButtonStyles}`;
const TaskSection = styled.section`${taskSectionStyles}`;
const TaskItems = styled.ul`${taskItemsStyles}`;
const TaskItem = styled.li`${taskItemStyles}`;
const TaskContent = styled.div`${taskContentStyles}`;
const TaskTitle = styled.div`${taskTitleStyles}`;
const TaskProgress = styled.div`${taskProgressStyles}`;
const TaskActions = styled.div`${taskActionsStyles}`;
const ActionButton = styled.button`${actionButtonStyles}`;
const EmptyMessage = styled.p`${emptyMessageStyles}`;

export default SimpleTaskList;
