import React, { useState } from 'react';
import styled from 'styled-components';
import { FaCog, FaTimes } from 'react-icons/fa';
import { useTimer } from '../../pomodoro-timer/src/context/TimerContext';
import { availableSounds } from '../utils/audioUtils';

const Settings = () => {
  const { settings, updateSettings } = useTimer();
  const [isOpen, setIsOpen] = useState(false);
  
  // Local state for form values
  const [formValues, setFormValues] = useState({
    workTime: settings.workTime,
    shortBreakTime: settings.shortBreakTime,
    longBreakTime: settings.longBreakTime,
    longBreakInterval: settings.longBreakInterval,
    autoStartNextSession: settings.autoStartNextSession,
    tickingSound: settings.tickingSound,
    volume: settings.volume,
    selectedSound: settings.selectedSound,
  });
  
  // Toggle settings modal
  const toggleSettings = () => {
    setIsOpen(!isOpen);
    
    // Reset form values when opening
    if (!isOpen) {
      setFormValues({
        workTime: settings.workTime,
        shortBreakTime: settings.shortBreakTime,
        longBreakTime: settings.longBreakTime,
        longBreakInterval: settings.longBreakInterval,
        autoStartNextSession: settings.autoStartNextSession,
        tickingSound: settings.tickingSound,
        volume: settings.volume,
        selectedSound: settings.selectedSound,
      });
    }
  };
  
  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    setFormValues(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : 
              type === 'number' ? parseInt(value) || 0 : value,
    }));
  };
  
  // Save settings
  const saveSettings = (e) => {
    e.preventDefault();
    updateSettings(formValues);
    setIsOpen(false);
  };
  
  return (
    <>
      <SettingsButton onClick={toggleSettings}>
        <FaCog />
      </SettingsButton>
      
      {isOpen && (
        <SettingsModal>
          <SettingsContent>
            <SettingsHeader>
              <h2>Settings</h2>
              <CloseButton onClick={toggleSettings}>
                <FaTimes />
              </CloseButton>
            </SettingsHeader>
            
            <SettingsForm onSubmit={saveSettings}>
              <SettingsSection>
                <h3>Timer</h3>
                
                <FormGroup>
                  <label htmlFor="workTime">Focus Time (minutes)</label>
                  <input
                    type="number"
                    id="workTime"
                    name="workTime"
                    min="1"
                    max="60"
                    value={formValues.workTime}
                    onChange={handleInputChange}
                  />
                </FormGroup>
                
                <FormGroup>
                  <label htmlFor="shortBreakTime">Short Break (minutes)</label>
                  <input
                    type="number"
                    id="shortBreakTime"
                    name="shortBreakTime"
                    min="1"
                    max="30"
                    value={formValues.shortBreakTime}
                    onChange={handleInputChange}
                  />
                </FormGroup>
                
                <FormGroup>
                  <label htmlFor="longBreakTime">Long Break (minutes)</label>
                  <input
                    type="number"
                    id="longBreakTime"
                    name="longBreakTime"
                    min="1"
                    max="60"
                    value={formValues.longBreakTime}
                    onChange={handleInputChange}
                  />
                </FormGroup>
                
                <FormGroup>
                  <label htmlFor="longBreakInterval">Long Break After (pomodoros)</label>
                  <input
                    type="number"
                    id="longBreakInterval"
                    name="longBreakInterval"
                    min="1"
                    max="10"
                    value={formValues.longBreakInterval}
                    onChange={handleInputChange}
                  />
                </FormGroup>
                
                <CheckboxGroup>
                  <input
                    type="checkbox"
                    id="autoStartNextSession"
                    name="autoStartNextSession"
                    checked={formValues.autoStartNextSession}
                    onChange={handleInputChange}
                  />
                  <label htmlFor="autoStartNextSession">Auto-start next session</label>
                </CheckboxGroup>
              </SettingsSection>
              
              <SettingsSection>
                <h3>Sound</h3>
                
                <FormGroup>
                  <label htmlFor="selectedSound">Timer Completion Sound</label>
                  <select
                    id="selectedSound"
                    name="selectedSound"
                    value={formValues.selectedSound}
                    onChange={handleInputChange}
                  >
                    {availableSounds.map(sound => (
                      <option key={sound.id} value={sound.id}>
                        {sound.name}
                      </option>
                    ))}
                  </select>
                </FormGroup>
                
                <FormGroup>
                  <label htmlFor="volume">Volume ({formValues.volume}%)</label>
                  <input
                    type="range"
                    id="volume"
                    name="volume"
                    min="0"
                    max="100"
                    value={formValues.volume}
                    onChange={handleInputChange}
                  />
                </FormGroup>
                
                <CheckboxGroup>
                  <input
                    type="checkbox"
                    id="tickingSound"
                    name="tickingSound"
                    checked={formValues.tickingSound}
                    onChange={handleInputChange}
                  />
                  <label htmlFor="tickingSound">Play ticking sound during focus time</label>
                </CheckboxGroup>
              </SettingsSection>
              
              <ButtonGroup>
                <CancelButton type="button" onClick={toggleSettings}>
                  Cancel
                </CancelButton>
                <SaveButton type="submit">
                  Save Settings
                </SaveButton>
              </ButtonGroup>
            </SettingsForm>
          </SettingsContent>
        </SettingsModal>
      )}
    </>
  );
};

// Styled components
const SettingsButton = styled.button\`
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  background-color: #555;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  border: none;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #444;
  }
\`;

const SettingsModal = styled.div\`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
\`;

const SettingsContent = styled.div\`
  background-color: white;
  border-radius: 0.5rem;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
\`;

const SettingsHeader = styled.div\`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #eee;
  
  h2 {
    margin: 0;
    font-size: 1.5rem;
  }
\`;

const CloseButton = styled.button\`
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #777;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    color: #333;
  }
\`;

const SettingsForm = styled.form\`
  padding: 1.5rem;
\`;

const SettingsSection = styled.div\`
  margin-bottom: 2rem;
  
  h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    color: #555;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.5rem;
  }
\`;

const FormGroup = styled.div\`
  margin-bottom: 1rem;
  
  label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #555;
  }
  
  input, select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 0.25rem;
    font-size: 1rem;
  }
  
  input[type="range"] {
    padding: 0.5rem 0;
  }
\`;

const CheckboxGroup = styled.div\`
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  
  input {
    margin-right: 0.75rem;
  }
  
  label {
    margin-bottom: 0;
    font-size: 0.9rem;
    color: #555;
  }
\`;

const ButtonGroup = styled.div\`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
\`;

const Button = styled.button\`
  padding: 0.75rem 1.5rem;
  border-radius: 0.25rem;
  font-size: 1rem;
  cursor: pointer;
  border: none;
  transition: background-color 0.2s;
\`;

const SaveButton = styled(Button)\`
  background-color: #4caf50;
  color: white;
  
  &:hover {
    background-color: #43a047;
  }
\`;

const CancelButton = styled(Button)\`
  background-color: #f0f0f0;
  color: #555;
  
  &:hover {
    background-color: #e0e0e0;
  }
\`;

export default Settings;
