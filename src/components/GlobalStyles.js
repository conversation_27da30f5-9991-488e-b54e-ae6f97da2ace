import { createGlobalStyle } from 'styled-components';

const GlobalStyles = createGlobalStyle`
  :root {
    --bg-color: ${props => props.theme['--bg-color']};
    --text-color: ${props => props.theme['--text-color']};
    --card-bg: ${props => props.theme['--card-bg']};
    --header-bg: ${props => props.theme['--header-bg']};
    --header-text: ${props => props.theme['--header-text']};
    --nav-bg: ${props => props.theme['--nav-bg']};
    --nav-text: ${props => props.theme['--nav-text']};
    --nav-hover-bg: ${props => props.theme['--nav-hover-bg']};
    --nav-active-bg: ${props => props.theme['--nav-active-bg']};
    --nav-active-text: ${props => props.theme['--nav-active-text']};
  }
  
  body {
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: background-color 0.3s, color 0.3s;
  }
`;

export default GlobalStyles;
