import React, { useEffect } from 'react';
import styled, { css } from 'styled-components';
import { FaPlay, FaPause, FaStop, FaForward } from 'react-icons/fa';
import { useTimer } from '../../pomodoro-timer/src/context/TimerContext';
import { useTask } from '../context/TaskContext';
import { startTicking, stopTicking, updateTickingVolume } from '../utils/audioUtils';
import { pomodoroApi } from '../services/pomodoroApi';
import { taskApi } from '../services/taskApi';
import { isAuthenticated } from '../utils/auth';

const Timer = () => {
  const {
    timerState,
    settings,
    startTimer,
    pauseTimer,
    resetTimer,
    skipSession,
    formatTime
  } = useTimer();

  const {
    getActiveTask,
    incrementTaskPomodoros
  } = useTask();

  const activeTask = getActiveTask();

  // Handle ticking sound
  useEffect(() => {
    if (timerState.isRunning && timerState.currentSession === 'work' && settings.tickingSound) {
      startTicking(settings.volume / 100);
    } else {
      stopTicking();
    }

    return () => {
      stopTicking();
    };
  }, [timerState.isRunning, timerState.currentSession, settings.tickingSound, settings.volume]);

  // Update ticking volume when settings change
  useEffect(() => {
    updateTickingVolume(settings.volume / 100);
  }, [settings.volume]);

  // Handle pomodoro completion
  useEffect(() => {
    const isWorkSession = timerState.currentSession === 'work';
    const justCompleted = timerState.timeRemaining === 0 && !timerState.isRunning && !timerState.isPaused;

    console.log(`[Timer] Checking pomodoro completion. isWorkSession: ${isWorkSession}, justCompleted: ${justCompleted}, activeTask: ${activeTask ? 'exists' : 'null'}`);

    if (isWorkSession && justCompleted && activeTask) {
      console.log(`[Timer] Pomodoro completed for task:`, activeTask);

      // Create pomodoro record in database if authenticated
      if (isAuthenticated()) {
        console.log(`[Timer] User is authenticated, creating pomodoro record in database`);
        try {
          pomodoroApi.createPomodoro({
            taskId: activeTask.id,
            projectId: activeTask.projectId,
            startTime: new Date(Date.now() - settings.workTime * 60 * 1000),
            endTime: new Date(),
            duration: settings.workTime,
            completed: true,
            interrupted: false
          }).then((response) => {
            console.log(`[Timer] Pomodoro created successfully:`, response);
          }).catch(error => {
            console.error('[Timer] Error saving pomodoro:', error);
          });
        } catch (error) {
          console.error('[Timer] Error in pomodoro completion:', error);
        }
      }
    }
  }, [timerState, activeTask, settings.workTime]);

  // Get session name for display
  const getSessionName = () => {
    switch (timerState.currentSession) {
      case 'work':
        return 'Focus Time';
      case 'shortBreak':
        return 'Short Break';
      case 'longBreak':
        return 'Long Break';
      default:
        return 'Focus Time';
    }
  };

  // Get color based on current session
  const getSessionColor = () => {
    switch (timerState.currentSession) {
      case 'work':
        return '#d95550'; // Red for work
      case 'shortBreak':
        return '#4c9195'; // Teal for short break
      case 'longBreak':
        return '#457ca3'; // Blue for long break
      default:
        return '#d95550';
    }
  };

  console.log('TIMER DISPLAY:', timerState.timeRemaining, settings.workTime);

  return (
    <TimerContainer sessionColor={getSessionColor()}>
      <SessionName>{getSessionName()}</SessionName>

      <TimerDisplay>
        {formatTime(timerState.timeRemaining)}
      </TimerDisplay>

      <PomodoroCount>
        {timerState.currentSession === 'work' && (
          <span>Pomodoro {timerState.pomodoroCount + 1} / {settings.longBreakInterval}</span>
        )}
      </PomodoroCount>

      {activeTask && (
        <ActiveTask>
          Working on: <TaskTitle>{activeTask.title}</TaskTitle>
          <TaskProgress>
            {activeTask.completedPomodoros} / {activeTask.estimatedPomodoros} pomodoros
          </TaskProgress>
        </ActiveTask>
      )}

      <Controls>
        {!timerState.isRunning ? (
          <ControlButton onClick={startTimer}>
            <FaPlay />
          </ControlButton>
        ) : (
          <ControlButton onClick={pauseTimer}>
            <FaPause />
          </ControlButton>
        )}

        <ControlButton onClick={resetTimer}>
          <FaStop />
        </ControlButton>

        <ControlButton onClick={skipSession}>
          <FaForward />
        </ControlButton>
      </Controls>
    </TimerContainer>
  );
};

// Styled components
const timerContainerStyles = css`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  border-radius: 1rem;
  background-color: ${props => props.sessionColor || '#d95550'};
  color: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: background-color 0.5s ease;
  max-width: 500px;
  margin: 0 auto;
`;

const sessionNameStyles = css`
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 500;
`;

const timerDisplayStyles = css`
  font-size: 5rem;
  font-weight: 700;
  margin: 1rem 0;
  font-variant-numeric: tabular-nums;
  letter-spacing: 2px;
`;

const pomodoroCountStyles = css`
  font-size: 1rem;
  margin-bottom: 1.5rem;
  opacity: 0.9;
`;

const activeTaskStyles = css`
  background-color: rgba(255, 255, 255, 0.2);
  padding: 1rem;
  border-radius: 0.5rem;
  margin: 1rem 0;
  width: 100%;
  text-align: center;
`;

const taskTitleStyles = css`
  font-weight: 600;
  font-size: 1.1rem;
  margin: 0.5rem 0;
`;

const taskProgressStyles = css`
  font-size: 0.9rem;
  opacity: 0.9;
`;

const controlsStyles = css`
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
`;

const controlButtonStyles = css`
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 3.5rem;
  height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  font-size: 1.2rem;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(255, 255, 255, 0.3);
  }

  &:active {
    transform: scale(0.95);
  }
`;

const TimerContainer = styled.div`${timerContainerStyles}`;
const SessionName = styled.h2`${sessionNameStyles}`;
const TimerDisplay = styled.div`${timerDisplayStyles}`;
const PomodoroCount = styled.div`${pomodoroCountStyles}`;
const ActiveTask = styled.div`${activeTaskStyles}`;
const TaskTitle = styled.div`${taskTitleStyles}`;
const TaskProgress = styled.div`${taskProgressStyles}`;
const Controls = styled.div`${controlsStyles}`;
const ControlButton = styled.button`${controlButtonStyles}`;

export default Timer;
