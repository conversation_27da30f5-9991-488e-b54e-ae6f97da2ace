import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

const PomodoroCountDebug = () => {
  // Load stats from localStorage
  const [stats, setStats] = useState(() => {
    const savedStats = localStorage.getItem('pomodoroStats');
    return savedStats ? JSON.parse(savedStats) : {
      totalPomodoros: 0,
      pomodorosByDate: {},
      completedTasks: 0,
      experiencePoints: 0,
      unlockedAchievements: [],
      currentStreak: 0,
      longestStreak: 0,
      maxPomodorosInDay: 0
    };
  });

  // Get today's date in YYYY-MM-DD format
  const today = new Date().toISOString().split('T')[0];

  // Calculate pomodoros for today
  const pomodorosToday = stats.pomodorosByDate[today] || 0;

  // Function to manually increment pomodoro count for testing
  const incrementPomodoroCount = () => {
    // Update pomodoro count
    const todayCount = (stats.pomodorosByDate[today] || 0) + 1;

    // Update stats
    const newStats = {
      ...stats,
      totalPomodoros: stats.totalPomodoros + 1,
      pomodorosByDate: {
        ...stats.pomodorosByDate,
        [today]: todayCount
      },
      maxPomodorosInDay: Math.max(stats.maxPomodorosInDay, todayCount)
    };

    // Update state
    setStats(newStats);

    // Save to localStorage
    localStorage.setItem('pomodoroStats', JSON.stringify(newStats));
  };

  // Function to reset pomodoro count for testing
  const resetPomodoroCount = () => {
    const newStats = {
      ...stats,
      totalPomodoros: 0,
      pomodorosByDate: {}
    };

    // Update state
    setStats(newStats);

    // Save to localStorage
    localStorage.setItem('pomodoroStats', JSON.stringify(newStats));
  };

  return (
    <DebugContainer>
      <h3>Pomodoro Count Debug</h3>

      <DebugInfo>
        <DebugRow>
          <DebugLabel>Total Pomodoros:</DebugLabel>
          <DebugValue>{stats.totalPomodoros}</DebugValue>
        </DebugRow>

        <DebugRow>
          <DebugLabel>Pomodoros Today:</DebugLabel>
          <DebugValue>{pomodorosToday}</DebugValue>
        </DebugRow>

        <DebugRow>
          <DebugLabel>Today's Date:</DebugLabel>
          <DebugValue>{today}</DebugValue>
        </DebugRow>

        <DebugRow>
          <DebugLabel>Max Pomodoros in a Day:</DebugLabel>
          <DebugValue>{stats.maxPomodorosInDay}</DebugValue>
        </DebugRow>
      </DebugInfo>

      <DebugActions>
        <DebugButton onClick={incrementPomodoroCount}>
          Increment Pomodoro Count
        </DebugButton>

        <DebugButton onClick={resetPomodoroCount} isReset>
          Reset Pomodoro Count
        </DebugButton>
      </DebugActions>

      <DebugNote>
        Note: This component is for debugging purposes only. It directly manipulates the pomodoro count in localStorage.
      </DebugNote>
    </DebugContainer>
  );
};

// Styled components
const DebugContainer = styled.div`
  margin: 2rem 0;
  padding: 1.5rem;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 0.5rem;

  h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    color: #343a40;
  }
`;

const DebugInfo = styled.div`
  margin-bottom: 1.5rem;
`;

const DebugRow = styled.div`
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #dee2e6;

  &:last-child {
    border-bottom: none;
  }
`;

const DebugLabel = styled.div`
  font-weight: 500;
  color: #495057;
`;

const DebugValue = styled.div`
  font-family: monospace;
  color: #007bff;
  font-weight: 600;
`;

const DebugActions = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
`;

const DebugButton = styled.button`
  padding: 0.5rem 1rem;
  background-color: ${props => props.isReset ? '#dc3545' : '#28a745'};
  color: white;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  font-weight: 500;

  &:hover {
    background-color: ${props => props.isReset ? '#c82333' : '#218838'};
  }
`;

const DebugNote = styled.div`
  font-size: 0.8rem;
  color: #6c757d;
  font-style: italic;
`;

export default PomodoroCountDebug;
