import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { FaArrowLeft, FaPlus, FaCheck, FaTrash, FaEdit } from 'react-icons/fa';
import SimpleTimerFinal from '../components/SimpleTimerFinal';
import { useGlobalTimer } from '../contexts/GlobalTimerContext';
import { useSettings } from '../context/SettingsContext';
import { isAuthenticated } from '../services/authService';
import { pomodoroApi, taskApi } from '../services/apiService';
import StandaloneTodoList from '../components/StandaloneTodoList';

const StandalonePomodoroPage = () => {
  const {
    isRunning,
    isPaused,
    currentSession,
    pomodoroCount,
    startTimer,
    pauseTimer,
    resumeTimer,
    resetTimer,
    skipTimer
  } = useGlobalTimer();

  const { settings } = useSettings();
  const [selectedTodoId, setSelectedTodoId] = useState(null);
  const [selectedTodoTitle, setSelectedTodoTitle] = useState(null);

  // Handle pomodoro completion
  const handlePomodoroCompleted = async (completionData) => {
    console.log('Pomodoro completed:', completionData);

    // If this was a work session and it was completed normally (not interrupted)
    if (currentSession === 'work' && !completionData.wasInterrupted) {
      try {
        // Record the pomodoro in the database if authenticated
        if (isAuthenticated()) {
          await pomodoroApi.createPomodoro({
            taskId: selectedTodoId, // This can be null for standalone pomodoros
            startTime: new Date(Date.now() - settings.workTime * 60 * 1000),
            endTime: new Date(),
            duration: settings.workTime,
            completed: true,
            interrupted: false,
            isStandalone: true // Flag for standalone pomodoros
          });
          console.log('Standalone pomodoro recorded successfully');

          // If a todo was selected, increment its completed pomodoros count
          if (selectedTodoId) {
            try {
              await taskApi.incrementStandaloneTaskPomodoros(selectedTodoId);
              console.log('Incremented todo completed pomodoros count');
            } catch (error) {
              console.error('Error incrementing todo completed pomodoros:', error);
            }
          }
        }
      } catch (error) {
        console.error('Error recording standalone pomodoro:', error);
      }
    }
  };

  // Handle todo selection for the timer
  const handleTodoSelect = (todoId, todoTitle) => {
    setSelectedTodoId(todoId);
    setSelectedTodoTitle(todoTitle);
  };

  // Clear selected todo
  const clearSelectedTodo = () => {
    setSelectedTodoId(null);
    setSelectedTodoTitle(null);
  };

  return (
    <PageContainer>
      <Header>
        <BackButton as={Link} to="/app">
          <FaArrowLeft /> Back to App
        </BackButton>
        <PageTitle>Immersive Pomodoro</PageTitle>
      </Header>

      <ContentContainer>
        <TimerSection>
          <SimpleTimerFinal
            onComplete={handlePomodoroCompleted}
            taskId={selectedTodoId}
            taskName={selectedTodoTitle}
          />

          {selectedTodoId && (
            <SelectedTodoInfo>
              <p>Selected Todo: <strong>{selectedTodoTitle}</strong></p>
              <ClearSelectionButton onClick={clearSelectedTodo}>
                Clear Selection
              </ClearSelectionButton>
            </SelectedTodoInfo>
          )}
        </TimerSection>

        <TodoSection>
          <SectionTitle>Todo List</SectionTitle>
          <StandaloneTodoList
            onTodoSelect={handleTodoSelect}
            selectedTodoId={selectedTodoId}
          />
        </TodoSection>
      </ContentContainer>
    </PageContainer>
  );
};

// Styled components
const PageContainer = styled.div`
  min-height: 100vh;
  background-color: ${props => props.theme['--bg-color']};
  color: ${props => props.theme['--text-color']};
  display: flex;
  flex-direction: column;
`;

const Header = styled.header`
  display: flex;
  align-items: center;
  padding: 1rem 2rem;
  background-color: ${props => props.theme['--header-bg'] || '#1e1e2e'};
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const BackButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: ${props => props.theme['--text-color']};
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  text-decoration: none;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const PageTitle = styled.h1`
  margin: 0 auto;
  font-size: 1.5rem;
  font-weight: 600;
`;

const ContentContainer = styled.div`
  display: flex;
  flex: 1;
  padding: 2rem;
  gap: 2rem;

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const TimerSection = styled.section`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const TodoSection = styled.section`
  flex: 1;
  display: flex;
  flex-direction: column;
`;

const SectionTitle = styled.h2`
  font-size: 1.25rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid ${props => props.theme['--border-color'] || '#e0e0e0'};
`;

const SelectedTodoInfo = styled.div`
  margin-top: 1rem;
  padding: 1rem;
  background-color: ${props => props.theme['--card-bg'] || '#f5f5f5'};
  border-radius: 8px;
  text-align: center;
`;

const ClearSelectionButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme['--accent-color'] || '#d95550'};
  cursor: pointer;
  margin-top: 0.5rem;

  &:hover {
    text-decoration: underline;
  }
`;

export default StandalonePomodoroPage;
