// Get today's date in YYYY-MM-DD format
export const getTodayDateString = () => {
  const today = new Date();
  return today.toISOString().split('T')[0];
};

// Get yesterday's date in YYYY-MM-DD format
export const getYesterdayDateString = () => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return yesterday.toISOString().split('T')[0];
};

// Get date for a specific day in the past (days ago)
export const getDateStringDaysAgo = (daysAgo) => {
  const date = new Date();
  date.setDate(date.getDate() - daysAgo);
  return date.toISOString().split('T')[0];
};

// Get start of current week date in YYYY-MM-DD format (Sunday)
export const getStartOfWeekDateString = () => {
  const now = new Date();
  const dayOfWeek = now.getDay(); // 0 = Sunday, 6 = Saturday
  const startOfWeek = new Date(now);
  startOfWeek.setDate(now.getDate() - dayOfWeek);
  return startOfWeek.toISOString().split('T')[0];
};

// Get start of current month date in YYYY-MM-DD format
export const getStartOfMonthDateString = () => {
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  return startOfMonth.toISOString().split('T')[0];
};

// Calculate points for a completed pomodoro
export const calculatePomodoroPoints = (isUninterrupted = true) => {
  // Base points for completing a pomodoro
  const basePoints = 10;

  // Bonus for completing without interruption (no pauses)
  const uninterruptedBonus = isUninterrupted ? 5 : 0;

  return basePoints + uninterruptedBonus;
};

// Calculate streak bonus points
export const calculateStreakBonus = (streakDays) => {
  // Bonus points for maintaining a streak
  if (streakDays >= 30) {
    return 50; // Monthly streak
  } else if (streakDays >= 7) {
    return 20; // Weekly streak
  } else if (streakDays >= 3) {
    return 10; // Few days streak
  }
  return 0;
};

// Define achievements
export const achievements = [
  {
    id: 'first_pomodoro',
    name: 'First Focus',
    description: 'Complete your first Pomodoro',
    condition: (stats) => stats.totalPomodoros >= 1,
    icon: '🍅',
  },
  {
    id: 'pomodoro_master',
    name: 'Pomodoro Master',
    description: 'Complete 100 Pomodoros',
    condition: (stats) => stats.totalPomodoros >= 100,
    icon: '🏆',
  },
  {
    id: 'daily_five',
    name: 'High Five',
    description: 'Complete 5 Pomodoros in a single day',
    condition: (stats) => stats.maxPomodorosInDay >= 5,
    icon: '✋',
  },
  {
    id: 'weekly_streak',
    name: 'Weekly Warrior',
    description: 'Maintain a 7-day streak',
    condition: (stats) => stats.longestStreak >= 7,
    icon: '🔥',
  },
  {
    id: 'monthly_streak',
    name: 'Monthly Master',
    description: 'Maintain a 30-day streak',
    condition: (stats) => stats.longestStreak >= 30,
    icon: '🌟',
  },
  {
    id: 'task_completer',
    name: 'Task Terminator',
    description: 'Complete 10 tasks',
    condition: (stats) => stats.completedTasks >= 10,
    icon: '✅',
  },
];

// Check for newly unlocked achievements
export const checkNewAchievements = (stats, unlockedAchievements) => {
  const newlyUnlocked = [];

  achievements.forEach(achievement => {
    const isUnlocked = unlockedAchievements.includes(achievement.id);
    const shouldUnlock = achievement.condition(stats);

    if (!isUnlocked && shouldUnlock) {
      newlyUnlocked.push(achievement.id);
    }
  });

  return newlyUnlocked;
};

// Get achievement details by ID
export const getAchievementById = (achievementId) => {
  return achievements.find(a => a.id === achievementId);
};

// Calculate level based on experience points
export const calculateLevel = (experiencePoints) => {
  // Simple level calculation: level = sqrt(xp / 100) + 1
  return Math.floor(Math.sqrt(experiencePoints / 100)) + 1;
};

// Calculate experience needed for next level
export const experienceForNextLevel = (currentLevel) => {
  return 100 * Math.pow(currentLevel, 2);
};

// Calculate progress percentage to next level
export const levelProgressPercentage = (experiencePoints) => {
  const currentLevel = calculateLevel(experiencePoints);
  const currentLevelXP = 100 * Math.pow(currentLevel - 1, 2);
  const nextLevelXP = 100 * Math.pow(currentLevel, 2);

  const xpInCurrentLevel = experiencePoints - currentLevelXP;
  const xpNeededForNextLevel = nextLevelXP - currentLevelXP;

  return (xpInCurrentLevel / xpNeededForNextLevel) * 100;
};
