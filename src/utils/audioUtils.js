// Audio context instance
let audioContext = null;

// Initialize audio context (must be called after user interaction)
export const initAudioContext = () => {
  if (!audioContext) {
    try {
      const AudioContext = window.AudioContext || window.webkitAudioContext;
      audioContext = new AudioContext();
    } catch (error) {
      console.error('Web Audio API is not supported in this browser', error);
    }
  }
  return audioContext;
};

// Sound buffers cache
const soundBuffers = {};

// Load a sound file
export const loadSound = async (soundName) => {
  if (!audioContext) {
    initAudioContext();
  }
  
  if (soundBuffers[soundName]) {
    return soundBuffers[soundName];
  }
  
  try {
    const response = await fetch(`/sounds/${soundName}.mp3`);
    const arrayBuffer = await response.arrayBuffer();
    const audioBuffer = await audioContext.decodeAudioBuffer(arrayBuffer);
    soundBuffers[soundName] = audioBuffer;
    return audioBuffer;
  } catch (error) {
    console.error(`Error loading sound: ${soundName}`, error);
    return null;
  }
};

// Play a sound with volume control
export const playSound = (soundName, volume = 1.0) => {
  if (!audioContext) {
    initAudioContext();
  }
  
  // Resume audio context if it's suspended (browser policy)
  if (audioContext.state === 'suspended') {
    audioContext.resume();
  }
  
  const buffer = soundBuffers[soundName];
  if (!buffer) {
    console.warn(`Sound not loaded: ${soundName}`);
    return;
  }
  
  // Create source node
  const source = audioContext.createBufferSource();
  source.buffer = buffer;
  
  // Create gain node for volume control
  const gainNode = audioContext.createGain();
  gainNode.gain.value = volume;
  
  // Connect nodes
  source.connect(gainNode);
  gainNode.connect(audioContext.destination);
  
  // Play the sound
  source.start(0);
  
  return source;
};

// Preload all sounds
export const preloadSounds = async (soundNames) => {
  const loadPromises = soundNames.map(name => loadSound(name));
  return Promise.all(loadPromises);
};

// Available sounds
export const availableSounds = [
  { id: 'bell', name: 'Bell' },
  { id: 'digital', name: 'Digital Alarm' },
  { id: 'kitchen', name: 'Kitchen Timer' },
  { id: 'bird', name: 'Bird Chirp' },
];

// Ticking sound
let tickingSource = null;
let tickingGain = null;

// Start ticking sound
export const startTicking = (volume = 0.2) => {
  if (!audioContext) {
    initAudioContext();
  }
  
  // Stop any existing ticking
  stopTicking();
  
  // Load ticking sound if not already loaded
  if (!soundBuffers['ticking']) {
    loadSound('ticking').then(buffer => {
      if (buffer) {
        playTickingLoop(buffer, volume);
      }
    });
  } else {
    playTickingLoop(soundBuffers['ticking'], volume);
  }
};

// Play ticking sound in a loop
const playTickingLoop = (buffer, volume) => {
  if (!audioContext) return;
  
  // Resume audio context if it's suspended
  if (audioContext.state === 'suspended') {
    audioContext.resume();
  }
  
  // Create source node
  tickingSource = audioContext.createBufferSource();
  tickingSource.buffer = buffer;
  tickingSource.loop = true;
  
  // Create gain node for volume control
  tickingGain = audioContext.createGain();
  tickingGain.gain.value = volume;
  
  // Connect nodes
  tickingSource.connect(tickingGain);
  tickingGain.connect(audioContext.destination);
  
  // Start playing
  tickingSource.start(0);
};

// Stop ticking sound
export const stopTicking = () => {
  if (tickingSource) {
    try {
      tickingSource.stop();
    } catch (error) {
      // Ignore errors when stopping
    }
    tickingSource = null;
    tickingGain = null;
  }
};

// Update ticking volume
export const updateTickingVolume = (volume) => {
  if (tickingGain) {
    tickingGain.gain.value = volume;
  }
};
