import React, { createContext, useContext, useState, useEffect } from 'react';

// Create the context
const TaskContext = createContext();

// Custom hook to use the task context
export const useTask = () => {
  const context = useContext(TaskContext);
  if (!context) {
    throw new Error('useTask must be used within a TaskProvider');
  }
  return context;
};

// Task provider component
export const TaskProvider = ({ children }) => {
  // Load tasks from localStorage or use empty array
  const [tasks, setTasks] = useState(() => {
    const savedTasks = localStorage.getItem('pomodoroTasks');
    return savedTasks ? JSON.parse(savedTasks) : [];
  });

  // Current active task
  const [activeTaskId, setActiveTaskId] = useState(() => {
    const savedActiveTaskId = localStorage.getItem('pomodoroActiveTaskId');
    return savedActiveTaskId || null;
  });

  // Save tasks to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('pomodoroTasks', JSON.stringify(tasks));
  }, [tasks]);

  // Save active task ID to localStorage
  useEffect(() => {
    if (activeTaskId) {
      localStorage.setItem('pomodoroActiveTaskId', activeTaskId);
    } else {
      localStorage.removeItem('pomodoroActiveTaskId');
    }
  }, [activeTaskId]);

  // Add a new task
  const addTask = (taskData) => {
    const newTask = {
      id: Date.now().toString(),
      title: taskData.title,
      completed: false,
      estimatedPomodoros: taskData.estimatedPomodoros || 1,
      completedPomodoros: 0,
      createdAt: new Date().toISOString(),
    };

    setTasks(prevTasks => [...prevTasks, newTask]);
    return newTask.id;
  };

  // Update a task
  const updateTask = (taskId, updates) => {
    setTasks(prevTasks => 
      prevTasks.map(task => 
        task.id === taskId ? { ...task, ...updates } : task
      )
    );
  };

  // Delete a task
  const deleteTask = (taskId) => {
    setTasks(prevTasks => prevTasks.filter(task => task.id !== taskId));
    
    // If the deleted task was active, clear the active task
    if (activeTaskId === taskId) {
      setActiveTaskId(null);
    }
  };

  // Set the active task
  const setActiveTask = (taskId) => {
    setActiveTaskId(taskId);
  };

  // Get the active task
  const getActiveTask = () => {
    return tasks.find(task => task.id === activeTaskId) || null;
  };

  // Increment the actual pomodoros count for a task
  const incrementTaskPomodoros = (taskId) => {
    if (!taskId) return;
    
    setTasks(prevTasks => 
      prevTasks.map(task => 
        task.id === taskId 
          ? { ...task, completedPomodoros: task.completedPomodoros + 1 } 
          : task
      )
    );
  };

  // Mark a task as completed
  const completeTask = (taskId) => {
    updateTask(taskId, { completed: true });
    
    // If the completed task was active, clear the active task
    if (activeTaskId === taskId) {
      setActiveTaskId(null);
    }
  };

  // Context value
  const value = {
    tasks,
    activeTaskId,
    addTask,
    updateTask,
    deleteTask,
    setActiveTask,
    getActiveTask,
    incrementTaskPomodoros,
    completeTask,
  };

  return (
    <TaskContext.Provider value={value}>
      {children}
    </TaskContext.Provider>
  );
};

export default TaskContext;
