import React, { useState, useEffect, useRef } from 'react';
import styled, { ThemeProvider } from 'styled-components';
import GlobalStyles from './components/GlobalStyles';
import SimpleTaskList from './components/SimpleTaskList';
import './App.css';
import { TimerProvider } from '../pomodoro-timer/src/context/TimerContext';
import Timer from './components/Timer';

// Default settings
// const DEFAULT_SETTINGS = {
//   workTime: 25,
//   shortBreakTime: 5,
//   longBreakTime: 15,
//   autoStartNextSession: false,
//   completionSound: true,
//   volume: 0.5
// };

// Theme definitions
const lightTheme = {
  '--bg-color': '#f5f5f5',
  '--text-color': '#333',
  '--card-bg': '#ffffff',
  '--header-bg': '#ffffff',
  '--header-text': '#333',
  '--nav-bg': '#ffffff',
  '--nav-text': '#555',
  '--nav-hover-bg': '#f0f0f0',
  '--nav-active-bg': '#d95550',
  '--nav-active-text': '#ffffff',
};

const darkTheme = {
  '--bg-color': '#121212',
  '--text-color': '#e0e0e0',
  '--card-bg': '#1e1e1e',
  '--header-bg': '#1e1e1e',
  '--header-text': '#e0e0e0',
  '--nav-bg': '#1e1e1e',
  '--nav-text': '#b0b0b0',
  '--nav-hover-bg': '#2c2c2c',
  '--nav-active-bg': '#d95550',
  '--nav-active-text': '#ffffff',
};

function App() {
  // State for active tab
  const [activeTab, setActiveTab] = useState('timer');

  // State for dark mode
  const [darkMode, setDarkMode] = useState(() => {
    const savedMode = localStorage.getItem('pomodoroDarkMode');
    return savedMode ? JSON.parse(savedMode) : false;
  });

  // Theme definitions (keep as is)
  const theme = darkMode ? darkTheme : lightTheme;

  // Toggle dark mode
  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
  };

  return (
    <TimerProvider>
      <ThemeProvider theme={theme}>
        <GlobalStyles />
        <AppContainer>
          <Header>
            <h1>🍅 Pomodoro Timer</h1>
            <ThemeToggle onClick={toggleDarkMode}>
              Toggle {darkMode ? 'Light' : 'Dark'} Mode
            </ThemeToggle>
          </Header>

          <Navigation>
            <NavButton
              isActive={activeTab === 'timer'}
              onClick={() => setActiveTab('timer')}
            >
              Timer
            </NavButton>
            <NavButton
              isActive={activeTab === 'tasks'}
              onClick={() => setActiveTab('tasks')}
            >
              Tasks
            </NavButton>
          </Navigation>

          <MainContent>
            {activeTab === 'timer' && <Timer />}
            {activeTab === 'tasks' && <SimpleTaskList />}
          </MainContent>
        </AppContainer>
      </ThemeProvider>
    </TimerProvider>
  );
}

export default App;
