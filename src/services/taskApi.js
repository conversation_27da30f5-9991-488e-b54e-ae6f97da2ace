import { apiService } from './apiService';

export const taskApi = {
  createTask: async (taskData) => {
    return apiService.post('/api/tasks', taskData);
  },

  getTasks: async () => {
    return apiService.get('/api/tasks');
  },

  updateTask: async (id, taskData) => {
    return apiService.put(`/api/tasks/${id}`, taskData);
  },

  deleteTask: async (id) => {
    return apiService.delete(`/api/tasks/${id}`);
  },
}; 