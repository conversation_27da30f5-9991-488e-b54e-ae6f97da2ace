import api from './api';

/**
 * Export content as Markdown files
 * @param {string} type - 'all', 'blog', or 'pillar'
 * @returns {Promise<Blob>} ZIP file blob
 */
export const exportContent = async (type = 'all') => {
  try {
    const response = await api.get(`/content-export/export?type=${type}`, {
      responseType: 'blob'
    });
    
    return response.data;
  } catch (error) {
    console.error('Error exporting content:', error);
    throw error;
  }
};

/**
 * Import content from Markdown files
 * @param {File} file - ZIP file containing Markdown files
 * @param {string} mode - 'update' or 'replace'
 * @returns {Promise<Object>} Import results
 */
export const importContent = async (file, mode = 'update') => {
  try {
    const formData = new FormData();
    formData.append('importFile', file);
    formData.append('mode', mode);
    
    const response = await api.post('/content-export/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error importing content:', error);
    throw error;
  }
};

/**
 * Download a blob as a file
 * @param {Blob} blob - File blob
 * @param {string} filename - Filename for download
 */
export const downloadBlob = (blob, filename) => {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};
