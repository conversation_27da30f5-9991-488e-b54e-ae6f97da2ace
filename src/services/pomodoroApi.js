import { apiService } from './apiService';

export const pomodoroApi = {
  createPomodoro: async (pomodoroData) => {
    return apiService.post('/api/pomodoros', pomodoroData);
  },

  getPomodoros: async () => {
    return apiService.get('/api/pomodoros');
  },

  updatePomodoro: async (id, pomodoroData) => {
    return apiService.put(`/api/pomodoros/${id}`, pomodoroData);
  },

  deletePomodoro: async (id) => {
    return apiService.delete(`/api/pomodoros/${id}`);
  },
}; 