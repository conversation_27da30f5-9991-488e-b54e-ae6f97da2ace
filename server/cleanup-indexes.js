const mongoose = require('mongoose');

// Connect to MongoDB
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/pomodoro';

async function cleanupIndexes() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');
    
    const db = mongoose.connection.db;
    
    // List all collections
    const collections = await db.listCollections().toArray();
    
    for (const collection of collections) {
      const collectionName = collection.name;
      console.log(`\n=== ${collectionName} Collection ===`);
      
      try {
        // Get all indexes for this collection
        const indexes = await db.collection(collectionName).indexes();
        
        console.log('Current indexes:');
        indexes.forEach((index, i) => {
          console.log(`${i + 1}. ${index.name}: ${JSON.stringify(index.key)}`);
        });
        
        // Look for potential duplicate indexes
        const indexKeys = indexes.map(idx => JSON.stringify(idx.key));
        const duplicates = indexKeys.filter((key, index) => indexKeys.indexOf(key) !== index);
        
        if (duplicates.length > 0) {
          console.log('⚠️  Potential duplicate index keys found:', duplicates);
        }
        
      } catch (err) {
        console.log(`Error checking indexes for ${collectionName}:`, err.message);
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

cleanupIndexes();