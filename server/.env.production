PORT=5000
MONGODB_URI=**************************************************************************
JWT_SECRET=YY1sui@sz
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
CLIENT_URL=https://ai-pomo.com

# Email configuration
CONTACT_EMAIL=<EMAIL>
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=AI Pomo
EMAIL_ENABLED=true

# SMTP configuration with TLS
EMAIL_HOST=smtp.mailersend.net
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_REQUIRE_TLS=true
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=mssp.Apd2qhE.x2p0347j5e34zdrn.JsB4SAQ

# MongoDB Docker configuration
MONGODB_CONTAINER_NAME=pomodoro-mongodb
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DATABASE=pomodoro-timer
MONGODB_USERNAME=hiruben
MONGODB_PASSWORD=changeme

# Backup configuration
ENABLE_AUTO_BACKUP=false
BACKUP_FREQUENCY=daily
MAX_BACKUPS=10
BACKUP_DIR=backups

# Paddle Configuration
PADDLE_API_KEY=pdl_live_apikey_01jxk437vp8js7emrf1jvdg2t5_nzBc8f9A1FXFQQBsBXFRDb_AIS
PADDLE_WEBHOOK_SECRET=# Optional - leave empty if Paddle doesn't provide one
PADDLE_NOTIFICATION_SET_ID=ntfset_01jxk2ty9a4x0p7hm6sjepxqwn
PADDLE_VENDOR_ID=pro_01jxjzx642xnz9k48cwt9hv4r2
PADDLE_ENVIRONMENT=production

# Product Configuration - From your Paddle Dashboard
PADDLE_MONTHLY_PRICE_ID=pri_01jxk01813b3zfs36kcz66x0xg
PADDLE_YEARLY_PRICE_ID=pri_01jxk02vycmv0zjfck99vcxqmt
PADDLE_LIFETIME_PRICE_ID=pri_01jxk0448wa25ssmshke9c7r1m

# Webhook URL (optional, defaults to production URL)
PADDLE_WEBHOOK_URL=https://ai-pomo.com/api/paddle/webhook