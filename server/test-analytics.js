const mongoose = require('mongoose');
const Analytics = require('./src/models/Analytics');
require('dotenv').config();

/**
 * Test script to verify analytics functionality
 */
async function testAnalytics() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Test timezone conversion
    const now = new Date();
    const chinaOffset = 8 * 60 * 60 * 1000;
    const chinaTime = new Date(now.getTime() + chinaOffset);
    
    console.log('\n=== TIMEZONE TEST ===');
    console.log('Current UTC time:', now.toISOString());
    console.log('China time (UTC+8):', chinaTime.toISOString());
    
    // Test start of day calculation
    const getStartOfDayChina = (date) => {
      const chinaDate = new Date(date.getTime() + chinaOffset);
      chinaDate.setUTCHours(0, 0, 0, 0);
      return new Date(chinaDate.getTime() - chinaOffset);
    };
    
    const startOfDay = getStartOfDayChina(now);
    console.log('Start of day in China (UTC):', startOfDay.toISOString());
    console.log('Start of day in China (Local):', new Date(startOfDay.getTime() + chinaOffset).toISOString());

    // Test analytics data retrieval
    console.log('\n=== ANALYTICS DATA TEST ===');
    
    // Get total records
    const totalRecords = await Analytics.countDocuments();
    console.log('Total analytics records:', totalRecords);
    
    // Get human vs bot traffic
    const humanTraffic = await Analytics.countDocuments({ isBot: false });
    const botTraffic = await Analytics.countDocuments({ isBot: true });
    console.log('Human traffic records:', humanTraffic);
    console.log('Bot traffic records:', botTraffic);
    
    // Get recent records
    const recentRecords = await Analytics.find({ isBot: false })
      .sort({ timestamp: -1 })
      .limit(5)
      .select('path timestamp visitorId sessionId isNewSession device browser.name');
    
    console.log('\nRecent human traffic records:');
    recentRecords.forEach((record, index) => {
      console.log(`${index + 1}. ${record.path} - ${record.timestamp.toISOString()} - ${record.device} - ${record.browser?.name || 'Unknown'}`);
    });
    
    // Test session aggregation
    console.log('\n=== SESSION AGGREGATION TEST ===');
    
    const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    const sessionStats = await Analytics.aggregate([
      { 
        $match: { 
          timestamp: { $gte: last7Days },
          isBot: false 
        }
      },
      {
        $group: {
          _id: '$sessionId',
          pageViews: { $sum: 1 },
          maxDuration: { $max: '$sessionDuration' },
          isNewSession: { $first: '$isNewSession' },
          startTime: { $min: '$timestamp' },
          endTime: { $max: '$timestamp' }
        }
      },
      {
        $group: {
          _id: null,
          totalSessions: { $sum: 1 },
          newSessions: { 
            $sum: { $cond: [{ $eq: ['$isNewSession', true] }, 1, 0] }
          },
          avgDuration: { $avg: '$maxDuration' },
          singlePageSessions: {
            $sum: { $cond: [{ $eq: ['$pageViews', 1] }, 1, 0] }
          }
        }
      }
    ]);
    
    const sessionData = sessionStats[0] || {
      totalSessions: 0,
      newSessions: 0,
      avgDuration: 0,
      singlePageSessions: 0
    };
    
    console.log('Session statistics (last 7 days):');
    console.log('- Total sessions:', sessionData.totalSessions);
    console.log('- New sessions:', sessionData.newSessions);
    console.log('- Average duration:', Math.round(sessionData.avgDuration || 0), 'seconds');
    console.log('- Single page sessions:', sessionData.singlePageSessions);
    console.log('- Bounce rate:', sessionData.totalSessions > 0 ? Math.round((sessionData.singlePageSessions / sessionData.totalSessions) * 100) : 0, '%');
    
    // Test daily traffic aggregation
    console.log('\n=== DAILY TRAFFIC TEST ===');
    
    const dailyTraffic = await Analytics.aggregate([
      { 
        $match: { 
          timestamp: { $gte: last7Days },
          isBot: false 
        }
      },
      {
        $addFields: {
          chinaTimestamp: {
            $add: ['$timestamp', 8 * 60 * 60 * 1000] // Add 8 hours for China time
          }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$chinaTimestamp' },
            month: { $month: '$chinaTimestamp' },
            day: { $dayOfMonth: '$chinaTimestamp' }
          },
          pageViews: { $sum: 1 },
          uniqueVisitors: { $addToSet: '$visitorId' }
        }
      },
      {
        $project: {
          date: {
            $dateFromParts: {
              year: '$_id.year',
              month: '$_id.month',
              day: '$_id.day'
            }
          },
          pageViews: 1,
          uniqueVisitors: { $size: '$uniqueVisitors' }
        }
      },
      { $sort: { date: 1 } }
    ]);
    
    console.log('Daily traffic (last 7 days):');
    dailyTraffic.forEach(day => {
      console.log(`- ${day.date.toISOString().split('T')[0]}: ${day.pageViews} page views, ${day.uniqueVisitors} unique visitors`);
    });
    
    console.log('\n=== TEST COMPLETED ===');
    
  } catch (error) {
    console.error('Test error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the test
testAnalytics();
