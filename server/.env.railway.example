# Railway Environment Variables Configuration
# Set these in your Railway project settings

# Node Environment
NODE_ENV=production
PORT=5000

# MongoDB Connection (Railway MongoDB Plugin)
# Use Railway's reference variable:
MONGODB_URI=${{ MongoDB.MONGO_URL }}

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here

# Client URL (your Vercel frontend URL)
CLIENT_URL=https://your-vercel-app.vercel.app

# Paddle Payment System Configuration
PADDLE_API_KEY=your_paddle_api_key_here
PADDLE_WEBHOOK_SECRET=your_paddle_webhook_secret_here
PADDLE_VENDOR_ID=pro_01jxjzx642xnz9k48cwt9hv4r2
PADDLE_ENVIRONMENT=production
PADDLE_MONTHLY_PRICE_ID=pri_01jxk01813b3zfs36kcz66x0xg
PADDLE_YEARLY_PRICE_ID=pri_01jxk02vycmv0zjfck99vcxqmt
PADDLE_LIFETIME_PRICE_ID=pri_01jxk0448wa25ssmshke9c7r1m

# Email Configuration (Optional - for contact forms, etc.)
EMAIL_HOST=your_smtp_host
EMAIL_PORT=587
EMAIL_USER=your_email_username
EMAIL_PASS=your_email_password