// Simple Paddle configuration
const config = {
  environment: process.env.PADDLE_ENVIRONMENT || 'sandbox',
  apiKey: process.env.PADDLE_API_KEY,
  vendorId: process.env.PADDLE_VENDOR_ID || 'pro_01jxjzx642xnz9k48cwt9hv4r2',
  
  // Price IDs from your Paddle Dashboard
  priceIds: {
    monthly: process.env.PADDLE_MONTHLY_PRICE_ID || 'pri_01jxk01813b3zfs36kcz66x0xg',
    yearly: process.env.PADDLE_YEARLY_PRICE_ID || 'pri_01jxk02vycmv0zjfck99vcxqmt',
    lifetime: process.env.PADDLE_LIFETIME_PRICE_ID || 'pri_01jxk0448wa25ssmshke9c7r1m',
  },
  
  // URLs
  successUrl: (process.env.CLIENT_URL || 'http://localhost:3000') + '/premium?success=true',
  cancelUrl: (process.env.CLIENT_URL || 'http://localhost:3000') + '/premium?cancelled=true',
};

module.exports = config;