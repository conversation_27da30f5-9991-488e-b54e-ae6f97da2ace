const mongoose = require('mongoose');
const { exec } = require('child_process');
const path = require('path');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB connected'))
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

// Function to run a script
const runScript = (scriptPath) => {
  return new Promise((resolve, reject) => {
    const fullPath = path.join(__dirname, scriptPath);
    console.log(`Running script: ${fullPath}`);
    
    exec(`node ${fullPath}`, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error executing script: ${error}`);
        reject(error);
        return;
      }
      
      console.log(stdout);
      if (stderr) console.error(stderr);
      resolve();
    });
  });
};

// Run initialization scripts
const initBlog = async () => {
  try {
    // Run scripts in sequence
    await runScript('./createInitialBlogCategories.js');
    await runScript('./createSampleBlogPost.js');
    
    console.log('Blog initialization completed successfully');
  } catch (error) {
    console.error('Error initializing blog:', error);
  } finally {
    mongoose.disconnect();
    console.log('MongoDB disconnected');
  }
};

// Run the initialization
initBlog();
