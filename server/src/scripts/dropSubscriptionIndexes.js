/**
 * <PERSON><PERSON>t to drop indexes on the subscriptions collection
 *
 * Run this script with: node src/scripts/dropSubscriptionIndexes.js
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Define the Subscription schema without indexes
const SubscriptionSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    plan: {
      type: String,
      enum: ['free', 'yearly', 'lifetime'],
      default: 'free',
      required: true,
    },
    paymentMethod: {
      type: String,
      enum: ['paypal', 'usdt'],
    },
    paymentAmount: {
      type: Number,
    },
    paymentStatus: {
      type: String,
      enum: ['pending', 'confirmed', 'rejected'],
      default: 'pending',
    },
    paymentDate: {
      type: Date,
    },
    confirmationDate: {
      type: Date,
    },
    expiryDate: {
      type: Date,
    },
    transactionId: {
      type: String,
    },
    payerEmail: {
      type: String,
    },
    payerName: {
      type: String,
    },
    additionalInfo: {
      type: String,
    },
    active: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
  }
);

// Create the model
const Subscription = mongoose.model('Subscription', SubscriptionSchema);

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI)
  .then(async () => {
    console.log('MongoDB connected');

    try {
      // Get the collection
      const collection = mongoose.connection.collection('subscriptions');

      // List all indexes
      console.log('Current indexes:');
      const indexes = await collection.indexes();
      console.log(JSON.stringify(indexes, null, 2));

      // Drop the user_1 index specifically
      if (indexes.some(index => index.name === 'user_1')) {
        console.log('Dropping user_1 index...');
        await collection.dropIndex('user_1');
        console.log('user_1 index dropped successfully');
      } else {
        console.log('No user_1 index found');
      }

      // Verify indexes were dropped
      console.log('Indexes after dropping:');
      const remainingIndexes = await collection.indexes();
      console.log(JSON.stringify(remainingIndexes, null, 2));

    } catch (error) {
      console.error('Error dropping indexes:', error);
    } finally {
      // Close the connection
      await mongoose.connection.close();
      console.log('MongoDB connection closed');
      process.exit(0);
    }
  })
  .catch(err => {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  });
