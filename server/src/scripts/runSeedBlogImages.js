#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to seed blog images
 * Run this script to populate the database with curated blog images
 */

const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../../.env') });

const { seedBlogImages } = require('./seedBlogImages');

// Run the seeding function
seedBlogImages()
  .then(() => {
    console.log('Blog images seeding completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error seeding blog images:', error);
    process.exit(1);
  });
