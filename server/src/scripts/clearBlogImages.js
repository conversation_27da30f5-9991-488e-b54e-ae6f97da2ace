require('dotenv').config();
const mongoose = require('mongoose');
const BlogImage = require('../models/BlogImage');

// Function to clear all blog images
const clearBlogImages = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/ai-pomo');
    console.log('Connected to MongoDB');

    // Count existing images
    const existingCount = await BlogImage.countDocuments();
    console.log(`Found ${existingCount} existing blog images`);

    if (existingCount === 0) {
      console.log('No images to delete.');
      return;
    }

    // Delete all images
    const result = await BlogImage.deleteMany({});
    console.log(`Successfully deleted ${result.deletedCount} blog images`);

  } catch (error) {
    console.error('Error clearing blog images:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
};

// Run the clearing function if this script is executed directly
if (require.main === module) {
  clearBlogImages();
}

module.exports = { clearBlogImages };
