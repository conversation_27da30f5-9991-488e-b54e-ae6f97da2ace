const mongoose = require('mongoose');
const BlogCategory = require('../models/BlogCategory');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB connected'))
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

// Initial categories
const initialCategories = [
  {
    name: 'Productivity',
    slug: 'productivity',
    description: 'Tips and strategies to boost your productivity and efficiency'
  },
  {
    name: 'Pomodoro Technique',
    slug: 'pomodoro-technique',
    description: 'Deep dives into the Pomodoro technique and how to use it effectively'
  },
  {
    name: 'Time Management',
    slug: 'time-management',
    description: 'Methods and tools for better time management in your daily life'
  },
  {
    name: 'Focus',
    slug: 'focus',
    description: 'Strategies to improve focus and concentration in a distracted world'
  },
  {
    name: 'Work-Life Balance',
    slug: 'work-life-balance',
    description: 'Finding harmony between work responsibilities and personal life'
  },
  {
    name: 'AI Tools',
    slug: 'ai-tools',
    description: 'Exploring AI-powered tools for productivity and task management'
  }
];

// Create categories
const createCategories = async () => {
  try {
    // Check if categories already exist
    const existingCount = await BlogCategory.countDocuments();

    if (existingCount > 0) {
      console.log(`${existingCount} categories already exist. Skipping creation.`);
      return;
    }

    // Create categories
    const result = await BlogCategory.insertMany(initialCategories);
    console.log(`${result.length} categories created successfully`);
  } catch (error) {
    console.error('Error creating categories:', error);
  } finally {
    mongoose.disconnect();
    console.log('MongoDB disconnected');
  }
};

// Run the function
createCategories();
