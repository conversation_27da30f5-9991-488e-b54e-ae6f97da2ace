const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Subscription = require('../models/Subscription');

// Load environment variables
dotenv.config();

// Migration script to prepare existing subscriptions for Paddle integration
async function migrateToPaddle() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Find all active legacy subscriptions (PayPal/USDT)
    const legacySubscriptions = await Subscription.find({
      paymentMethod: { $in: ['paypal', 'usdt'] },
      active: true
    });

    console.log(`Found ${legacySubscriptions.length} legacy subscriptions to process`);

    let migrated = 0;

    for (const subscription of legacySubscriptions) {
      console.log(`Processing subscription ${subscription._id}:`);
      console.log(`  User: ${subscription.user}`);
      console.log(`  Plan: ${subscription.plan}`);
      console.log(`  Payment Method: ${subscription.paymentMethod}`);
      console.log(`  Status: ${subscription.paymentStatus}`);

      // Add migration note to admin notes
      const migrationNote = `Legacy ${subscription.paymentMethod} subscription. Migrated to Paddle system on ${new Date().toISOString()}. Original payment method preserved for reference.`;
      
      if (subscription.adminNotes) {
        subscription.adminNotes += `\n\n${migrationNote}`;
      } else {
        subscription.adminNotes = migrationNote;
      }

      // Save the updated subscription
      await subscription.save();
      
      console.log(`  ✓ Migration note added`);
      migrated++;
    }

    // Summary of active subscriptions
    const totalSubscriptions = await Subscription.countDocuments({});
    const activeSubscriptions = await Subscription.countDocuments({ active: true });
    const paddleSubscriptions = await Subscription.countDocuments({ paymentMethod: 'paddle' });
    const legacyActiveSubscriptions = await Subscription.countDocuments({
      paymentMethod: { $in: ['paypal', 'usdt'] },
      active: true
    });

    console.log('\n=== MIGRATION SUMMARY ===');
    console.log(`Total subscriptions in database: ${totalSubscriptions}`);
    console.log(`Active subscriptions: ${activeSubscriptions}`);
    console.log(`Paddle subscriptions: ${paddleSubscriptions}`);
    console.log(`Legacy active subscriptions: ${legacyActiveSubscriptions}`);
    console.log(`Subscriptions processed: ${migrated}`);

    console.log('\n=== NEXT STEPS ===');
    console.log('1. Set up your Paddle account and get API keys');
    console.log('2. Configure environment variables:');
    console.log('   - PADDLE_API_KEY');
    console.log('   - PADDLE_WEBHOOK_SECRET');
    console.log('   - PADDLE_VENDOR_ID');
    console.log('   - PADDLE_YEARLY_PRODUCT_ID');
    console.log('   - PADDLE_YEARLY_PRICE_ID');
    console.log('   - PADDLE_LIFETIME_PRODUCT_ID');
    console.log('   - PADDLE_LIFETIME_PRICE_ID');
    console.log('   - PADDLE_ENVIRONMENT (sandbox or production)');
    console.log('3. Create products and price plans in Paddle Dashboard');
    console.log('4. Configure webhook URL in Paddle Dashboard');
    console.log('5. Test the integration in sandbox mode');

    if (legacyActiveSubscriptions > 0) {
      console.log('\n=== LEGACY SUBSCRIPTION HANDLING ===');
      console.log(`You have ${legacyActiveSubscriptions} active legacy subscriptions.`);
      console.log('Options for handling them:');
      console.log('1. Keep them active until they expire naturally');
      console.log('2. Contact users to migrate to new Paddle-based subscriptions');
      console.log('3. Offer transition incentives for early adopters');
      console.log('4. Gradually phase out legacy payment methods');
    }

  } catch (error) {
    console.error('Migration error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the migration
if (require.main === module) {
  console.log('Starting Paddle migration...\n');
  migrateToPaddle()
    .then(() => {
      console.log('\nMigration completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\nMigration failed:', error);
      process.exit(1);
    });
}

module.exports = migrateToPaddle;