require('dotenv').config();
const mongoose = require('mongoose');
const BlogImage = require('../models/BlogImage');

async function removeDuplicateImages() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');
    
    // Get all active images sorted by creation date (oldest first)
    const images = await BlogImage.find({ isActive: true }).sort({ createdAt: 1 });
    console.log('Total active images:', images.length);
    
    // Track unique URLs and duplicates to remove
    const seenUrls = new Set();
    const duplicatesToRemove = [];
    const keptImages = [];
    
    for (const image of images) {
      if (seenUrls.has(image.url)) {
        // This is a duplicate - mark for removal
        duplicatesToRemove.push(image);
        console.log(`🗑️  Duplicate found: "${image.title}" (ID: ${image._id})`);
      } else {
        // This is the first occurrence - keep it
        seenUrls.add(image.url);
        keptImages.push(image);
        console.log(`✅ Keeping: "${image.title}" (ID: ${image._id})`);
      }
    }
    
    console.log('\n=== SUMMARY ===');
    console.log('Total images:', images.length);
    console.log('Unique images to keep:', keptImages.length);
    console.log('Duplicates to remove:', duplicatesToRemove.length);
    
    if (duplicatesToRemove.length > 0) {
      console.log('\n=== REMOVING DUPLICATES ===');
      
      // Remove duplicates in batches
      const batchSize = 10;
      for (let i = 0; i < duplicatesToRemove.length; i += batchSize) {
        const batch = duplicatesToRemove.slice(i, i + batchSize);
        const ids = batch.map(img => img._id);
        
        const result = await BlogImage.deleteMany({ _id: { $in: ids } });
        console.log(`Removed batch ${Math.floor(i/batchSize) + 1}: ${result.deletedCount} images`);
        
        // Log which images were removed
        batch.forEach(img => {
          console.log(`  - Removed: "${img.title}" (${img._id})`);
        });
      }
      
      console.log('\n✅ Duplicate removal completed!');
      
      // Verify the cleanup
      const remainingImages = await BlogImage.find({ isActive: true });
      console.log('Remaining images after cleanup:', remainingImages.length);
      
      // Check if any duplicates still exist
      const urlCounts = {};
      remainingImages.forEach(img => {
        urlCounts[img.url] = (urlCounts[img.url] || 0) + 1;
      });
      
      const stillDuplicated = Object.entries(urlCounts).filter(([url, count]) => count > 1);
      if (stillDuplicated.length > 0) {
        console.log('⚠️  Still have duplicates:', stillDuplicated.length);
        stillDuplicated.forEach(([url, count]) => {
          console.log(`  - ${count} copies of: ${url.substring(0, 60)}...`);
        });
      } else {
        console.log('🎉 No more duplicates found!');
      }
    } else {
      console.log('No duplicates found to remove.');
    }
    
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

// Run the cleanup
removeDuplicateImages();
