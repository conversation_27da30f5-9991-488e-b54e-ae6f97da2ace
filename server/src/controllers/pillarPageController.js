const PillarPage = require('../models/PillarPage');
const BlogPost = require('../models/BlogPost');
const slugify = require('../utils/slugify');
const { clearPillarPageCache } = require('../middleware/cache');

// Get all published pillar pages
exports.getPillarPages = async (req, res) => {
  try {
    const pillarPages = await PillarPage.find({ published: true })
      .populate('author', 'name')
      .populate('clusterPages.blogPost', 'title slug excerpt coverImage readTime createdAt')
      .sort({ menuOrder: 1, createdAt: -1 });

    res.json(pillarPages);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get all pillar pages for admin (including unpublished)
exports.getAllPillarPages = async (req, res) => {
  try {
    const pillarPages = await PillarPage.find()
      .populate('author', 'name')
      .populate('clusterPages.blogPost', 'title slug excerpt coverImage readTime createdAt')
      .sort({ menuOrder: 1, createdAt: -1 });

    res.json(pillarPages);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get pillar page by slug (optimized - without cluster pages for faster loading)
exports.getPillarPageBySlug = async (req, res) => {
  try {
    const { slug } = req.params;
    const { includeClusterPages = 'true' } = req.query;

    let query = PillarPage.findOne({ slug, published: true })
      .populate('author', 'name');

    // Only populate cluster pages if requested (for performance)
    if (includeClusterPages === 'true') {
      query = query.populate('clusterPages.blogPost', 'title slug excerpt coverImage readTime createdAt category tags');
    }

    const pillarPage = await query;

    if (!pillarPage) {
      return res.status(404).json({ message: 'Pillar page not found' });
    }

    // Sort cluster pages by display order if they exist
    if (includeClusterPages === 'true' && pillarPage.clusterPages && pillarPage.clusterPages.length > 0) {
      pillarPage.clusterPages.sort((a, b) => a.displayOrder - b.displayOrder);
    }

    res.json(pillarPage);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get pillar page by category (optimized for direct category lookup)
exports.getPillarPageByCategory = async (req, res) => {
  try {
    const { category } = req.params;
    const { includeClusterPages = 'true' } = req.query;

    let query = PillarPage.findOne({ category, published: true })
      .populate('author', 'name');

    // Only populate cluster pages if requested (for performance)
    if (includeClusterPages === 'true') {
      query = query.populate('clusterPages.blogPost', 'title slug excerpt coverImage readTime createdAt category tags');
    }

    const pillarPage = await query;

    if (!pillarPage) {
      return res.status(404).json({ message: 'Pillar page not found for this category' });
    }

    // Sort cluster pages by display order if they exist
    if (includeClusterPages === 'true' && pillarPage.clusterPages && pillarPage.clusterPages.length > 0) {
      pillarPage.clusterPages.sort((a, b) => a.displayOrder - b.displayOrder);
    }

    res.json(pillarPage);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get pillar page by ID (for admin)
exports.getPillarPageById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const pillarPage = await PillarPage.findById(id)
      .populate('author', 'name')
      .populate('clusterPages.blogPost', 'title slug excerpt coverImage readTime createdAt category tags');

    if (!pillarPage) {
      return res.status(404).json({ message: 'Pillar page not found' });
    }

    res.json(pillarPage);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Create a new pillar page
exports.createPillarPage = async (req, res) => {
  try {
    const {
      title, content, excerpt, coverImage, category,
      readTime, published, featured, seoTitle, seoDescription, seoKeywords,
      clusterPages, menuOrder
    } = req.body;

    const pillarPage = new PillarPage({
      title,
      slug: slugify(title),
      content,
      excerpt,
      author: req.user.id,
      coverImage,
      category,
      readTime,
      published: published || false,
      featured: featured || false,
      seoTitle: seoTitle || title,
      seoDescription: seoDescription || excerpt,
      seoKeywords: seoKeywords || '',
      clusterPages: clusterPages || [],
      menuOrder: menuOrder || 0
    });

    await pillarPage.save();

    // Clear cache after creating pillar page
    clearPillarPageCache();

    res.status(201).json(pillarPage);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Update pillar page
exports.updatePillarPage = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title, content, excerpt, coverImage, category,
      readTime, published, featured, seoTitle, seoDescription, seoKeywords,
      clusterPages, menuOrder
    } = req.body;

    const pillarPage = await PillarPage.findById(id);

    if (!pillarPage) {
      return res.status(404).json({ message: 'Pillar page not found' });
    }

    // Update fields
    pillarPage.title = title;
    pillarPage.slug = slugify(title);
    pillarPage.content = content;
    pillarPage.excerpt = excerpt;
    pillarPage.coverImage = coverImage;
    pillarPage.category = category;
    pillarPage.readTime = readTime;
    pillarPage.published = published;
    pillarPage.featured = featured;
    pillarPage.seoTitle = seoTitle || title;
    pillarPage.seoDescription = seoDescription || excerpt;
    pillarPage.seoKeywords = seoKeywords || '';
    pillarPage.clusterPages = clusterPages || [];
    pillarPage.menuOrder = menuOrder || 0;

    await pillarPage.save();

    // Clear cache after updating pillar page
    clearPillarPageCache();

    res.json(pillarPage);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Delete pillar page
exports.deletePillarPage = async (req, res) => {
  try {
    const { id } = req.params;

    const pillarPage = await PillarPage.findById(id);

    if (!pillarPage) {
      return res.status(404).json({ message: 'Pillar page not found' });
    }

    await PillarPage.findByIdAndDelete(id);

    // Clear cache after deleting pillar page
    clearPillarPageCache();

    res.json({ message: 'Pillar page deleted successfully' });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get cluster pages for a specific pillar page (lazy loading)
exports.getPillarPageClusterPages = async (req, res) => {
  try {
    const { slug } = req.params;

    const pillarPage = await PillarPage.findOne({ slug, published: true })
      .select('clusterPages')
      .populate('clusterPages.blogPost', 'title slug excerpt coverImage readTime createdAt category tags');

    if (!pillarPage) {
      return res.status(404).json({ message: 'Pillar page not found' });
    }

    // Sort cluster pages by display order
    if (pillarPage.clusterPages && pillarPage.clusterPages.length > 0) {
      pillarPage.clusterPages.sort((a, b) => a.displayOrder - b.displayOrder);
    }

    res.json(pillarPage.clusterPages || []);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get available blog posts for cluster association
exports.getAvailableBlogPosts = async (req, res) => {
  try {
    const blogPosts = await BlogPost.find({ published: true })
      .select('title slug excerpt coverImage readTime createdAt category tags')
      .sort({ createdAt: -1 });

    res.json(blogPosts);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};
