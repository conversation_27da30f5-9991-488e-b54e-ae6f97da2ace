/**
 * Email Campaign Controller
 * 
 * Handles email marketing campaigns
 */

const EmailCampaign = require('../models/EmailCampaign');
const EmailList = require('../models/EmailList');
const EmailContact = require('../models/EmailContact');
const emailService = require('../services/emailService');

/**
 * Get all campaigns for current user
 */
const getCampaigns = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const status = req.query.status || '';

    const query = { createdBy: req.user.id };
    if (status) {
      query.status = status;
    }

    const campaigns = await EmailCampaign.find(query)
      .populate('emailLists', 'name totalContacts')
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean();

    const total = await EmailCampaign.countDocuments(query);

    res.json({
      success: true,
      data: {
        campaigns,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error getting campaigns:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get campaigns',
      error: error.message
    });
  }
};

/**
 * Get single campaign
 */
const getCampaign = async (req, res) => {
  try {
    const { campaignId } = req.params;

    const campaign = await EmailCampaign.findOne({
      _id: campaignId,
      createdBy: req.user.id
    }).populate('emailLists', 'name totalContacts activeContacts');

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Campaign not found'
      });
    }

    res.json({
      success: true,
      data: campaign
    });
  } catch (error) {
    console.error('Error getting campaign:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get campaign',
      error: error.message
    });
  }
};

/**
 * Create new campaign
 */
const createCampaign = async (req, res) => {
  try {
    const {
      name,
      description,
      subject,
      textContent,
      htmlContent,
      fromName,
      fromEmail,
      replyToEmail,
      emailLists,
      tags,
      excludeTags,
      batchSize,
      delayBetweenBatches
    } = req.body;

    if (!name || !subject || !textContent || !htmlContent || !emailLists || emailLists.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Name, subject, content, and at least one email list are required'
      });
    }

    // Verify all email lists belong to user
    const lists = await EmailList.find({
      _id: { $in: emailLists },
      createdBy: req.user.id,
      isActive: true
    });

    if (lists.length !== emailLists.length) {
      return res.status(400).json({
        success: false,
        message: 'One or more email lists not found'
      });
    }

    const campaign = new EmailCampaign({
      name,
      description,
      subject,
      textContent,
      htmlContent,
      fromName: fromName || 'AI-Pomo Team',
      fromEmail: fromEmail || '<EMAIL>',
      replyToEmail: replyToEmail || '<EMAIL>',
      emailLists,
      tags: tags || [],
      excludeTags: excludeTags || [],
      batchSize: batchSize || 100,
      delayBetweenBatches: delayBetweenBatches || 60000,
      createdBy: req.user.id
    });

    await campaign.save();

    res.json({
      success: true,
      message: 'Campaign created successfully',
      data: campaign
    });
  } catch (error) {
    console.error('Error creating campaign:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create campaign',
      error: error.message
    });
  }
};

/**
 * Update campaign (only if draft)
 */
const updateCampaign = async (req, res) => {
  try {
    const { campaignId } = req.params;
    const updateData = req.body;

    const campaign = await EmailCampaign.findOne({
      _id: campaignId,
      createdBy: req.user.id
    });

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Campaign not found'
      });
    }

    if (campaign.status !== 'draft') {
      return res.status(400).json({
        success: false,
        message: 'Can only update draft campaigns'
      });
    }

    // Update allowed fields
    const allowedFields = [
      'name', 'description', 'subject', 'textContent', 'htmlContent',
      'fromName', 'fromEmail', 'replyToEmail', 'emailLists', 'tags',
      'excludeTags', 'batchSize', 'delayBetweenBatches'
    ];

    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        campaign[field] = updateData[field];
      }
    });

    await campaign.save();

    res.json({
      success: true,
      message: 'Campaign updated successfully',
      data: campaign
    });
  } catch (error) {
    console.error('Error updating campaign:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update campaign',
      error: error.message
    });
  }
};

/**
 * Delete campaign
 */
const deleteCampaign = async (req, res) => {
  try {
    const { campaignId } = req.params;

    const campaign = await EmailCampaign.findOne({
      _id: campaignId,
      createdBy: req.user.id
    });

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Campaign not found'
      });
    }

    if (campaign.status === 'sending') {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete campaign that is currently sending'
      });
    }

    await campaign.deleteOne();

    res.json({
      success: true,
      message: 'Campaign deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting campaign:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete campaign',
      error: error.message
    });
  }
};

/**
 * Send test email
 */
const sendTestEmail = async (req, res) => {
  try {
    const { campaignId } = req.params;
    const { testEmail } = req.body;

    if (!testEmail) {
      return res.status(400).json({
        success: false,
        message: 'Test email address is required'
      });
    }

    const campaign = await EmailCampaign.findOne({
      _id: campaignId,
      createdBy: req.user.id
    });

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Campaign not found'
      });
    }

    // Send test email
    const emailOptions = {
      to: testEmail,
      subject: `[TEST] ${campaign.subject}`,
      text: campaign.textContent,
      html: campaign.htmlContent,
      from: `${campaign.fromName} <${campaign.fromEmail}>`,
      replyTo: campaign.replyToEmail
    };

    await emailService.sendEmail(emailOptions);

    res.json({
      success: true,
      message: 'Test email sent successfully'
    });
  } catch (error) {
    console.error('Error sending test email:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send test email',
      error: error.message
    });
  }
};

/**
 * Schedule campaign
 */
const scheduleCampaign = async (req, res) => {
  try {
    const { campaignId } = req.params;
    const { scheduledAt } = req.body;

    const campaign = await EmailCampaign.findOne({
      _id: campaignId,
      createdBy: req.user.id
    });

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Campaign not found'
      });
    }

    if (campaign.status !== 'draft') {
      return res.status(400).json({
        success: false,
        message: 'Can only schedule draft campaigns'
      });
    }

    const scheduleTime = scheduledAt ? new Date(scheduledAt) : new Date();
    
    if (scheduleTime <= new Date()) {
      return res.status(400).json({
        success: false,
        message: 'Schedule time must be in the future'
      });
    }

    // Calculate recipients
    const recipients = await getRecipients(campaign);
    
    campaign.status = 'scheduled';
    campaign.scheduledAt = scheduleTime;
    campaign.totalRecipients = recipients.length;
    campaign.sendingProgress.total = recipients.length;
    campaign.sendingProgress.totalBatches = Math.ceil(recipients.length / campaign.batchSize);

    await campaign.save();

    res.json({
      success: true,
      message: 'Campaign scheduled successfully',
      data: {
        scheduledAt: scheduleTime,
        totalRecipients: recipients.length
      }
    });
  } catch (error) {
    console.error('Error scheduling campaign:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to schedule campaign',
      error: error.message
    });
  }
};

/**
 * Send campaign immediately
 */
const sendCampaign = async (req, res) => {
  try {
    const { campaignId } = req.params;

    const campaign = await EmailCampaign.findOne({
      _id: campaignId,
      createdBy: req.user.id
    });

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Campaign not found'
      });
    }

    if (campaign.status !== 'draft' && campaign.status !== 'scheduled') {
      return res.status(400).json({
        success: false,
        message: 'Can only send draft or scheduled campaigns'
      });
    }

    // Start sending process
    await startCampaignSending(campaign);

    res.json({
      success: true,
      message: 'Campaign sending started',
      data: {
        totalRecipients: campaign.totalRecipients,
        totalBatches: campaign.sendingProgress.totalBatches
      }
    });
  } catch (error) {
    console.error('Error sending campaign:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send campaign',
      error: error.message
    });
  }
};

/**
 * Pause campaign
 */
const pauseCampaign = async (req, res) => {
  try {
    const { campaignId } = req.params;

    const campaign = await EmailCampaign.findOne({
      _id: campaignId,
      createdBy: req.user.id
    });

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Campaign not found'
      });
    }

    if (campaign.status !== 'sending' && campaign.status !== 'scheduled') {
      return res.status(400).json({
        success: false,
        message: 'Can only pause sending or scheduled campaigns'
      });
    }

    campaign.status = 'paused';
    await campaign.save();

    res.json({
      success: true,
      message: 'Campaign paused successfully'
    });
  } catch (error) {
    console.error('Error pausing campaign:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to pause campaign',
      error: error.message
    });
  }
};

/**
 * Get campaign recipients
 */
async function getRecipients(campaign) {
  let query = {
    emailLists: { $in: campaign.emailLists },
    status: 'active'
  };

  // Include tags filter
  if (campaign.tags && campaign.tags.length > 0) {
    query.tags = { $in: campaign.tags };
  }

  // Exclude tags filter
  if (campaign.excludeTags && campaign.excludeTags.length > 0) {
    query.tags = { $nin: campaign.excludeTags };
  }

  return await EmailContact.find(query).select('email firstName lastName');
}

/**
 * Start campaign sending process
 */
async function startCampaignSending(campaign) {
  try {
    // Get all recipients
    const recipients = await getRecipients(campaign);
    
    // Update campaign status
    campaign.status = 'sending';
    campaign.totalRecipients = recipients.length;
    campaign.sendingProgress.total = recipients.length;
    campaign.sendingProgress.totalBatches = Math.ceil(recipients.length / campaign.batchSize);
    campaign.sendingProgress.current = 0;
    campaign.sendingProgress.currentBatch = 0;
    
    await campaign.save();

    // Process in batches
    for (let i = 0; i < recipients.length; i += campaign.batchSize) {
      const batch = recipients.slice(i, i + campaign.batchSize);
      
      // Send batch
      await sendEmailBatch(campaign, batch);
      
      // Update progress
      campaign.sendingProgress.current += batch.length;
      campaign.sendingProgress.currentBatch++;
      campaign.sendingProgress.lastSentAt = new Date();
      
      if (i + campaign.batchSize < recipients.length) {
        campaign.sendingProgress.nextBatchAt = new Date(Date.now() + campaign.delayBetweenBatches);
      }
      
      await campaign.save();
      
      // Wait between batches (except for last batch)
      if (i + campaign.batchSize < recipients.length) {
        await new Promise(resolve => setTimeout(resolve, campaign.delayBetweenBatches));
      }
    }

    // Mark as sent
    campaign.status = 'sent';
    campaign.sentAt = new Date();
    await campaign.save();

  } catch (error) {
    console.error('Error in campaign sending process:', error);
    campaign.status = 'paused';
    campaign.errors.push({
      error: error.message,
      timestamp: new Date()
    });
    await campaign.save();
  }
}

/**
 * Send email batch
 */
async function sendEmailBatch(campaign, recipients) {
  const promises = recipients.map(async (recipient) => {
    try {
      // Personalize content
      const personalizedSubject = personalizeContent(campaign.subject, recipient);
      const personalizedText = personalizeContent(campaign.textContent, recipient);
      const personalizedHtml = personalizeContent(campaign.htmlContent, recipient);

      const emailOptions = {
        to: recipient.email,
        subject: personalizedSubject,
        text: personalizedText,
        html: personalizedHtml,
        from: `${campaign.fromName} <${campaign.fromEmail}>`,
        replyTo: campaign.replyToEmail
      };

      await emailService.sendEmail(emailOptions);
      
      // Update campaign stats
      campaign.emailsSent++;
      
      // Update contact stats
      await EmailContact.findByIdAndUpdate(recipient._id, {
        $inc: { emailsSent: 1 },
        lastEmailSent: new Date()
      });

    } catch (error) {
      console.error(`Error sending email to ${recipient.email}:`, error);
      campaign.errors.push({
        email: recipient.email,
        error: error.message,
        timestamp: new Date()
      });
    }
  });

  await Promise.all(promises);
}

/**
 * Personalize email content
 */
function personalizeContent(content, recipient) {
  return content
    .replace(/\{\{firstName\}\}/g, recipient.firstName || '')
    .replace(/\{\{lastName\}\}/g, recipient.lastName || '')
    .replace(/\{\{email\}\}/g, recipient.email || '')
    .replace(/\{\{name\}\}/g, recipient.firstName || recipient.email.split('@')[0]);
}

module.exports = {
  getCampaigns,
  getCampaign,
  createCampaign,
  updateCampaign,
  deleteCampaign,
  sendTestEmail,
  scheduleCampaign,
  sendCampaign,
  pauseCampaign
};