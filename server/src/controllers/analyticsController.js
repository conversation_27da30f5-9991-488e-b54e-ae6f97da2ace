const Analytics = require('../models/Analytics');

/**
 * Convert date to China timezone (UTC+8)
 * Fixed timezone conversion logic
 */
const toChinaTime = (date) => {
  const chinaOffset = 8 * 60 * 60 * 1000; // UTC+8 in milliseconds
  return new Date(date.getTime() + chinaOffset);
};

/**
 * Validate and sanitize input parameters
 */
const validateTimeRange = (timeRange) => {
  const validTimeRanges = ['1d', '7d', '30d', '90d'];
  return validTimeRanges.includes(timeRange) ? timeRange : '7d';
};

/**
 * Get start of day in China timezone
 * Returns UTC timestamp for the start of day in China time
 */
const getStartOfDayChina = (date) => {
  // Convert to China time
  const chinaDate = toChinaTime(date);
  // Set to start of day in China time
  chinaDate.setUTCHours(0, 0, 0, 0);
  // Convert back to UTC for database query
  const chinaOffset = 8 * 60 * 60 * 1000;
  return new Date(chinaDate.getTime() - chinaOffset);
};

/**
 * Get traffic analytics dashboard data
 */
const getDashboard = async (req, res) => {
  try {
    const { timeRange = '7d' } = req.query;

    // Validate timeRange parameter
    const validatedTimeRange = validateTimeRange(timeRange);

    // Calculate date range in China time
    const nowChina = toChinaTime(new Date());
    let startDate;

    switch (validatedTimeRange) {
      case '1d':
        startDate = getStartOfDayChina(new Date(nowChina.getTime() - 0 * 24 * 60 * 60 * 1000)); // Today
        break;
      case '7d':
        startDate = getStartOfDayChina(new Date(nowChina.getTime() - 6 * 24 * 60 * 60 * 1000)); // Last 7 days
        break;
      case '30d':
        startDate = getStartOfDayChina(new Date(nowChina.getTime() - 29 * 24 * 60 * 60 * 1000)); // Last 30 days
        break;
      case '90d':
        startDate = getStartOfDayChina(new Date(nowChina.getTime() - 89 * 24 * 60 * 60 * 1000)); // Last 90 days
        break;
      default:
        startDate = getStartOfDayChina(new Date(nowChina.getTime() - 6 * 24 * 60 * 60 * 1000)); // Last 7 days
    }

    // Base query for human visitors only
    const baseQuery = {
      timestamp: { $gte: startDate },
      isBot: false
    };

    // Get total page views
    const totalPageViews = await Analytics.countDocuments(baseQuery);

    // Get unique visitors
    const uniqueVisitors = await Analytics.distinct('visitorId', baseQuery);
    const uniqueVisitorCount = uniqueVisitors.length;

    // Get session statistics
    const sessionStats = await Analytics.aggregate([
      { $match: baseQuery },
      {
        $group: {
          _id: '$sessionId',
          pageViews: { $sum: 1 },
          maxDuration: { $max: '$sessionDuration' },
          isNewSession: { $first: '$isNewSession' },
          startTime: { $min: '$timestamp' },
          endTime: { $max: '$timestamp' }
        }
      },
      {
        $group: {
          _id: null,
          totalSessions: { $sum: 1 },
          newSessions: {
            $sum: { $cond: [{ $eq: ['$isNewSession', true] }, 1, 0] }
          },
          avgDuration: { $avg: '$maxDuration' },
          singlePageSessions: {
            $sum: { $cond: [{ $eq: ['$pageViews', 1] }, 1, 0] }
          }
        }
      }
    ]);

    const sessionData = sessionStats[0] || {
      totalSessions: 0,
      newSessions: 0,
      avgDuration: 0,
      singlePageSessions: 0
    };

    const totalSessionCount = sessionData.totalSessions;
    const newSessionCount = sessionData.newSessions;
    const avgSessionDuration = sessionData.avgDuration || 0;
    const bounces = sessionData.singlePageSessions;

    // Get daily traffic data (grouped by China time)
    const dailyTraffic = await Analytics.aggregate([
      { $match: baseQuery },
      {
        $addFields: {
          chinaTimestamp: {
            $add: ['$timestamp', 8 * 60 * 60 * 1000] // Add 8 hours for China time
          }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$chinaTimestamp' },
            month: { $month: '$chinaTimestamp' },
            day: { $dayOfMonth: '$chinaTimestamp' }
          },
          pageViews: { $sum: 1 },
          uniqueVisitors: { $addToSet: '$visitorId' }
        }
      },
      {
        $project: {
          date: {
            $dateFromParts: {
              year: '$_id.year',
              month: '$_id.month',
              day: '$_id.day'
            }
          },
          pageViews: 1,
          uniqueVisitors: { $size: '$uniqueVisitors' }
        }
      },
      { $sort: { date: 1 } }
    ]);

    // Get top pages
    const topPages = await Analytics.aggregate([
      { $match: baseQuery },
      { $group: { _id: '$path', views: { $sum: 1 }, uniqueVisitors: { $addToSet: '$visitorId' } } },
      { $project: { path: '$_id', views: 1, uniqueVisitors: { $size: '$uniqueVisitors' } } },
      { $sort: { views: -1 } },
      { $limit: 10 }
    ]);

    // Get referrer data
    const topReferrers = await Analytics.aggregate([
      { $match: { ...baseQuery, referrer: { $ne: '', $exists: true } } },
      { $group: { _id: '$referrer', visits: { $sum: 1 } } },
      { $project: { referrer: '$_id', visits: 1 } },
      { $sort: { visits: -1 } },
      { $limit: 10 }
    ]);

    // Get device breakdown
    const deviceBreakdown = await Analytics.aggregate([
      { $match: baseQuery },
      { $group: { _id: '$device', count: { $sum: 1 } } },
      { $project: { device: '$_id', count: 1 } },
      { $sort: { count: -1 } }
    ]);

    // Get browser breakdown
    const browserBreakdown = await Analytics.aggregate([
      { $match: baseQuery },
      { $group: { _id: '$browser.name', count: { $sum: 1 } } },
      { $project: { browser: '$_id', count: 1 } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    // Get country breakdown
    const countryBreakdown = await Analytics.aggregate([
      { $match: baseQuery },
      { $group: { _id: '$country', count: { $sum: 1 } } },
      { $project: { country: '$_id', count: 1 } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    // Calculate bounce rate
    const bounceRate = totalSessionCount > 0 ? Math.round((bounces / totalSessionCount) * 100) : 0;

    // Debug logging
    console.log('=== ANALYTICS DEBUG ===');
    console.log('Requested time range:', timeRange);
    console.log('Validated time range:', validatedTimeRange);
    console.log('Start date (UTC):', startDate);
    console.log('Start date (China):', toChinaTime(startDate));
    console.log('Total page views:', totalPageViews);
    console.log('Unique visitors:', uniqueVisitorCount);
    console.log('Total sessions:', totalSessionCount);
    console.log('New sessions:', newSessionCount);
    console.log('Single page sessions (bounces):', bounces);
    console.log('Bounce rate:', bounceRate + '%');
    console.log('Avg session duration:', Math.round(avgSessionDuration) + 's');
    console.log('======================');

    res.json({
      timeRange: validatedTimeRange,
      timezone: 'UTC+8 (China Standard Time)',
      summary: {
        totalPageViews,
        uniqueVisitors: uniqueVisitorCount,
        totalSessions: totalSessionCount,
        newSessions: newSessionCount,
        avgSessionDuration: Math.round(avgSessionDuration),
        bounceRate: bounceRate
      },
      dailyTraffic,
      topPages,
      topReferrers,
      deviceBreakdown,
      browserBreakdown,
      countryBreakdown
    });
  } catch (error) {
    console.error('Error fetching analytics dashboard:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get real-time analytics data
 */
const getRealTime = async (req, res) => {
  try {
    const now = new Date();
    const last5Minutes = new Date(now.getTime() - 5 * 60 * 1000);
    const lastHour = new Date(now.getTime() - 60 * 60 * 1000);

    const baseQuery = { isBot: false };

    // Active visitors (last 5 minutes)
    const activeVisitors = await Analytics.distinct('visitorId', {
      ...baseQuery,
      timestamp: { $gte: last5Minutes }
    });

    // Recent page views (last hour)
    const recentPageViews = await Analytics.find({
      ...baseQuery,
      timestamp: { $gte: lastHour }
    })
    .sort({ timestamp: -1 })
    .limit(50)
    .select('path timestamp visitorId country device browser.name');

    // Current popular pages (last hour)
    const popularPages = await Analytics.aggregate([
      { $match: { ...baseQuery, timestamp: { $gte: lastHour } } },
      { $group: { _id: '$path', views: { $sum: 1 } } },
      { $project: { path: '$_id', views: 1 } },
      { $sort: { views: -1 } },
      { $limit: 5 }
    ]);

    res.json({
      activeVisitors: activeVisitors.length,
      recentPageViews,
      popularPages,
      timestamp: toChinaTime(now),
      timezone: 'UTC+8 (China Standard Time)'
    });
  } catch (error) {
    console.error('Error fetching real-time analytics:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get bot traffic data (for monitoring purposes)
 */
const getBotTraffic = async (req, res) => {
  try {
    const { timeRange = '7d' } = req.query;

    // Calculate date range in China time
    const now = new Date();
    let startDate;

    switch (timeRange) {
      case '1d':
        startDate = getStartOfDayChina(new Date(now.getTime() - 0 * 24 * 60 * 60 * 1000));
        break;
      case '7d':
        startDate = getStartOfDayChina(new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000));
        break;
      case '30d':
        startDate = getStartOfDayChina(new Date(now.getTime() - 29 * 24 * 60 * 60 * 1000));
        break;
      default:
        startDate = getStartOfDayChina(new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000));
    }

    // Bot traffic query
    const botQuery = {
      timestamp: { $gte: startDate },
      isBot: true
    };

    // Get bot traffic by type
    const botTrafficByType = await Analytics.aggregate([
      { $match: botQuery },
      { $group: { _id: '$botType', count: { $sum: 1 } } },
      { $project: { botType: '$_id', count: 1 } },
      { $sort: { count: -1 } }
    ]);

    // Get top bot user agents
    const topBotUserAgents = await Analytics.aggregate([
      { $match: botQuery },
      { $group: { _id: '$userAgent', count: { $sum: 1 } } },
      { $project: { userAgent: '$_id', count: 1 } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    // Daily bot traffic (grouped by China time)
    const dailyBotTraffic = await Analytics.aggregate([
      { $match: botQuery },
      {
        $addFields: {
          chinaTimestamp: {
            $add: ['$timestamp', 8 * 60 * 60 * 1000] // Add 8 hours for China time
          }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$chinaTimestamp' },
            month: { $month: '$chinaTimestamp' },
            day: { $dayOfMonth: '$chinaTimestamp' }
          },
          requests: { $sum: 1 }
        }
      },
      {
        $project: {
          date: {
            $dateFromParts: {
              year: '$_id.year',
              month: '$_id.month',
              day: '$_id.day'
            }
          },
          requests: 1
        }
      },
      { $sort: { date: 1 } }
    ]);

    const totalBotRequests = await Analytics.countDocuments(botQuery);

    res.json({
      timeRange,
      timezone: 'UTC+8 (China Standard Time)',
      totalBotRequests,
      botTrafficByType,
      topBotUserAgents,
      dailyBotTraffic
    });
  } catch (error) {
    console.error('Error fetching bot traffic data:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get recent analytics data for debugging
 */
const getDebugData = async (req, res) => {
  try {
    const recentData = await Analytics.find({
      isBot: false
    })
    .sort({ timestamp: -1 })
    .limit(50)
    .select('path timestamp visitorId sessionId isNewSession device browser.name');

    res.json({
      message: 'Recent 50 analytics records (human visitors only)',
      count: recentData.length,
      data: recentData
    });
  } catch (error) {
    console.error('Error fetching debug data:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get analytics export data
 */
const exportData = async (req, res) => {
  try {
    const { timeRange = '30d', format = 'json' } = req.query;

    // Calculate date range in China time
    const now = new Date();
    let startDate;

    switch (timeRange) {
      case '1d':
        startDate = getStartOfDayChina(new Date(now.getTime() - 0 * 24 * 60 * 60 * 1000));
        break;
      case '7d':
        startDate = getStartOfDayChina(new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000));
        break;
      case '30d':
        startDate = getStartOfDayChina(new Date(now.getTime() - 29 * 24 * 60 * 60 * 1000));
        break;
      case '90d':
        startDate = getStartOfDayChina(new Date(now.getTime() - 89 * 24 * 60 * 60 * 1000));
        break;
      default:
        startDate = getStartOfDayChina(new Date(now.getTime() - 29 * 24 * 60 * 60 * 1000));
    }

    const data = await Analytics.find({
      timestamp: { $gte: startDate },
      isBot: false
    })
    .select('-__v')
    .sort({ timestamp: -1 })
    .limit(10000); // Limit to prevent memory issues

    if (format === 'csv') {
      // Convert to CSV format
      const csv = convertToCSV(data);
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename=analytics-${timeRange}.csv`);
      res.send(csv);
    } else {
      res.json({
        timeRange,
        timezone: 'UTC+8 (China Standard Time)',
        exportDate: toChinaTime(now),
        recordCount: data.length,
        data
      });
    }
  } catch (error) {
    console.error('Error exporting analytics data:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Helper function to convert data to CSV
 */
function convertToCSV(data) {
  if (data.length === 0) return '';

  const headers = Object.keys(data[0].toObject());
  const csvHeaders = headers.join(',');

  const csvRows = data.map(row => {
    const obj = row.toObject();
    return headers.map(header => {
      const value = obj[header];
      if (typeof value === 'object' && value !== null) {
        return JSON.stringify(value).replace(/"/g, '""');
      }
      return `"${String(value).replace(/"/g, '""')}"`;
    }).join(',');
  });

  return [csvHeaders, ...csvRows].join('\n');
}

module.exports = {
  getDashboard,
  getRealTime,
  getBotTraffic,
  getDebugData,
  exportData
};
