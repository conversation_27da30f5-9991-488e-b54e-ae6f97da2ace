const BlogPost = require('../models/BlogPost');
const BlogCategory = require('../models/BlogCategory');
const BlogImage = require('../models/BlogImage');
const User = require('../models/User');
const slugify = require('../utils/slugify');
const { clearClusterPageCache } = require('../middleware/cache');

// Get all published blog posts
exports.getBlogPosts = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const category = req.query.category;
    const tag = req.query.tag;
    const search = req.query.search;

    let query = { published: true };

    if (category) {
      query.category = category;
    }

    if (tag) {
      query.tags = tag;
    }

    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { content: { $regex: search, $options: 'i' } },
        { excerpt: { $regex: search, $options: 'i' } },
        { seoKeywords: { $regex: search, $options: 'i' } }
      ];
    }

    const posts = await BlogPost.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('author', 'name avatar')
      .select('title slug excerpt coverImage category tags readTime createdAt author published featured')
      .lean();

    const total = await BlogPost.countDocuments(query);

    // Add caching headers for better performance
    res.set('Cache-Control', 'public, max-age=300'); // 5 minutes cache

    res.json({
      posts,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get all blog posts (including unpublished) for admin
exports.getAllBlogPosts = async (req, res) => {
  try {
    const posts = await BlogPost.find()
      .sort({ createdAt: -1 })
      .populate('author', 'name avatar');

    res.json(posts);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get a single blog post by slug
exports.getBlogPostBySlug = async (req, res) => {
  try {
    const { slug } = req.params;

    const post = await BlogPost.findOne({
      slug,
      published: true
    }).populate('author', 'name avatar');

    if (!post) {
      return res.status(404).json({ message: 'Blog post not found' });
    }

    // Ensure author information is available (simplified)
    if (!post.author) {
      post.author = { name: 'AI Pomo Team', avatar: '' };
    }

    // Add caching headers for better performance
    res.set('Cache-Control', 'public, max-age=600'); // 10 minutes cache for individual posts

    res.json(post);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get a single blog post by ID (for admin editing)
exports.getBlogPostById = async (req, res) => {
  try {
    const { id } = req.params;

    const post = await BlogPost.findById(id)
      .populate('author', 'name avatar');

    if (!post) {
      return res.status(404).json({ message: 'Blog post not found' });
    }

    // Ensure author information is available
    if (!post.author) {
      // Find a default admin user to use as author
      const adminUser = await User.findOne({ isAdmin: true });

      // If an admin user is found, use it as the author
      if (adminUser) {
        post.author = adminUser;
        await post.save();
      } else {
        // If no admin user is found, create a placeholder author object
        post.author = { name: 'AI Pomo Team', avatar: '' };
      }
    }

    res.json(post);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Create a new blog post
exports.createBlogPost = async (req, res) => {
  try {
    const {
      title, content, excerpt, coverImage,
      tags, category, readTime, published,
      featured, seoTitle, seoDescription, seoKeywords
    } = req.body;

    const postTags = tags || [];

    const post = new BlogPost({
      title,
      slug: slugify(title),
      content,
      excerpt,
      author: req.user.id,
      coverImage,
      tags: postTags,
      category,
      readTime,
      published: published || false,
      featured: featured || false,
      seoTitle: seoTitle || title,
      seoDescription: seoDescription || excerpt,
      seoKeywords: seoKeywords || postTags.join(', ')
    });

    await post.save();

    // Clear cluster page cache since blog posts affect cluster pages
    clearClusterPageCache();

    res.status(201).json(post);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Update a blog post
exports.updateBlogPost = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title, content, excerpt, coverImage,
      tags, category, readTime, published,
      featured, seoTitle, seoDescription, seoKeywords
    } = req.body;

    let post = await BlogPost.findById(id);

    if (!post) {
      return res.status(404).json({ message: 'Blog post not found' });
    }

    // Update fields
    const updateData = {};
    if (title !== undefined) updateData.title = title;
    if (content !== undefined) updateData.content = content;
    if (excerpt !== undefined) updateData.excerpt = excerpt;
    if (coverImage !== undefined) updateData.coverImage = coverImage;
    if (tags !== undefined) updateData.tags = tags;
    if (category !== undefined) updateData.category = category;
    if (readTime !== undefined) updateData.readTime = readTime;
    if (published !== undefined) updateData.published = published;
    if (featured !== undefined) updateData.featured = featured;
    if (seoTitle !== undefined) updateData.seoTitle = seoTitle;
    if (seoDescription !== undefined) updateData.seoDescription = seoDescription;
    if (seoKeywords !== undefined) updateData.seoKeywords = seoKeywords;

    // If title is being updated, slug will be regenerated by the pre-save hook

    // Update post
    post = await BlogPost.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    );

    // Clear cluster page cache since blog posts affect cluster pages
    clearClusterPageCache();

    res.json(post);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Delete a blog post
exports.deleteBlogPost = async (req, res) => {
  try {
    const { id } = req.params;

    const post = await BlogPost.findByIdAndDelete(id);

    if (!post) {
      return res.status(404).json({ message: 'Blog post not found' });
    }

    // Clear cluster page cache since blog posts affect cluster pages
    clearClusterPageCache();

    res.json({ message: 'Blog post removed' });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get all categories
exports.getCategories = async (req, res) => {
  try {
    const categories = await BlogCategory.find().sort({ name: 1 });
    res.json(categories);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Create a new category
exports.createCategory = async (req, res) => {
  try {
    const { name, description } = req.body;

    const category = new BlogCategory({
      name,
      description
    });

    await category.save();

    res.status(201).json(category);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Create a blog post via API (for third-party integrations like n8n)
exports.createBlogPostViaApi = async (req, res) => {
  try {
    const {
      title, content, excerpt, coverImage,
      tags, category, readTime, published,
      featured, seoTitle, seoDescription, seoKeywords
    } = req.body;

    // Validate required fields
    if (!title || !content) {
      return res.status(400).json({
        message: 'Title and content are required',
        success: false
      });
    }

    // Check if category exists
    let categoryDoc;
    if (category) {
      categoryDoc = await BlogCategory.findOne({
        $or: [
          { slug: category.toLowerCase() },
          { name: new RegExp(`^${category}$`, 'i') }
        ]
      });

      if (!categoryDoc) {
        return res.status(400).json({
          message: `Category '${category}' not found`,
          success: false
        });
      }
    }

    // Create excerpt if not provided
    const postExcerpt = excerpt || content.substring(0, 160) + '...';

    // Calculate read time if not provided (average reading speed: 200 words per minute)
    const postReadTime = readTime || Math.max(1, Math.ceil(content.split(/\s+/).length / 200));

    // Auto-select cover image if not provided
    let selectedCoverImage = coverImage;
    if (!selectedCoverImage) {
      try {
        // Try to find an appropriate image based on category or tags
        const imageQuery = { isActive: true };

        if (categoryDoc) {
          // First try to match by category
          imageQuery.category = categoryDoc.slug;
        } else if (tags && tags.length > 0) {
          // If no category match, try to match by tags
          imageQuery.tags = { $in: tags };
        }

        // Get a random image from matching criteria
        const imageCount = await BlogImage.countDocuments(imageQuery);
        if (imageCount > 0) {
          const randomIndex = Math.floor(Math.random() * imageCount);
          const selectedImage = await BlogImage.findOne(imageQuery).skip(randomIndex);

          if (selectedImage) {
            selectedCoverImage = selectedImage.url;
            // Mark the image as used
            await selectedImage.incrementUsage();
          }
        }

        // Fallback to any random image if no specific match found
        if (!selectedCoverImage) {
          const fallbackImageCount = await BlogImage.countDocuments({ isActive: true });
          if (fallbackImageCount > 0) {
            const randomIndex = Math.floor(Math.random() * fallbackImageCount);
            const fallbackImage = await BlogImage.findOne({ isActive: true }).skip(randomIndex);
            if (fallbackImage) {
              selectedCoverImage = fallbackImage.url;
              await fallbackImage.incrementUsage();
            }
          }
        }

        // Final fallback to default image
        if (!selectedCoverImage) {
          selectedCoverImage = 'https://images.unsplash.com/photo-1513128034602-7814ccaddd4e';
        }
      } catch (imageError) {
        console.error('Error selecting cover image:', imageError);
        selectedCoverImage = 'https://images.unsplash.com/photo-1513128034602-7814ccaddd4e';
      }
    }

    // Create the blog post
    const post = new BlogPost({
      title,
      slug: slugify(title),
      content,
      excerpt: postExcerpt,
      author: req.user.id,
      coverImage: selectedCoverImage,
      tags: tags || [],
      category: categoryDoc ? categoryDoc.slug : 'uncategorized',
      readTime: postReadTime,
      published: published !== undefined ? published : true, // Default to published
      featured: featured || false,
      seoTitle: seoTitle || title,
      seoDescription: seoDescription || postExcerpt,
      seoKeywords: seoKeywords || (tags ? tags.join(', ') : '')
    });

    await post.save();

    // Clear cluster page cache since blog posts affect cluster pages
    clearClusterPageCache();

    // Populate the author information
    const populatedPost = await BlogPost.findById(post._id).populate('author', 'name avatar');

    res.status(201).json({
      success: true,
      message: 'Blog post created successfully',
      post: {
        id: populatedPost._id,
        title: populatedPost.title,
        slug: populatedPost.slug,
        published: populatedPost.published,
        createdAt: populatedPost.createdAt,
        author: populatedPost.author
      }
    });
  } catch (err) {
    console.error('Error creating blog post via API:', err);
    res.status(500).json({
      message: 'Server error',
      success: false,
      error: err.message
    });
  }
};
