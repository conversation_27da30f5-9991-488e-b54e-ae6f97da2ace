/**
 * Contact Controller
 *
 * Handles contact form submissions and other contact-related functionality
 */

const emailService = require('../services/emailService');

/**
 * Handle contact form submissions
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const submitContactForm = async (req, res) => {
  try {
    console.log('Contact form submission received:', req.body);

    const { name, email, message, type } = req.body;

    // Validate required fields
    if (!name || !email || !message || !type) {
      console.log('Validation failed:', { name, email, message, type });
      return res.status(400).json({
        success: false,
        message: 'Name, email, message, and type are required'
      });
    }

    // Generate subject based on type
    let subject;
    switch (type) {
      case 'premium':
        subject = 'Premium Subscription Request';
        break;
      case 'feature':
        subject = 'Feature Request';
        break;
      case 'bug':
        subject = 'Bug Report';
        break;
      case 'feedback':
        subject = 'General Feedback';
        break;
      case 'general':
        subject = 'General Inquiry';
        break;
      case 'sales':
        subject = 'Sales & Pricing Inquiry';
        break;
      case 'enterprise':
        subject = 'Enterprise Solutions Inquiry';
        break;
      case 'support':
        subject = 'Technical Support Request';
        break;
      default:
        subject = 'Contact Form Submission';
    }

    // Respond immediately to the user
    res.status(200).json({
      success: true,
      message: 'Your message has been received! We\'ll get back to you within 24 hours.',
    });

    // Send the email asynchronously in the background
    // This will not block the response to the user
    emailService.sendContactFormEmail({
      name,
      email,
      message,
      subject
    })
    .then((emailResult) => {
      console.log('Contact form email sent successfully:', emailResult);
    })
    .catch((emailError) => {
      console.error('Error sending contact form email:', emailError);
      // Email failed, but user already got success response
      // Could implement retry logic or admin notification here
    });
  } catch (error) {
    console.error('Error in contact form submission:', error);

    return res.status(500).json({
      success: false,
      message: 'Failed to send your message. Please try again later.',
      error: error.message
    });
  }
};

module.exports = {
  submitContactForm
};
