/**
 * Image Controller
 * 
 * Handles image-related API requests for blog editor
 */

const Image = require('../models/Image');

/**
 * Get all available tags
 */
const getTags = async (req, res) => {
  try {
    const tags = await Image.distinct('tags');
    const sortedTags = tags.sort();
    
    res.json({
      success: true,
      data: {
        tags: sortedTags,
        count: sortedTags.length
      }
    });
  } catch (error) {
    console.error('Error getting tags:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get tags',
      error: error.message
    });
  }
};

/**
 * Get images by tag
 */
const getImagesByTag = async (req, res) => {
  try {
    const { tag } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const sortBy = req.query.sortBy || 'downloads'; // downloads, views, recent
    
    let sortOptions = {};
    switch (sortBy) {
      case 'views':
        sortOptions = { views: -1, downloads: -1 };
        break;
      case 'recent':
        sortOptions = { importedAt: -1 };
        break;
      case 'popular':
        sortOptions = { usageCount: -1, downloads: -1 };
        break;
      default:
        sortOptions = { downloads: -1, views: -1 };
    }
    
    const skip = (page - 1) * limit;
    
    const query = tag === 'all' ? {} : { tags: { $in: [tag] } };
    
    const [images, total] = await Promise.all([
      Image.find(query)
        .sort(sortOptions)
        .skip(skip)
        .limit(limit)
        .lean(),
      Image.countDocuments(query)
    ]);
    
    res.json({
      success: true,
      data: {
        images,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        sortBy
      }
    });
  } catch (error) {
    console.error('Error getting images by tag:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get images',
      error: error.message
    });
  }
};

/**
 * Search images
 */
const searchImages = async (req, res) => {
  try {
    const { q: query } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const tag = req.query.tag;
    
    if (!query) {
      return res.status(400).json({
        success: false,
        message: 'Search query is required'
      });
    }
    
    const skip = (page - 1) * limit;
    
    // Build search criteria
    const searchCriteria = {
      $or: [
        { title: { $regex: query, $options: 'i' } },
        { tags: { $regex: query, $options: 'i' } },
        { user: { $regex: query, $options: 'i' } }
      ]
    };
    
    // Add tag filter if specified
    if (tag && tag !== 'all') {
      searchCriteria.tags = { $in: [tag] };
    }
    
    const [images, total] = await Promise.all([
      Image.find(searchCriteria)
        .sort({ downloads: -1, views: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Image.countDocuments(searchCriteria)
    ]);
    
    res.json({
      success: true,
      data: {
        images,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        query,
        tag
      }
    });
  } catch (error) {
    console.error('Error searching images:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search images',
      error: error.message
    });
  }
};

/**
 * Get popular images (most downloaded/viewed)
 */
const getPopularImages = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 50;
    
    const images = await Image.getPopularImages(limit);
    
    res.json({
      success: true,
      data: {
        images,
        count: images.length
      }
    });
  } catch (error) {
    console.error('Error getting popular images:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get popular images',
      error: error.message
    });
  }
};

/**
 * Get recently used images
 */
const getRecentlyUsed = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 20;
    
    const images = await Image.getRecentlyUsed(limit);
    
    res.json({
      success: true,
      data: {
        images,
        count: images.length
      }
    });
  } catch (error) {
    console.error('Error getting recently used images:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get recently used images',
      error: error.message
    });
  }
};

/**
 * Record image usage (when used in a blog post)
 */
const recordImageUsage = async (req, res) => {
  try {
    const { imageId } = req.params;
    
    const image = await Image.findById(imageId);
    if (!image) {
      return res.status(404).json({
        success: false,
        message: 'Image not found'
      });
    }
    
    await image.recordUsage();
    
    res.json({
      success: true,
      message: 'Image usage recorded',
      data: {
        usageCount: image.usageCount,
        lastUsed: image.lastUsed
      }
    });
  } catch (error) {
    console.error('Error recording image usage:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to record image usage',
      error: error.message
    });
  }
};

/**
 * Get image statistics
 */
const getImageStats = async (req, res) => {
  try {
    const [totalImages, totalTags, topTags, recentImages] = await Promise.all([
      Image.countDocuments(),
      Image.distinct('tags').then(tags => tags.length),
      Image.aggregate([
        { $unwind: '$tags' },
        { $group: { _id: '$tags', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 10 }
      ]),
      Image.find().sort({ importedAt: -1 }).limit(5).lean()
    ]);
    
    res.json({
      success: true,
      data: {
        totalImages,
        totalTags,
        topTags,
        recentImages
      }
    });
  } catch (error) {
    console.error('Error getting image stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get image statistics',
      error: error.message
    });
  }
};

module.exports = {
  getTags,
  getImagesByTag,
  searchImages,
  getPopularImages,
  getRecentlyUsed,
  recordImageUsage,
  getImageStats
};