/**
 * Email List Controller
 * 
 * Handles email list management for marketing campaigns
 */

const EmailList = require('../models/EmailList');
const EmailContact = require('../models/EmailContact');
const EmailCampaign = require('../models/EmailCampaign');
const User = require('../models/User');
const emailService = require('../services/emailService');
const csv = require('csv-parser');
const { Readable } = require('stream');

/**
 * Get all email lists for current user
 */
const getEmailLists = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const search = req.query.search || '';
    const tag = req.query.tag || '';

    const query = { 
      createdBy: req.user.id,
      isActive: true
    };

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    if (tag) {
      query.tags = tag;
    }

    const lists = await EmailList.find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean();

    const total = await EmailList.countDocuments(query);

    res.json({
      success: true,
      data: {
        lists,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error getting email lists:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get email lists',
      error: error.message
    });
  }
};

/**
 * Get single email list with contacts
 */
const getEmailList = async (req, res) => {
  try {
    const { listId } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;

    const list = await EmailList.findOne({
      _id: listId,
      createdBy: req.user.id,
      isActive: true
    });

    if (!list) {
      return res.status(404).json({
        success: false,
        message: 'Email list not found'
      });
    }

    // Get contacts for this list
    const contacts = await EmailContact.find({
      emailLists: listId,
      status: { $ne: 'unsubscribed' }
    })
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(limit)
    .lean();

    const totalContacts = await EmailContact.countDocuments({
      emailLists: listId,
      status: { $ne: 'unsubscribed' }
    });

    res.json({
      success: true,
      data: {
        list,
        contacts,
        pagination: {
          page,
          limit,
          total: totalContacts,
          pages: Math.ceil(totalContacts / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error getting email list:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get email list',
      error: error.message
    });
  }
};

/**
 * Create new email list
 */
const createEmailList = async (req, res) => {
  try {
    const { name, description, tags } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'List name is required'
      });
    }

    // Check if list with same name already exists
    const existingList = await EmailList.findOne({
      name,
      createdBy: req.user.id,
      isActive: true
    });

    if (existingList) {
      return res.status(400).json({
        success: false,
        message: 'A list with this name already exists'
      });
    }

    const list = new EmailList({
      name,
      description,
      tags: tags || [],
      createdBy: req.user.id
    });

    await list.save();

    res.json({
      success: true,
      message: 'Email list created successfully',
      data: list
    });
  } catch (error) {
    console.error('Error creating email list:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create email list',
      error: error.message
    });
  }
};

/**
 * Update email list
 */
const updateEmailList = async (req, res) => {
  try {
    const { listId } = req.params;
    const { name, description, tags } = req.body;

    const list = await EmailList.findOne({
      _id: listId,
      createdBy: req.user.id,
      isActive: true
    });

    if (!list) {
      return res.status(404).json({
        success: false,
        message: 'Email list not found'
      });
    }

    if (name) list.name = name;
    if (description !== undefined) list.description = description;
    if (tags !== undefined) list.tags = tags;

    await list.save();

    res.json({
      success: true,
      message: 'Email list updated successfully',
      data: list
    });
  } catch (error) {
    console.error('Error updating email list:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update email list',
      error: error.message
    });
  }
};

/**
 * Delete email list (soft delete)
 */
const deleteEmailList = async (req, res) => {
  try {
    const { listId } = req.params;

    const list = await EmailList.findOne({
      _id: listId,
      createdBy: req.user.id,
      isActive: true
    });

    if (!list) {
      return res.status(404).json({
        success: false,
        message: 'Email list not found'
      });
    }

    list.isActive = false;
    await list.save();

    // Also remove this list from all contacts
    await EmailContact.updateMany(
      { emailLists: listId },
      { $pull: { emailLists: listId } }
    );

    res.json({
      success: true,
      message: 'Email list deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting email list:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete email list',
      error: error.message
    });
  }
};

/**
 * Add contact to email list
 */
const addContact = async (req, res) => {
  try {
    const { listId } = req.params;
    const { email, firstName, lastName, company, jobTitle, phone, website, tags, customFields } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email is required'
      });
    }

    // Verify list exists and belongs to user
    const list = await EmailList.findOne({
      _id: listId,
      createdBy: req.user.id,
      isActive: true
    });

    if (!list) {
      return res.status(404).json({
        success: false,
        message: 'Email list not found'
      });
    }

    // Check if contact already exists
    let contact = await EmailContact.findOne({ email: email.toLowerCase() });

    if (contact) {
      // Add to list if not already in it
      if (!contact.emailLists.includes(listId)) {
        contact.emailLists.push(listId);
        
        // Update contact info if provided
        if (firstName) contact.firstName = firstName;
        if (lastName) contact.lastName = lastName;
        if (company) contact.company = company;
        if (jobTitle) contact.jobTitle = jobTitle;
        if (phone) contact.phone = phone;
        if (website) contact.website = website;
        if (tags && tags.length > 0) {
          contact.tags = [...new Set([...contact.tags, ...tags])];
        }
        if (customFields) {
          contact.customFields = { ...contact.customFields, ...customFields };
        }
        
        await contact.save();
      }
    } else {
      // Create new contact
      contact = new EmailContact({
        email: email.toLowerCase(),
        firstName,
        lastName,
        company,
        jobTitle,
        phone,
        website,
        emailLists: [listId],
        tags: tags || [],
        customFields: customFields || {},
        source: 'manual',
        createdBy: req.user.id
      });
      
      await contact.save();
    }

    // Update list statistics
    await updateListStatistics(listId);

    res.json({
      success: true,
      message: 'Contact added successfully',
      data: contact
    });
  } catch (error) {
    console.error('Error adding contact:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add contact',
      error: error.message
    });
  }
};

/**
 * Bulk import contacts from CSV
 */
const importContacts = async (req, res) => {
  try {
    const { listId } = req.params;
    const csvData = req.body.csvData;

    if (!csvData) {
      return res.status(400).json({
        success: false,
        message: 'CSV data is required'
      });
    }

    // Verify list exists and belongs to user
    const list = await EmailList.findOne({
      _id: listId,
      createdBy: req.user.id,
      isActive: true
    });

    if (!list) {
      return res.status(404).json({
        success: false,
        message: 'Email list not found'
      });
    }

    const contacts = [];
    const errors = [];
    let processed = 0;
    let added = 0;
    let updated = 0;

    // Parse CSV data
    const stream = Readable.from([csvData]);
    
    return new Promise((resolve, reject) => {
      stream
        .pipe(csv())
        .on('data', async (row) => {
          try {
            processed++;
            
            if (!row.email) {
              errors.push(`Row ${processed}: Email is required`);
              return;
            }

            const email = row.email.toLowerCase().trim();
            
            // Validate email format
            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
              errors.push(`Row ${processed}: Invalid email format - ${email}`);
              return;
            }

            // Check if contact exists
            let contact = await EmailContact.findOne({ email });

            if (contact) {
              if (!contact.emailLists.includes(listId)) {
                contact.emailLists.push(listId);
                updated++;
              }
              
              // Update fields if provided in CSV
              if (row.firstName) contact.firstName = row.firstName.trim();
              if (row.lastName) contact.lastName = row.lastName.trim();
              if (row.company) contact.company = row.company.trim();
              if (row.jobTitle) contact.jobTitle = row.jobTitle.trim();
              if (row.phone) contact.phone = row.phone.trim();
              if (row.website) contact.website = row.website.trim();
              
              await contact.save();
            } else {
              // Create new contact
              contact = new EmailContact({
                email,
                firstName: row.firstName ? row.firstName.trim() : '',
                lastName: row.lastName ? row.lastName.trim() : '',
                company: row.company ? row.company.trim() : '',
                jobTitle: row.jobTitle ? row.jobTitle.trim() : '',
                phone: row.phone ? row.phone.trim() : '',
                website: row.website ? row.website.trim() : '',
                emailLists: [listId],
                source: 'import',
                createdBy: req.user.id
              });
              
              await contact.save();
              added++;
            }

            contacts.push(contact);
          } catch (error) {
            errors.push(`Row ${processed}: ${error.message}`);
          }
        })
        .on('end', async () => {
          try {
            // Update list statistics
            await updateListStatistics(listId);

            resolve(res.json({
              success: true,
              message: 'Contacts imported successfully',
              data: {
                processed,
                added,
                updated,
                errors,
                totalContacts: contacts.length
              }
            }));
          } catch (error) {
            reject(error);
          }
        })
        .on('error', (error) => {
          reject(error);
        });
    });

  } catch (error) {
    console.error('Error importing contacts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to import contacts',
      error: error.message
    });
  }
};

/**
 * Remove contact from list
 */
const removeContact = async (req, res) => {
  try {
    const { listId, contactId } = req.params;

    const contact = await EmailContact.findById(contactId);
    if (!contact) {
      return res.status(404).json({
        success: false,
        message: 'Contact not found'
      });
    }

    // Remove list from contact
    contact.emailLists = contact.emailLists.filter(id => id.toString() !== listId);
    await contact.save();

    // Update list statistics
    await updateListStatistics(listId);

    res.json({
      success: true,
      message: 'Contact removed from list successfully'
    });
  } catch (error) {
    console.error('Error removing contact:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove contact',
      error: error.message
    });
  }
};

/**
 * Get users who have registered in the system
 */
const getRegisteredUsers = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const search = req.query.search || '';

    const query = {};
    
    if (search) {
      query.$or = [
        { email: { $regex: search, $options: 'i' } },
        { username: { $regex: search, $options: 'i' } }
      ];
    }

    const users = await User.find(query)
      .select('email username createdAt isActive')
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean();

    const total = await User.countDocuments(query);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error getting registered users:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get registered users',
      error: error.message
    });
  }
};

/**
 * Import registered users to email list
 */
const importRegisteredUsers = async (req, res) => {
  try {
    const { listId } = req.params;
    const { userIds, allUsers } = req.body;

    // Verify list exists and belongs to user
    const list = await EmailList.findOne({
      _id: listId,
      createdBy: req.user.id,
      isActive: true
    });

    if (!list) {
      return res.status(404).json({
        success: false,
        message: 'Email list not found'
      });
    }

    let query = {};
    if (!allUsers && userIds && userIds.length > 0) {
      query._id = { $in: userIds };
    }

    const users = await User.find(query).select('email username');
    
    let added = 0;
    let updated = 0;
    const errors = [];

    for (const user of users) {
      try {
        let contact = await EmailContact.findOne({ email: user.email });

        if (contact) {
          if (!contact.emailLists.includes(listId)) {
            contact.emailLists.push(listId);
            updated++;
          }
          await contact.save();
        } else {
          contact = new EmailContact({
            email: user.email,
            firstName: user.username || '',
            emailLists: [listId],
            source: 'registration',
            createdBy: req.user.id
          });
          await contact.save();
          added++;
        }
      } catch (error) {
        errors.push(`${user.email}: ${error.message}`);
      }
    }

    // Update list statistics
    await updateListStatistics(listId);

    res.json({
      success: true,
      message: 'Registered users imported successfully',
      data: {
        processed: users.length,
        added,
        updated,
        errors
      }
    });
  } catch (error) {
    console.error('Error importing registered users:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to import registered users',
      error: error.message
    });
  }
};

/**
 * Helper function to update list statistics
 */
async function updateListStatistics(listId) {
  const totalContacts = await EmailContact.countDocuments({ emailLists: listId });
  const activeContacts = await EmailContact.countDocuments({ 
    emailLists: listId, 
    status: 'active' 
  });
  const unsubscribedContacts = await EmailContact.countDocuments({ 
    emailLists: listId, 
    status: 'unsubscribed' 
  });

  await EmailList.findByIdAndUpdate(listId, {
    totalContacts,
    activeContacts,
    unsubscribedContacts
  });
}

module.exports = {
  getEmailLists,
  getEmailList,
  createEmailList,
  updateEmailList,
  deleteEmailList,
  addContact,
  importContacts,
  removeContact,
  getRegisteredUsers,
  importRegisteredUsers
};