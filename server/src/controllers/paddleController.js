const paddleConfig = require('../config/paddle');
const https = require('https');

const testPaddle = (req, res) => {
  try {
    res.json({
      success: true,
      message: 'Paddle integration is working',
      config: {
        environment: paddleConfig.environment,
        vendorId: paddleConfig.vendorId,
        priceIds: paddleConfig.priceIds,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
};

const makePaddleAPIRequest = (path, method, data) => {
  return new Promise((resolve, reject) => {
    const postData = data ? JSON.stringify(data) : null;
    
    const options = {
      hostname: 'api.paddle.com',
      port: 443,
      path: path,
      method: method,
      headers: {
        'Authorization': `Bearer ${paddleConfig.apiKey}`,
        'Content-Type': 'application/json',
      },
    };

    if (postData) {
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = https.request(options, (res) => {
      let responseBody = '';
      
      res.on('data', (chunk) => {
        responseBody += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedResponse = JSON.parse(responseBody);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(parsedResponse);
          } else {
            reject(new Error(`Paddle API error: ${res.statusCode} - ${responseBody}`));
          }
        } catch (error) {
          reject(new Error(`Failed to parse Paddle API response: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error(`Paddle API request failed: ${error.message}`));
    });

    if (postData) {
      req.write(postData);
    }
    
    req.end();
  });
};

const createCheckout = async (req, res) => {
  try {
    const { planType } = req.body;
    const userId = req.user.id;
    
    if (!['monthly', 'yearly', 'lifetime'].includes(planType)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid plan type',
      });
    }

    const priceId = paddleConfig.priceIds[planType];
    
    const checkoutData = {
      items: [
        {
          price_id: priceId,
          quantity: 1,
        },
      ],
      customer_email: req.user.email,
      success_url: paddleConfig.successUrl,
      cancel_url: paddleConfig.cancelUrl,
      custom_data: {
        user_id: userId,
        plan_type: planType,
      },
    };

    const checkoutResponse = await makePaddleAPIRequest('/checkout-sessions', 'POST', checkoutData);
    
    res.json({
      success: true,
      planType,
      priceId,
      checkoutUrl: checkoutResponse.data.url,
      sessionId: checkoutResponse.data.id,
    });
  } catch (error) {
    console.error('Paddle checkout error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
};

const handleWebhook = async (req, res) => {
  try {
    const event = req.body;
    
    console.log('Paddle webhook received:', event.event_type);
    
    switch (event.event_type) {
      case 'subscription.created':
        await handleSubscriptionCreated(event.data);
        break;
      case 'subscription.updated':
        await handleSubscriptionUpdated(event.data);
        break;
      case 'subscription.canceled':
        await handleSubscriptionCanceled(event.data);
        break;
      case 'transaction.completed':
        await handleTransactionCompleted(event.data);
        break;
      default:
        console.log(`Unhandled webhook event: ${event.event_type}`);
    }
    
    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Webhook handling error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
};

const handleSubscriptionCreated = async (subscriptionData) => {
  const User = require('../models/User');
  const Subscription = require('../models/Subscription');
  
  try {
    const userId = subscriptionData.custom_data?.user_id;
    if (!userId) {
      throw new Error('No user_id in subscription custom_data');
    }
    
    const subscription = new Subscription({
      userId,
      plan: subscriptionData.custom_data.plan_type,
      paymentMethod: 'paddle',
      paddleSubscriptionId: subscriptionData.id,
      paddleCustomerId: subscriptionData.customer_id,
      paddleProductId: subscriptionData.items[0]?.product_id,
      paddlePriceId: subscriptionData.items[0]?.price_id,
      startDate: new Date(subscriptionData.started_at),
      nextBillingDate: subscriptionData.next_billed_at ? new Date(subscriptionData.next_billed_at) : null,
      status: 'active',
    });
    
    await subscription.save();
    await User.findByIdAndUpdate(userId, { isPremium: true });
    
    console.log(`Subscription created for user ${userId}: ${subscriptionData.id}`);
  } catch (error) {
    console.error('Error handling subscription creation:', error);
    throw error;
  }
};

const handleSubscriptionUpdated = async (subscriptionData) => {
  const Subscription = require('../models/Subscription');
  
  try {
    await Subscription.findOneAndUpdate(
      { paddleSubscriptionId: subscriptionData.id },
      {
        status: subscriptionData.status,
        nextBillingDate: subscriptionData.next_billed_at ? new Date(subscriptionData.next_billed_at) : null,
      }
    );
    
    console.log(`Subscription updated: ${subscriptionData.id}`);
  } catch (error) {
    console.error('Error handling subscription update:', error);
    throw error;
  }
};

const handleSubscriptionCanceled = async (subscriptionData) => {
  const User = require('../models/User');
  const Subscription = require('../models/Subscription');
  
  try {
    const subscription = await Subscription.findOneAndUpdate(
      { paddleSubscriptionId: subscriptionData.id },
      { 
        status: 'canceled',
        endDate: new Date(),
      }
    );
    
    if (subscription) {
      await User.findByIdAndUpdate(subscription.userId, { isPremium: false });
    }
    
    console.log(`Subscription canceled: ${subscriptionData.id}`);
  } catch (error) {
    console.error('Error handling subscription cancellation:', error);
    throw error;
  }
};

const handleTransactionCompleted = async (transactionData) => {
  const User = require('../models/User');
  const Subscription = require('../models/Subscription');
  
  try {
    const userId = transactionData.custom_data?.user_id;
    const planType = transactionData.custom_data?.plan_type;
    
    if (!userId || !planType) {
      throw new Error('Missing user_id or plan_type in transaction custom_data');
    }
    
    if (planType === 'lifetime') {
      const subscription = new Subscription({
        userId,
        plan: 'lifetime',
        paymentMethod: 'paddle',
        paddleTransactionId: transactionData.id,
        paddleCustomerId: transactionData.customer_id,
        paddleProductId: transactionData.items[0]?.product_id,
        paddlePriceId: transactionData.items[0]?.price_id,
        startDate: new Date(),
        status: 'active',
      });
      
      await subscription.save();
      await User.findByIdAndUpdate(userId, { isPremium: true });
      
      console.log(`Lifetime subscription created for user ${userId}`);
    }
  } catch (error) {
    console.error('Error handling transaction completion:', error);
    throw error;
  }
};

module.exports = {
  testPaddle,
  createCheckout,
  handleWebhook,
};