/**
 * Admin Controller
 *
 * Handles admin-related functionality including subscription management
 */

const User = require('../models/User');
const Subscription = require('../models/Subscription');
const Payment = require('../models/Payment');
const emailService = require('../services/emailService');
const subscriptionService = require('../services/subscriptionService');

/**
 * Get all pending subscription requests
 */
exports.getPendingSubscriptions = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    // Get all pending subscriptions with user details
    const pendingSubscriptions = await Subscription.find({
      paymentStatus: 'pending'
    })
    .populate('user', 'name email')
    .sort({ createdAt: -1 });

    res.json(pendingSubscriptions);
  } catch (error) {
    console.error('Error getting pending subscriptions:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get all subscriptions (with optional filters)
 */
exports.getAllSubscriptions = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    // Parse query parameters
    const { status, plan, page = 1, limit = 20 } = req.query;
    const skip = (page - 1) * limit;

    // Build query
    const query = {};
    if (status) query.paymentStatus = status;
    if (plan) query.plan = plan;

    // Get subscriptions with pagination
    const subscriptions = await Subscription.find(query)
      .populate('user', 'name email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await Subscription.countDocuments(query);

    res.json({
      subscriptions,
      pagination: {
        total,
        page: parseInt(page),
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting all subscriptions:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Confirm a subscription payment
 */
exports.confirmSubscription = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    const { subscriptionId } = req.params;
    const { expiryDate, notes } = req.body;

    // Find the subscription
    const subscription = await Subscription.findById(subscriptionId);
    if (!subscription) {
      return res.status(404).json({ message: 'Subscription not found' });
    }

    // Update subscription status
    subscription.paymentStatus = 'confirmed';
    subscription.confirmationDate = new Date();
    subscription.active = true;

    // Set expiry date based on plan
    if (expiryDate) {
      subscription.expiryDate = new Date(expiryDate);
    } else if (subscription.plan === 'yearly') {
      // Default: 1 year from now
      const oneYearFromNow = new Date();
      oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
      subscription.expiryDate = oneYearFromNow;
    } else if (subscription.plan === 'lifetime') {
      // Set a far future date for lifetime (e.g., 100 years)
      const farFuture = new Date();
      farFuture.setFullYear(farFuture.getFullYear() + 100);
      subscription.expiryDate = farFuture;
    }

    // Add admin notes if provided
    if (notes) {
      subscription.adminNotes = notes;
    }

    await subscription.save();

    // Update user's subscription status using centralized service
    const user = await User.findById(subscription.user);
    if (user) {
      await subscriptionService.updateUserSubscriptionStatus(subscription.user, {
        plan: subscription.plan,
        active: true,
        paymentStatus: 'confirmed',
        subscriptionStatus: 'active',
        expiryDate: subscription.expiryDate,
        subscriptionId: subscription._id
      });
    }

    // Send confirmation email to user
    try {
      await emailService.sendEmail({
        to: user.email,
        subject: 'Your Premium Subscription is Active!',
        text: `
Dear ${user.name},

Great news! Your payment for the ${subscription.plan} plan has been confirmed and your premium subscription is now active.

Subscription Details:
- Plan: ${subscription.plan}
- Payment Amount: $${subscription.paymentAmount}
- Payment Method: ${subscription.paymentMethod}
- Expiry Date: ${subscription.expiryDate ? new Date(subscription.expiryDate).toLocaleDateString() : 'Lifetime'}

You now have access to all premium features including unlimited projects!

Thank you for your support.

Best regards,
The Pomodoro Timer Team
        `,
      });
    } catch (emailError) {
      console.error('Error sending confirmation email:', emailError);
      // Continue even if email fails
    }

    res.json({
      message: 'Subscription confirmed successfully',
      subscription
    });
  } catch (error) {
    console.error('Error confirming subscription:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Reject a subscription payment
 */
exports.rejectSubscription = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    const { subscriptionId } = req.params;
    const { reason } = req.body;

    // Find the subscription
    const subscription = await Subscription.findById(subscriptionId);
    if (!subscription) {
      return res.status(404).json({ message: 'Subscription not found' });
    }

    // Update subscription status
    subscription.paymentStatus = 'rejected';
    subscription.rejectionReason = reason || 'Payment could not be verified';
    subscription.active = false;

    await subscription.save();

    // Send rejection email to user
    const user = await User.findById(subscription.user);
    if (user) {
      try {
        await emailService.sendEmail({
          to: user.email,
          subject: 'Subscription Payment Not Confirmed',
          text: `
Dear ${user.name},

We were unable to confirm your payment for the ${subscription.plan} plan.

Reason: ${subscription.rejectionReason}

Please contact us at ${process.env.CONTACT_EMAIL || '<EMAIL>'} if you believe this is an error or if you would like to try a different payment method.

Best regards,
The Pomodoro Timer Team
          `,
        });
      } catch (emailError) {
        console.error('Error sending rejection email:', emailError);
        // Continue even if email fails
      }
    }

    res.json({
      message: 'Subscription rejected',
      subscription
    });
  } catch (error) {
    console.error('Error rejecting subscription:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get today's stats for admin dashboard
 */
exports.getTodayStats = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    // Get models
    const Task = require('../models/Task');
    const Project = require('../models/Project');
    const Pomodoro = require('../models/Pomodoro');
    const User = require('../models/User');

    // Get today's date range in China time (UTC+8)
    // Create date in UTC
    const now = new Date();

    // Convert to China time by adding 8 hours to UTC
    const chinaTimeOffset = 8 * 60 * 60 * 1000; // 8 hours in milliseconds
    const chinaTime = new Date(now.getTime() + chinaTimeOffset);

    // Set to start of day in China time
    const today = new Date(chinaTime);
    today.setUTCHours(0, 0, 0, 0);

    // Set to end of day in China time
    const tomorrow = new Date(today);
    tomorrow.setUTCDate(tomorrow.getUTCDate() + 1);

    // Get today's user registrations
    const newUsers = await User.countDocuments({
      createdAt: { $gte: today, $lt: tomorrow }
    });

    // Get today's pomodoros
    const todayPomodoros = await Pomodoro.countDocuments({
      completed: true,
      endTime: { $gte: today, $lt: tomorrow }
    });

    // Get today's total pomodoro duration
    const pomodoroStats = await Pomodoro.aggregate([
      {
        $match: {
          completed: true,
          endTime: { $gte: today, $lt: tomorrow }
        }
      },
      {
        $group: {
          _id: null,
          totalDuration: { $sum: '$duration' },
          count: { $sum: 1 }
        }
      }
    ]);

    const totalPomodoroMinutes = pomodoroStats.length > 0 ? pomodoroStats[0].totalDuration : 0;

    // Get today's new projects
    const newProjects = await Project.countDocuments({
      createdAt: { $gte: today, $lt: tomorrow }
    });

    // Get today's completed projects
    const completedProjects = await Project.countDocuments({
      status: 'completed',
      updatedAt: { $gte: today, $lt: tomorrow }
    });

    // Get today's new tasks
    const newTasks = await Task.countDocuments({
      createdAt: { $gte: today, $lt: tomorrow }
    });

    // Get today's completed tasks
    const completedTasks = await Task.countDocuments({
      completed: true,
      updatedAt: { $gte: today, $lt: tomorrow }
    });

    // Get today's payments
    const payments = await Subscription.aggregate([
      {
        $match: {
          paymentStatus: 'confirmed',
          confirmationDate: { $gte: today, $lt: tomorrow }
        }
      },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: '$paymentAmount' },
          count: { $sum: 1 }
        }
      }
    ]);

    const todayRevenue = payments.length > 0 ? payments[0].totalAmount : 0;
    const paymentCount = payments.length > 0 ? payments[0].count : 0;

    // Get active users today (users who completed at least one pomodoro today)
    const activeUsers = await Pomodoro.aggregate([
      {
        $match: {
          completed: true,
          endTime: { $gte: today, $lt: tomorrow }
        }
      },
      {
        $group: {
          _id: '$user'
        }
      },
      {
        $count: 'count'
      }
    ]);

    const activeUserCount = activeUsers.length > 0 ? activeUsers[0].count : 0;

    // Format date in China time for display
    const chinaDateStr = today.toISOString().split('T')[0];

    res.json({
      date: chinaDateStr,
      timezone: 'UTC+8 (China Standard Time)',
      newUsers,
      activeUsers: activeUserCount,
      pomodoros: {
        count: todayPomodoros,
        totalMinutes: totalPomodoroMinutes
      },
      projects: {
        new: newProjects,
        completed: completedProjects
      },
      tasks: {
        new: newTasks,
        completed: completedTasks
      },
      revenue: {
        amount: todayRevenue,
        transactions: paymentCount
      }
    });
  } catch (error) {
    console.error('Error fetching today stats:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get quick stats for admin dashboard
 */
exports.getQuickStats = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    // Get total users
    const totalUsers = await User.countDocuments();

    // Convert to China time (UTC+8)
    const chinaTimeOffset = 8 * 60 * 60 * 1000; // 8 hours in milliseconds
    const now = new Date();
    const chinaTime = new Date(now.getTime() + chinaTimeOffset);

    // Get new users in the last 7 days (China time)
    const lastWeek = new Date(chinaTime);
    lastWeek.setDate(chinaTime.getDate() - 7);
    const newUsers = await User.countDocuments({ createdAt: { $gte: lastWeek } });

    // Get total revenue
    const confirmedSubscriptions = await Subscription.find({ paymentStatus: 'confirmed' });
    const totalRevenue = confirmedSubscriptions.reduce((sum, sub) => sum + (sub.paymentAmount || 0), 0);

    // Calculate revenue change (comparing last 30 days to previous 30 days) in China time
    const lastMonth = new Date(chinaTime);
    lastMonth.setDate(chinaTime.getDate() - 30);
    const previousMonth = new Date(chinaTime);
    previousMonth.setDate(chinaTime.getDate() - 60);

    const lastMonthSubs = await Subscription.find({
      paymentStatus: 'confirmed',
      confirmationDate: { $gte: lastMonth }
    });

    const previousMonthSubs = await Subscription.find({
      paymentStatus: 'confirmed',
      confirmationDate: { $gte: previousMonth, $lt: lastMonth }
    });

    const lastMonthRevenue = lastMonthSubs.reduce((sum, sub) => sum + (sub.paymentAmount || 0), 0);
    const previousMonthRevenue = previousMonthSubs.reduce((sum, sub) => sum + (sub.paymentAmount || 0), 0);

    let revenueChange = 0;
    if (previousMonthRevenue > 0) {
      revenueChange = Math.round(((lastMonthRevenue - previousMonthRevenue) / previousMonthRevenue) * 100);
    } else if (lastMonthRevenue > 0) {
      revenueChange = 100; // If there was no revenue before but there is now, that's a 100% increase
    }

    // Get total pomodoros, projects, and tasks
    const Task = require('../models/Task');
    const Project = require('../models/Project');
    const Pomodoro = require('../models/Pomodoro');

    const totalPomodoros = await Pomodoro.countDocuments({ completed: true });
    const newPomodoros = await Pomodoro.countDocuments({
      completed: true,
      endTime: { $gte: lastWeek }
    });

    const totalProjects = await Project.countDocuments();
    const newProjects = await Project.countDocuments({ createdAt: { $gte: lastWeek } });

    // Add timezone info to the response for clarity
    const timezoneInfo = 'UTC+8 (China Standard Time)';

    res.json({
      totalUsers,
      newUsers,
      totalRevenue,
      revenueChange,
      totalPomodoros,
      newPomodoros,
      totalProjects,
      newProjects,
      timezone: timezoneInfo
    });
  } catch (error) {
    console.error('Error fetching quick stats:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get detailed stats for admin dashboard
 */
exports.getStats = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    const {
      timeRange = 'this_week',
      startDate: customStartDate,
      endDate: customEndDate
    } = req.query;

    // Convert to China time (UTC+8)
    const chinaTimeOffset = 8 * 60 * 60 * 1000; // 8 hours in milliseconds
    const now = new Date();
    const chinaTime = new Date(now.getTime() + chinaTimeOffset);

    let startDate;
    let endDate = new Date(chinaTime);
    let timeRangeLabel = '';

    // Determine the start date based on the time range in China time
    switch (timeRange) {
      case 'all':
        // No start date filter for all time
        startDate = null;
        timeRangeLabel = 'All Time';
        break;

      case 'today':
        // Start of today in China time
        startDate = new Date(chinaTime);
        startDate.setHours(0, 0, 0, 0);
        timeRangeLabel = 'Today';
        break;

      case 'this_week':
        // Start of this week (Monday) in China time
        startDate = new Date(chinaTime);
        const dayOfWeek = startDate.getDay() || 7; // Convert Sunday (0) to 7
        startDate.setDate(startDate.getDate() - dayOfWeek + 1); // Go to Monday
        startDate.setHours(0, 0, 0, 0);
        timeRangeLabel = 'This Week';
        break;

      case 'last_week':
        // Start of last week (Monday) in China time
        startDate = new Date(chinaTime);
        const lastWeekDay = startDate.getDay() || 7; // Convert Sunday (0) to 7
        startDate.setDate(startDate.getDate() - lastWeekDay + 1 - 7); // Go to last Monday
        startDate.setHours(0, 0, 0, 0);

        // End of last week (Sunday) in China time
        endDate = new Date(startDate);
        endDate.setDate(endDate.getDate() + 6);
        endDate.setHours(23, 59, 59, 999);
        timeRangeLabel = 'Last Week';
        break;

      case 'this_month':
        // Start of this month in China time
        startDate = new Date(chinaTime);
        startDate.setDate(1);
        startDate.setHours(0, 0, 0, 0);
        timeRangeLabel = 'This Month';
        break;

      case 'last_month':
        // Start of last month in China time
        startDate = new Date(chinaTime);
        startDate.setMonth(startDate.getMonth() - 1);
        startDate.setDate(1);
        startDate.setHours(0, 0, 0, 0);

        // End of last month in China time
        endDate = new Date(chinaTime);
        endDate.setDate(0); // Last day of previous month
        endDate.setHours(23, 59, 59, 999);
        timeRangeLabel = 'Last Month';
        break;

      case 'this_year':
        // Start of this year in China time
        startDate = new Date(chinaTime);
        startDate.setMonth(0, 1);
        startDate.setHours(0, 0, 0, 0);
        timeRangeLabel = 'This Year';
        break;

      case 'last_year':
        // Start of last year in China time
        startDate = new Date(chinaTime);
        startDate.setFullYear(startDate.getFullYear() - 1);
        startDate.setMonth(0, 1);
        startDate.setHours(0, 0, 0, 0);

        // End of last year in China time
        endDate = new Date(chinaTime);
        endDate.setFullYear(endDate.getFullYear() - 1);
        endDate.setMonth(11, 31);
        endDate.setHours(23, 59, 59, 999);
        timeRangeLabel = 'Last Year';
        break;

      case 'custom':
        // Custom date range
        if (customStartDate && customEndDate) {
          startDate = new Date(customStartDate);
          endDate = new Date(customEndDate);
          endDate.setHours(23, 59, 59, 999);
          timeRangeLabel = 'Custom Range';
        } else {
          // Default to this week if custom dates are not provided
          startDate = new Date(chinaTime);
          const dayOfWeek = startDate.getDay() || 7;
          startDate.setDate(startDate.getDate() - dayOfWeek + 1);
          startDate.setHours(0, 0, 0, 0);
          timeRangeLabel = 'This Week';
        }
        break;

      default:
        // Default to this week
        startDate = new Date(chinaTime);
        const defaultDayOfWeek = startDate.getDay() || 7;
        startDate.setDate(startDate.getDate() - defaultDayOfWeek + 1);
        startDate.setHours(0, 0, 0, 0);
        timeRangeLabel = 'This Week';
    }

    // Add timezone info to the response for clarity
    const timezoneInfo = 'UTC+8 (China Standard Time)';

    // Get models
    const Task = require('../models/Task');
    const Project = require('../models/Project');
    const Pomodoro = require('../models/Pomodoro');
    const mongoose = require('mongoose');

    // Build match conditions for date filtering
    let userMatchCondition = {};
    let subscriptionMatchCondition = { paymentStatus: 'confirmed' };
    let pomodoroMatchCondition = { completed: true };

    if (startDate) {
      userMatchCondition.createdAt = { $gte: startDate };
      subscriptionMatchCondition.confirmationDate = { $gte: startDate };
      pomodoroMatchCondition.endTime = { $gte: startDate };

      // Add end date for specific time ranges
      if (timeRange === 'last_week' || timeRange === 'last_month' || timeRange === 'last_year' || timeRange === 'custom') {
        userMatchCondition.createdAt.$lte = endDate;
        subscriptionMatchCondition.confirmationDate.$lte = endDate;
        pomodoroMatchCondition.endTime.$lte = endDate;
      }
    }

    // Get user registrations by date
    const userRegistrations = await User.aggregate([
      {
        $match: userMatchCondition
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      },
      {
        $project: {
          date: '$_id',
          count: 1,
          _id: 0
        }
      }
    ]);

    // Get payments by date
    const payments = await Subscription.aggregate([
      {
        $match: subscriptionMatchCondition
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$confirmationDate' }
          },
          amount: { $sum: '$paymentAmount' },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      },
      {
        $project: {
          date: '$_id',
          amount: 1,
          count: 1,
          _id: 0
        }
      }
    ]);

    // Get pomodoros by date
    const pomodoros = await Pomodoro.aggregate([
      {
        $match: pomodoroMatchCondition
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$endTime' }
          },
          count: { $sum: 1 },
          totalDuration: { $sum: '$duration' }
        }
      },
      {
        $sort: { _id: 1 }
      },
      {
        $project: {
          date: '$_id',
          count: 1,
          totalDuration: 1,
          _id: 0
        }
      }
    ]);

    // Get subscription plans distribution
    const subscriptionPlans = await User.aggregate([
      {
        $group: {
          _id: '$subscription.plan',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          plan: { $ifNull: ['$_id', 'free'] },
          count: 1,
          _id: 0
        }
      }
    ]);

    res.json({
      userRegistrations,
      payments,
      pomodoros,
      subscriptionPlans,
      timeRange,
      timeRangeLabel,
      timezone: timezoneInfo,
      dateRange: {
        startDate: startDate ? startDate.toISOString() : null,
        endDate: endDate.toISOString()
      }
    });
  } catch (error) {
    console.error('Error fetching detailed stats:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get users with pagination and filters
 */
exports.getUsers = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    const {
      page = 1,
      limit = 10,
      sort = 'createdAt',
      direction = 'desc',
      status,
      subscription,
      search
    } = req.query;

    // Build query
    const query = {};

    if (status) {
      query.isActive = status === 'active';
    }

    if (subscription) {
      query['subscription.plan'] = subscription;
    }

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    // Count total documents for pagination
    const total = await User.countDocuments(query);

    // Calculate pagination
    const totalPages = Math.ceil(total / limit);
    const skip = (page - 1) * limit;

    // Build sort object
    const sortObj = {};
    sortObj[sort] = direction === 'asc' ? 1 : -1;

    // Fetch users
    const users = await User.find(query)
      .sort(sortObj)
      .skip(skip)
      .limit(parseInt(limit));

    res.json({
      users,
      pagination: {
        total,
        pages: totalPages,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get user stats
 */
exports.getUserStats = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    const { userId } = req.params;

    // Get models
    const Task = require('../models/Task');
    const Project = require('../models/Project');
    const Pomodoro = require('../models/Pomodoro');
    const mongoose = require('mongoose');

    // Verify user exists
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get pomodoro stats
    const totalPomodoros = await Pomodoro.countDocuments({
      user: mongoose.Types.ObjectId(userId),
      completed: true
    });

    // Get pomodoros by date (last 30 days in China time)
    // Convert to China time (UTC+8)
    const chinaTimeOffset = 8 * 60 * 60 * 1000; // 8 hours in milliseconds
    const now = new Date();
    const chinaTime = new Date(now.getTime() + chinaTimeOffset);

    // Calculate 30 days ago in China time
    const thirtyDaysAgo = new Date(chinaTime);
    thirtyDaysAgo.setDate(chinaTime.getDate() - 30);

    const pomodorosByDate = await Pomodoro.aggregate([
      {
        $match: {
          user: mongoose.Types.ObjectId(userId),
          completed: true,
          endTime: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$endTime' }
          },
          count: { $sum: 1 },
          totalDuration: { $sum: '$duration' }
        }
      },
      {
        $sort: { _id: 1 }
      },
      {
        $project: {
          date: '$_id',
          count: 1,
          totalDuration: 1,
          _id: 0
        }
      }
    ]);

    // Get project stats
    const totalProjects = await Project.countDocuments({ user: mongoose.Types.ObjectId(userId) });
    const activeProjects = await Project.countDocuments({
      user: mongoose.Types.ObjectId(userId),
      status: { $in: ['open', 'working'] }
    });
    const completedProjects = await Project.countDocuments({
      user: mongoose.Types.ObjectId(userId),
      status: 'completed'
    });

    // Get task stats
    const totalTasks = await Task.countDocuments({ user: mongoose.Types.ObjectId(userId) });
    const completedTasks = await Task.countDocuments({
      user: mongoose.Types.ObjectId(userId),
      completed: true
    });

    // Get payment history
    const subscriptions = await Subscription.find({
      user: mongoose.Types.ObjectId(userId),
      paymentStatus: 'confirmed'
    }).sort({ confirmationDate: -1 });

    const totalSpent = subscriptions.reduce((sum, sub) => sum + (sub.paymentAmount || 0), 0);

    res.json({
      pomodoros: {
        total: totalPomodoros,
        byDate: pomodorosByDate
      },
      projects: {
        total: totalProjects,
        active: activeProjects,
        completed: completedProjects
      },
      tasks: {
        total: totalTasks,
        completed: completedTasks
      },
      payments: {
        total: totalSpent,
        subscriptions: subscriptions.length
      },
      user: {
        name: user.name,
        email: user.email,
        createdAt: user.createdAt,
        subscription: user.subscription
      },
      timezone: 'UTC+8 (China Standard Time)'
    });
  } catch (error) {
    console.error('Error fetching user stats:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get user payment history
 */
exports.getUserPaymentHistory = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    const { userId } = req.params;
    const mongoose = require('mongoose');

    // Get subscriptions
    const subscriptions = await Subscription.find({ user: mongoose.Types.ObjectId(userId) })
      .sort({ createdAt: -1 });

    // Get detailed payment records
    const payments = await Payment.find({ user: mongoose.Types.ObjectId(userId) })
      .populate('subscription', 'plan paddleSubscriptionId')
      .sort({ attemptedAt: -1 });

    res.json({
      subscriptions,
      payments,
      summary: {
        totalSubscriptions: subscriptions.length,
        totalPayments: payments.length,
        successfulPayments: payments.filter(p => p.status === 'completed').length,
        failedPayments: payments.filter(p => p.status === 'failed').length,
        totalPaid: payments
          .filter(p => p.status === 'completed')
          .reduce((sum, p) => sum + (p.amount || 0), 0),
      }
    });
  } catch (error) {
    console.error('Error fetching user payment history:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Update user
 */
exports.updateUser = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    const { userId } = req.params;
    const { name, email, isAdmin, subscriptionPlan, isActive } = req.body;

    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Update user fields
    if (name) user.name = name;
    if (email) user.email = email;
    if (typeof isAdmin === 'boolean') user.isAdmin = isAdmin;
    if (typeof isActive === 'boolean') user.isActive = isActive;

    // Update subscription if provided
    if (subscriptionPlan) {
      const previousPlan = user.subscription?.plan || 'free';
      
      // Check if user is being downgraded to free (admin cancellation)
      const isAdminCancellation = subscriptionPlan === 'free' && 
                                  previousPlan !== 'free' && 
                                  ['monthly', 'yearly', 'lifetime'].includes(previousPlan);

      if (!user.subscription) {
        user.subscription = {};
      }

      user.subscription.plan = subscriptionPlan;

      // Set expiry date based on plan
      if (subscriptionPlan === 'monthly') {
        const oneMonthFromNow = new Date();
        oneMonthFromNow.setMonth(oneMonthFromNow.getMonth() + 1);
        user.subscription.expiryDate = oneMonthFromNow;
      } else if (subscriptionPlan === 'yearly') {
        const oneYearFromNow = new Date();
        oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
        user.subscription.expiryDate = oneYearFromNow;
      } else if (subscriptionPlan === 'lifetime') {
        const farFuture = new Date();
        farFuture.setFullYear(farFuture.getFullYear() + 100);
        user.subscription.expiryDate = farFuture;
      } else {
        // Free plan has no expiry
        user.subscription.expiryDate = null;
      }

      user.subscription.active = subscriptionPlan !== 'free';

      // Set max projects based on plan
      if (subscriptionPlan === 'free') {
        user.maxProjects = 3;
      } else {
        // For premium plans (monthly, yearly or lifetime)
        user.maxProjects = 999; // Unlimited
      }

      // If admin is cancelling subscription (setting to free), trigger cancellation logic
      if (isAdminCancellation) {
        console.log('Admin cancellation detected for user:', userId, 'from plan:', previousPlan, 'to free');
        
        try {
          // Find current active subscription to cancel
          const currentSubscription = await Subscription.findOne({
            user: userId,
            $or: [
              { paymentStatus: 'confirmed' },
              { paymentStatus: 'trial' },
              { subscriptionStatus: 'trialing' },
              { subscriptionStatus: 'active' }
            ]
          }).sort({ createdAt: -1 });

          if (currentSubscription && currentSubscription.paddleSubscriptionId) {
            console.log('Found Paddle subscription to cancel:', currentSubscription.paddleSubscriptionId);
            
            // Import makePaddleAPIRequest function
            const { makePaddleAPIRequest } = require('../routes/paddle');
            
            // Determine if in trial period
            const now = new Date();
            const inTrialPeriod = currentSubscription.trialEndsAt && now < new Date(currentSubscription.trialEndsAt);
            
            let cancelData;
            let shouldCallPaddle = true;
            
            if (inTrialPeriod) {
              // For trial period: cancel immediately to prevent future charges
              cancelData = {
                effective_from: 'immediately'
              };
              console.log('Admin cancelling trial subscription immediately');
            } else if (currentSubscription.plan === 'lifetime') {
              // For lifetime plans, check if Paddle cancellation is appropriate
              // Lifetime plans might not have recurring subscriptions to cancel
              console.log('Admin cancelling lifetime subscription - may not need Paddle API call');
              
              // For lifetime, we might still want to call Paddle to mark as cancelled
              // but it's not as critical since there are no future charges
              cancelData = {
                effective_from: 'immediately'
              };
              
              // Could set shouldCallPaddle = false if lifetime plans don't have
              // recurring subscriptions in Paddle, but we'll try the call anyway
            } else {
              // For recurring paid subscriptions: admin cancellation is immediate
              cancelData = {
                effective_from: 'immediately' // Admin cancellations are immediate
              };
              console.log('Admin cancelling recurring paid subscription immediately');
            }
            
            // Cancel with Paddle (with error handling for lifetime plans)
            let paddleCancelSuccess = false;
            if (shouldCallPaddle) {
              try {
                const paddleResponse = await makePaddleAPIRequest(
                  `/subscriptions/${currentSubscription.paddleSubscriptionId}/cancel`, 
                  'POST', 
                  cancelData
                );
                console.log('Paddle subscription cancelled successfully by admin:', paddleResponse);
                paddleCancelSuccess = true;
              } catch (paddleApiError) {
                console.error('CRITICAL: Paddle API cancellation failed:', paddleApiError);
                console.error('Subscription may still be active on Paddle side!');
                
                // For critical trial subscriptions, we should NOT proceed with local cancellation
                // if Paddle cancellation fails, as user might still be charged
                if (inTrialPeriod) {
                  console.error('ABORTING: Trial subscription cancellation failed with Paddle - user may be charged!');
                  throw new Error(`Critical: Failed to cancel trial subscription with Paddle. User ${userId} may be charged after trial ends. Paddle error: ${paddleApiError.message}`);
                } else {
                  console.warn('Paid subscription Paddle cancellation failed, but proceeding with local update');
                  // For paid subscriptions, we still mark as cancelled locally even if Paddle fails
                  // Admin will need to manually verify with Paddle
                }
              }
            }
            
            // Update subscription record
            currentSubscription.subscriptionStatus = 'cancelled';
            currentSubscription.paymentStatus = 'cancelled';
            currentSubscription.active = false;
            currentSubscription.cancelledAt = new Date();
            currentSubscription.cancelReason = 'admin_cancelled';
            currentSubscription.adminNotes = (currentSubscription.adminNotes || '') + 
              `\n[${new Date().toISOString()}] Cancelled by admin - Paddle API success: ${paddleCancelSuccess}`;
            await currentSubscription.save();
            
          } else {
            console.log('No Paddle subscription found to cancel for user:', userId);
          }
          
          // Use centralized service to update user status
          await subscriptionService.updateUserSubscriptionStatus(userId, {
            plan: 'free',
            active: false,
            paymentStatus: 'cancelled',
            subscriptionStatus: 'free',
            expiryDate: null,
            subscriptionId: currentSubscription?._id
          });
          
        } catch (paddleError) {
          console.error('Error cancelling Paddle subscription during admin update:', paddleError);
          // Continue with local update even if Paddle fails
          console.log('Local subscription update completed despite Paddle API error');
        }
      }
    }

    // Only save user if no subscription plan change occurred
    // If subscription was modified, centralized service already updated the user
    if (!subscriptionPlan) {
      await user.save();
    }

    // Reload user to get the latest state after potential subscription updates
    const updatedUser = await User.findById(userId);
    res.json(updatedUser);
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Update user status (enable/disable)
 */
exports.updateUserStatus = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    const { userId } = req.params;
    const { isActive } = req.body;

    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    user.isActive = isActive;
    await user.save();

    res.json(user);
  } catch (error) {
    console.error('Error updating user status:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Update user subscription
 */
exports.updateUserSubscription = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    const { userId } = req.params;
    const { plan } = req.body;

    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const previousPlan = user.subscription?.plan || 'free';
    
    // Check if user is being downgraded to free (admin cancellation)
    const isAdminCancellation = plan === 'free' && 
                                previousPlan !== 'free' && 
                                ['monthly', 'yearly', 'lifetime'].includes(previousPlan);

    if (!user.subscription) {
      user.subscription = {};
    }

    user.subscription.plan = plan;

    // Set expiry date based on plan
    if (plan === 'monthly') {
      const oneMonthFromNow = new Date();
      oneMonthFromNow.setMonth(oneMonthFromNow.getMonth() + 1);
      user.subscription.expiryDate = oneMonthFromNow;
    } else if (plan === 'yearly') {
      const oneYearFromNow = new Date();
      oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
      user.subscription.expiryDate = oneYearFromNow;
    } else if (plan === 'lifetime') {
      const farFuture = new Date();
      farFuture.setFullYear(farFuture.getFullYear() + 100);
      user.subscription.expiryDate = farFuture;
    } else {
      // Free plan has no expiry
      user.subscription.expiryDate = null;
    }

    user.subscription.active = plan !== 'free';

    // Set max projects based on plan
    if (plan === 'free') {
      user.maxProjects = 3;
    } else {
      // For premium plans (monthly, yearly or lifetime)
      user.maxProjects = 999; // Unlimited
    }

    // If admin is cancelling subscription (setting to free), trigger cancellation logic
    if (isAdminCancellation) {
      console.log('Admin cancellation detected in updateUserSubscription for user:', userId, 'from plan:', previousPlan, 'to free');
      
      try {
        // Find current active subscription to cancel
        const currentSubscription = await Subscription.findOne({
          user: userId,
          $or: [
            { paymentStatus: 'confirmed' },
            { paymentStatus: 'trial' },
            { subscriptionStatus: 'trialing' },
            { subscriptionStatus: 'active' }
          ]
        }).sort({ createdAt: -1 });

        if (currentSubscription && currentSubscription.paddleSubscriptionId) {
          console.log('Found Paddle subscription to cancel:', currentSubscription.paddleSubscriptionId);
          
          // Import makePaddleAPIRequest function
          const { makePaddleAPIRequest } = require('../routes/paddle');
          
          // Determine if in trial period
          const now = new Date();
          const inTrialPeriod = currentSubscription.trialEndsAt && now < new Date(currentSubscription.trialEndsAt);
          
          let cancelData;
          let shouldCallPaddle = true;
          
          if (inTrialPeriod) {
            // For trial period: cancel immediately to prevent future charges
            cancelData = {
              effective_from: 'immediately'
            };
            console.log('Admin cancelling trial subscription immediately');
          } else if (currentSubscription.plan === 'lifetime') {
            // For lifetime plans, check if Paddle cancellation is appropriate
            console.log('Admin cancelling lifetime subscription via updateUserSubscription');
            cancelData = {
              effective_from: 'immediately'
            };
          } else {
            // For recurring paid subscriptions: admin cancellation is immediate
            cancelData = {
              effective_from: 'immediately' // Admin cancellations are immediate
            };
            console.log('Admin cancelling recurring paid subscription immediately');
          }
          
          // Cancel with Paddle (with error handling for lifetime plans)
          let paddleCancelSuccess = false;
          if (shouldCallPaddle) {
            try {
              const paddleResponse = await makePaddleAPIRequest(
                `/subscriptions/${currentSubscription.paddleSubscriptionId}/cancel`, 
                'POST', 
                cancelData
              );
              console.log('Paddle subscription cancelled successfully by admin via updateUserSubscription:', paddleResponse);
              paddleCancelSuccess = true;
            } catch (paddleApiError) {
              console.error('CRITICAL: Paddle API cancellation failed in updateUserSubscription:', paddleApiError);
              console.error('Subscription may still be active on Paddle side!');
              
              // For critical trial subscriptions, we should NOT proceed with local cancellation
              if (inTrialPeriod) {
                console.error('ABORTING: Trial subscription cancellation failed with Paddle in updateUserSubscription - user may be charged!');
                throw new Error(`Critical: Failed to cancel trial subscription with Paddle in updateUserSubscription. User ${userId} may be charged after trial ends. Paddle error: ${paddleApiError.message}`);
              } else {
                console.warn('Paid subscription Paddle cancellation failed in updateUserSubscription, but proceeding with local update');
              }
            }
          }
          
          // Update subscription record
          currentSubscription.subscriptionStatus = 'cancelled';
          currentSubscription.paymentStatus = 'cancelled';
          currentSubscription.active = false;
          currentSubscription.cancelledAt = new Date();
          currentSubscription.cancelReason = 'admin_cancelled';
          currentSubscription.adminNotes = (currentSubscription.adminNotes || '') + 
            `\n[${new Date().toISOString()}] Cancelled by admin via updateUserSubscription - Paddle API success: ${paddleCancelSuccess}`;
          await currentSubscription.save();
          
        } else {
          console.log('No Paddle subscription found to cancel for user:', userId);
        }
        
        // Use centralized service to update user status
        await subscriptionService.updateUserSubscriptionStatus(userId, {
          plan: 'free',
          active: false,
          paymentStatus: 'cancelled',
          subscriptionStatus: 'free',
          expiryDate: null,
          subscriptionId: currentSubscription?._id
        });
        
      } catch (paddleError) {
        console.error('Error cancelling Paddle subscription during admin updateUserSubscription:', paddleError);
        // Continue with local update even if Paddle fails
        console.log('Local subscription update completed despite Paddle API error');
      }
    }

    // Don't save user here as centralized service has already updated it properly
    // await user.save(); // REMOVED - this was overriding centralized service updates

    // Create a subscription record for tracking (only for non-free plans)
    let subscription = null;
    if (plan !== 'free') {
      subscription = new Subscription({
        user: userId,
        plan,
        paymentMethod: 'admin',
        paymentAmount: plan === 'monthly' ? 8 : (plan === 'yearly' ? 60 : (plan === 'lifetime' ? 100 : 0)),
        paymentStatus: 'confirmed',
        confirmationDate: new Date(),
        adminNotes: 'Subscription updated by admin via updateUserSubscription',
        expiryDate: user.subscription.expiryDate
      });

      await subscription.save();
    }

    // Reload user to get the latest state after subscription updates
    const updatedUser = await User.findById(userId);

    res.json({
      user: updatedUser,
      subscription,
      adminCancellation: isAdminCancellation
    });
  } catch (error) {
    console.error('Error updating user subscription:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Reset user password
 */
exports.resetUserPassword = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    const { userId } = req.params;
    const { tempPassword } = req.body;

    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Hash the temporary password
    const bcrypt = require('bcryptjs');
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(tempPassword, salt);

    user.password = hashedPassword;
    user.passwordResetRequired = true;

    await user.save();

    // Send email to user with temporary password
    try {
      await emailService.sendEmail({
        to: user.email,
        subject: 'Your Password Has Been Reset',
        text: `
Dear ${user.name},

Your password has been reset by an administrator.

Your temporary password is: ${tempPassword}

Please log in with this temporary password and change it immediately.

Best regards,
The Pomodoro Timer Team
        `,
      });
    } catch (emailError) {
      console.error('Error sending password reset email:', emailError);
      // Continue even if email fails
    }

    res.json({ message: 'Password reset successful' });
  } catch (error) {
    console.error('Error resetting user password:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Send message to user
 */
exports.sendUserMessage = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    const { userId } = req.params;
    const { subject, body } = req.body;

    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Send email to user
    try {
      await emailService.sendEmail({
        to: user.email,
        subject: subject,
        text: body,
      });
    } catch (emailError) {
      console.error('Error sending message to user:', emailError);
      return res.status(500).json({ message: 'Failed to send email' });
    }

    res.json({ message: 'Message sent successfully' });
  } catch (error) {
    console.error('Error sending message to user:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Validate subscription data consistency
 */
exports.validateSubscriptionConsistency = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    const { userId } = req.params;
    const validation = await subscriptionService.validateSubscriptionConsistency(userId);
    
    res.json(validation);
  } catch (error) {
    console.error('Error validating subscription consistency:', error);
    res.status(500).json({ 
      success: false,
      message: 'Server error while validating subscription consistency',
      error: error.message 
    });
  }
};

/**
 * Get upcoming billing events for monitoring
 */
exports.getUpcomingBilling = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    const { days = 7 } = req.query; // Default to next 7 days
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + parseInt(days));

    // Find subscriptions with upcoming billing dates
    const upcomingBilling = await Subscription.find({
      subscriptionStatus: { $in: ['active', 'trialing'] },
      nextBillingDate: {
        $gte: new Date(),
        $lte: futureDate
      }
    })
    .populate('user', 'name email')
    .sort({ nextBillingDate: 1 });

    // Find subscriptions that are past due
    const pastDue = await Subscription.find({
      subscriptionStatus: 'past_due',
      lastBillingAttempt: { $exists: true }
    })
    .populate('user', 'name email')
    .sort({ lastBillingAttempt: -1 });

    // Get trial ending soon
    const trialEndingSoon = await Subscription.find({
      subscriptionStatus: 'trialing',
      trialEndsAt: {
        $gte: new Date(),
        $lte: futureDate
      }
    })
    .populate('user', 'name email')
    .sort({ trialEndsAt: 1 });

    res.json({
      upcomingBilling: upcomingBilling.map(sub => ({
        subscriptionId: sub._id,
        user: sub.user,
        plan: sub.plan,
        nextBillingDate: sub.nextBillingDate,
        amount: sub.paymentAmount,
        billingInterval: sub.billingInterval,
        paddleSubscriptionId: sub.paddleSubscriptionId
      })),
      pastDue: pastDue.map(sub => ({
        subscriptionId: sub._id,
        user: sub.user,
        plan: sub.plan,
        lastBillingAttempt: sub.lastBillingAttempt,
        retryCount: sub.billingRetryCount,
        amount: sub.paymentAmount,
        paddleSubscriptionId: sub.paddleSubscriptionId
      })),
      trialEndingSoon: trialEndingSoon.map(sub => ({
        subscriptionId: sub._id,
        user: sub.user,
        plan: sub.plan,
        trialEndsAt: sub.trialEndsAt,
        nextBillingDate: sub.nextBillingDate,
        amount: sub.paymentAmount,
        paddleSubscriptionId: sub.paddleSubscriptionId
      })),
      summary: {
        upcomingCount: upcomingBilling.length,
        pastDueCount: pastDue.length,
        trialsEndingCount: trialEndingSoon.length,
        totalMonthlyRevenue: upcomingBilling
          .filter(sub => sub.billingInterval === 'month')
          .reduce((sum, sub) => sum + (sub.paymentAmount || 0), 0),
        totalYearlyRevenue: upcomingBilling
          .filter(sub => sub.billingInterval === 'year')
          .reduce((sum, sub) => sum + (sub.paymentAmount || 0), 0)
      }
    });

  } catch (error) {
    console.error('Error fetching upcoming billing:', error);
    res.status(500).json({ 
      success: false,
      message: 'Server error while fetching upcoming billing',
      error: error.message 
    });
  }
};

/**
 * Fix subscription data inconsistency
 */
exports.fixSubscriptionConsistency = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    const { userId } = req.params;
    
    // Get current subscription and fix inconsistencies
    const subscription = await subscriptionService.getCurrentUserSubscription(userId);
    
    res.json({
      success: true,
      message: 'Subscription consistency fixed',
      subscription
    });
  } catch (error) {
    console.error('Error fixing subscription consistency:', error);
    res.status(500).json({ 
      success: false,
      message: 'Server error while fixing subscription consistency',
      error: error.message 
    });
  }
};

/**
 * Manually update user subscription status
 * For handling cases where Paddle payment succeeded but local status is inconsistent
 */
exports.manualUpdateSubscription = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    const {
      userId,
      plan,
      subscriptionStatus,
      paymentStatus,
      paddleSubscriptionId,
      expiryDate,
      notes
    } = req.body;

    if (!userId) {
      return res.status(400).json({ message: 'User ID is required' });
    }

    // Find user by ID or email
    let user;
    const mongoose = require('mongoose');
    
    // Check if userId is a valid ObjectId
    if (mongoose.Types.ObjectId.isValid(userId)) {
      user = await User.findById(userId);
    }
    
    // If not found by ID, try to find by email
    if (!user) {
      user = await User.findOne({ email: userId });
    }

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    console.log('Manual subscription update for user:', user._id, user.email);

    // Calculate expiry date if not provided
    let calculatedExpiryDate = null;
    if (expiryDate) {
      calculatedExpiryDate = new Date(expiryDate);
    } else if (plan === 'monthly') {
      calculatedExpiryDate = new Date();
      calculatedExpiryDate.setMonth(calculatedExpiryDate.getMonth() + 1);
    } else if (plan === 'yearly') {
      calculatedExpiryDate = new Date();
      calculatedExpiryDate.setFullYear(calculatedExpiryDate.getFullYear() + 1);
    } else if (plan === 'lifetime') {
      calculatedExpiryDate = new Date();
      calculatedExpiryDate.setFullYear(calculatedExpiryDate.getFullYear() + 100);
    }

    // Find existing subscription for this user
    let subscription = await Subscription.findOne({ user: user._id }).sort({ createdAt: -1 });
    
    if (subscription) {
      // Update existing subscription
      subscription.plan = plan;
      subscription.subscriptionStatus = subscriptionStatus;
      subscription.paymentStatus = paymentStatus;
      subscription.active = paymentStatus === 'confirmed' || subscriptionStatus === 'active';
      subscription.confirmationDate = paymentStatus === 'confirmed' ? new Date() : subscription.confirmationDate;
      subscription.expiryDate = calculatedExpiryDate;
      subscription.adminNotes = (subscription.adminNotes || '') + `\n[${new Date().toISOString()}] Manual update: ${notes || 'Status updated by admin'}`;
      
      if (paddleSubscriptionId) {
        subscription.paddleSubscriptionId = paddleSubscriptionId;
      }
      
      await subscription.save();
      console.log('Updated existing subscription:', subscription._id);
    } else {
      // Create new subscription record
      subscription = new Subscription({
        user: user._id,
        plan,
        paymentMethod: 'paddle',
        paymentAmount: plan === 'monthly' ? 8 : (plan === 'yearly' ? 60 : (plan === 'lifetime' ? 100 : 0)),
        paymentStatus,
        subscriptionStatus,
        paddleSubscriptionId,
        active: paymentStatus === 'confirmed' || subscriptionStatus === 'active',
        confirmationDate: paymentStatus === 'confirmed' ? new Date() : null,
        expiryDate: calculatedExpiryDate,
        adminNotes: `[${new Date().toISOString()}] Manual creation: ${notes || 'Created by admin'}`,
        payerEmail: user.email,
        payerName: user.name
      });
      
      await subscription.save();
      console.log('Created new subscription:', subscription._id);
    }

    // Update user subscription status using centralized service
    await subscriptionService.updateUserSubscriptionStatus(user._id, {
      plan,
      active: subscription.active,
      paymentStatus,
      subscriptionStatus,
      expiryDate: calculatedExpiryDate,
      subscriptionId: subscription._id
    });
    console.log('Updated user subscription status:', user.subscriptionStatus);

    // Send notification email to user if subscription is now active
    if (isActive && paymentStatus === 'confirmed') {
      try {
        await emailService.sendEmail({
          to: user.email,
          subject: 'Your Premium Subscription is Now Active!',
          text: `
Dear ${user.name},

Your premium subscription has been activated by our admin team.

Subscription Details:
- Plan: ${plan}
- Status: ${subscriptionStatus}
- Expires: ${calculatedExpiryDate ? calculatedExpiryDate.toLocaleDateString() : 'Never (Lifetime)'}

You now have access to all premium features including unlimited projects!

If you have any questions, please contact our support team.

Best regards,
The AI-Pomo Team
          `,
        });
        console.log('Sent activation email to user');
      } catch (emailError) {
        console.error('Error sending activation email:', emailError);
        // Continue even if email fails
      }
    }

    res.json({
      success: true,
      message: 'Subscription status updated successfully',
      user: {
        id: user._id,
        email: user.email,
        name: user.name,
        subscriptionStatus: user.subscriptionStatus,
        maxProjects: user.maxProjects
      },
      subscription: {
        id: subscription._id,
        plan: subscription.plan,
        subscriptionStatus: subscription.subscriptionStatus,
        paymentStatus: subscription.paymentStatus,
        active: subscription.active,
        expiryDate: subscription.expiryDate
      }
    });

  } catch (error) {
    console.error('Error manually updating subscription:', error);
    res.status(500).json({ 
      success: false,
      message: 'Server error while updating subscription',
      error: error.message 
    });
  }
};

/**
 * Verify user's Paddle subscription status
 * This is critical for confirming cancellations worked properly
 */
exports.verifyUserPaddleStatus = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    const { userId } = req.params;
    const mongoose = require('mongoose');
    
    // Find user's subscription with Paddle ID
    const subscription = await Subscription.findOne({
      user: mongoose.Types.ObjectId(userId),
      paddleSubscriptionId: { $exists: true, $ne: null }
    }).sort({ createdAt: -1 });

    if (!subscription || !subscription.paddleSubscriptionId) {
      return res.json({
        success: true,
        message: 'No Paddle subscription found for this user',
        localStatus: 'free',
        paddleStatus: null,
        paddleSubscriptionId: null
      });
    }

    // Check status with Paddle API
    let paddleStatus = null;
    let paddleError = null;
    
    try {
      const { makePaddleAPIRequest } = require('../routes/paddle');
      const paddleResponse = await makePaddleAPIRequest(
        `/subscriptions/${subscription.paddleSubscriptionId}`,
        'GET'
      );
      
      paddleStatus = {
        id: paddleResponse.data?.id,
        status: paddleResponse.data?.status,
        next_billed_at: paddleResponse.data?.next_billed_at,
        canceled_at: paddleResponse.data?.canceled_at,
        trial_ends_at: paddleResponse.data?.trial_ends_at,
        billing_cycle: paddleResponse.data?.billing_cycle
      };
      
      console.log('Paddle status check for user:', userId, paddleStatus);
      
    } catch (error) {
      console.error('Error checking Paddle status:', error);
      paddleError = error.message;
    }

    const user = await User.findById(userId);
    
    res.json({
      success: true,
      user: {
        id: user._id,
        email: user.email,
        name: user.name,
        subscriptionStatus: user.subscriptionStatus,
        maxProjects: user.maxProjects
      },
      localSubscription: {
        plan: subscription.plan,
        active: subscription.active,
        subscriptionStatus: subscription.subscriptionStatus,
        paymentStatus: subscription.paymentStatus,
        trialEndsAt: subscription.trialEndsAt,
        nextBillingDate: subscription.nextBillingDate,
        cancelledAt: subscription.cancelledAt,
        cancelReason: subscription.cancelReason,
        adminNotes: subscription.adminNotes
      },
      paddleSubscriptionId: subscription.paddleSubscriptionId,
      paddleStatus: paddleStatus,
      paddleError: paddleError,
      statusMatch: paddleStatus ? 
        (paddleStatus.status === 'canceled' && subscription.subscriptionStatus === 'cancelled') ||
        (paddleStatus.status === 'active' && subscription.subscriptionStatus === 'active') ||
        (paddleStatus.status === 'trialing' && subscription.subscriptionStatus === 'trialing')
        : null,
      riskAssessment: {
        riskLevel: paddleStatus && paddleStatus.status !== 'canceled' && subscription.subscriptionStatus === 'cancelled' 
          ? 'HIGH - Paddle subscription still active but local shows cancelled!'
          : paddleStatus && paddleStatus.status === 'canceled' && subscription.subscriptionStatus === 'cancelled'
          ? 'LOW - Both Paddle and local show cancelled'
          : paddleStatus && paddleStatus.status === 'trialing' && subscription.subscriptionStatus === 'cancelled'
          ? 'CRITICAL - Trial active on Paddle but cancelled locally - user may be charged!'
          : 'UNKNOWN',
        recommendation: paddleStatus && paddleStatus.status !== 'canceled' && subscription.subscriptionStatus === 'cancelled'
          ? 'Manually cancel subscription in Paddle dashboard immediately!'
          : 'Status appears consistent'
      }
    });

  } catch (error) {
    console.error('Error verifying Paddle status:', error);
    res.status(500).json({ 
      success: false,
      message: 'Server error while verifying Paddle status',
      error: error.message 
    });
  }
};

/**
 * Delete user
 */
exports.deleteUser = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    const { userId } = req.params;
    const mongoose = require('mongoose');

    // Get models
    const Task = require('../models/Task');
    const Project = require('../models/Project');
    const Pomodoro = require('../models/Pomodoro');

    // Delete user's data
    await Task.deleteMany({ user: mongoose.Types.ObjectId(userId) });
    await Project.deleteMany({ user: mongoose.Types.ObjectId(userId) });
    await Pomodoro.deleteMany({ user: mongoose.Types.ObjectId(userId) });
    await Subscription.deleteMany({ user: mongoose.Types.ObjectId(userId) });

    // Delete user
    await User.findByIdAndDelete(userId);

    res.json({ message: 'User and all associated data deleted successfully' });
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = exports;
