const BlogImage = require('../models/BlogImage');

// Get all blog images with filtering
exports.getBlogImages = async (req, res) => {
  try {
    const { 
      category, 
      tags, 
      search, 
      page = 1, 
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const query = { isActive: true };

    // Filter by category
    if (category && category !== 'all') {
      query.category = category;
    }

    // Filter by tags
    if (tags) {
      const tagArray = tags.split(',').map(tag => tag.trim());
      query.tags = { $in: tagArray };
    }

    // Search in title and description
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $regex: search, $options: 'i' } }
      ];
    }

    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const skip = (page - 1) * limit;

    const images = await BlogImage.find(query)
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit));

    const total = await BlogImage.countDocuments(query);

    res.json({
      images,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / limit),
        total,
        hasNext: skip + images.length < total,
        hasPrev: page > 1
      }
    });
  } catch (err) {
    console.error('Error fetching blog images:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get image categories
exports.getImageCategories = async (req, res) => {
  try {
    const categories = await BlogImage.distinct('category', { isActive: true });
    res.json(categories);
  } catch (err) {
    console.error('Error fetching image categories:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get random image by category/tags
exports.getRandomImage = async (req, res) => {
  try {
    const { category, tags, exclude } = req.query;
    
    const query = { isActive: true };
    
    if (category && category !== 'all') {
      query.category = category;
    }
    
    if (tags) {
      const tagArray = tags.split(',').map(tag => tag.trim());
      query.tags = { $in: tagArray };
    }
    
    if (exclude) {
      const excludeArray = exclude.split(',').map(id => id.trim());
      query._id = { $nin: excludeArray };
    }

    const count = await BlogImage.countDocuments(query);
    
    if (count === 0) {
      return res.status(404).json({ message: 'No images found matching criteria' });
    }
    
    const random = Math.floor(Math.random() * count);
    const image = await BlogImage.findOne(query).skip(random);
    
    res.json(image);
  } catch (err) {
    console.error('Error fetching random image:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Create a new blog image (admin only)
exports.createBlogImage = async (req, res) => {
  try {
    const {
      title, url, thumbnailUrl, category, tags, description,
      source, photographer, photographerUrl
    } = req.body;

    const image = new BlogImage({
      title,
      url,
      thumbnailUrl,
      category,
      tags: tags || [],
      description,
      source,
      photographer,
      photographerUrl
    });

    await image.save();
    res.status(201).json(image);
  } catch (err) {
    console.error('Error creating blog image:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Update a blog image (admin only)
exports.updateBlogImage = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const image = await BlogImage.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    );

    if (!image) {
      return res.status(404).json({ message: 'Image not found' });
    }

    res.json(image);
  } catch (err) {
    console.error('Error updating blog image:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Delete a blog image (admin only)
exports.deleteBlogImage = async (req, res) => {
  try {
    const { id } = req.params;

    const image = await BlogImage.findByIdAndDelete(id);

    if (!image) {
      return res.status(404).json({ message: 'Image not found' });
    }

    res.json({ message: 'Image deleted successfully' });
  } catch (err) {
    console.error('Error deleting blog image:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Mark image as used (increment usage count)
exports.markImageAsUsed = async (req, res) => {
  try {
    const { id } = req.params;

    const image = await BlogImage.findById(id);

    if (!image) {
      return res.status(404).json({ message: 'Image not found' });
    }

    await image.incrementUsage();
    res.json({ message: 'Image usage recorded' });
  } catch (err) {
    console.error('Error marking image as used:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get image statistics (admin only)
exports.getImageStats = async (req, res) => {
  try {
    const totalImages = await BlogImage.countDocuments({ isActive: true });
    const totalUsage = await BlogImage.aggregate([
      { $match: { isActive: true } },
      { $group: { _id: null, totalUsage: { $sum: '$usageCount' } } }
    ]);

    const categoryStats = await BlogImage.aggregate([
      { $match: { isActive: true } },
      { $group: { _id: '$category', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    const mostUsed = await BlogImage.find({ isActive: true })
      .sort({ usageCount: -1 })
      .limit(10)
      .select('title url category usageCount lastUsed');

    res.json({
      totalImages,
      totalUsage: totalUsage[0]?.totalUsage || 0,
      categoryStats,
      mostUsed
    });
  } catch (err) {
    console.error('Error fetching image stats:', err);
    res.status(500).json({ message: 'Server error' });
  }
};
