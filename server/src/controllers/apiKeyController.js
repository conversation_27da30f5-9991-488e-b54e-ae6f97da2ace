/**
 * API Key Controller
 * Handles API key management
 */

const ApiKey = require('../models/ApiKey');

// Create a new API key
exports.createApiKey = async (req, res) => {
  try {
    const { name, permissions, expiresAt } = req.body;
    
    // Validate required fields
    if (!name) {
      return res.status(400).json({ message: 'API key name is required' });
    }

    // Generate a new API key
    const key = ApiKey.generateKey();
    
    // Create new API key document
    const apiKey = new ApiKey({
      user: req.user.id,
      name,
      key,
      permissions: permissions || ['blog:read'],
      expiresAt: expiresAt ? new Date(expiresAt) : null,
    });
    
    await apiKey.save();
    
    // Return the API key (only shown once)
    res.status(201).json({
      message: 'API key created successfully',
      apiKey: {
        id: apiKey._id,
        name: apiKey.name,
        key: apiKey.key, // Only returned once upon creation
        permissions: apiKey.permissions,
        expiresAt: apiKey.expiresAt,
        createdAt: apiKey.createdAt,
      },
    });
  } catch (err) {
    console.error('Error creating API key:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get all API keys for the current user
exports.getApiKeys = async (req, res) => {
  try {
    const apiKeys = await ApiKey.find({ user: req.user.id })
      .select('-key') // Don't return the actual key
      .sort({ createdAt: -1 });
    
    res.json(apiKeys);
  } catch (err) {
    console.error('Error getting API keys:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Delete an API key
exports.deleteApiKey = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Find and delete the API key
    const apiKey = await ApiKey.findOneAndDelete({
      _id: id,
      user: req.user.id,
    });
    
    if (!apiKey) {
      return res.status(404).json({ message: 'API key not found' });
    }
    
    res.json({ message: 'API key deleted successfully' });
  } catch (err) {
    console.error('Error deleting API key:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Update API key (name, permissions, active status)
exports.updateApiKey = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, permissions, isActive, expiresAt } = req.body;
    
    // Find the API key
    const apiKey = await ApiKey.findOne({
      _id: id,
      user: req.user.id,
    });
    
    if (!apiKey) {
      return res.status(404).json({ message: 'API key not found' });
    }
    
    // Update fields if provided
    if (name) apiKey.name = name;
    if (permissions) apiKey.permissions = permissions;
    if (isActive !== undefined) apiKey.isActive = isActive;
    if (expiresAt) apiKey.expiresAt = new Date(expiresAt);
    
    await apiKey.save();
    
    res.json({
      message: 'API key updated successfully',
      apiKey: {
        id: apiKey._id,
        name: apiKey.name,
        permissions: apiKey.permissions,
        isActive: apiKey.isActive,
        expiresAt: apiKey.expiresAt,
        lastUsed: apiKey.lastUsed,
        createdAt: apiKey.createdAt,
        updatedAt: apiKey.updatedAt,
      },
    });
  } catch (err) {
    console.error('Error updating API key:', err);
    res.status(500).json({ message: 'Server error' });
  }
};
