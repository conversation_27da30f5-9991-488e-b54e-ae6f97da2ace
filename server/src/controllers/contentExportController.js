const BlogPost = require('../models/BlogPost');
const PillarPage = require('../models/PillarPage');
const User = require('../models/User');
const fs = require('fs').promises;
const path = require('path');
const archiver = require('archiver');
const slugify = require('slugify');

/**
 * Convert blog post to Markdown format
 */
const blogPostToMarkdown = (post) => {
  const frontMatter = `---
title: "${post.title}"
slug: "${post.slug}"
excerpt: "${post.excerpt}"
author: "${post.author?.name || 'Unknown'}"
coverImage: "${post.coverImage}"
tags: [${post.tags?.map(tag => `"${tag}"`).join(', ') || ''}]
category: "${post.category}"
readTime: ${post.readTime}
published: ${post.published}
featured: ${post.featured}
seoTitle: "${post.seoTitle || ''}"
seoDescription: "${post.seoDescription || ''}"
seoKeywords: "${post.seoKeywords || ''}"
createdAt: "${post.createdAt}"
updatedAt: "${post.updatedAt}"
---

`;

  return frontMatter + post.content;
};

/**
 * Convert pillar page to Markdown format
 */
const pillarPageToMarkdown = (page) => {
  const clusterPages = page.clusterPages?.map(cp => ({
    blogPostId: cp.blogPost?._id || cp.blogPost,
    blogPostSlug: cp.blogPost?.slug || '',
    displayOrder: cp.displayOrder,
    customTitle: cp.customTitle || '',
    customDescription: cp.customDescription || ''
  })) || [];

  const frontMatter = `---
title: "${page.title}"
slug: "${page.slug}"
excerpt: "${page.excerpt}"
author: "${page.author?.name || 'Unknown'}"
coverImage: "${page.coverImage}"
category: "${page.category}"
readTime: ${page.readTime}
published: ${page.published}
featured: ${page.featured}
menuOrder: ${page.menuOrder || 0}
seoTitle: "${page.seoTitle || ''}"
seoDescription: "${page.seoDescription || ''}"
seoKeywords: "${page.seoKeywords || ''}"
clusterPages: ${JSON.stringify(clusterPages, null, 2)}
createdAt: "${page.createdAt}"
updatedAt: "${page.updatedAt}"
---

`;

  return frontMatter + page.content;
};

/**
 * Parse Markdown front matter and content
 */
const parseMarkdown = (markdown) => {
  const frontMatterRegex = /^---\s*\n([\s\S]*?)\n---\s*\n([\s\S]*)$/;
  const match = markdown.match(frontMatterRegex);
  
  if (!match) {
    throw new Error('Invalid Markdown format: Front matter not found');
  }

  const frontMatterText = match[1];
  const content = match[2];
  
  // Parse YAML-like front matter
  const frontMatter = {};
  const lines = frontMatterText.split('\n');
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) continue;
    
    const colonIndex = line.indexOf(':');
    if (colonIndex === -1) continue;
    
    const key = line.substring(0, colonIndex).trim();
    let value = line.substring(colonIndex + 1).trim();
    
    // Handle multi-line values (like clusterPages)
    if (key === 'clusterPages' && value === '[') {
      const arrayLines = [];
      i++; // Skip the opening bracket line
      let bracketCount = 1;
      
      while (i < lines.length && bracketCount > 0) {
        const arrayLine = lines[i];
        arrayLines.push(arrayLine);
        
        // Count brackets to find the end of the array
        bracketCount += (arrayLine.match(/\[/g) || []).length;
        bracketCount -= (arrayLine.match(/\]/g) || []).length;
        i++;
      }
      i--; // Adjust for the loop increment
      
      try {
        value = JSON.parse('[' + arrayLines.join('\n'));
      } catch (e) {
        console.error('Error parsing clusterPages:', e);
        value = [];
      }
    } else {
      // Handle different value types
      if (value.startsWith('"') && value.endsWith('"')) {
        value = value.slice(1, -1); // Remove quotes
      } else if (value.startsWith('[') && value.endsWith(']')) {
        try {
          value = JSON.parse(value);
        } catch (e) {
          value = [];
        }
      } else if (value === 'true') {
        value = true;
      } else if (value === 'false') {
        value = false;
      } else if (!isNaN(value) && value !== '') {
        value = Number(value);
      }
    }
    
    frontMatter[key] = value;
  }
  
  return { frontMatter, content };
};

/**
 * Export all blog posts and pillar pages as Markdown files
 */
exports.exportContent = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    const { type = 'all' } = req.query; // 'all', 'blog', 'pillar'

    // Create temporary directory for export
    const tempDir = path.join(__dirname, '../../temp');
    await fs.mkdir(tempDir, { recursive: true });
    const exportDir = path.join(tempDir, 'exports', `export-${Date.now()}`);
    await fs.mkdir(exportDir, { recursive: true });

    let exportedCount = 0;

    // Export blog posts
    if (type === 'all' || type === 'blog') {
      const blogPosts = await BlogPost.find()
        .populate('author', 'name')
        .sort({ createdAt: -1 });

      const blogDir = path.join(exportDir, 'blog-posts');
      await fs.mkdir(blogDir, { recursive: true });

      for (const post of blogPosts) {
        const markdown = blogPostToMarkdown(post);
        const filename = `${post.slug}.md`;
        await fs.writeFile(path.join(blogDir, filename), markdown, 'utf8');
        exportedCount++;
      }
    }

    // Export pillar pages
    if (type === 'all' || type === 'pillar') {
      const pillarPages = await PillarPage.find()
        .populate('author', 'name')
        .populate('clusterPages.blogPost', 'slug')
        .sort({ menuOrder: 1, createdAt: -1 });

      const pillarDir = path.join(exportDir, 'pillar-pages');
      await fs.mkdir(pillarDir, { recursive: true });

      for (const page of pillarPages) {
        const markdown = pillarPageToMarkdown(page);
        const filename = `${page.slug}.md`;
        await fs.writeFile(path.join(pillarDir, filename), markdown, 'utf8');
        exportedCount++;
      }
    }

    // Create ZIP archive
    const archive = archiver('zip', { zlib: { level: 9 } });
    const zipFilename = `content-export-${type}-${new Date().toISOString().split('T')[0]}.zip`;
    
    res.setHeader('Content-Type', 'application/zip');
    res.setHeader('Content-Disposition', `attachment; filename="${zipFilename}"`);
    
    archive.pipe(res);
    archive.directory(exportDir, false);
    await archive.finalize();

    // Clean up temporary directory
    setTimeout(async () => {
      try {
        await fs.rmdir(exportDir, { recursive: true });
      } catch (error) {
        console.error('Error cleaning up export directory:', error);
      }
    }, 5000);

  } catch (error) {
    console.error('Error exporting content:', error);
    res.status(500).json({ 
      message: 'Failed to export content', 
      error: error.message 
    });
  }
};

/**
 * Import blog posts and pillar pages from Markdown files
 */
exports.importContent = async (req, res) => {
  try {
    console.log('Import request received'); // Debug log
    console.log('User:', req.user?.email); // Debug log
    console.log('File:', req.file); // Debug log
    console.log('Body:', req.body); // Debug log

    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Unauthorized: Admin access required' });
    }

    if (!req.file) {
      console.log('No file found in request'); // Debug log
      return res.status(400).json({ message: 'No import file provided' });
    }

    const importFile = req.file;
    const { mode = 'update' } = req.body; // 'update' or 'replace'

    // Validate file type
    if (!importFile.originalname.endsWith('.zip')) {
      return res.status(400).json({ message: 'Import file must be a ZIP archive' });
    }

    const AdmZip = require('adm-zip');
    const zip = new AdmZip(importFile.buffer);
    const zipEntries = zip.getEntries();

    const results = {
      blogPosts: { created: 0, updated: 0, errors: [] },
      pillarPages: { created: 0, updated: 0, errors: [] }
    };

    // Process each file in the ZIP - BLOG POSTS FIRST, then PILLAR PAGES
    const blogPostEntries = [];
    const pillarPageEntries = [];

    // Separate blog posts and pillar pages
    for (const entry of zipEntries) {
      if (entry.isDirectory || !entry.entryName.endsWith('.md')) {
        continue;
      }

      if (entry.entryName.startsWith('blog-posts/')) {
        blogPostEntries.push(entry);
      } else if (entry.entryName.startsWith('pillar-pages/')) {
        pillarPageEntries.push(entry);
      }
    }

    // Import blog posts first
    for (const entry of blogPostEntries) {
      try {
        const content = entry.getData().toString('utf8');
        const { frontMatter, content: markdownContent } = parseMarkdown(content);
        await importBlogPost(frontMatter, markdownContent, mode, req.user.id, results);
      } catch (error) {
        console.error(`Error processing blog post ${entry.entryName}:`, error);
        results.blogPosts.errors.push({
          file: entry.entryName,
          error: error.message
        });
      }
    }

    // Then import pillar pages (so blog post references can be resolved)
    for (const entry of pillarPageEntries) {
      try {
        const content = entry.getData().toString('utf8');
        const { frontMatter, content: markdownContent } = parseMarkdown(content);
        await importPillarPage(frontMatter, markdownContent, mode, req.user.id, results);
      } catch (error) {
        console.error(`Error processing pillar page ${entry.entryName}:`, error);
        results.pillarPages.errors.push({
          file: entry.entryName,
          error: error.message
        });
      }
    }

    res.json({
      message: 'Import completed',
      results
    });

  } catch (error) {
    console.error('Error importing content:', error);
    res.status(500).json({
      message: 'Failed to import content',
      error: error.message
    });
  }
};

/**
 * Import a single blog post
 */
const importBlogPost = async (frontMatter, content, mode, userId, results) => {
  const existingPost = await BlogPost.findOne({ slug: frontMatter.slug });

  const postData = {
    title: frontMatter.title,
    slug: frontMatter.slug,
    content,
    excerpt: frontMatter.excerpt,
    author: userId, // Use current admin user as author
    coverImage: frontMatter.coverImage,
    tags: frontMatter.tags || [],
    category: frontMatter.category,
    readTime: frontMatter.readTime || 5,
    published: frontMatter.published || false,
    featured: frontMatter.featured || false,
    seoTitle: frontMatter.seoTitle || frontMatter.title,
    seoDescription: frontMatter.seoDescription || frontMatter.excerpt,
    seoKeywords: frontMatter.seoKeywords || ''
  };

  if (existingPost) {
    if (mode === 'update') {
      await BlogPost.findByIdAndUpdate(existingPost._id, postData);
      results.blogPosts.updated++;
    }
  } else {
    await BlogPost.create(postData);
    results.blogPosts.created++;
  }
};

/**
 * Import a single pillar page
 */
const importPillarPage = async (frontMatter, content, mode, userId, results) => {
  const existingPage = await PillarPage.findOne({ slug: frontMatter.slug });

  // Process cluster pages - resolve blog post references
  let clusterPages = [];
  if (frontMatter.clusterPages && Array.isArray(frontMatter.clusterPages)) {
    for (const cluster of frontMatter.clusterPages) {
      let blogPostId = null;

      // Try to find the blog post by ID first, then by slug
      if (cluster.blogPostId) {
        const blogPost = await BlogPost.findById(cluster.blogPostId).catch(() => null);
        if (blogPost) {
          blogPostId = blogPost._id;
        }
      }

      // If not found by ID, try to find by slug
      if (!blogPostId && cluster.blogPostSlug) {
        const blogPost = await BlogPost.findOne({ slug: cluster.blogPostSlug });
        if (blogPost) {
          blogPostId = blogPost._id;
        }
      }

      // Only add cluster page if we found a valid blog post
      if (blogPostId) {
        clusterPages.push({
          blogPost: blogPostId,
          displayOrder: cluster.displayOrder || 0,
          customTitle: cluster.customTitle || '',
          customDescription: cluster.customDescription || ''
        });
      }
    }
  }

  const pageData = {
    title: frontMatter.title,
    slug: frontMatter.slug,
    content,
    excerpt: frontMatter.excerpt,
    author: userId, // Use current admin user as author
    coverImage: frontMatter.coverImage,
    category: frontMatter.category,
    readTime: frontMatter.readTime || 5,
    published: frontMatter.published || false,
    featured: frontMatter.featured || false,
    menuOrder: frontMatter.menuOrder || 0,
    seoTitle: frontMatter.seoTitle || frontMatter.title,
    seoDescription: frontMatter.seoDescription || frontMatter.excerpt,
    seoKeywords: frontMatter.seoKeywords || '',
    clusterPages: clusterPages
  };

  if (existingPage) {
    if (mode === 'update') {
      await PillarPage.findByIdAndUpdate(existingPage._id, pageData);
      results.pillarPages.updated++;
    }
  } else {
    await PillarPage.create(pageData);
    results.pillarPages.created++;
  }
};

module.exports = {
  exportContent: exports.exportContent,
  importContent: exports.importContent
};
