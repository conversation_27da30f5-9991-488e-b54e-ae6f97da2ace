const Subscription = require('../models/Subscription');
const User = require('../models/User');
const emailService = require('../services/emailService');
const subscriptionService = require('../services/subscriptionService');

// Get current user's subscription
exports.getCurrentSubscription = async (req, res) => {
  try {
    const subscription = await subscriptionService.getCurrentUserSubscription(req.user.id);
    res.json(subscription);
  } catch (err) {
    console.error('Error getting subscription:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Create a new subscription request
exports.createSubscriptionRequest = async (req, res) => {
  try {
    console.log('Received subscription request:', req.body);
    console.log('User from request:', req.user);

    const { plan, paymentMethod, payerName, payerEmail, additionalInfo } = req.body;

    // Check if user is authenticated
    if (!req.user) {
      console.error('User not authenticated');
      return res.status(401).json({ message: 'User not authenticated' });
    }

    // Validate plan
    if (!['yearly', 'lifetime'].includes(plan)) {
      console.error('Invalid subscription plan:', plan);
      return res.status(400).json({ message: 'Invalid subscription plan' });
    }

    // Validate payment method
    if (!['paypal', 'usdt'].includes(paymentMethod)) {
      console.error('Invalid payment method:', paymentMethod);
      return res.status(400).json({ message: 'Invalid payment method' });
    }

    // Validate required fields
    if (!payerName || !payerEmail) {
      console.error('Missing required fields. Name:', payerName, 'Email:', payerEmail);
      return res.status(400).json({ message: 'Name and email are required' });
    }

    // Set payment amount based on plan
    const paymentAmount = plan === 'yearly' ? 30 : 60;

    console.log('Creating subscription with user ID:', req.user.id);

    // Check if user already has a pending subscription request
    const existingPendingSubscription = await Subscription.findOne({
      user: req.user.id,
      paymentStatus: 'pending'
    });

    let subscription;

    if (existingPendingSubscription) {
      console.log('User already has a pending subscription. Updating it:', existingPendingSubscription._id);

      // Update the existing subscription
      existingPendingSubscription.plan = plan;
      existingPendingSubscription.paymentMethod = paymentMethod;
      existingPendingSubscription.paymentAmount = paymentAmount;
      existingPendingSubscription.paymentDate = new Date();
      existingPendingSubscription.payerName = payerName;
      existingPendingSubscription.payerEmail = payerEmail;
      existingPendingSubscription.additionalInfo = additionalInfo;

      console.log('Saving updated subscription:', existingPendingSubscription);
      subscription = await existingPendingSubscription.save();
      console.log('Subscription updated successfully');
    } else {
      try {
        // Create new subscription request
        subscription = new Subscription({
          user: req.user.id,
          plan,
          paymentMethod,
          paymentAmount,
          paymentStatus: 'pending',
          paymentDate: new Date(),
          payerName,
          payerEmail,
          additionalInfo,
          active: false,
        });

        console.log('Saving new subscription:', subscription);
        await subscription.save();
        console.log('New subscription saved successfully');
      } catch (saveError) {
        // Check if it's a duplicate key error
        if (saveError.code === 11000 && saveError.keyPattern && saveError.keyPattern.user) {
          console.log('Duplicate key error detected. Updating existing subscription instead.');

          // Find the existing subscription and update it
          const existingSubscription = await Subscription.findOne({ user: req.user.id });

          if (existingSubscription) {
            console.log('Found existing subscription:', existingSubscription._id);

            // Update the existing subscription
            existingSubscription.plan = plan;
            existingSubscription.paymentMethod = paymentMethod;
            existingSubscription.paymentAmount = paymentAmount;
            existingSubscription.paymentDate = new Date();
            existingSubscription.payerName = payerName;
            existingSubscription.payerEmail = payerEmail;
            existingSubscription.additionalInfo = additionalInfo;
            existingSubscription.paymentStatus = 'pending';

            console.log('Saving updated subscription:', existingSubscription);
            subscription = await existingSubscription.save();
            console.log('Existing subscription updated successfully');
          } else {
            // This shouldn't happen, but just in case
            throw new Error('Could not find existing subscription despite duplicate key error');
          }
        } else {
          // If it's not a duplicate key error, rethrow it
          throw saveError;
        }
      }
    }

    // Send email notification to admin using the same approach as contact form
    try {
      console.log('Sending email notification');

      // Format the message
      const formattedMessage = `
New Premium Subscription Payment:

User ID: ${req.user.id}
Plan: ${plan} ($${paymentAmount})
Payment Method: ${paymentMethod}

Payer Information:
Name: ${payerName}
Email: ${payerEmail}

Additional Information:
${additionalInfo || 'None provided'}

This payment is pending confirmation.
      `;

      // Send email using the same service as contact form
      const emailResult = await emailService.sendContactFormEmail({
        name: payerName,
        email: payerEmail,
        message: formattedMessage,
        subject: 'New Premium Subscription Payment'
      });

      console.log('Email notification sent successfully:', emailResult);
    } catch (emailError) {
      console.error('Error sending email notification:', emailError);
      console.error('Error details:', emailError.message);
      // Continue even if email fails
    }

    console.log('Returning subscription response');
    res.status(201).json(subscription);
  } catch (err) {
    console.error('Error creating subscription request:', err);
    if (err.name === 'ValidationError') {
      console.error('Validation error details:', err.errors);
      return res.status(400).json({ message: 'Validation error', errors: err.errors });
    }
    res.status(500).json({ message: 'Server error' });
  }
};

// Get subscription history for current user
exports.getSubscriptionHistory = async (req, res) => {
  try {
    const subscriptions = await Subscription.find({
      user: req.user.id,
    }).sort({ createdAt: -1 });

    res.json(subscriptions);
  } catch (err) {
    console.error('Error getting subscription history:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Cancel current subscription
exports.cancelSubscription = async (req, res) => {
  try {
    console.log('Processing subscription cancellation for user:', req.user.id);

    // Use centralized service to cancel subscription
    const cancelledSubscription = await subscriptionService.cancelUserSubscription(req.user.id);

    // Check if we're still in trial period using correct field
    const now = new Date();
    const inTrialPeriod = cancelledSubscription.trialEndsAt && now < new Date(cancelledSubscription.trialEndsAt);

    console.log('Subscription created:', cancelledSubscription.createdAt);
    console.log('Trial ends at:', cancelledSubscription.trialEndsAt);
    console.log('Current time:', now);
    console.log('In trial period:', inTrialPeriod);

    // Cancel subscription with Paddle if we have a Paddle subscription ID
    if (cancelledSubscription.paddleSubscriptionId) {
      try {
        console.log('Cancelling Paddle subscription:', cancelledSubscription.paddleSubscriptionId);
        
        // Import makePaddleAPIRequest function
        const { makePaddleAPIRequest } = require('../routes/paddle');
        
        let cancelData;
        
        if (inTrialPeriod) {
          // For trial period: cancel immediately to prevent future charges
          cancelData = {
            effective_from: 'immediately' // Cancel immediately for trial users
          };
          console.log('Trial period cancellation - cancelling immediately with Paddle');
        } else {
          // For paid subscription: cancel at period end to give user full value
          cancelData = {
            effective_from: 'next_billing_period' // Cancel at end of current billing period
          };
          console.log('Paid subscription cancellation - cancelling at period end');
        }
        
        await makePaddleAPIRequest(
          `/subscriptions/${cancelledSubscription.paddleSubscriptionId}/cancel`, 
          'POST', 
          cancelData
        );
        
        console.log('Paddle subscription cancelled successfully');
        
      } catch (paddleError) {
        console.error('Error cancelling with Paddle:', paddleError);
        // Continue with local cancellation even if Paddle fails
        console.log('Local cancellation completed despite Paddle API error');
      }
    }

    console.log('Subscription cancelled successfully');

    res.json({
      success: true,
      message: inTrialPeriod 
        ? 'Subscription cancelled successfully. Since you were in the trial period, you will not be charged.'
        : 'Subscription cancelled successfully. You will continue to have access until the end of your billing period.',
      inTrialPeriod,
      trialEndDate,
      subscription: cancelledSubscription
    });

  } catch (err) {
    console.error('Error cancelling subscription:', err);
    
    if (err.message === 'No active subscription found to cancel') {
      return res.status(404).json({ 
        success: false,
        message: err.message 
      });
    }
    
    res.status(500).json({ 
      success: false,
      message: 'Server error while cancelling subscription' 
    });
  }
};

module.exports = exports;
