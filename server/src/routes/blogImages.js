const express = require('express');
const router = express.Router();
const blogImageController = require('../controllers/blogImageController');
const auth = require('../middleware/auth');
const isAdmin = require('../middleware/isAdmin');

// Public routes (for blog post creation)
router.get('/', blogImageController.getBlogImages);
router.get('/categories', blogImageController.getImageCategories);
router.get('/random', blogImageController.getRandomImage);

// Protected routes (require authentication)
router.post('/:id/use', auth.authenticateJWT, blogImageController.markImageAsUsed);

// Admin routes
router.post('/', auth.authenticateJWT, isAdmin, blogImageController.createBlogImage);
router.put('/:id', auth.authenticateJWT, isAdmin, blogImageController.updateBlogImage);
router.delete('/:id', auth.authenticateJWT, isAdmin, blogImageController.deleteBlogImage);
router.get('/stats', auth.authenticateJWT, isAdmin, blogImageController.getImageStats);

module.exports = router;
