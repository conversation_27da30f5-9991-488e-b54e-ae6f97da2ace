/**
 * Image Routes
 * 
 * Routes for image management API
 */

const express = require('express');
const router = express.Router();
const imageController = require('../controllers/imageController');
const auth = require('../middleware/auth');
const isAdmin = require('../middleware/isAdmin');

// Public routes (for blog display)
router.get('/tags', imageController.getTags);
router.get('/tag/:tag', imageController.getImagesByTag);
router.get('/search', imageController.searchImages);
router.get('/popular', imageController.getPopularImages);

// Admin routes (for blog editor)
router.get('/recently-used', auth.authenticateJWT, isAdmin, imageController.getRecentlyUsed);
router.get('/stats', auth.authenticateJWT, isAdmin, imageController.getImageStats);
router.post('/:imageId/usage', auth.authenticateJWT, isAdmin, imageController.recordImageUsage);

module.exports = router;