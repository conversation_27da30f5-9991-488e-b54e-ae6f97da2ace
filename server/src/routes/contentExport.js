const express = require('express');
const router = express.Router();
const multer = require('multer');
const contentExportController = require('../controllers/contentExportController');
const auth = require('../middleware/auth');
const isAdmin = require('../middleware/isAdmin');

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: { fileSize: 50 * 1024 * 1024 } // 50MB limit
});

// Export content as Markdown files
// GET /api/content-export/export?type=all|blog|pillar
router.get('/export', 
  auth.authenticateJWT, 
  isAdmin, 
  contentExportController.exportContent
);

// Test file upload endpoint
router.post('/test-upload',
  auth.authenticateJWT,
  isAdmin,
  upload.single('importFile'),
  (req, res) => {
    console.log('Test upload endpoint hit');
    console.log('File:', req.file);
    console.log('Body:', req.body);
    res.json({
      message: 'Test endpoint reached',
      file: req.file ? {
        originalname: req.file.originalname,
        size: req.file.size,
        mimetype: req.file.mimetype
      } : null,
      body: req.body
    });
  }
);

// Import content from Markdown files
// POST /api/content-export/import
router.post('/import',
  auth.authenticateJWT,
  isAdmin,
  upload.single('importFile'),
  contentExportController.importContent
);

module.exports = router;
