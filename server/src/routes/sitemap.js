const express = require('express');
const router = express.Router();
const BlogPost = require('../models/BlogPost');
const BlogCategory = require('../models/BlogCategory');
const config = require('../config/config');

// Generate XML sitemap
router.get('/sitemap.xml', async (req, res) => {
  try {
    const baseUrl = config.appUrl || 'https://www.ai-pomo.com';
    console.log('Generating sitemap with base URL:', baseUrl);

    // Static pages - only content-rich pages for SEO
    const staticPages = [
      // Main pages
      { url: '', changefreq: 'daily', priority: '1.0' },
      { url: '/blog', changefreq: 'daily', priority: '0.8' },

      // Content pages
      { url: '/features', changefreq: 'weekly', priority: '0.8' },
      { url: '/use-cases', changefreq: 'weekly', priority: '0.7' },
      { url: '/solutions', changefreq: 'weekly', priority: '0.7' },
      { url: '/about', changefreq: 'monthly', priority: '0.6' },
      { url: '/contact', changefreq: 'monthly', priority: '0.6' },
      { url: '/pricing', changefreq: 'weekly', priority: '0.8' },

      // Landing pages
      { url: '/pomodoro-for-studying', changefreq: 'weekly', priority: '0.7' },
      { url: '/pomodoro-for-adhd', changefreq: 'weekly', priority: '0.7' },

      // Legal pages
      { url: '/privacy', changefreq: 'monthly', priority: '0.4' },
      { url: '/terms', changefreq: 'monthly', priority: '0.4' },
      { url: '/gdpr', changefreq: 'monthly', priority: '0.4' },
      { url: '/refund-policy', changefreq: 'monthly', priority: '0.4' },

      // Timer tools - High Priority SEO Keywords
      { url: '/timers', changefreq: 'weekly', priority: '0.8' },
      { url: '/countdown-timer', changefreq: 'weekly', priority: '0.9' },
      { url: '/online-timer', changefreq: 'weekly', priority: '0.9' },
      { url: '/tomato-timer', changefreq: 'weekly', priority: '0.8' },
      
      // Specialized timer tools
      { url: '/cute-timer', changefreq: 'weekly', priority: '0.7' },
      { url: '/homework-timer', changefreq: 'weekly', priority: '0.7' },
      { url: '/classroom-timer', changefreq: 'weekly', priority: '0.8' },

      // Super high-priority timer tools (massive search volume)
      { url: '/online-stopwatch', changefreq: 'weekly', priority: '0.9' },
      { url: '/online-alarm', changefreq: 'weekly', priority: '0.9' },
      { url: '/interval-timer', changefreq: 'weekly', priority: '0.8' },
      { url: '/kitchen-timer', changefreq: 'weekly', priority: '0.7' },
      
      { url: '/visual-timer', changefreq: 'weekly', priority: '0.7' },
      
      // Timer Categories
      { url: '/study-timer', changefreq: 'weekly', priority: '0.8' },
      { url: '/work-timer', changefreq: 'weekly', priority: '0.8' },
      { url: '/break-timer', changefreq: 'weekly', priority: '0.7' },
      { url: '/custom-timer', changefreq: 'weekly', priority: '0.7' },
      
      // Scenario-based Timer Landing Pages
      { url: '/remote-work-timer', changefreq: 'weekly', priority: '0.7' },
      { url: '/student-study-timer', changefreq: 'weekly', priority: '0.8' },
      { url: '/developer-coding-timer', changefreq: 'weekly', priority: '0.7' },
      { url: '/writer-timer', changefreq: 'weekly', priority: '0.7' },
      
      { url: '/timer/5-minutes', changefreq: 'weekly', priority: '0.7' },
      { url: '/timer/10-minutes', changefreq: 'weekly', priority: '0.7' },
      { url: '/timer/15-minutes', changefreq: 'weekly', priority: '0.7' },
      { url: '/timer/20-minutes', changefreq: 'weekly', priority: '0.7' },
      { url: '/timer/25-minutes', changefreq: 'weekly', priority: '0.8' },
      { url: '/timer/30-minutes', changefreq: 'weekly', priority: '0.7' },
      { url: '/timer/35-minutes', changefreq: 'weekly', priority: '0.6' },
      { url: '/timer/40-minutes', changefreq: 'weekly', priority: '0.6' },
      { url: '/timer/45-minutes', changefreq: 'weekly', priority: '0.6' },
      { url: '/timer/50-minutes', changefreq: 'weekly', priority: '0.6' },
      { url: '/timer/60-minutes', changefreq: 'weekly', priority: '0.6' },

      // Set timer pages (natural language queries)
      { url: '/set-timer-for-5-minutes', changefreq: 'weekly', priority: '0.8' },
      { url: '/set-timer-for-10-minutes', changefreq: 'weekly', priority: '0.8' },
      { url: '/set-timer-for-30-minutes', changefreq: 'weekly', priority: '0.8' },

      // Timer variant pages (format variations)
      { url: '/timer-5-minutes', changefreq: 'weekly', priority: '0.8' },
      { url: '/timer-10-minutes', changefreq: 'weekly', priority: '0.8' },
      { url: '/timer-20-minutes', changefreq: 'weekly', priority: '0.8' },

      // Alternative landing pages
      { url: '/classic', changefreq: 'monthly', priority: '0.3' },
      { url: '/old', changefreq: 'monthly', priority: '0.3' },
      { url: '/gmail', changefreq: 'monthly', priority: '0.3' },
    ];

    // Get published blog posts
    const blogPosts = await BlogPost.find({ published: true })
      .select('slug updatedAt createdAt')
      .sort({ createdAt: -1 });

    console.log(`Found ${blogPosts.length} published blog posts`);

    // Get blog categories
    const categories = await BlogCategory.find()
      .select('slug updatedAt createdAt');

    console.log(`Found ${categories.length} blog categories`);

    // Get unique tags synchronously by querying all posts with tags
    const postsWithTags = await BlogPost.find({ published: true, tags: { $exists: true } })
      .select('tags updatedAt createdAt');
    
    const uniqueTags = new Map();
    postsWithTags.forEach(post => {
      if (post.tags) {
        let tagsArray = [];
        if (Array.isArray(post.tags)) {
          tagsArray = post.tags;
        } else if (typeof post.tags === 'string') {
          tagsArray = post.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
        }
        
        // Further split each tag if it contains commas
        tagsArray.forEach(tagGroup => {
          if (tagGroup && tagGroup.trim()) {
            // Split by comma and process each individual tag
            const individualTags = tagGroup.split(',').map(tag => tag.trim()).filter(tag => tag);
            
            individualTags.forEach(tag => {
              if (tag && tag.trim()) {
                const normalizedTag = tag.toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
                const tagSlug = normalizedTag.toLowerCase().replace(/\s+/g, '-');
                
                if (!uniqueTags.has(tagSlug)) {
                  uniqueTags.set(tagSlug, {
                    tag: normalizedTag,
                    slug: tagSlug,
                    lastmod: post.updatedAt || post.createdAt
                  });
                } else {
                  // Update lastmod if this post is newer
                  const existing = uniqueTags.get(tagSlug);
                  const postDate = post.updatedAt || post.createdAt;
                  if (postDate > existing.lastmod) {
                    existing.lastmod = postDate;
                  }
                }
              }
            });
          }
        });
      }
    });

    const tagsArray = Array.from(uniqueTags.values());
    console.log(`Found ${tagsArray.length} unique tags`);

    // Build sitemap XML
    let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

    // Add static pages
    staticPages.forEach(page => {
      sitemap += `
  <url>
    <loc>${baseUrl}${page.url}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`;
    });

    // Add blog posts
    blogPosts.forEach(post => {
      const lastmod = post.updatedAt || post.createdAt;
      sitemap += `
  <url>
    <loc>${baseUrl}/blog/${post.slug}</loc>
    <lastmod>${lastmod.toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>`;
    });

    // Add blog categories
    categories.forEach(category => {
      const lastmod = category.updatedAt || category.createdAt;
      sitemap += `
  <url>
    <loc>${baseUrl}/blog?category=${category.slug}</loc>
    <lastmod>${lastmod.toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>`;
    });

    // Add tags list page
    sitemap += `
  <url>
    <loc>${baseUrl}/tags</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.7</priority>
  </url>`;

    // Add individual tag pages
    tagsArray.forEach(tagData => {
      sitemap += `
  <url>
    <loc>${baseUrl}/tags/${tagData.slug}</loc>
    <lastmod>${tagData.lastmod.toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>`;
    });

    sitemap += `
</urlset>`;

    const totalUrls = staticPages.length + blogPosts.length + categories.length + 1 + tagsArray.length;
    console.log('Sitemap generated successfully with', totalUrls, 'URLs');
    console.log('- Static pages:', staticPages.length);
    console.log('- Blog posts:', blogPosts.length);
    console.log('- Categories:', categories.length);
    console.log('- Tags list page: 1');
    console.log('- Individual tag pages:', tagsArray.length);
    res.set('Content-Type', 'application/xml');
    res.send(sitemap);
  } catch (error) {
    console.error('Error generating sitemap:', error);
    res.status(500).send('Error generating sitemap');
  }
});

// Generate robots.txt
router.get('/robots.txt', (req, res) => {
  const baseUrl = config.appUrl || 'https://www.ai-pomo.com';

  const robots = `User-agent: *
Allow: /
Disallow: /admin/
Disallow: /app/
Disallow: /login
Disallow: /register
Disallow: /forgot-password
Disallow: /change-password

# Sitemap
Sitemap: ${baseUrl}/api/sitemap.xml

# Crawl-delay
Crawl-delay: 1`;

  res.set('Content-Type', 'text/plain');
  res.send(robots);
});

module.exports = router;
