const express = require('express');
const router = express.Router();
const https = require('https');
const crypto = require('crypto');
const auth = require('../middleware/auth');

// Paddle configuration
const paddleConfig = {
  environment: process.env.PADDLE_ENVIRONMENT || 'sandbox',
  apiKey: process.env.PADDLE_API_KEY,
  vendorId: process.env.PADDLE_VENDOR_ID || 'pro_01jxjzx642xnz9k48cwt9hv4r2',
  webhookSecret: process.env.PADDLE_WEBHOOK_SECRET || '',
  priceIds: {
    monthly: process.env.PADDLE_MONTHLY_PRICE_ID || 'pri_01jxk01813b3zfs36kcz66x0xg',
    yearly: process.env.PADDLE_YEARLY_PRICE_ID || 'pri_01jxk02vycmv0zjfck99vcxqmt',
    lifetime: process.env.PADDLE_LIFETIME_PRICE_ID || 'pri_01jxk0448wa25ssmshke9c7r1m',
  },
  successUrl: (process.env.CLIENT_URL || 'https://www.ai-pomo.com') + '/premium?success=true',
  cancelUrl: (process.env.CLIENT_URL || 'https://www.ai-pomo.com') + '/premium?cancelled=true',
};

// Helper function to make Paddle API requests
const makePaddleAPIRequest = (path, method, data) => {
  return new Promise((resolve, reject) => {
    if (!paddleConfig.apiKey) {
      reject(new Error('Paddle API key not configured'));
      return;
    }

    const postData = data ? JSON.stringify(data) : null;
    
    const options = {
      hostname: paddleConfig.environment === 'sandbox' ? 'sandbox-api.paddle.com' : 'api.paddle.com',
      port: 443,
      path: path,
      method: method,
      headers: {
        'Authorization': `Bearer ${paddleConfig.apiKey}`,
        'Content-Type': 'application/json',
      },
    };

    if (postData) {
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = https.request(options, (res) => {
      let responseBody = '';
      
      res.on('data', (chunk) => {
        responseBody += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedResponse = JSON.parse(responseBody);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(parsedResponse);
          } else {
            reject(new Error(`Paddle API error: ${res.statusCode} - ${responseBody}`));
          }
        } catch (error) {
          reject(new Error(`Failed to parse Paddle API response: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error(`Paddle API request failed: ${error.message}`));
    });

    if (postData) {
      req.write(postData);
    }
    
    req.end();
  });
};

// Test endpoint
router.get('/test', (req, res) => {
  try {
    res.json({
      success: true,
      message: 'Paddle integration is working',
      config: {
        environment: paddleConfig.environment,
        vendorId: paddleConfig.vendorId,
        priceIds: paddleConfig.priceIds,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

// Create checkout with auth middleware
router.post('/checkout', auth.authenticateJWT, async (req, res) => {
  try {
    const { planType } = req.body;
    
    // Get real user ID from authenticated request, fallback to test for safety
    const userId = req.user?.id || req.user?._id || 'test-user-id';
    
    console.log('Creating checkout for user:', userId, 'plan:', planType);
    
    if (!['monthly', 'yearly', 'lifetime'].includes(planType)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid plan type',
      });
    }

    const priceId = paddleConfig.priceIds[planType];
    
    // Create Paddle subscription checkout with trial period
    // Use Paddle.js overlay checkout instead of API calls
    console.log('Creating subscription checkout for:', planType);
    res.json({
      success: true,
      planType,
      priceId,
      useOverlay: true,
      isSubscription: true,
      paddleData: {
        environment: paddleConfig.environment,
        vendorId: paddleConfig.vendorId,
        priceId: priceId,
        successUrl: paddleConfig.successUrl,
        cancelUrl: paddleConfig.cancelUrl,
        trialDays: 7, // 7-day trial period
        customData: {
          user_id: userId,
          plan_type: planType,
          subscription_type: 'recurring'
        }
      },
    });
  } catch (error) {
    console.error('Paddle checkout error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

// Helper function to verify Paddle webhook signature
const verifyPaddleWebhookSignature = (req) => {
  // Skip verification in development mode if no secret is configured
  if (!paddleConfig.webhookSecret) {
    console.warn('⚠️  Paddle webhook secret not configured - skipping signature verification');
    return true;
  }

  const signature = req.get('Paddle-Signature');
  if (!signature) {
    console.error('No Paddle-Signature header found');
    return false;
  }

  const signatureParts = signature.split(';');
  const tsHeader = signatureParts.find(part => part.startsWith('ts='));
  const h1Header = signatureParts.find(part => part.startsWith('h1='));

  if (!tsHeader || !h1Header) {
    console.error('Invalid signature format');
    return false;
  }

  const timestamp = tsHeader.replace('ts=', '');
  const receivedSignature = h1Header.replace('h1=', '');

  // Construct the signed payload
  const signedPayload = `${timestamp}:${JSON.stringify(req.body)}`;
  
  // Calculate expected signature
  const expectedSignature = crypto
    .createHmac('sha256', paddleConfig.webhookSecret)
    .update(signedPayload)
    .digest('hex');

  // Compare signatures
  const isValid = crypto.timingSafeEqual(
    Buffer.from(receivedSignature),
    Buffer.from(expectedSignature)
  );

  if (!isValid) {
    console.error('Webhook signature verification failed');
  }

  return isValid;
};

// Webhook endpoint (no auth required for Paddle webhooks)
router.post('/webhook', async (req, res) => {
  try {
    // Verify webhook signature
    if (!verifyPaddleWebhookSignature(req)) {
      console.error('Invalid webhook signature');
      return res.status(401).json({ success: false, error: 'Invalid signature' });
    }

    const event = req.body;
    
    // Validate webhook data structure
    if (!event || !event.event_type || !event.data) {
      console.error('Invalid webhook data structure:', event);
      return res.status(400).json({ success: false, error: 'Invalid webhook data' });
    }

    console.log('Paddle webhook received:', event.event_type);
    console.log('Webhook data:', JSON.stringify(event, null, 2));

    // Handle different subscription events
    switch (event.event_type) {
      case 'subscription.created':
        await handleSubscriptionCreated(event.data);
        break;
      
      case 'subscription.activated':
        await handleSubscriptionActivated(event.data);
        break;
      
      case 'subscription.trial_ended':
        await handleTrialEnded(event.data);
        break;
      
      case 'subscription.payment_success':
        await handlePaymentSuccess(event.data);
        break;
      
      case 'subscription.payment_failed':
        await handlePaymentFailed(event.data);
        break;
      
      case 'subscription.cancelled':
        await handleSubscriptionCancelled(event.data);
        break;
      
      case 'subscription.expired':
        await handleSubscriptionExpired(event.data);
        break;
        
      case 'subscription.updated':
        await handleSubscriptionUpdated(event.data);
        break;
        
      case 'subscription.paused':
        await handleSubscriptionPaused(event.data);
        break;
        
      case 'subscription.resumed':
        await handleSubscriptionResumed(event.data);
        break;
      
      default:
        console.log('Unhandled webhook event:', event.event_type);
    }

    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Webhook handling error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Webhook handler functions
const User = require('../models/User');
const Subscription = require('../models/Subscription');
const Payment = require('../models/Payment');
const subscriptionService = require('../services/subscriptionService');

async function handleSubscriptionCreated(data) {
  console.log('Handling subscription created:', data.id);
  try {
    const customData = data.custom_data || {};
    const userId = customData.user_id;
    
    if (!userId || userId === 'test-user-id') {
      console.error('No valid user_id found in subscription custom_data, userId:', userId);
      return;
    }

    // Check if this subscription already exists (webhook idempotency)
    const existingSubscription = await Subscription.findOne({ 
      paddleSubscriptionId: data.id 
    });
    
    if (existingSubscription) {
      console.log('Subscription already exists, skipping creation:', data.id);
      return;
    }

    // Verify user exists in database
    const user = await User.findById(userId);
    if (!user) {
      console.error('User not found for subscription creation:', userId);
      return;
    }

    // Create subscription record with billing cycle information
    // Validate critical fields
    if (!data.id) {
      console.error('Missing subscription ID in webhook data');
      return;
    }

    if (!data.next_billed_at) {
      console.error('Missing next_billed_at in webhook data');
      return;
    }

    const trialEndDate = new Date(data.next_billed_at);
    if (isNaN(trialEndDate.getTime())) {
      console.error('Invalid next_billed_at date:', data.next_billed_at);
      return;
    }

    // Validate plan type
    const planType = customData.plan_type || 'monthly';
    if (!['monthly', 'yearly', 'lifetime'].includes(planType)) {
      console.error('Invalid plan type:', planType);
      return;
    }

    const subscription = await Subscription.create({
      user: userId,
      paddleSubscriptionId: data.id,
      plan: planType,
      paymentStatus: 'trial', // Initially in trial
      paymentAmount: data.items?.[0]?.price?.unit_price?.amount || 0,
      paymentMethod: 'paddle',
      subscriptionStatus: 'trialing',
      trialEndsAt: trialEndDate,
      nextBillingDate: trialEndDate,
      currentPeriodStart: new Date(),
      currentPeriodEnd: trialEndDate,
      billingInterval: planType === 'yearly' ? 'year' : 
                        planType === 'lifetime' ? 'lifetime' : 'month',
      lastPaddleEventId: data.event_id || data.id,
      createdAt: new Date()
    });

    // Update user subscription status using centralized service
    // During trial period, user should have premium access
    await subscriptionService.updateUserSubscriptionStatus(userId, {
      plan: customData.plan_type || 'monthly',
      active: true, // Trial users get premium access
      paymentStatus: 'trial',
      subscriptionStatus: 'trialing',
      expiryDate: new Date(data.next_billed_at),
      paddleSubscriptionId: data.id,
      subscriptionId: subscription._id
    });

    // Send subscription success email
    try {
      const emailService = require('../services/emailService');
      await emailService.sendSubscriptionSuccessEmail({
        name: user.name,
        email: user.email,
        plan: customData.plan_type || 'monthly',
        amount: data.items[0]?.price?.unit_price?.amount || 0,
        trialEndsAt: trialEndDate,
        nextBillingDate: trialEndDate
      });
      console.log('Subscription success email sent to:', user.email);
    } catch (emailError) {
      console.error('Failed to send subscription success email:', emailError);
      // Don't fail the webhook if email fails
    }

    console.log('Subscription created successfully for user:', userId);
  } catch (error) {
    console.error('Error handling subscription created:', error);
  }
}

async function handleSubscriptionActivated(data) {
  console.log('Handling subscription activated:', data.id);
  try {
    // Find subscription by Paddle ID
    const subscription = await Subscription.findOne({ 
      paddleSubscriptionId: data.id 
    });
    
    if (subscription && subscription.user && subscription.user !== 'test-user-id') {
      // Update subscription status
      subscription.subscriptionStatus = 'active';
      subscription.paymentStatus = 'confirmed';
      await subscription.save();

      // Update user status using centralized service
      await subscriptionService.updateUserSubscriptionStatus(subscription.user, {
        plan: subscription.plan,
        active: true,
        paymentStatus: 'confirmed',
        subscriptionStatus: 'active',
        expiryDate: new Date(data.next_billed_at),
        subscriptionId: subscription._id
      });

      console.log('Subscription activated for user:', subscription.user);
    }
  } catch (error) {
    console.error('Error handling subscription activated:', error);
  }
}

async function handleTrialEnded(data) {
  console.log('Handling trial ended:', data.id);
  try {
    const subscription = await Subscription.findOne({ 
      paddleSubscriptionId: data.id 
    });
    
    if (subscription) {
      // Handle different plan types appropriately
      if (subscription.plan === 'lifetime') {
        // For lifetime plans, trial ending means they should get full access immediately
        subscription.subscriptionStatus = 'active';
        subscription.paymentStatus = 'confirmed'; // Lifetime is paid upfront
        subscription.trialEndsAt = new Date();
        subscription.lastPaddleEventId = data.event_id;
        
        // Set far future expiry for lifetime
        const farFuture = new Date();
        farFuture.setFullYear(farFuture.getFullYear() + 100);
        subscription.currentPeriodEnd = farFuture;
        subscription.nextBillingDate = null; // No future billing for lifetime
        
        await subscription.save();

        await subscriptionService.updateUserSubscriptionStatus(subscription.user, {
          plan: subscription.plan,
          active: true,
          paymentStatus: 'confirmed',
          subscriptionStatus: 'active',
          expiryDate: farFuture,
          subscriptionId: subscription._id
        });
        
        console.log('Lifetime trial ended for user:', subscription.user, '- Full access granted');
      } else {
        // For recurring plans (monthly/yearly), trial ended means awaiting first payment
        subscription.subscriptionStatus = 'active'; // Paddle will automatically attempt payment
        subscription.trialEndsAt = new Date(); // Mark trial as ended
        subscription.lastPaddleEventId = data.event_id;
        await subscription.save();

        // Update user status - keep premium access as Paddle will charge soon
        await subscriptionService.updateUserSubscriptionStatus(subscription.user, {
          plan: subscription.plan,
          active: true, // Keep access while Paddle processes payment
          paymentStatus: 'pending', // Payment is pending after trial
          subscriptionStatus: 'active',
          expiryDate: subscription.currentPeriodEnd,
          subscriptionId: subscription._id
        });

        console.log('Trial ended for user:', subscription.user, '- Paddle will automatically charge');
        
        // Note: Paddle automatically attempts the first payment when trial ends
        // We'll receive either payment_success or payment_failed webhook next
      }
    }
  } catch (error) {
    console.error('Error handling trial ended:', error);
  }
}

async function handlePaymentSuccess(data) {
  console.log('Handling payment success:', data.subscription_id);
  try {
    const subscription = await Subscription.findOne({ 
      paddleSubscriptionId: data.subscription_id 
    });
    
    if (subscription) {
      // Get billing dates from Paddle data - Paddle provides accurate billing information
      const currentPeriodEnd = new Date(data.billing_period?.ends_at);
      const nextBillingDate = data.next_billed_at ? new Date(data.next_billed_at) : null;
      
      console.log('Billing period info:', {
        starts_at: data.billing_period?.starts_at,
        ends_at: data.billing_period?.ends_at,
        next_billed_at: data.next_billed_at,
        billingInterval: subscription.billingInterval
      });

      // Update subscription with payment success and new billing cycle
      subscription.paymentStatus = 'confirmed';
      subscription.subscriptionStatus = 'active';
      subscription.currentPeriodStart = new Date(data.billing_period?.starts_at);
      subscription.lastPaddleEventId = data.event_id;
      subscription.billingRetryCount = 0; // Reset retry count on successful payment
      
      // Handle different plan types for billing dates
      if (subscription.plan === 'lifetime') {
        // For lifetime plans, set far future expiry and no next billing
        const farFuture = new Date();
        farFuture.setFullYear(farFuture.getFullYear() + 100);
        subscription.currentPeriodEnd = farFuture;
        subscription.nextBillingDate = null; // No future billing for lifetime
      } else {
        // For recurring plans, use Paddle's billing information
        subscription.currentPeriodEnd = currentPeriodEnd;
        subscription.nextBillingDate = nextBillingDate;
      }
      
      await subscription.save();

      // Create payment record with safe field access and idempotency check
      try {
        const transactionId = data.id || `paddle_${Date.now()}`;
        
        // Check if payment record already exists
        const existingPayment = await Payment.findOne({
          paddleTransactionId: transactionId
        });
        
        if (!existingPayment) {
          await Payment.create({
            subscription: subscription._id,
            user: subscription.user,
            paddleTransactionId: transactionId,
            paddleSubscriptionId: data.subscription_id || subscription.paddleSubscriptionId,
            paddleInvoiceId: data.invoice_id || null,
            paddleReceiptUrl: data.receipt_url || null,
            amount: data.totals?.total || subscription.paymentAmount || 0,
            currency: data.currency_code || 'USD',
            status: 'completed',
            paymentMethod: 'paddle',
            periodStart: data.billing_period?.starts_at ? new Date(data.billing_period.starts_at) : subscription.currentPeriodStart || new Date(),
            periodEnd: currentPeriodEnd,
            completedAt: new Date(),
            type: subscription.subscriptionStatus === 'trialing' ? 'trial_conversion' : 'recurring',
            taxAmount: data.totals?.tax || 0,
            netAmount: data.totals?.subtotal || subscription.paymentAmount || 0,
            paddleEventData: data,
          });
          console.log('Payment record created successfully');
        } else {
          console.log('Payment record already exists, skipping creation:', transactionId);
        }
      } catch (paymentError) {
        console.error('Error creating payment record:', paymentError);
        // Don't fail the webhook if payment record creation fails
      }

      // Update user subscription status using centralized service
      await subscriptionService.updateUserSubscriptionStatus(subscription.user, {
        plan: subscription.plan,
        active: true,
        paymentStatus: 'confirmed',
        subscriptionStatus: 'active',
        expiryDate: currentPeriodEnd,
        subscriptionId: subscription._id
      });

      console.log('Payment successful for user:', subscription.user);
      console.log('Next billing date:', nextBillingDate);
    }
  } catch (error) {
    console.error('Error handling payment success:', error);
  }
}

async function handlePaymentFailed(data) {
  console.log('Handling payment failed:', data.subscription_id);
  try {
    const subscription = await Subscription.findOne({ 
      paddleSubscriptionId: data.subscription_id 
    });
    
    if (subscription) {
      // Update subscription status
      subscription.paymentStatus = 'failed';
      subscription.subscriptionStatus = 'past_due';
      subscription.lastBillingAttempt = new Date();
      subscription.billingRetryCount = (subscription.billingRetryCount || 0) + 1;
      subscription.lastPaddleEventId = data.event_id;
      await subscription.save();

      // Create failed payment record
      await Payment.create({
        subscription: subscription._id,
        user: subscription.user,
        paddleTransactionId: data.id,
        paddleSubscriptionId: data.subscription_id,
        amount: data.totals?.total || subscription.paymentAmount,
        currency: data.currency_code || 'USD',
        status: 'failed',
        paymentMethod: 'paddle',
        periodStart: subscription.currentPeriodStart,
        periodEnd: subscription.currentPeriodEnd,
        failedAt: new Date(),
        failureReason: data.failure_reason || 'Payment declined',
        retryCount: subscription.billingRetryCount,
        type: 'recurring',
        paddleEventData: data,
      });

      // Handle grace period based on retry count
      const maxRetries = 3; // Paddle typically retries 3 times over 7 days
      const stillInGracePeriod = subscription.billingRetryCount <= maxRetries;
      
      await subscriptionService.updateUserSubscriptionStatus(subscription.user, {
        plan: subscription.plan,
        active: stillInGracePeriod, // Keep access during grace period
        paymentStatus: 'failed',
        subscriptionStatus: 'past_due',
        expiryDate: subscription.currentPeriodEnd,
        subscriptionId: subscription._id
      });

      console.log('Payment failed for user:', subscription.user, 'Retry count:', subscription.billingRetryCount);
      console.log('Grace period active:', stillInGracePeriod);
      
      // If max retries exceeded, Paddle will send subscription.cancelled webhook
      if (subscription.billingRetryCount > maxRetries) {
        console.warn('Max payment retries exceeded for subscription:', data.subscription_id);
      }
    }
  } catch (error) {
    console.error('Error handling payment failed:', error);
  }
}

async function handleSubscriptionCancelled(data) {
  console.log('Handling subscription cancelled:', data.id);
  try {
    const subscription = await Subscription.findOne({ 
      paddleSubscriptionId: data.id 
    });
    
    if (subscription) {
      subscription.subscriptionStatus = 'cancelled';
      subscription.cancelledAt = new Date();
      subscription.lastPaddleEventId = data.event_id;
      
      // Check if this is an immediate cancellation (trial period) or scheduled cancellation
      const now = new Date();
      const isImmediateCancellation = data.scheduled_change?.effective_at ? 
        new Date(data.scheduled_change.effective_at) <= now : true;
      
      // For trial period cancellations or immediate cancellations, downgrade immediately
      // For end-of-period cancellations, this webhook fires when access actually ends
      if (isImmediateCancellation || subscription.subscriptionStatus === 'trialing') {
        console.log('Immediate cancellation detected - downgrading user immediately');
        
        await subscription.save();

        // Downgrade user to free tier immediately
        await subscriptionService.updateUserSubscriptionStatus(subscription.user, {
          plan: 'free',
          active: false,
          paymentStatus: 'cancelled',
          subscriptionStatus: 'free',
          expiryDate: null,
          subscriptionId: subscription._id
        });
      } else {
        console.log('End-of-period cancellation - access has ended');
        
        await subscription.save();

        // User access ends now (this webhook fires at the end of billing period)
        await subscriptionService.updateUserSubscriptionStatus(subscription.user, {
          plan: 'free',
          active: false,
          paymentStatus: 'cancelled',
          subscriptionStatus: 'free',
          expiryDate: null,
          subscriptionId: subscription._id
        });
      }

      console.log('Subscription cancelled for user:', subscription.user);
    }
  } catch (error) {
    console.error('Error handling subscription cancelled:', error);
  }
}

async function handleSubscriptionExpired(data) {
  console.log('Handling subscription expired:', data.id);
  try {
    const subscription = await Subscription.findOne({ 
      paddleSubscriptionId: data.id 
    });
    
    if (subscription) {
      subscription.subscriptionStatus = 'expired';
      subscription.expiredAt = new Date();
      subscription.lastPaddleEventId = data.event_id;
      await subscription.save();

      // Use centralized service to downgrade user to free tier
      await subscriptionService.updateUserSubscriptionStatus(subscription.user, {
        plan: 'free',
        active: false,
        paymentStatus: 'expired',
        subscriptionStatus: 'free',
        expiryDate: null,
        subscriptionId: subscription._id
      });

      console.log('Subscription expired for user:', subscription.user);
    }
  } catch (error) {
    console.error('Error handling subscription expired:', error);
  }
}

async function handleSubscriptionUpdated(data) {
  console.log('Handling subscription updated:', data.id);
  try {
    const subscription = await Subscription.findOne({ 
      paddleSubscriptionId: data.id 
    });
    
    if (subscription) {
      // Update subscription details from Paddle
      subscription.subscriptionStatus = data.status || subscription.subscriptionStatus;
      subscription.lastPaddleEventId = data.event_id;
      
      // Update billing cycle information if provided
      if (data.next_billed_at) {
        subscription.nextBillingDate = new Date(data.next_billed_at);
      }
      
      if (data.current_billing_period) {
        subscription.currentPeriodStart = new Date(data.current_billing_period.starts_at);
        subscription.currentPeriodEnd = new Date(data.current_billing_period.ends_at);
      }
      
      // Update pricing if changed
      if (data.items && data.items[0]?.price?.unit_price?.amount) {
        subscription.paymentAmount = data.items[0].price.unit_price.amount;
      }
      
      await subscription.save();
      
      // Sync user status if subscription is still active
      if (['active', 'trialing'].includes(data.status)) {
        await subscriptionService.updateUserSubscriptionStatus(subscription.user, {
          plan: subscription.plan,
          active: true,
          paymentStatus: subscription.paymentStatus,
          subscriptionStatus: data.status,
          expiryDate: subscription.currentPeriodEnd,
          subscriptionId: subscription._id
        });
      }
      
      console.log('Subscription updated for user:', subscription.user, 'New status:', data.status);
    }
  } catch (error) {
    console.error('Error handling subscription updated:', error);
  }
}

async function handleSubscriptionPaused(data) {
  console.log('Handling subscription paused:', data.id);
  try {
    const subscription = await Subscription.findOne({ 
      paddleSubscriptionId: data.id 
    });
    
    if (subscription) {
      subscription.subscriptionStatus = 'paused';
      subscription.lastPaddleEventId = data.event_id;
      await subscription.save();

      // Update user status - keep access during pause (business decision)
      await subscriptionService.updateUserSubscriptionStatus(subscription.user, {
        plan: subscription.plan,
        active: false, // Paused users lose access
        paymentStatus: subscription.paymentStatus,
        subscriptionStatus: 'paused',
        expiryDate: subscription.currentPeriodEnd,
        subscriptionId: subscription._id
      });

      console.log('Subscription paused for user:', subscription.user);
    }
  } catch (error) {
    console.error('Error handling subscription paused:', error);
  }
}

async function handleSubscriptionResumed(data) {
  console.log('Handling subscription resumed:', data.id);
  try {
    const subscription = await Subscription.findOne({ 
      paddleSubscriptionId: data.id 
    });
    
    if (subscription) {
      subscription.subscriptionStatus = 'active';
      subscription.lastPaddleEventId = data.event_id;
      
      // Update billing information if provided
      if (data.next_billed_at) {
        subscription.nextBillingDate = new Date(data.next_billed_at);
      }
      
      await subscription.save();

      // Restore user access
      await subscriptionService.updateUserSubscriptionStatus(subscription.user, {
        plan: subscription.plan,
        active: true, // Restore access
        paymentStatus: subscription.paymentStatus,
        subscriptionStatus: 'active',
        expiryDate: subscription.currentPeriodEnd,
        subscriptionId: subscription._id
      });

      console.log('Subscription resumed for user:', subscription.user);
    }
  } catch (error) {
    console.error('Error handling subscription resumed:', error);
  }
}

module.exports = router;
module.exports.makePaddleAPIRequest = makePaddleAPIRequest;