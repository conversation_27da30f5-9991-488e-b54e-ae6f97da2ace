const express = require('express');
const router = express.Router();
const pillarPageController = require('../controllers/pillarPageController');
const auth = require('../middleware/auth');
const isAdmin = require('../middleware/isAdmin');
const { cacheMiddleware } = require('../middleware/cache');

// Public routes with smart caching
router.get('/', cacheMiddleware(), pillarPageController.getPillarPages);
router.get('/slug/:slug', cacheMiddleware(), pillarPageController.getPillarPageBySlug);
router.get('/slug/:slug/cluster-pages', cacheMiddleware(), pillarPageController.getPillarPageClusterPages);
router.get('/category/:category', cacheMiddleware(), pillarPageController.getPillarPageByCategory);

// Admin routes
router.get('/all', auth.authenticateJWT, isAdmin, pillarPageController.getAllPillarPages);
router.get('/:id', auth.authenticateJWT, isAdmin, pillarPageController.getPillarPageById);
router.post('/', auth.authenticateJWT, isAdmin, pillarPageController.createPillarPage);
router.put('/:id', auth.authenticateJWT, isAdmin, pillarPageController.updatePillarPage);
router.delete('/:id', auth.authenticateJWT, isAdmin, pillarPageController.deletePillarPage);
router.get('/blog-posts/available', auth.authenticateJWT, isAdmin, pillarPageController.getAvailableBlogPosts);

module.exports = router;
