/**
 * Email Outreach Routes
 * 
 * Routes for university email outreach campaigns
 */

const express = require('express');
const router = express.Router();
const emailOutreachController = require('../controllers/emailOutreachController');
const auth = require('../middleware/auth');
const isAdmin = require('../middleware/isAdmin');

// All routes require admin authentication
router.use(auth.authenticateJWT);
router.use(isAdmin);

// GET /api/email-outreach/universities - Get all university contacts
router.get('/universities', emailOutreachController.getUniversityContacts);

// GET /api/email-outreach/template/:universityId - Get email template for specific university
router.get('/template/:universityId', emailOutreachController.getEmailTemplate);

// GET /api/email-outreach/templates - Get all email templates
router.get('/templates', emailOutreachController.getAllEmailTemplates);

// PUT /api/email-outreach/template/:universityId - Update email template
router.put('/template/:universityId', emailOutreachController.updateEmailTemplate);

// DELETE /api/email-outreach/template/:universityId - Delete email template
router.delete('/template/:universityId', emailOutreachController.deleteEmailTemplate);

// POST /api/email-outreach/templates/initialize - Initialize default templates
router.post('/templates/initialize', emailOutreachController.initializeDefaultTemplates);

// POST /api/email-outreach/send - Send email to specific university
router.post('/send', emailOutreachController.sendUniversityEmail);

// POST /api/email-outreach/send-bulk - Send emails to multiple universities
router.post('/send-bulk', emailOutreachController.sendBulkUniversityEmails);

module.exports = router;