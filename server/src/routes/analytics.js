const express = require('express');
const router = express.Router();
const { authenticateJWT } = require('../middleware/auth');
const isAdmin = require('../middleware/isAdmin');
const {
  getDashboard,
  getRealTime,
  getBotTraffic,
  getDebugData,
  exportData
} = require('../controllers/analyticsController');

/**
 * @route   GET /analytics/dashboard
 * @desc    Get analytics dashboard data
 * @access  Admin only
 * @query   timeRange: 1d, 7d, 30d, 90d (default: 7d)
 */
router.get('/dashboard', authenticateJWT, isAdmin, getDashboard);

/**
 * @route   GET /analytics/realtime
 * @desc    Get real-time analytics data
 * @access  Admin only
 */
router.get('/realtime', authenticateJWT, isAdmin, getRealTime);

/**
 * @route   GET /analytics/bots
 * @desc    Get bot traffic data
 * @access  Admin only
 * @query   timeRange: 1d, 7d, 30d (default: 7d)
 */
router.get('/bots', authenticateJWT, isAdmin, getBotTraffic);

/**
 * @route   GET /analytics/debug
 * @desc    Get recent analytics data for debugging
 * @access  Admin only
 */
router.get('/debug', authenticateJWT, isAdmin, getDebugData);

/**
 * @route   GET /analytics/export
 * @desc    Export analytics data
 * @access  Admin only
 * @query   timeRange: 1d, 7d, 30d, 90d (default: 30d)
 * @query   format: json, csv (default: json)
 */
router.get('/export', authenticateJWT, isAdmin, exportData);

module.exports = router;
