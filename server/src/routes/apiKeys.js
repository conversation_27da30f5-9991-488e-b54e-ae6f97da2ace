/**
 * API Key Routes
 * Routes for API key management
 */

const express = require('express');
const router = express.Router();
const apiKeyController = require('../controllers/apiKeyController');
const auth = require('../middleware/auth');

// All routes require authentication
router.use(auth.authenticateJWT);

// @route   POST /api/api-keys
// @desc    Create a new API key
// @access  Private
router.post('/', apiKeyController.createApiKey);

// @route   GET /api/api-keys
// @desc    Get all API keys for the current user
// @access  Private
router.get('/', apiKeyController.getApiKeys);

// @route   DELETE /api/api-keys/:id
// @desc    Delete an API key
// @access  Private
router.delete('/:id', apiKeyController.deleteApiKey);

// @route   PUT /api/api-keys/:id
// @desc    Update an API key
// @access  Private
router.put('/:id', apiKeyController.updateApiKey);

module.exports = router;
