/**
 * Email Campaign Routes
 * 
 * Routes for email campaign management
 */

const express = require('express');
const router = express.Router();
const emailCampaignController = require('../controllers/emailCampaignController');
const auth = require('../middleware/auth');
const isAdmin = require('../middleware/isAdmin');

// All routes require admin authentication
router.use(auth.authenticateJWT);
router.use(isAdmin);

// GET /api/email-campaigns - Get all campaigns
router.get('/', emailCampaignController.getCampaigns);

// GET /api/email-campaigns/:campaignId - Get single campaign
router.get('/:campaignId', emailCampaignController.getCampaign);

// POST /api/email-campaigns - Create new campaign
router.post('/', emailCampaignController.createCampaign);

// PUT /api/email-campaigns/:campaignId - Update campaign
router.put('/:campaignId', emailCampaignController.updateCampaign);

// DELETE /api/email-campaigns/:campaignId - Delete campaign
router.delete('/:campaignId', emailCampaignController.deleteCampaign);

// POST /api/email-campaigns/:campaignId/test - Send test email
router.post('/:campaignId/test', emailCampaignController.sendTestEmail);

// POST /api/email-campaigns/:campaignId/schedule - Schedule campaign
router.post('/:campaignId/schedule', emailCampaignController.scheduleCampaign);

// POST /api/email-campaigns/:campaignId/send - Send campaign immediately
router.post('/:campaignId/send', emailCampaignController.sendCampaign);

// POST /api/email-campaigns/:campaignId/pause - Pause campaign
router.post('/:campaignId/pause', emailCampaignController.pauseCampaign);

module.exports = router;