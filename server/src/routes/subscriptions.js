const express = require('express');
const router = express.Router();
const subscriptionController = require('../controllers/subscriptionController');
const auth = require('../middleware/auth');
const passport = require('passport');

// Custom middleware to ensure authentication and provide detailed logging
const ensureAuthenticated = (req, res, next) => {
  console.log('Checking authentication for subscription route');

  // Check if Authorization header exists
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    console.error('No Authorization header found');
    return res.status(401).json({ message: 'No authorization token provided' });
  }

  // Use passport JWT authentication
  passport.authenticate('jwt', { session: false }, (err, user, info) => {
    if (err) {
      console.error('Authentication error:', err);
      return res.status(500).json({ message: 'Authentication error' });
    }

    if (!user) {
      console.error('Authentication failed:', info ? info.message : 'Unknown reason');
      return res.status(401).json({ message: 'Authentication failed' });
    }

    console.log('User authenticated successfully:', user.id);
    req.user = user;
    next();
  })(req, res, next);
};

// @route   GET /api/subscriptions/current
// @desc    Get current user's subscription
// @access  Private
router.get('/current', ensureAuthenticated, subscriptionController.getCurrentSubscription);

// @route   POST /api/subscriptions
// @desc    Create a new subscription request
// @access  Private
router.post('/', ensureAuthenticated, subscriptionController.createSubscriptionRequest);

// @route   GET /api/subscriptions/history
// @desc    Get subscription history for current user
// @access  Private
router.get('/history', ensureAuthenticated, subscriptionController.getSubscriptionHistory);

// @route   POST /api/subscriptions/cancel
// @desc    Cancel current subscription
// @access  Private
router.post('/cancel', ensureAuthenticated, subscriptionController.cancelSubscription);

module.exports = router;
