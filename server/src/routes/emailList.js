/**
 * Email List Routes
 * 
 * Routes for email list management
 */

const express = require('express');
const router = express.Router();
const emailListController = require('../controllers/emailListController');
const auth = require('../middleware/auth');
const isAdmin = require('../middleware/isAdmin');

// All routes require admin authentication
router.use(auth.authenticateJWT);
router.use(isAdmin);

// GET /api/email-lists - Get all email lists
router.get('/', emailListController.getEmailLists);

// GET /api/email-lists/:listId - Get single email list with contacts
router.get('/:listId', emailListController.getEmailList);

// POST /api/email-lists - Create new email list
router.post('/', emailListController.createEmailList);

// PUT /api/email-lists/:listId - Update email list
router.put('/:listId', emailListController.updateEmailList);

// DELETE /api/email-lists/:listId - Delete email list
router.delete('/:listId', emailListController.deleteEmailList);

// POST /api/email-lists/:listId/contacts - Add contact to list
router.post('/:listId/contacts', emailListController.addContact);

// POST /api/email-lists/:listId/import - Import contacts from CSV
router.post('/:listId/import', emailListController.importContacts);

// DELETE /api/email-lists/:listId/contacts/:contactId - Remove contact from list
router.delete('/:listId/contacts/:contactId', emailListController.removeContact);

// GET /api/email-lists/users/registered - Get registered users
router.get('/users/registered', emailListController.getRegisteredUsers);

// POST /api/email-lists/:listId/import-users - Import registered users to list
router.post('/:listId/import-users', emailListController.importRegisteredUsers);

module.exports = router;