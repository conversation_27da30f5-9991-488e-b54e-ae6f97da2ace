/**
 * API Key Authentication Middleware
 * Authenticates requests using API keys
 */

const ApiKey = require('../models/ApiKey');
const User = require('../models/User');

/**
 * Middleware to authenticate API key
 * @param {Array} requiredPermissions - Array of required permissions
 */
const authenticateApiKey = (requiredPermissions = []) => {
  return async (req, res, next) => {
    try {
      // Get API key from header
      const apiKey = req.header('X-API-Key');

      if (!apiKey) {
        return res.status(401).json({ message: 'API key is required' });
      }

      // Find API key in database
      const keyDoc = await ApiKey.findOne({ key: apiKey, isActive: true });

      if (!keyDoc) {
        return res.status(401).json({ message: 'Invalid or inactive API key' });
      }

      // Check if API key is expired
      if (keyDoc.expiresAt && new Date() > keyDoc.expiresAt) {
        return res.status(401).json({ message: 'API key has expired' });
      }

      // Check permissions if required
      if (requiredPermissions.length > 0) {
        const hasPermission = requiredPermissions.every(permission =>
          keyDoc.permissions.includes(permission)
        );

        if (!hasPermission) {
          return res.status(403).json({
            message: 'API key does not have the required permissions',
            requiredPermissions,
            providedPermissions: keyDoc.permissions
          });
        }
      }

      // Find user associated with API key
      const user = await User.findById(keyDoc.user);

      if (!user) {
        return res.status(401).json({ message: 'User associated with API key not found' });
      }

      // Update last used timestamp
      keyDoc.lastUsed = new Date();
      await keyDoc.save();

      // Attach user and API key to request
      req.user = user;
      req.apiKey = keyDoc;

      next();
    } catch (err) {
      console.error('API key authentication error:', err);
      res.status(500).json({ message: 'Server error during API key authentication' });
    }
  };
};

module.exports = { authenticateApiKey };
