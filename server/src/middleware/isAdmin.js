/**
 * Middleware to check if the authenticated user is an admin
 * This middleware should be used after the auth.authenticateJWT middleware
 */
const isAdmin = (req, res, next) => {
  // Check if user exists and is an admin
  if (req.user && req.user.isAdmin) {
    return next();
  }
  
  // If not admin, return unauthorized
  return res.status(403).json({ message: 'Access denied. Admin privileges required.' });
};

module.exports = isAdmin;
