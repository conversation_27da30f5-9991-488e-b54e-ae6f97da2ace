const Analytics = require('../models/Analytics');
const { detectBot, parseUserAgent } = require('../utils/botDetection');
const crypto = require('crypto');

/**
 * Generate a unique visitor ID based on IP and User Agent
 * @param {string} ip - Client IP address
 * @param {string} userAgent - User agent string
 * @returns {string} - Unique visitor ID
 */
function generateVisitorId(ip, userAgent) {
  const hash = crypto.createHash('sha256');
  hash.update(ip + userAgent);
  return hash.digest('hex').substring(0, 16);
}

/**
 * Generate a session ID
 * @returns {string} - Unique session ID
 */
function generateSessionId() {
  return crypto.randomBytes(16).toString('hex');
}

/**
 * Analytics middleware to track website traffic
 * Only tracks real human visitors, filters out bots and crawlers
 */
const analyticsMiddleware = async (req, res, next) => {
  const startTime = Date.now();

  try {
    // Skip analytics for certain paths
    const skipPaths = [
      '/api',
      '/favicon.ico',
      '/robots.txt',
      '/sitemap.xml',
      '/.well-known',
      '/health',
      '/ping'
    ];

    const shouldSkip = skipPaths.some(path => req.path.startsWith(path));
    if (shouldSkip) {
      return next();
    }

    // Get request information
    const userAgent = req.get('User-Agent') || '';
    const ip = req.ip || req.connection.remoteAddress || '';
    const referer = req.get('Referer') || '';
    const acceptLanguage = req.get('Accept-Language') || '';

    // Detect if this is a bot
    const botDetection = detectBot(req);

    // Parse user agent for browser/OS info
    const userAgentInfo = parseUserAgent(userAgent);

    // Generate visitor and session IDs
    const visitorId = generateVisitorId(ip, userAgent);

    // Extract language from Accept-Language header
    const language = acceptLanguage.split(',')[0]?.split('-')[0] || 'en';

    // Check if this is a new session (within 30 minutes)
    const existingSession = await Analytics.findOne({
      visitorId,
      timestamp: { $gte: new Date(Date.now() - 30 * 60 * 1000) } // 30 minutes
    }).sort({ timestamp: -1 });

    const isNewSession = !existingSession;

    // Use existing session ID or generate new one
    const sessionId = existingSession?.sessionId || generateSessionId();

    // Prepare analytics data
    const analyticsData = {
      sessionId,
      visitorId,
      path: req.path,
      method: req.method,
      referrer: referer,
      userAgent,
      browser: userAgentInfo.browser,
      os: userAgentInfo.os,
      device: userAgentInfo.device,
      ip,
      timestamp: new Date(),
      isNewSession,
      isAuthenticated: !!req.user,
      userId: req.user?.id || null,
      isBot: botDetection.isBot,
      botType: botDetection.botType,
      language,
    };

    // Add response time after request completes
    const originalSend = res.send;
    res.send = function(data) {
      analyticsData.responseTime = Date.now() - startTime;

      // Save analytics data asynchronously (don't block response)
      setImmediate(async () => {
        try {
          // Only save if it's not a bot
          if (!botDetection.isBot) {
            // Create new analytics record
            await Analytics.create(analyticsData);

            // Update session duration for existing sessions
            if (!isNewSession && existingSession) {
              const sessionDuration = Math.floor((Date.now() - existingSession.timestamp.getTime()) / 1000);

              // Update the session duration on the most recent record for this session
              await Analytics.updateOne(
                {
                  sessionId: existingSession.sessionId,
                  timestamp: { $gte: existingSession.timestamp }
                },
                {
                  $set: { sessionDuration: sessionDuration }
                },
                { sort: { timestamp: -1 } }
              );
            }
          } else {
            // Still save bot data for monitoring, but mark it as bot traffic
            await Analytics.create(analyticsData);
          }
        } catch (error) {
          console.error('Error saving analytics data:', error);
        }
      });

      originalSend.call(this, data);
    };

    // Attach analytics info to request for potential use in routes
    req.analytics = {
      visitorId,
      sessionId,
      isBot: botDetection.isBot,
      botType: botDetection.botType,
      device: userAgentInfo.device,
      browser: userAgentInfo.browser.name,
      os: userAgentInfo.os.name
    };

    next();
  } catch (error) {
    console.error('Analytics middleware error:', error);
    // Don't block the request if analytics fails
    next();
  }
};

/**
 * Middleware to track page views for authenticated users
 */
const trackPageView = async (req, res, next) => {
  try {
    if (req.user && req.analytics && !req.analytics.isBot) {
      // Track specific page views for authenticated users
      const pageViewData = {
        userId: req.user.id,
        path: req.path,
        timestamp: new Date(),
        sessionId: req.analytics.sessionId,
        device: req.analytics.device,
        browser: req.analytics.browser,
        os: req.analytics.os
      };

      // You can save this to a separate PageViews collection if needed
      // For now, it's included in the main Analytics collection
    }

    next();
  } catch (error) {
    console.error('Page view tracking error:', error);
    next();
  }
};

module.exports = {
  analyticsMiddleware,
  trackPageView,
  generateVisitorId,
  generateSessionId
};
