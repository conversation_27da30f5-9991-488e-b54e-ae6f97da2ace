const NodeCache = require('node-cache');

// Create cache instance with shorter TTL for frequently changing content
const cache = new NodeCache({ stdTTL: 60 }); // 1 minute default TTL

// Cache middleware for GET requests with smart TTL
const cacheMiddleware = (duration) => {
  return (req, res, next) => {
    // Only cache GET requests
    if (req.method !== 'GET') {
      return next();
    }

    const key = req.originalUrl;
    let cachedResponse;

    try {
      cachedResponse = cache.get(key);
    } catch (cacheError) {
      console.error(`Cache retrieval error for ${key}:`, cacheError.message);
      cachedResponse = null;
    }

    if (cachedResponse) {
      console.log(`Cache hit for: ${key}`);
      return res.json(cachedResponse);
    }

    // Store original res.json
    const originalJson = res.json;

    // Override res.json to cache the response
    res.json = function(data) {
      // Determine cache duration based on content type
      let cacheDuration = duration;

      if (!cacheDuration) {
        // Smart caching based on URL patterns
        if (key.includes('/cluster-pages')) {
          // Cluster pages change frequently - shorter cache
          cacheDuration = 30; // 30 seconds
        } else if (key.includes('/category/') || key.includes('/slug/')) {
          // Individual pillar pages - medium cache
          cacheDuration = 120; // 2 minutes
        } else {
          // General listings - longer cache
          cacheDuration = 300; // 5 minutes
        }
      }

      // Convert Mongoose documents to plain objects before caching
      let cacheData = data;
      try {
        // Handle both single documents and arrays
        if (data && typeof data === 'object') {
          if (Array.isArray(data)) {
            // Handle arrays of documents
            cacheData = data.map(item => {
              if (item && typeof item.toJSON === 'function') {
                return item.toJSON();
              } else if (item && typeof item.toObject === 'function') {
                return item.toObject();
              }
              return item;
            });
          } else if (typeof data.toJSON === 'function') {
            // Handle single Mongoose document
            cacheData = data.toJSON();
          } else if (typeof data.toObject === 'function') {
            // Handle single Mongoose document (alternative method)
            cacheData = data.toObject();
          }
        }

        // Cache the serializable data
        cache.set(key, cacheData, cacheDuration);
        console.log(`Cached response for: ${key} (TTL: ${cacheDuration}s)`);
      } catch (cacheError) {
        console.error(`Failed to cache response for ${key}:`, cacheError.message);
        // Continue without caching if serialization fails
      }

      // Call original res.json with original data
      return originalJson.call(this, data);
    };

    next();
  };
};

// Clear cache for specific patterns
const clearCache = (pattern) => {
  try {
    const keys = cache.keys();
    const keysToDelete = keys.filter(key => key.includes(pattern));

    keysToDelete.forEach(key => {
      cache.del(key);
      console.log(`Cleared cache for: ${key}`);
    });
  } catch (error) {
    console.error('Error clearing cache:', error.message);
  }
};

// Clear all pillar page related cache
const clearPillarPageCache = () => {
  clearCache('/pillar-pages');
  console.log('Cleared all pillar page cache');
};

// Clear only cluster page caches (when blog posts change)
const clearClusterPageCache = () => {
  clearCache('/cluster-pages');
  console.log('Cleared cluster page cache');
};

module.exports = {
  cacheMiddleware,
  clearCache,
  clearPillarPageCache,
  clearClusterPageCache
};
