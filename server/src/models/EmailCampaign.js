const mongoose = require('mongoose');

const emailCampaignSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  subject: {
    type: String,
    required: true,
    trim: true
  },
  textContent: {
    type: String,
    required: true
  },
  htmlContent: {
    type: String,
    required: true
  },
  // Campaign settings
  fromName: {
    type: String,
    required: true,
    default: 'AI-Pomo Team'
  },
  fromEmail: {
    type: String,
    required: true,
    default: '<EMAIL>'
  },
  replyToEmail: {
    type: String,
    default: '<EMAIL>'
  },
  // Targeting
  emailLists: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'EmailList',
    required: true
  }],
  tags: [{
    type: String,
    trim: true
  }],
  excludeTags: [{
    type: String,
    trim: true
  }],
  // Scheduling
  status: {
    type: String,
    enum: ['draft', 'scheduled', 'sending', 'sent', 'paused', 'cancelled'],
    default: 'draft'
  },
  scheduledAt: {
    type: Date
  },
  sentAt: {
    type: Date
  },
  // Batch sending settings
  batchSize: {
    type: Number,
    default: 100,
    min: 1,
    max: 1000
  },
  delayBetweenBatches: {
    type: Number,
    default: 60000, // 1 minute in milliseconds
    min: 10000 // minimum 10 seconds
  },
  // Statistics
  totalRecipients: {
    type: Number,
    default: 0
  },
  emailsSent: {
    type: Number,
    default: 0
  },
  emailsDelivered: {
    type: Number,
    default: 0
  },
  emailsBounced: {
    type: Number,
    default: 0
  },
  emailsOpened: {
    type: Number,
    default: 0
  },
  emailsClicked: {
    type: Number,
    default: 0
  },
  emailsUnsubscribed: {
    type: Number,
    default: 0
  },
  emailsComplained: {
    type: Number,
    default: 0
  },
  // Progress tracking
  sendingProgress: {
    current: { type: Number, default: 0 },
    total: { type: Number, default: 0 },
    percentage: { type: Number, default: 0 },
    currentBatch: { type: Number, default: 0 },
    totalBatches: { type: Number, default: 0 },
    lastSentAt: { type: Date },
    nextBatchAt: { type: Date }
  },
  // Error tracking
  errors: [{
    email: String,
    error: String,
    timestamp: { type: Date, default: Date.now }
  }],
  // System fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update timestamps
emailCampaignSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  
  // Update progress percentage
  if (this.sendingProgress.total > 0) {
    this.sendingProgress.percentage = Math.round(
      (this.sendingProgress.current / this.sendingProgress.total) * 100
    );
  }
  
  next();
});

// Indexes
emailCampaignSchema.index({ name: 1 });
emailCampaignSchema.index({ status: 1 });
emailCampaignSchema.index({ createdBy: 1 });
emailCampaignSchema.index({ emailLists: 1 });
emailCampaignSchema.index({ scheduledAt: 1 });
emailCampaignSchema.index({ createdAt: -1 });

// Virtual for open rate
emailCampaignSchema.virtual('openRate').get(function() {
  if (this.emailsDelivered === 0) return 0;
  return Math.round((this.emailsOpened / this.emailsDelivered) * 100 * 100) / 100;
});

// Virtual for click rate
emailCampaignSchema.virtual('clickRate').get(function() {
  if (this.emailsDelivered === 0) return 0;
  return Math.round((this.emailsClicked / this.emailsDelivered) * 100 * 100) / 100;
});

// Virtual for unsubscribe rate
emailCampaignSchema.virtual('unsubscribeRate').get(function() {
  if (this.emailsDelivered === 0) return 0;
  return Math.round((this.emailsUnsubscribed / this.emailsDelivered) * 100 * 100) / 100;
});

module.exports = mongoose.model('EmailCampaign', emailCampaignSchema);