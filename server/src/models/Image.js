/**
 * Image Model
 * 
 * Stores image information with tags for blog posts
 */

const mongoose = require('mongoose');

const imageSchema = new mongoose.Schema({
  // Pixabay image ID
  pixabayId: {
    type: Number,
    required: true,
    unique: true,
    index: true
  },
  
  // Image title/description
  title: {
    type: String,
    required: true
  },
  
  // Pixabay user who uploaded the image
  user: {
    type: String,
    required: true
  },
  
  // Image statistics
  views: {
    type: Number,
    default: 0
  },
  
  downloads: {
    type: Number,
    default: 0
  },
  
  // Original Pixabay page URL
  pageUrl: {
    type: String,
    required: true
  },
  
  // Image URLs in different sizes
  urls: {
    small: {
      type: String,
      required: true
    },
    medium: {
      type: String,
      required: true
    },
    large: {
      type: String,
      required: true
    },
    full: {
      type: String,
      required: true
    },
    vector: {
      type: String,
      default: ''
    }
  },
  
  // Tags associated with this image
  tags: [{
    type: String,
    required: true,
    index: true
  }],
  
  // Source platform
  source: {
    type: String,
    default: 'Pixabay'
  },
  
  // Usage tracking for blog posts
  usageCount: {
    type: Number,
    default: 0
  },
  
  // Last used timestamp
  lastUsed: {
    type: Date,
    default: null
  },
  
  // Import metadata
  importedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Indexes for efficient querying
imageSchema.index({ tags: 1 });
imageSchema.index({ pixabayId: 1 });
imageSchema.index({ views: -1 });
imageSchema.index({ downloads: -1 });
imageSchema.index({ usageCount: -1 });

// Methods
imageSchema.methods.recordUsage = function() {
  this.usageCount += 1;
  this.lastUsed = new Date();
  return this.save();
};

// Static methods
imageSchema.statics.getByTag = function(tag, limit = 20) {
  return this.find({ tags: { $in: [tag] } })
    .sort({ downloads: -1, views: -1 })
    .limit(limit);
};

imageSchema.statics.getPopularImages = function(limit = 50) {
  return this.find({})
    .sort({ downloads: -1, views: -1 })
    .limit(limit);
};

imageSchema.statics.getRecentlyUsed = function(limit = 20) {
  return this.find({ lastUsed: { $ne: null } })
    .sort({ lastUsed: -1 })
    .limit(limit);
};

const Image = mongoose.model('Image', imageSchema);

module.exports = Image;