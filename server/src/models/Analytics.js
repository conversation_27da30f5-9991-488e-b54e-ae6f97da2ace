const mongoose = require('mongoose');

const AnalyticsSchema = new mongoose.Schema(
  {
    // Visitor identification
    sessionId: {
      type: String,
      required: true,
      index: true,
    },
    visitorId: {
      type: String,
      required: true,
      index: true,
    },
    
    // Request details
    path: {
      type: String,
      required: true,
      index: true,
    },
    method: {
      type: String,
      required: true,
      default: 'GET',
    },
    referrer: {
      type: String,
      default: '',
    },
    
    // User agent and device info
    userAgent: {
      type: String,
      required: true,
    },
    browser: {
      name: String,
      version: String,
    },
    os: {
      name: String,
      version: String,
    },
    device: {
      type: String,
      enum: ['desktop', 'mobile', 'tablet', 'unknown'],
      default: 'unknown',
    },
    
    // Location data
    ip: {
      type: String,
      required: true,
    },
    country: {
      type: String,
      default: 'Unknown',
    },
    city: {
      type: String,
      default: 'Unknown',
    },
    
    // Timing data
    timestamp: {
      type: Date,
      default: Date.now,
    },
    responseTime: {
      type: Number, // in milliseconds
    },
    
    // Session data
    isNewSession: {
      type: <PERSON>olean,
      default: false,
    },
    sessionDuration: {
      type: Number, // in seconds
      default: 0,
    },
    pageViews: {
      type: Number,
      default: 1,
    },
    
    // User status
    isAuthenticated: {
      type: Boolean,
      default: false,
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      default: null,
    },
    
    // Bot detection flags
    isBot: {
      type: Boolean,
      default: false,
    },
    botType: {
      type: String,
      enum: ['search_engine', 'social_media', 'seo_tool', 'ai_bot', 'monitoring', 'unknown', null],
      default: null,
    },
    
    // Additional metadata
    language: {
      type: String,
      default: 'en',
    },
    timezone: {
      type: String,
      default: 'UTC',
    },
    screenResolution: {
      width: Number,
      height: Number,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for better query performance
// Note: Using TTL index for timestamp which also serves as primary timestamp index
AnalyticsSchema.index({ path: 1, timestamp: -1 });
AnalyticsSchema.index({ visitorId: 1, timestamp: -1 });
AnalyticsSchema.index({ isBot: 1, timestamp: -1 });
AnalyticsSchema.index({ country: 1, timestamp: -1 });

// TTL index to automatically delete old records after 1 year (also serves as timestamp index)
AnalyticsSchema.index({ timestamp: 1 }, { expireAfterSeconds: 365 * 24 * 60 * 60 });

module.exports = mongoose.model('Analytics', AnalyticsSchema);
