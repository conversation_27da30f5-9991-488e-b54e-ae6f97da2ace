const mongoose = require('mongoose');

// Define the schema without any indexes on the user field
const SubscriptionSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      // No index defined here
    },
    plan: {
      type: String,
      enum: ['free', 'monthly', 'yearly', 'lifetime'],
      default: 'free',
      required: true,
    },
    paymentMethod: {
      type: String,
      enum: ['paddle'],
      default: 'paddle',
      required: function() {
        return this.plan !== 'free';
      },
    },
    paymentAmount: {
      type: Number,
      required: function() {
        return this.plan !== 'free';
      },
    },
    paymentStatus: {
      type: String,
      enum: ['pending', 'confirmed', 'rejected', 'trial', 'failed', 'cancelled'],
      default: 'pending',
      required: function() {
        return this.plan !== 'free';
      },
    },
    paymentDate: {
      type: Date,
    },
    confirmationDate: {
      type: Date,
    },
    expiryDate: {
      type: Date,
    },
    transactionId: {
      type: String,
    },
    // Paddle specific fields
    paddleSubscriptionId: {
      type: String,
      unique: true,
      sparse: true, // Allows null values but ensures uniqueness when present
    },
    subscriptionStatus: {
      type: String,
      enum: ['trialing', 'active', 'cancelled', 'expired', 'past_due'],
      default: 'trialing',
    },
    trialEndsAt: {
      type: Date,
    },
    // Billing cycle information
    nextBillingDate: {
      type: Date,
    },
    currentPeriodStart: {
      type: Date,
    },
    currentPeriodEnd: {
      type: Date,
    },
    billingInterval: {
      type: String,
      enum: ['month', 'year'],
      default: 'month',
    },
    // Cancellation information
    cancelledAt: {
      type: Date,
    },
    cancelReason: {
      type: String,
    },
    // Paddle webhook data for billing cycles
    lastPaddleEventId: {
      type: String,
    },
    lastBillingAttempt: {
      type: Date,
    },
    billingRetryCount: {
      type: Number,
      default: 0,
    },
    paddleTransactionId: {
      type: String,
    },
    paddleCustomerId: {
      type: String,
    },
    paddleProductId: {
      type: String,
    },
    paddlePriceId: {
      type: String,
    },
    payerEmail: {
      type: String,
      required: function() {
        return this.plan !== 'free';
      },
    },
    payerName: {
      type: String,
      required: function() {
        return this.plan !== 'free';
      },
    },
    additionalInfo: {
      type: String,
    },
    active: {
      type: Boolean,
      default: function() {
        return this.plan === 'free';
      },
    },
    adminNotes: {
      type: String,
    },
    rejectionReason: {
      type: String,
    },
  },
  {
    timestamps: true,
  }
);

module.exports = mongoose.model('Subscription', SubscriptionSchema);
