const mongoose = require('mongoose');

const BlogImageSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
      trim: true,
    },
    url: {
      type: String,
      required: true,
      trim: true,
    },
    thumbnailUrl: {
      type: String,
      trim: true,
    },
    category: {
      type: String,
      required: true,
      trim: true,
      enum: [
        'productivity',
        'pomodoro',
        'time-management',
        'focus',
        'workspace',
        'technology',
        'ai',
        'planning',
        'goals',
        'success',
        'teamwork',
        'remote-work',
        'study',
        'business',
        'creativity',
        'wellness',
        'general'
      ]
    },
    tags: [{
      type: String,
      trim: true,
    }],
    description: {
      type: String,
      trim: true,
    },
    source: {
      type: String,
      trim: true,
      default: 'unsplash'
    },
    photographer: {
      type: String,
      trim: true,
    },
    photographerUrl: {
      type: String,
      trim: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    usageCount: {
      type: Number,
      default: 0,
    },
    lastUsed: {
      type: Date,
    },
  },
  {
    timestamps: true,
  }
);

// Index for efficient searching
BlogImageSchema.index({ category: 1, isActive: 1 });
BlogImageSchema.index({ tags: 1, isActive: 1 });
BlogImageSchema.index({ usageCount: -1 });

// Method to increment usage count
BlogImageSchema.methods.incrementUsage = function() {
  this.usageCount += 1;
  this.lastUsed = new Date();
  return this.save();
};

module.exports = mongoose.model('BlogImage', BlogImageSchema);
