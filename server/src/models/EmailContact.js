const mongoose = require('mongoose');

const emailContactSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    trim: true,
    lowercase: true,
    validate: {
      validator: function(email) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
      },
      message: 'Invalid email format'
    }
  },
  firstName: {
    type: String,
    trim: true
  },
  lastName: {
    type: String,
    trim: true
  },
  fullName: {
    type: String,
    trim: true
  },
  company: {
    type: String,
    trim: true
  },
  jobTitle: {
    type: String,
    trim: true
  },
  phone: {
    type: String,
    trim: true
  },
  website: {
    type: String,
    trim: true
  },
  source: {
    type: String,
    enum: ['manual', 'import', 'signup', 'api', 'registration'],
    default: 'manual'
  },
  status: {
    type: String,
    enum: ['active', 'unsubscribed', 'bounced', 'complained'],
    default: 'active'
  },
  emailLists: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'EmailList'
  }],
  tags: [{
    type: String,
    trim: true
  }],
  customFields: {
    type: Map,
    of: String
  },
  // Subscription tracking
  subscribedAt: {
    type: Date,
    default: Date.now
  },
  unsubscribedAt: {
    type: Date
  },
  unsubscribeReason: {
    type: String,
    trim: true
  },
  // Email tracking
  emailsSent: {
    type: Number,
    default: 0
  },
  emailsOpened: {
    type: Number,
    default: 0
  },
  emailsClicked: {
    type: Number,
    default: 0
  },
  lastEmailSent: {
    type: Date
  },
  lastEmailOpened: {
    type: Date
  },
  lastEmailClicked: {
    type: Date
  },
  // System fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Compound index for unique email per list
emailContactSchema.index({ email: 1, emailLists: 1 });

// Other indexes
emailContactSchema.index({ email: 1 });
emailContactSchema.index({ status: 1 });
emailContactSchema.index({ emailLists: 1 });
emailContactSchema.index({ tags: 1 });
emailContactSchema.index({ createdAt: -1 });

// Virtual for getting full name
emailContactSchema.virtual('displayName').get(function() {
  if (this.fullName) return this.fullName;
  if (this.firstName && this.lastName) return `${this.firstName} ${this.lastName}`;
  if (this.firstName) return this.firstName;
  return this.email.split('@')[0];
});

// Update timestamps
emailContactSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  
  // Auto-generate full name if not provided
  if (!this.fullName && (this.firstName || this.lastName)) {
    this.fullName = `${this.firstName || ''} ${this.lastName || ''}`.trim();
  }
  
  next();
});

module.exports = mongoose.model('EmailContact', emailContactSchema);