const mongoose = require('mongoose');

// Payment record for each billing cycle
const PaymentSchema = new mongoose.Schema(
  {
    subscription: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Subscription',
      required: true,
    },
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    // Paddle payment information
    paddleTransactionId: {
      type: String,
      unique: true,
      sparse: true,
    },
    paddleSubscriptionId: {
      type: String,
      required: true,
    },
    paddleInvoiceId: {
      type: String,
    },
    paddleReceiptUrl: {
      type: String,
    },
    // Payment details
    amount: {
      type: Number,
      required: true,
    },
    currency: {
      type: String,
      default: 'USD',
    },
    // Payment status and timing
    status: {
      type: String,
      enum: ['pending', 'completed', 'failed', 'cancelled', 'refunded'],
      default: 'pending',
    },
    paymentMethod: {
      type: String,
      default: 'paddle',
    },
    // Billing period this payment covers
    periodStart: {
      type: Date,
      required: true,
    },
    periodEnd: {
      type: Date,
      required: true,
    },
    // Payment attempt information
    attemptedAt: {
      type: Date,
      default: Date.now,
    },
    completedAt: {
      type: Date,
    },
    failedAt: {
      type: Date,
    },
    // Failure information
    failureReason: {
      type: String,
    },
    retryCount: {
      type: Number,
      default: 0,
    },
    nextRetryAt: {
      type: Date,
    },
    // Payment type
    type: {
      type: String,
      enum: ['trial_conversion', 'recurring', 'one_time', 'refund'],
      default: 'recurring',
    },
    // Tax and fees
    taxAmount: {
      type: Number,
      default: 0,
    },
    netAmount: {
      type: Number, // Amount after fees
    },
    // Paddle raw data for debugging
    paddleEventData: {
      type: mongoose.Schema.Types.Mixed,
    },
    // Admin notes
    adminNotes: {
      type: String,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for efficient queries
PaymentSchema.index({ subscription: 1, attemptedAt: -1 });
PaymentSchema.index({ user: 1, attemptedAt: -1 });
PaymentSchema.index({ status: 1, attemptedAt: -1 });
PaymentSchema.index({ paddleTransactionId: 1 });
PaymentSchema.index({ type: 1, status: 1 });

module.exports = mongoose.model('Payment', PaymentSchema);