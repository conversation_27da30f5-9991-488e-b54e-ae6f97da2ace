const mongoose = require('mongoose');

const emailTemplateSchema = new mongoose.Schema({
  universityId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  universityName: {
    type: String,
    required: true
  },
  subject: {
    type: String,
    required: true
  },
  textContent: {
    type: String,
    required: true
  },
  htmlContent: {
    type: String,
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
});

// Update the updatedAt field before saving
emailTemplateSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Create indexes for better query performance
emailTemplateSchema.index({ universityId: 1 });
emailTemplateSchema.index({ isActive: 1 });
emailTemplateSchema.index({ updatedAt: -1 });

module.exports = mongoose.model('EmailTemplate', emailTemplateSchema);