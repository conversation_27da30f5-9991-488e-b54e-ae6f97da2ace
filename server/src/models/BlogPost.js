const mongoose = require('mongoose');

const BlogPostSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
      trim: true,
    },
    slug: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      lowercase: true,
    },
    content: {
      type: String,
      required: true,
    },
    excerpt: {
      type: String,
      required: true,
    },
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    coverImage: {
      type: String,
      required: true,
    },
    tags: [{
      type: String,
      trim: true,
    }],
    category: {
      type: String,
      required: true,
      trim: true,
    },
    readTime: {
      type: Number, // in minutes
      required: true,
    },
    published: {
      type: Boolean,
      default: false,
    },
    featured: {
      type: Boolean,
      default: false,
    },
    seoTitle: {
      type: String,
      trim: true,
    },
    seoDescription: {
      type: String,
      trim: true,
    },
    seoKeywords: {
      type: String,
      trim: true,
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes for better query performance
BlogPostSchema.index({ published: 1, createdAt: -1 });
// slug index already created by unique: true
BlogPostSchema.index({ category: 1, published: 1 });
BlogPostSchema.index({ tags: 1, published: 1 });
BlogPostSchema.index({ featured: 1, published: 1 });
BlogPostSchema.index({
  title: 'text',
  content: 'text',
  excerpt: 'text',
  seoKeywords: 'text'
});

// Create slug from title before saving
BlogPostSchema.pre('save', function(next) {
  if (!this.isModified('title')) {
    return next();
  }

  this.slug = this.title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');

  next();
});

module.exports = mongoose.model('BlogPost', BlogPostSchema);
