const mongoose = require('mongoose');

const PillarPageSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
      trim: true,
    },
    slug: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      lowercase: true,
    },
    content: {
      type: String,
      required: true,
    },
    excerpt: {
      type: String,
      required: true,
    },
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    coverImage: {
      type: String,
      required: true,
    },
    category: {
      type: String,
      required: true,
      enum: ['pomodoro-technique', 'time-management', 'adhd-productivity', 'ai-productivity'],
      trim: true,
    },
    readTime: {
      type: Number, // in minutes
      required: true,
    },
    published: {
      type: Boolean,
      default: false,
    },
    featured: {
      type: Boolean,
      default: false,
    },
    // SEO fields
    seoTitle: {
      type: String,
      trim: true,
    },
    seoDescription: {
      type: String,
      trim: true,
    },
    seoKeywords: {
      type: String,
      trim: true,
    },
    // Cluster page associations
    clusterPages: [{
      blogPost: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'BlogPost',
        required: true,
      },
      displayOrder: {
        type: Number,
        default: 0,
      },
      customTitle: {
        type: String,
        trim: true,
      },
      customDescription: {
        type: String,
        trim: true,
      }
    }],
    // Navigation order for the resources menu
    menuOrder: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes for better query performance
PillarPageSchema.index({ published: 1, menuOrder: 1 });
// slug index already created by unique: true
PillarPageSchema.index({ category: 1, published: 1 });
PillarPageSchema.index({ featured: 1, published: 1 });
PillarPageSchema.index({
  title: 'text',
  content: 'text',
  excerpt: 'text',
  seoKeywords: 'text'
});

// Create slug from title before saving
PillarPageSchema.pre('save', function(next) {
  if (!this.isModified('title')) {
    return next();
  }

  this.slug = this.title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');

  next();
});

module.exports = mongoose.model('PillarPage', PillarPageSchema);
