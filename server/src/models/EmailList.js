const mongoose = require('mongoose');

const emailListSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  tags: [{
    type: String,
    trim: true
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  // Statistics
  totalContacts: {
    type: Number,
    default: 0
  },
  activeContacts: {
    type: Number,
    default: 0
  },
  unsubscribedContacts: {
    type: Number,
    default: 0
  }
});

// Update timestamps
emailListSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Indexes for better performance
emailListSchema.index({ name: 1 });
emailListSchema.index({ createdBy: 1 });
emailListSchema.index({ isActive: 1 });
emailListSchema.index({ tags: 1 });

module.exports = mongoose.model('EmailList', emailListSchema);