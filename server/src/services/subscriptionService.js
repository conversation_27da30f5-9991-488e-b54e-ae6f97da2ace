/**
 * Centralized Subscription Management Service
 * 
 * This service ensures ALL subscription-related fields are updated consistently
 * across User model and Subscription model to prevent data sync issues.
 */

const User = require('../models/User');
const Subscription = require('../models/Subscription');

/**
 * Centralized function to update user subscription status
 * This ensures all subscription fields are kept in sync
 */
exports.updateUserSubscriptionStatus = async (userId, subscriptionData) => {
  const {
    plan = 'free',
    paymentStatus = 'pending',
    subscriptionStatus = 'free',
    active = false,
    expiryDate = null,
    paddleSubscriptionId = null,
    subscriptionId = null
  } = subscriptionData;

  console.log('Updating subscription status for user:', userId, 'with data:', subscriptionData);

  try {
    // Calculate derived values - include trial and past_due states
    const isActivePlan = active || 
                        paymentStatus === 'confirmed' || 
                        paymentStatus === 'trial' ||
                        subscriptionStatus === 'active' ||
                        subscriptionStatus === 'trialing' ||
                        subscriptionStatus === 'past_due';
    const userSubscriptionStatus = isActivePlan ? 'premium' : 'free';
    const maxProjects = isActivePlan ? 999 : 2;

    // Update User model with ALL subscription fields in sync
    const userUpdate = {
      subscriptionStatus: userSubscriptionStatus,
      maxProjects: maxProjects,
      subscriptionExpiresAt: expiryDate,
      
      // Update nested subscription object
      'subscription.plan': plan,
      'subscription.active': isActivePlan,
      'subscription.expiryDate': expiryDate
    };

    if (subscriptionId) {
      userUpdate.subscriptionId = subscriptionId;
    }

    const updatedUser = await User.findByIdAndUpdate(
      userId, 
      userUpdate,
      { new: true }
    );

    if (!updatedUser) {
      throw new Error(`User not found: ${userId}`);
    }

    console.log('User subscription status updated successfully:', {
      userId: updatedUser._id,
      subscriptionStatus: updatedUser.subscriptionStatus,
      maxProjects: updatedUser.maxProjects,
      subscriptionPlan: updatedUser.subscription.plan,
      subscriptionActive: updatedUser.subscription.active
    });

    return updatedUser;

  } catch (error) {
    console.error('Error updating user subscription status:', error);
    throw error;
  }
};

/**
 * Get user's current subscription with full synchronization check
 */
exports.getCurrentUserSubscription = async (userId) => {
  try {
    const user = await User.findById(userId);
    if (!user) {
      throw new Error(`User not found: ${userId}`);
    }

    // Find the most relevant active subscription with priority order
    // 1. First try to find truly active subscriptions (not cancelled)
    let subscription = await Subscription.findOne({
      user: userId,
      $and: [
        {
          $or: [
            { subscriptionStatus: 'active' },
            { subscriptionStatus: 'trialing' },
            { subscriptionStatus: 'past_due' }
          ]
        },
        {
          subscriptionStatus: { $ne: 'cancelled' }
        }
      ]
    }).sort({ createdAt: -1 });

    // 2. If no active non-cancelled subscription, look for other active states
    // BUT exclude cancelled subscriptions to prevent admin cancellations from being overridden
    if (!subscription) {
      subscription = await Subscription.findOne({
        user: userId,
        $and: [
          {
            $or: [
              { active: true },
              { paymentStatus: 'confirmed' },
              { paymentStatus: 'trial' }
            ]
          },
          {
            // Exclude cancelled subscriptions - these should not be considered active
            $and: [
              { subscriptionStatus: { $ne: 'cancelled' } },
              { paymentStatus: { $ne: 'cancelled' } }
            ]
          }
        ]
      }).sort({ createdAt: -1 });
    }

    // If no active subscription found, ensure user is set to free
    if (!subscription) {
      await this.updateUserSubscriptionStatus(userId, {
        plan: 'free',
        active: false,
        paymentStatus: 'pending',
        subscriptionStatus: 'free'
      });

      return {
        _id: null,
        plan: 'free',
        active: false,
        paymentMethod: null,
        subscriptionStatus: 'free',
        paymentStatus: 'pending',
        createdAt: new Date(),
        expiryDate: null,
        paddleSubscriptionId: null,
        trialEndsAt: null,
        nextBillingDate: null,
        currentPeriodStart: null,
        currentPeriodEnd: null
      };
    }

    // Check for data inconsistencies and fix them
    // BUT respect admin cancellations - do not override cancelled subscriptions
    const isSubscriptionCancelled = subscription.subscriptionStatus === 'cancelled' || 
                                   subscription.paymentStatus === 'cancelled' ||
                                   subscription.cancelReason === 'admin_cancelled';

    if (!isSubscriptionCancelled) {
      const userShouldBePremium = subscription.active || 
                                 subscription.paymentStatus === 'confirmed' || 
                                 subscription.subscriptionStatus === 'active' ||
                                 subscription.subscriptionStatus === 'trialing';
      
      const userIsPremium = user.subscriptionStatus === 'premium';

      if (userShouldBePremium !== userIsPremium) {
        console.warn('Subscription data inconsistency detected for user:', userId);
        console.warn('Subscription says premium:', userShouldBePremium);
        console.warn('User status says premium:', userIsPremium);
        console.warn('Subscription not cancelled, fixing inconsistency...');
        
        // Fix the inconsistency only if subscription is not cancelled
        await this.updateUserSubscriptionStatus(userId, {
          plan: subscription.plan,
          active: subscription.active,
          paymentStatus: subscription.paymentStatus,
          subscriptionStatus: subscription.subscriptionStatus,
          expiryDate: subscription.expiryDate,
          subscriptionId: subscription._id
        });
      }
    } else {
      console.log('Subscription is cancelled (admin or user cancelled), respecting cancellation status for user:', userId);
      
      // For cancelled subscriptions, ensure user is set to free if not already
      if (user.subscriptionStatus !== 'free') {
        console.log('User still marked as premium despite cancelled subscription, fixing to free status');
        await this.updateUserSubscriptionStatus(userId, {
          plan: 'free',
          active: false,
          paymentStatus: 'cancelled',
          subscriptionStatus: 'free',
          expiryDate: null,
          subscriptionId: subscription._id
        });
      }
    }

    // Return a consistent object structure
    return {
      _id: subscription._id,
      plan: subscription.plan,
      active: subscription.active,
      paymentMethod: subscription.paymentMethod,
      subscriptionStatus: subscription.subscriptionStatus,
      paymentStatus: subscription.paymentStatus,
      expiryDate: subscription.expiryDate,
      createdAt: subscription.createdAt,
      paddleSubscriptionId: subscription.paddleSubscriptionId,
      trialEndsAt: subscription.trialEndsAt,
      nextBillingDate: subscription.nextBillingDate,
      currentPeriodStart: subscription.currentPeriodStart,
      currentPeriodEnd: subscription.currentPeriodEnd
    };

  } catch (error) {
    console.error('Error getting current user subscription:', error);
    throw error;
  }
};

/**
 * Cancel user subscription with full synchronization
 */
exports.cancelUserSubscription = async (userId) => {
  try {
    console.log('Cancelling subscription for user:', userId);

    // Find current active subscription
    const currentSubscription = await Subscription.findOne({
      user: userId,
      $or: [
        { paymentStatus: 'confirmed' },
        { paymentStatus: 'trial' },
        { subscriptionStatus: 'trialing' },
        { subscriptionStatus: 'active' }
      ]
    }).sort({ createdAt: -1 });

    if (!currentSubscription) {
      throw new Error('No active subscription found to cancel');
    }

    // Mark subscription as cancelled but don't immediately downgrade
    currentSubscription.cancelledAt = new Date();
    currentSubscription.cancelReason = 'user_requested';
    
    // Determine if user is still in trial period
    const now = new Date();
    const inTrialPeriod = currentSubscription.trialEndsAt && now < new Date(currentSubscription.trialEndsAt);
    
    if (inTrialPeriod) {
      // If in trial, cancel immediately without charge
      currentSubscription.paymentStatus = 'cancelled';
      currentSubscription.subscriptionStatus = 'cancelled';
      currentSubscription.active = false;
      await currentSubscription.save();
      
      // Downgrade user immediately for trial cancellations
      await this.updateUserSubscriptionStatus(userId, {
        plan: 'free',
        active: false,
        paymentStatus: 'cancelled',
        subscriptionStatus: 'free',
        expiryDate: null,
        subscriptionId: currentSubscription._id
      });
    } else {
      // For paid subscriptions, handle different plan types
      if (currentSubscription.plan === 'lifetime') {
        // Lifetime plans: User has already paid, they keep access
        // Mark as cancelled but keep active until manual admin intervention if needed
        currentSubscription.subscriptionStatus = 'cancelled';
        currentSubscription.cancelReason = 'user_requested_lifetime';
        await currentSubscription.save();
        
        // For lifetime, user keeps access but subscription is marked as cancelled
        // This prevents future billing but preserves paid access
        await this.updateUserSubscriptionStatus(userId, {
          plan: currentSubscription.plan,
          active: true, // Keep lifetime access - they paid for it
          paymentStatus: 'confirmed', // Keep confirmed - they paid
          subscriptionStatus: 'cancelled', // But mark as cancelled
          expiryDate: currentSubscription.currentPeriodEnd, // Far future date
          subscriptionId: currentSubscription._id
        });
        
        console.log('Lifetime subscription marked as cancelled but access retained for user:', userId);
      } else {
        // For recurring plans (monthly/yearly), mark for cancellation at period end
        currentSubscription.subscriptionStatus = 'cancelled'; // Mark as cancelled
        // Keep paymentStatus and active status until period ends
        await currentSubscription.save();
        
        // Keep user access until current period ends
        await this.updateUserSubscriptionStatus(userId, {
          plan: currentSubscription.plan,
          active: true, // Keep access until period end
          paymentStatus: 'cancelled', // But mark as cancelled
          subscriptionStatus: 'cancelled',
          expiryDate: currentSubscription.currentPeriodEnd,
          subscriptionId: currentSubscription._id
        });
      }
    }

    console.log('Subscription cancelled successfully for user:', userId);
    return currentSubscription;

  } catch (error) {
    console.error('Error cancelling subscription:', error);
    throw error;
  }
};

/**
 * Validate subscription data consistency for a user
 */
exports.validateSubscriptionConsistency = async (userId) => {
  try {
    const user = await User.findById(userId);
    const subscription = await Subscription.findOne({ user: userId }).sort({ createdAt: -1 });

    if (!user) {
      return { valid: false, errors: ['User not found'] };
    }

    const errors = [];

    // Check if subscription status matches
    if (subscription) {
      const subscriptionSaysPremium = subscription.active || 
                                     subscription.paymentStatus === 'confirmed' || 
                                     subscription.subscriptionStatus === 'active';
      const userSaysPremium = user.subscriptionStatus === 'premium';

      if (subscriptionSaysPremium !== userSaysPremium) {
        errors.push(`Subscription status mismatch: subscription=${subscriptionSaysPremium}, user=${userSaysPremium}`);
      }

      // Check plan consistency
      if (subscription.plan !== user.subscription?.plan) {
        errors.push(`Plan mismatch: subscription=${subscription.plan}, user=${user.subscription?.plan}`);
      }

      // Check maxProjects consistency
      const expectedMaxProjects = subscriptionSaysPremium ? 999 : 2;
      if (user.maxProjects !== expectedMaxProjects) {
        errors.push(`MaxProjects inconsistent: expected=${expectedMaxProjects}, actual=${user.maxProjects}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      user: {
        subscriptionStatus: user.subscriptionStatus,
        maxProjects: user.maxProjects,
        subscriptionPlan: user.subscription?.plan,
        subscriptionActive: user.subscription?.active
      },
      subscription: subscription ? {
        plan: subscription.plan,
        active: subscription.active,
        paymentStatus: subscription.paymentStatus,
        subscriptionStatus: subscription.subscriptionStatus
      } : null
    };

  } catch (error) {
    console.error('Error validating subscription consistency:', error);
    return { valid: false, errors: [error.message] };
  }
};

module.exports = exports;