/**
 * Bot Detection Utility
 * Detects and filters out various types of bots, crawlers, and automated tools
 */

// Known bot user agents (case-insensitive patterns)
const BOT_PATTERNS = [
  // Search engine bots
  /googlebot/i,
  /bingbot/i,
  /slurp/i, // Yahoo
  /duckduckbot/i,
  /baiduspider/i,
  /yandexbot/i,
  /facebookexternalhit/i,
  /twitterbot/i,
  /linkedinbot/i,
  /whatsapp/i,
  /telegrambot/i,

  // SEO and monitoring tools
  /ahrefsbot/i,
  /semrushbot/i,
  /mj12bot/i,
  /dotbot/i,
  /rogerbot/i, // Moz
  /exabot/i,
  /sogou/i,
  /360spider/i,
  /spider/i,
  /crawler/i,
  /scraper/i,
  /bot/i,

  // AI and ML bots
  /chatgpt/i,
  /gpt/i,
  /openai/i,
  /claude/i,
  /anthropic/i,
  /bard/i,
  /gemini/i,
  /perplexity/i,
  /you\.com/i,
  /bing.*ai/i,

  // Monitoring and uptime services
  /pingdom/i,
  /uptimerobot/i,
  /statuscake/i,
  /site24x7/i,
  /newrelic/i,
  /datadog/i,
  /nagios/i,
  /zabbix/i,

  // Security scanners
  /nessus/i,
  /nikto/i,
  /nmap/i,
  /masscan/i,
  /nuclei/i,
  /sqlmap/i,

  // Content aggregators
  /flipboard/i,
  /pocket/i,
  /instapaper/i,
  /readability/i,

  // Headless browsers and automation
  /headlesschrome/i,
  /phantomjs/i,
  /selenium/i,
  /webdriver/i,
  /puppeteer/i,
  /playwright/i,
  /cypress/i,

  // API clients and tools
  /curl/i,
  /wget/i,
  /httpie/i,
  /postman/i,
  /insomnia/i,
  /python-requests/i,
  /node-fetch/i,
  /axios/i,

  // Other known bots
  /archive\.org/i,
  /wayback/i,
  /ia_archiver/i,
  /ccbot/i,
  /gptbot/i,
  /claudebot/i,
  /anthropicbot/i,
];

// Suspicious patterns that might indicate bots
const SUSPICIOUS_PATTERNS = [
  /^$/i, // Empty user agent
  /unknown/i,
  /^mozilla\/4\.0$/i, // Very old browser
  /^mozilla\/5\.0$/i, // Incomplete user agent
  /test/i,
  /check/i,
  /monitor/i,
  /scan/i,
  /probe/i,
];

// Known bot IP ranges (you can expand this)
const BOT_IP_PATTERNS = [
  /^66\.249\./, // Google
  /^40\.77\./, // Bing
  /^157\.55\./, // Bing
  /^207\.46\./, // Bing
  /^69\.58\./, // Yahoo
  /^72\.30\./, // Yahoo
];

/**
 * Detect if a request is from a bot
 * @param {Object} req - Express request object
 * @returns {Object} - Detection result with isBot flag and bot type
 */
function detectBot(req) {
  const userAgent = req.get('User-Agent') || '';
  const ip = req.ip || req.connection.remoteAddress || '';

  // Check for empty or missing user agent
  if (!userAgent || userAgent.trim() === '') {
    return {
      isBot: true,
      botType: 'unknown',
      reason: 'Empty user agent'
    };
  }

  // Check against known bot patterns
  for (const pattern of BOT_PATTERNS) {
    if (pattern.test(userAgent)) {
      let botType = 'unknown';

      // Categorize bot type
      if (/google|bing|yahoo|duckduck|baidu|yandex/i.test(userAgent)) {
        botType = 'search_engine';
      } else if (/facebook|twitter|linkedin|whatsapp|telegram/i.test(userAgent)) {
        botType = 'social_media';
      } else if (/ahrefs|semrush|moz|mj12|dotbot/i.test(userAgent)) {
        botType = 'seo_tool';
      } else if (/chatgpt|gpt|openai|claude|anthropic|bard|gemini|perplexity/i.test(userAgent)) {
        botType = 'ai_bot';
      } else if (/pingdom|uptime|status|monitor|datadog|newrelic/i.test(userAgent)) {
        botType = 'monitoring';
      }

      return {
        isBot: true,
        botType,
        reason: `Matched bot pattern: ${pattern.source}`
      };
    }
  }

  // Check against suspicious patterns
  for (const pattern of SUSPICIOUS_PATTERNS) {
    if (pattern.test(userAgent)) {
      return {
        isBot: true,
        botType: 'unknown',
        reason: `Matched suspicious pattern: ${pattern.source}`
      };
    }
  }

  // Check IP patterns
  for (const pattern of BOT_IP_PATTERNS) {
    if (pattern.test(ip)) {
      return {
        isBot: true,
        botType: 'search_engine',
        reason: `Matched bot IP pattern: ${pattern.source}`
      };
    }
  }

  // Additional heuristics

  // Check for missing common headers that real browsers usually send
  const hasAccept = req.get('Accept');
  const hasAcceptLanguage = req.get('Accept-Language');
  const hasAcceptEncoding = req.get('Accept-Encoding');

  if (!hasAccept || !hasAcceptLanguage || !hasAcceptEncoding) {
    return {
      isBot: true,
      botType: 'unknown',
      reason: 'Missing common browser headers'
    };
  }

  // Check for very short user agent (likely automated)
  if (userAgent.length < 20) {
    return {
      isBot: true,
      botType: 'unknown',
      reason: 'User agent too short'
    };
  }

  // Check for requests without referer to non-entry pages (less aggressive)
  const path = req.path || '';
  const referer = req.get('Referer') || '';
  const isEntryPage = ['/', '/login', '/register', '/features', '/pricing', '/blog', '/about', '/contact'].includes(path);

  // Only flag as bot if it's a very suspicious pattern (accessing deep internal pages without referer)
  const isSuspiciousPath = path.includes('/admin') || path.includes('/api') || path.includes('/dashboard');

  if (isSuspiciousPath && !referer && !req.user) {
    // This might be a direct access to admin/api pages, suspicious for bots
    return {
      isBot: true,
      botType: 'unknown',
      reason: 'Direct access to admin/api page without referer'
    };
  }

  // If all checks pass, likely a human
  return {
    isBot: false,
    botType: null,
    reason: 'Passed all bot detection checks'
  };
}

/**
 * Parse user agent to extract browser and OS information
 * @param {string} userAgent - User agent string
 * @returns {Object} - Parsed browser and OS info
 */
function parseUserAgent(userAgent) {
  const result = {
    browser: { name: 'Unknown', version: 'Unknown' },
    os: { name: 'Unknown', version: 'Unknown' },
    device: 'unknown'
  };

  if (!userAgent) return result;

  // Browser detection
  if (/Chrome\/(\d+)/.test(userAgent)) {
    result.browser.name = 'Chrome';
    result.browser.version = RegExp.$1;
  } else if (/Firefox\/(\d+)/.test(userAgent)) {
    result.browser.name = 'Firefox';
    result.browser.version = RegExp.$1;
  } else if (/Safari\/(\d+)/.test(userAgent) && !/Chrome/.test(userAgent)) {
    result.browser.name = 'Safari';
    result.browser.version = RegExp.$1;
  } else if (/Edge\/(\d+)/.test(userAgent)) {
    result.browser.name = 'Edge';
    result.browser.version = RegExp.$1;
  }

  // OS detection
  if (/Windows NT (\d+\.\d+)/.test(userAgent)) {
    result.os.name = 'Windows';
    result.os.version = RegExp.$1;
  } else if (/Mac OS X (\d+[._]\d+)/.test(userAgent)) {
    result.os.name = 'macOS';
    result.os.version = RegExp.$1.replace('_', '.');
  } else if (/Linux/.test(userAgent)) {
    result.os.name = 'Linux';
  } else if (/Android (\d+)/.test(userAgent)) {
    result.os.name = 'Android';
    result.os.version = RegExp.$1;
  } else if (/iPhone OS (\d+[._]\d+)/.test(userAgent)) {
    result.os.name = 'iOS';
    result.os.version = RegExp.$1.replace('_', '.');
  }

  // Device detection
  if (/Mobile|Android|iPhone/.test(userAgent)) {
    result.device = 'mobile';
  } else if (/iPad|Tablet/.test(userAgent)) {
    result.device = 'tablet';
  } else {
    result.device = 'desktop';
  }

  return result;
}

module.exports = {
  detectBot,
  parseUserAgent,
  BOT_PATTERNS,
  SUSPICIOUS_PATTERNS
};
