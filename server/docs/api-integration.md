# API Integration Documentation

This document provides information on how to integrate with the AI Pomo Blog API for third-party automation tools like n8n.

## Authentication

All API requests require authentication using an API key. API keys can be created in the user interface by admin users.

### API Key Header

Include your API key in the `X-API-Key` header with all requests:

```
X-API-Key: your-api-key-here
```

## Blog Post API

### Create a Blog Post

Create a new blog post via the API.

**Endpoint:** `POST /blog/api`

**Required Permissions:** `blog:write`

**Request Body:**

```json
{
  "title": "Your Blog Post Title",
  "content": "The full content of your blog post in Markdown format.",
  "excerpt": "A short excerpt or summary of the blog post (optional).",
  "coverImage": "URL to the cover image (optional).",
  "tags": ["tag1", "tag2", "tag3"],
  "category": "category-slug-or-name",
  "readTime": 5,
  "published": true,
  "featured": false,
  "seoTitle": "SEO-optimized title (optional)",
  "seoDescription": "SEO meta description (optional)",
  "seoKeywords": "keyword1, keyword2, keyword3 (optional)"
}
```

**Required Fields:**
- `title`: The title of the blog post
- `content`: The full content of the blog post (Markdown format supported)

**Optional Fields:**
- `excerpt`: A short summary of the blog post (if not provided, one will be generated from the content)
- `coverImage`: URL to the cover image (if not provided, a default image will be used)
- `tags`: Array of tags for the blog post
- `category`: Category slug or name (if not found, will default to "uncategorized")
- `readTime`: Estimated reading time in minutes (if not provided, will be calculated based on content length)
- `published`: Whether the post should be published immediately (default: true)
- `featured`: Whether the post should be featured (default: false)
- `seoTitle`: SEO-optimized title (defaults to the post title)
- `seoDescription`: SEO meta description (defaults to the excerpt)
- `seoKeywords`: SEO keywords (defaults to tags joined by commas)

**Response:**

```json
{
  "success": true,
  "message": "Blog post created successfully",
  "post": {
    "id": "60d21b4667d0d8992e610c85",
    "title": "Your Blog Post Title",
    "slug": "your-blog-post-title",
    "published": true,
    "createdAt": "2023-06-22T15:30:45.123Z"
  }
}
```

**Error Responses:**

```json
{
  "success": false,
  "message": "Title and content are required"
}
```

```json
{
  "success": false,
  "message": "Category 'non-existent-category' not found"
}
```

```json
{
  "success": false,
  "message": "Server error",
  "error": "Error message details"
}
```

## API Key Management

API keys can be managed through the following endpoints:

### Create API Key

**Endpoint:** `POST /api-keys`

**Authentication:** JWT token (admin users only)

**Request Body:**

```json
{
  "name": "n8n Integration",
  "permissions": ["blog:read", "blog:write"],
  "expiresAt": "2024-12-31T23:59:59.999Z"
}
```

### List API Keys

**Endpoint:** `GET /api-keys`

**Authentication:** JWT token (admin users only)

### Delete API Key

**Endpoint:** `DELETE /api-keys/:id`

**Authentication:** JWT token (admin users only)

## Example Integration with n8n

1. Create an API key in the AI Pomo admin interface
2. In n8n, create a new workflow
3. Add an HTTP Request node
4. Configure the HTTP Request node:
   - Method: POST
   - URL: https://your-domain.com/blog/api
   - Headers: 
     - Key: X-API-Key
     - Value: your-api-key-here
   - Body: JSON with the blog post data
5. Connect the node to your trigger (e.g., RSS Feed, Schedule, etc.)
6. Test and activate the workflow

## Support

For any questions or issues with the API integration, <NAME_EMAIL>.
