/**
 * Complete Blog System Deployment Script
 * Sets up everything needed for blog functionality on production server
 */

const mongoose = require('mongoose');
const path = require('path');
const fs = require('fs');
const { setupImages } = require('./setupProductionImages');
const { createBlogApiKey } = require('./createApiKeyForBlog');
const { createInitialPosts } = require('./createInitialBlogPosts');
const BlogCategory = require('../src/models/BlogCategory');

// Load production environment variables
const loadProductionEnv = () => {
  const envPaths = [
    path.join(__dirname, '../.env.production'),
    path.join(__dirname, '../.env'),
    path.join(__dirname, '../../.env.production'),
    path.join(__dirname, '../../.env')
  ];

  for (const envPath of envPaths) {
    if (fs.existsSync(envPath)) {
      console.log(`📁 Loading environment from: ${envPath}`);
      require('dotenv').config({ path: envPath });
      return envPath;
    }
  }

  console.log('⚠️  No .env file found, using system environment variables');
  return null;
};

// Initialize environment
loadProductionEnv();

// Database connection
const connectDB = async () => {
  try {
    // Try to get MongoDB URI from environment variables
    let mongoUri = process.env.MONGODB_URI;

    if (!mongoUri) {
      // Fallback options for different environments
      const mongoHost = process.env.MONGO_HOST || 'localhost';
      const mongoPort = process.env.MONGO_PORT || '27017';
      const mongoDb = process.env.MONGO_DB || 'pomodoro-timer';
      const mongoUser = process.env.MONGO_USER;
      const mongoPass = process.env.MONGO_PASS;

      if (mongoUser && mongoPass) {
        mongoUri = `mongodb://${mongoUser}:${mongoPass}@${mongoHost}:${mongoPort}/${mongoDb}?authSource=admin`;
      } else {
        mongoUri = `mongodb://${mongoHost}:${mongoPort}/${mongoDb}`;
      }
    }

    console.log(`🔗 Connecting to MongoDB: ${mongoUri.replace(/\/\/.*@/, '//***:***@')}`);

    await mongoose.connect(mongoUri, {
      serverSelectionTimeoutMS: 5000, // Timeout after 5s instead of 30s
      socketTimeoutMS: 45000, // Close sockets after 45s of inactivity
    });

    console.log('✅ Connected to MongoDB successfully');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    console.log('\n💡 Troubleshooting tips:');
    console.log('1. Check if MongoDB is running');
    console.log('2. Verify MONGODB_URI environment variable');
    console.log('3. Check MongoDB authentication credentials');
    console.log('4. Ensure database permissions are correct');
    console.log('\n🔧 Environment variables to check:');
    console.log('   MONGODB_URI - Full connection string');
    console.log('   MONGO_HOST - MongoDB host (default: localhost)');
    console.log('   MONGO_PORT - MongoDB port (default: 27017)');
    console.log('   MONGO_USER - MongoDB username');
    console.log('   MONGO_PASS - MongoDB password');
    console.log('   MONGO_DB - Database name (default: pomodoro-timer)');
    process.exit(1);
  }
};

// Create default blog categories
const setupCategories = async () => {
  try {
    console.log('📂 Setting up blog categories...');

    const categories = [
      {
        name: 'Productivity',
        slug: 'productivity',
        description: 'Tips and strategies for improving productivity and focus',
        color: '#3B82F6'
      },
      {
        name: 'Time Management',
        slug: 'time-management',
        description: 'Effective time management techniques and tools',
        color: '#10B981'
      },
      {
        name: 'Pomodoro Technique',
        slug: 'pomodoro-technique',
        description: 'Everything about the Pomodoro Technique and focus methods',
        color: '#EF4444'
      },
      {
        name: 'AI & Technology',
        slug: 'ai-technology',
        description: 'How AI and technology enhance productivity',
        color: '#8B5CF6'
      },
      {
        name: 'Study Tips',
        slug: 'study-tips',
        description: 'Study strategies and learning techniques',
        color: '#F59E0B'
      },
      {
        name: 'Work-Life Balance',
        slug: 'work-life-balance',
        description: 'Maintaining balance between work and personal life',
        color: '#06B6D4'
      }
    ];

    let created = 0;
    let skipped = 0;

    for (const categoryData of categories) {
      const existing = await BlogCategory.findOne({ slug: categoryData.slug });

      if (existing) {
        console.log(`⏭️  Category exists: ${categoryData.name}`);
        skipped++;
        continue;
      }

      const category = new BlogCategory({
        ...categoryData,
        isActive: true,
        createdAt: new Date()
      });

      await category.save();
      console.log(`✅ Created category: ${categoryData.name}`);
      created++;
    }

    console.log(`📊 Categories: ${created} created, ${skipped} skipped`);
    return { created, skipped };

  } catch (error) {
    console.error('❌ Failed to setup categories:', error);
    throw error;
  }
};

// Complete deployment
const deployBlogSystem = async () => {
  try {
    console.log('🚀 Starting complete blog system deployment...');
    console.log('='.repeat(60));

    // Step 1: Setup categories
    console.log('\n📂 Step 1: Setting up blog categories');
    await setupCategories();

    // Step 2: Setup images
    console.log('\n🖼️  Step 2: Setting up blog images');
    await setupImages();

    // Step 3: Create initial blog posts
    console.log('\n📝 Step 3: Creating initial blog posts');
    try {
      await createInitialPosts();
    } catch (error) {
      console.log('⚠️  Initial posts creation skipped (may already exist or no admin user)');
      console.log('   Run "node createInitialBlogPosts.js create" separately if needed');
    }

    // Step 4: Create API key (optional)
    console.log('\n🔑 Step 4: Creating blog API key');
    try {
      await createBlogApiKey();
    } catch (error) {
      console.log('⚠️  API key creation skipped (may already exist or no admin user)');
      console.log('   Run "node createApiKeyForBlog.js" separately if needed');
    }

    console.log('\n🎉 Blog system deployment completed successfully!');
    console.log('='.repeat(60));

    // Summary
    const categoryCount = await BlogCategory.countDocuments();
    const imageCount = await mongoose.model('BlogImage').countDocuments();
    const postCount = await mongoose.model('BlogPost').countDocuments();

    console.log('\n📊 Deployment Summary:');
    console.log(`✅ Blog categories: ${categoryCount}`);
    console.log(`✅ Blog images: ${imageCount}`);
    console.log(`✅ Blog posts: ${postCount}`);
    console.log('✅ Database models: Ready');
    console.log('✅ API endpoints: Available');

    console.log('\n🔗 Next Steps:');
    console.log('1. Start your server: npm run dev (or pm2 start)');
    console.log('2. Access admin panel: /admin');
    console.log('3. Create your first blog post');
    console.log('4. Test blog API endpoints');

    console.log('\n📝 Available Endpoints:');
    console.log('• GET  /api/blog/posts - List blog posts');
    console.log('• POST /api/blog/posts - Create blog post (requires auth)');
    console.log('• GET  /api/blog/categories - List categories');
    console.log('• GET  /api/blog/images - List available images');

  } catch (error) {
    console.error('💥 Deployment failed:', error);
    throw error;
  }
};

// Check system status
const checkStatus = async () => {
  try {
    console.log('🔍 Checking blog system status...');

    const categoryCount = await BlogCategory.countDocuments();
    const imageCount = await mongoose.model('BlogImage').countDocuments();
    const postCount = await mongoose.model('BlogPost').countDocuments();

    console.log('\n📊 Current Status:');
    console.log(`📂 Categories: ${categoryCount}`);
    console.log(`🖼️  Images: ${imageCount}`);
    console.log(`📝 Posts: ${postCount}`);

    if (categoryCount === 0) {
      console.log('⚠️  No categories found - run setup');
    }

    if (imageCount === 0) {
      console.log('⚠️  No images found - run setup');
    }

    console.log('\n✅ System check complete');

  } catch (error) {
    console.error('❌ Status check failed:', error);
    throw error;
  }
};

// Main execution
const main = async () => {
  await connectDB();

  const command = process.argv[2];

  try {
    switch (command) {
      case 'deploy':
        await deployBlogSystem();
        break;

      case 'categories':
        await setupCategories();
        break;

      case 'images':
        await setupImages();
        break;

      case 'posts':
        await createInitialPosts();
        break;

      case 'status':
        await checkStatus();
        break;

      default:
        console.log('🚀 Blog System Deployment Commands:');
        console.log('');
        console.log('  node deployBlogSystem.js deploy      - Complete blog system setup');
        console.log('  node deployBlogSystem.js categories  - Setup categories only');
        console.log('  node deployBlogSystem.js images      - Setup images only');
        console.log('  node deployBlogSystem.js posts       - Create initial blog posts');
        console.log('  node deployBlogSystem.js status      - Check current status');
        console.log('');
        console.log('💡 Recommended: Run "deploy" for first-time setup');
        break;
    }
  } catch (error) {
    console.error('💥 Command failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
  }
};

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Script error:', error);
    process.exit(1);
  });
}

module.exports = { deployBlogSystem, setupCategories, checkStatus };
