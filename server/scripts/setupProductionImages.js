/**
 * Production Image Setup Script
 * Quick setup for blog images on production server
 * Customize the imageUrls array below with your preferred images
 */

const mongoose = require('mongoose');
const path = require('path');
const fs = require('fs');
const BlogImage = require('../src/models/BlogImage');

// Load production environment variables
const loadProductionEnv = () => {
  const envPaths = [
    path.join(__dirname, '../.env.production'),
    path.join(__dirname, '../.env'),
    path.join(__dirname, '../../.env.production'),
    path.join(__dirname, '../../.env')
  ];

  for (const envPath of envPaths) {
    if (fs.existsSync(envPath)) {
      console.log(`📁 Loading environment from: ${envPath}`);
      require('dotenv').config({ path: envPath });
      return envPath;
    }
  }

  console.log('⚠️  No .env file found, using system environment variables');
  return null;
};

// Initialize environment
loadProductionEnv();

// Database connection
const connectDB = async () => {
  try {
    // Try to get MongoDB URI from environment variables
    let mongoUri = process.env.MONGODB_URI;

    if (!mongoUri) {
      // Fallback options for different environments
      const mongoHost = process.env.MONGO_HOST || 'localhost';
      const mongoPort = process.env.MONGO_PORT || '27017';
      const mongoDb = process.env.MONGO_DB || 'pomodoro-timer';
      const mongoUser = process.env.MONGO_USER;
      const mongoPass = process.env.MONGO_PASS;

      if (mongoUser && mongoPass) {
        mongoUri = `mongodb://${mongoUser}:${mongoPass}@${mongoHost}:${mongoPort}/${mongoDb}?authSource=admin`;
      } else {
        mongoUri = `mongodb://${mongoHost}:${mongoPort}/${mongoDb}`;
      }
    }

    await mongoose.connect(mongoUri, {
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    });

    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    console.log('\n💡 Set environment variables: MONGODB_URI or MONGO_USER/MONGO_PASS');
    process.exit(1);
  }
};

// CUSTOMIZE THESE IMAGE URLS FOR YOUR PRODUCTION SERVER
const imageUrls = [
  // High-quality Unsplash images - free to use
  'https://images.unsplash.com/photo-1484480974693-6ca0a78fb36b?w=1200&h=800&fit=crop&q=80',
  'https://images.unsplash.com/photo-1551836022-deb4988cc6c0?w=1200&h=800&fit=crop&q=80',
  'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=1200&h=800&fit=crop&q=80',
  'https://images.unsplash.com/photo-1501139083538-0139583c060f?w=1200&h=800&fit=crop&q=80',
  'https://images.unsplash.com/photo-1495364141860-b0d03eccd065?w=1200&h=800&fit=crop&q=80',
  'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=1200&h=800&fit=crop&q=80',
  'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=1200&h=800&fit=crop&q=80',
  'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=1200&h=800&fit=crop&q=80',
  'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=1200&h=800&fit=crop&q=80',
  'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1200&h=800&fit=crop&q=80',
  'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=1200&h=800&fit=crop&q=80',
  'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=1200&h=800&fit=crop&q=80',
  'https://images.unsplash.com/photo-1509909756405-be0199881695?w=1200&h=800&fit=crop&q=80',
  'https://images.unsplash.com/photo-1551836022-d5d88e9218df?w=1200&h=800&fit=crop&q=80',
  'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=1200&h=800&fit=crop&q=80',

  // Add your own image URLs here
  // 'https://your-domain.com/images/blog-image-1.jpg',
  // 'https://your-cdn.com/productivity-image.png',
  // 'https://your-server.com/uploads/pomodoro-banner.jpg',
];

// Auto-generate alt text and categories based on image content
const generateImageData = (url, index) => {
  const categories = ['productivity', 'time-management', 'technology', 'study', 'wellness', 'business', 'pomodoro'];
  const productivityTerms = ['workspace', 'focus', 'productivity', 'organization', 'planning'];
  const techTerms = ['laptop', 'technology', 'digital', 'modern', 'innovation'];
  const studyTerms = ['learning', 'education', 'books', 'study', 'knowledge'];

  const category = categories[index % categories.length];

  return {
    title: `${category.charAt(0).toUpperCase() + category.slice(1)} Image ${index + 1}`,
    url: url,
    description: `Professional productivity and focus image ${index + 1}`,
    category: category,
    tags: [
      ...productivityTerms.slice(0, 2),
      ...techTerms.slice(0, 1),
      ...studyTerms.slice(0, 1),
      'pomodoro',
      'ai-pomo'
    ],
    source: 'unsplash',
    isActive: true
  };
};

// Main setup function
const setupImages = async () => {
  try {
    console.log('🚀 Setting up blog images for production...');

    // Check existing images
    const existingCount = await BlogImage.countDocuments();
    console.log(`📊 Current images in database: ${existingCount}`);

    let created = 0;
    let skipped = 0;

    for (let i = 0; i < imageUrls.length; i++) {
      const url = imageUrls[i];

      // Check if URL already exists
      const existing = await BlogImage.findOne({ url });
      if (existing) {
        console.log(`⏭️  Skipped: ${url} (already exists)`);
        skipped++;
        continue;
      }

      // Create new image record
      const imageData = generateImageData(url, i);
      const blogImage = new BlogImage(imageData);

      try {
        await blogImage.save();
        console.log(`✅ Added: Image ${i + 1} - ${imageData.category}`);
        created++;
      } catch (error) {
        console.error(`❌ Failed to add image ${i + 1}:`, error.message);
      }
    }

    console.log('\n📈 Setup Complete!');
    console.log(`✅ Created: ${created} new images`);
    console.log(`⏭️  Skipped: ${skipped} existing images`);
    console.log(`📝 Total images: ${await BlogImage.countDocuments()}`);

    // Show sample of created images
    const sampleImages = await BlogImage.find().limit(3).sort({ createdAt: -1 });
    console.log('\n🖼️  Sample images:');
    sampleImages.forEach((img, idx) => {
      console.log(`${idx + 1}. ${img.title} (${img.category})`);
      console.log(`   ${img.url}`);
    });

  } catch (error) {
    console.error('💥 Setup failed:', error);
    throw error;
  }
};

// Cleanup function (use with caution)
const clearAllImages = async () => {
  try {
    const count = await BlogImage.countDocuments();
    await BlogImage.deleteMany({});
    console.log(`🧹 Cleared ${count} images from database`);
  } catch (error) {
    console.error('❌ Failed to clear images:', error);
    throw error;
  }
};

// List current images
const listImages = async () => {
  try {
    const images = await BlogImage.find().sort({ category: 1, createdAt: -1 });

    console.log(`\n📸 Current Blog Images (${images.length} total):`);
    console.log('='.repeat(80));

    images.forEach((img, idx) => {
      console.log(`${idx + 1}. [${img.category}] ${img.title}`);
      console.log(`   URL: ${img.url}`);
      console.log(`   Tags: ${img.tags.join(', ')}`);
      console.log(`   Active: ${img.isActive ? '✅' : '❌'}`);
      console.log('');
    });
  } catch (error) {
    console.error('❌ Failed to list images:', error);
    throw error;
  }
};

// Main execution
const main = async () => {
  await connectDB();

  const command = process.argv[2];

  try {
    switch (command) {
      case 'setup':
        await setupImages();
        break;

      case 'list':
        await listImages();
        break;

      case 'clear':
        console.log('⚠️  WARNING: This will delete ALL blog images!');
        console.log('Type "yes" to confirm or Ctrl+C to cancel...');

        // Simple confirmation (in production, you might want to skip this)
        await clearAllImages();
        console.log('✅ All images cleared');
        break;

      case 'reset':
        await clearAllImages();
        await setupImages();
        break;

      default:
        console.log('📋 Production Image Setup Commands:');
        console.log('');
        console.log('  node setupProductionImages.js setup  - Add new images to database');
        console.log('  node setupProductionImages.js list   - Show all current images');
        console.log('  node setupProductionImages.js clear  - Remove all images (DANGER!)');
        console.log('  node setupProductionImages.js reset  - Clear and setup fresh images');
        console.log('');
        console.log('💡 Tip: Edit the imageUrls array in this file to customize your images');
        break;
    }
  } catch (error) {
    console.error('💥 Command failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
};

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Script error:', error);
    process.exit(1);
  });
}

module.exports = { setupImages, clearAllImages, listImages };
