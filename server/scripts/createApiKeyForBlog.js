/**
 * <PERSON><PERSON><PERSON> to create an API key for blog posting
 * This script generates a new API key with blog read/write permissions
 */

const mongoose = require('mongoose');
const crypto = require('crypto');
const path = require('path');
const fs = require('fs');

// Import models
const ApiKey = require('../src/models/ApiKey');
const User = require('../src/models/User');

// Load production environment variables
const loadProductionEnv = () => {
  const envPaths = [
    path.join(__dirname, '../.env.production'),
    path.join(__dirname, '../.env'),
    path.join(__dirname, '../../.env.production'),
    path.join(__dirname, '../../.env')
  ];

  for (const envPath of envPaths) {
    if (fs.existsSync(envPath)) {
      console.log(`📁 Loading environment from: ${envPath}`);
      require('dotenv').config({ path: envPath });
      return envPath;
    }
  }

  console.log('⚠️  No .env file found, using system environment variables');
  return null;
};

// Initialize environment
loadProductionEnv();

// Database connection
const connectDB = async () => {
  try {
    // Try to get MongoDB URI from environment variables
    let mongoUri = process.env.MONGODB_URI;

    if (!mongoUri) {
      // Fallback options for different environments
      const mongoHost = process.env.MONGO_HOST || 'localhost';
      const mongoPort = process.env.MONGO_PORT || '27017';
      const mongoDb = process.env.MONGO_DB || 'pomodoro-timer';
      const mongoUser = process.env.MONGO_USER;
      const mongoPass = process.env.MONGO_PASS;

      if (mongoUser && mongoPass) {
        mongoUri = `mongodb://${mongoUser}:${mongoPass}@${mongoHost}:${mongoPort}/${mongoDb}?authSource=admin`;
      } else {
        mongoUri = `mongodb://${mongoHost}:${mongoPort}/${mongoDb}`;
      }
    }

    await mongoose.connect(mongoUri, {
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    });

    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('MongoDB connection error:', error.message);
    console.log('\n💡 Set environment variables: MONGODB_URI or MONGO_USER/MONGO_PASS');
    process.exit(1);
  }
};

// Generate API key
const generateApiKey = () => {
  return crypto.randomBytes(32).toString('hex');
};

// Create API key for blog
const createBlogApiKey = async () => {
  try {
    // Connect to database
    await connectDB();

    // Find an admin user
    const adminUser = await User.findOne({ isAdmin: true });

    if (!adminUser) {
      console.error('No admin user found. Please create an admin user first.');
      process.exit(1);
    }

    // Generate new API key
    const apiKeyValue = generateApiKey();

    // Create API key document
    const apiKey = new ApiKey({
      key: apiKeyValue,
      name: 'Blog Post API Key',
      permissions: ['blog:read', 'blog:write'],
      user: adminUser._id,
      isActive: true,
      lastUsed: null
    });

    // Save to database
    await apiKey.save();

    console.log('✅ API Key created successfully!');
    console.log('');
    console.log('📋 API Key Details:');
    console.log(`Key: ${apiKeyValue}`);
    console.log(`Name: ${apiKey.name}`);
    console.log(`Permissions: ${apiKey.permissions.join(', ')}`);
    console.log(`User: ${adminUser.username} (${adminUser.email})`);
    console.log(`Database ID: ${apiKey._id}`);
    console.log('');
    console.log('🔐 Usage:');
    console.log('Include this key in your API requests as:');
    console.log(`X-API-Key: ${apiKeyValue}`);
    console.log('');
    console.log('⚠️  Security Note:');
    console.log('Store this API key securely. It will not be shown again.');

  } catch (error) {
    console.error('Error creating API key:', error);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log('Database connection closed');
    process.exit(0);
  }
};

// Run the script
if (require.main === module) {
  createBlogApiKey();
}

module.exports = { createBlogApiKey, generateApiKey };
