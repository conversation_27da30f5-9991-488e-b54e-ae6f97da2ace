/**
 * <PERSON><PERSON><PERSON> to create and manage blog image URLs for production server
 * This script populates the BlogImage collection with predefined image URLs
 * for use in blog post creation
 */

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const BlogImage = require('../src/models/BlogImage');

// Database connection
const connectDB = async () => {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/pomodoro-timer';
    await mongoose.connect(mongoUri);
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Function to read image URLs from the user-provided file
const readImageUrlsFromFile = () => {
  try {
    const filePath = path.join(__dirname, '..', '..', 'new-image-urls'); // Adjusted path to be relative to project root
    if (fs.existsSync(filePath)) {
      const fileContent = fs.readFileSync(filePath, 'utf-8');
      return fileContent.split('\n').map(url => url.trim()).filter(url => url.startsWith('http'));
    } else {
      console.warn('⚠️ new-image-urls file not found. Skipping custom URLs.');
      return [];
    }
  } catch (error) {
    console.error('❌ Error reading new-image-urls file:', error);
    return [];
  }
};

// Predefined image URLs for blog posts (can be kept as fallback or for initial setup)
const predefinedImageUrls = [
  // Productivity and Focus Images
  {
    url: 'https://images.unsplash.com/photo-1484480974693-6ca0a78fb36b?w=800&h=600&fit=crop',
    alt: 'Person working focused at desk with laptop',
    category: 'productivity',
    tags: ['focus', 'work', 'productivity', 'desk']
  },
  {
    url: 'https://images.unsplash.com/photo-1551836022-deb4988cc6c0?w=800&h=600&fit=crop',
    alt: 'Clean organized workspace with notebook and coffee',
    category: 'productivity',
    tags: ['workspace', 'organization', 'coffee', 'planning']
  },
  {
    url: 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=800&h=600&fit=crop',
    alt: 'Person writing in notebook with pen',
    category: 'productivity',
    tags: ['writing', 'planning', 'notebook', 'goals']
  },

  // Time Management Images
  {
    url: 'https://images.unsplash.com/photo-1501139083538-0139583c060f?w=800&h=600&fit=crop',
    alt: 'Classic alarm clock on wooden surface',
    category: 'time-management',
    tags: ['time', 'clock', 'schedule', 'deadline']
  },
  {
    url: 'https://images.unsplash.com/photo-1495364141860-b0d03eccd065?w=800&h=600&fit=crop',
    alt: 'Calendar with pen marking important dates',
    category: 'time-management',
    tags: ['calendar', 'planning', 'schedule', 'organization']
  },
  {
    url: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=600&fit=crop',
    alt: 'Hourglass with sand flowing representing time',
    category: 'time-management',
    tags: ['hourglass', 'time', 'deadline', 'urgency']
  },

  // Technology and AI Images
  {
    url: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=800&h=600&fit=crop',
    alt: 'Modern laptop with code on screen',
    category: 'technology',
    tags: ['laptop', 'coding', 'technology', 'development']
  },
  {
    url: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=600&fit=crop',
    alt: 'AI and machine learning concept illustration',
    category: 'technology',
    tags: ['ai', 'artificial intelligence', 'machine learning', 'future']
  },
  {
    url: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop',
    alt: 'Digital interface with data visualization',
    category: 'technology',
    tags: ['data', 'analytics', 'dashboard', 'metrics']
  },

  // Study and Learning Images
  {
    url: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=800&h=600&fit=crop',
    alt: 'Stack of books with reading glasses',
    category: 'study',
    tags: ['books', 'reading', 'learning', 'education']
  },
  {
    url: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=800&h=600&fit=crop',
    alt: 'Students studying together in library',
    category: 'study',
    tags: ['students', 'library', 'collaboration', 'learning']
  },
  {
    url: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=800&h=600&fit=crop',
    alt: 'Person taking notes while studying',
    category: 'study',
    tags: ['notes', 'studying', 'concentration', 'learning']
  },

  // Wellness and Balance Images
  {
    url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop',
    alt: 'Person meditating in peaceful environment',
    category: 'wellness',
    tags: ['meditation', 'mindfulness', 'peace', 'balance']
  },
  {
    url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop',
    alt: 'Yoga and wellness setup with plants',
    category: 'wellness',
    tags: ['yoga', 'wellness', 'plants', 'self-care']
  },
  {
    url: 'https://images.unsplash.com/photo-1499209974431-9dddcece7f88?w=800&h=600&fit=crop',
    alt: 'Healthy workspace with plants and natural light',
    category: 'wellness',
    tags: ['plants', 'natural light', 'healthy workspace', 'environment']
  },

  // Business and Professional Images
  {
    url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop',
    alt: 'Professional team meeting and collaboration',
    category: 'business',
    tags: ['team', 'meeting', 'collaboration', 'professional']
  },
  {
    url: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop',
    alt: 'Business charts and analytics on desk',
    category: 'business',
    tags: ['analytics', 'charts', 'business', 'data']
  },
  {
    url: 'https://images.unsplash.com/photo-**********-43269d4ea984?w=800&h=600&fit=crop',
    alt: 'Modern office space with clean design',
    category: 'business',
    tags: ['office', 'modern', 'workspace', 'professional']
  },

  // Pomodoro and Timer Specific Images
  {
    url: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=800&h=600&fit=crop',
    alt: 'Red tomato representing Pomodoro technique',
    category: 'pomodoro',
    tags: ['tomato', 'pomodoro', 'technique', 'focus']
  },
  {
    url: 'https://images.unsplash.com/photo-1509909756405-be0199881695?w=800&h=600&fit=crop',
    alt: 'Kitchen timer for time management',
    category: 'pomodoro',
    tags: ['timer', 'kitchen timer', 'time management', 'focus']
  },
  {
    url: 'https://images.unsplash.com/photo-1551836022-d5d88e9218df?w=800&h=600&fit=crop',
    alt: 'Minimalist desk setup with timer',
    category: 'pomodoro',
    tags: ['minimalist', 'desk', 'timer', 'clean workspace']
  }
];

// Function to create blog images
const createBlogImages = async () => {
  try {
    console.log('🚀 Starting blog image URL creation...');
    
    // Clear existing images (optional - comment out if you want to keep existing)
    // await BlogImage.deleteMany({});
    // console.log('🧹 Cleared existing blog images');

    let createdCount = 0;
    let skippedCount = 0;

    const newImageUrlsFromFile = readImageUrlsFromFile();
    const allImageObjects = [...predefinedImageUrls]; // Start with predefined ones

    newImageUrlsFromFile.forEach((url, index) => {
      allImageObjects.push({
        url: url,
        alt: `User provided blog image ${index + 1}`,
        category: 'user-provided', // Or try to infer from URL if possible
        tags: ['blog', 'image', 'user-upload'] // Generic tags
      });
    });

    console.log(`ℹ️ Found ${newImageUrlsFromFile.length} new image URLs from file.`);

    for (const imageData of allImageObjects) {
      try {
        // Check if image already exists
        const existingImage = await BlogImage.findOne({ url: imageData.url });
        
        if (existingImage) {
          console.log(`⏭️  Skipped: ${imageData.alt} (already exists)`);
          skippedCount++;
          continue;
        }

        // Create new blog image
        const blogImage = new BlogImage({
          url: imageData.url,
          alt: imageData.alt,
          category: imageData.category,
          tags: imageData.tags,
          isActive: true,
          createdAt: new Date()
        });

        await blogImage.save();
        console.log(`✅ Created: ${imageData.alt}`);
        createdCount++;

      } catch (error) {
        console.error(`❌ Error creating image "${imageData.alt}":`, error.message);
      }
    }

    console.log('\n📊 Summary:');
    console.log(`✅ Created: ${createdCount} new images`);
    console.log(`⏭️  Skipped: ${skippedCount} existing images`);
    console.log(`📝 Total available: ${await BlogImage.countDocuments()}`);

    // Display categories summary
    const categories = await BlogImage.aggregate([
      { $group: { _id: '$category', count: { $sum: 1 } } },
      { $sort: { _id: 1 } }
    ]);

    console.log('\n📂 Images by category:');
    categories.forEach(cat => {
      console.log(`   ${cat._id}: ${cat.count} images`);
    });

  } catch (error) {
    console.error('❌ Error creating blog images:', error);
  }
};

// Function to list all available images
const listImages = async () => {
  try {
    const images = await BlogImage.find({ isActive: true }).sort({ category: 1, createdAt: -1 });
    
    console.log('\n📸 Available Blog Images:');
    console.log('=' .repeat(80));
    
    let currentCategory = '';
    images.forEach((image, index) => {
      if (image.category !== currentCategory) {
        currentCategory = image.category;
        console.log(`\n📂 ${currentCategory.toUpperCase()}`);
        console.log('-'.repeat(40));
      }
      
      console.log(`${index + 1}. ${image.alt}`);
      console.log(`   URL: ${image.url}`);
      console.log(`   Tags: ${image.tags.join(', ')}`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ Error listing images:', error);
  }
};

// Main execution function
const main = async () => {
  await connectDB();

  const command = process.argv[2];

  switch (command) {
    case 'create':
      await createBlogImages();
      break;
    case 'list':
      await listImages();
      break;
    case 'both':
      await createBlogImages();
      await listImages();
      break;
    default:
      console.log('📋 Usage:');
      console.log('  node createBlogImageUrls.js create  - Create image URLs');
      console.log('  node createBlogImageUrls.js list    - List existing images');
      console.log('  node createBlogImageUrls.js both    - Create and list images');
      break;
  }

  await mongoose.connection.close();
  console.log('\n🔌 Database connection closed');
  process.exit(0);
};

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
}

module.exports = { createBlogImages, listImages };
