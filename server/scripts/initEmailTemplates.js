/**
 * <PERSON><PERSON><PERSON> to initialize email templates in database
 * Run this script once to migrate hardcoded templates to database
 */

const mongoose = require('mongoose');
const EmailTemplate = require('../src/models/EmailTemplate');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// University contacts and templates from controller
const UNIVERSITY_CONTACTS = [
  {
    id: 'oregon-state',
    name: 'Oregon State University',
    email: '<EMAIL>',
    authorityScore: 74,
    backlinks: 24,
    existingPage: 'https://success.oregonstate.edu/planning-time/pomodoro',
    strategy: 'upgrade_existing'
  },
  {
    id: 'university-illinois',
    name: 'University of Illinois',
    email: '<EMAIL>',
    authorityScore: 75,
    backlinks: 20,
    existingPage: 'https://publish.illinois.edu/educational/top-4-educational-tools-for-easier-studying/',
    strategy: 'update_resource'
  },
  {
    id: 'uc-berkeley',
    name: '<PERSON> Berkeley',
    email: '<EMAIL>',
    authorityScore: 79,
    backlinks: 5,
    existingPage: 'https://life.berkeley.edu/study-hacks-berkeley-style/',
    strategy: 'add_to_hacks'
  },
  {
    id: 'university-minnesota',
    name: 'University of Minnesota',
    email: '<EMAIL>',
    ccEmail: '<EMAIL>',
    authorityScore: 77,
    backlinks: 4,
    strategy: 'student_success'
  },
  {
    id: 'cuny',
    name: 'CUNY System',
    email: '<EMAIL>',
    authorityScore: 76,
    backlinks: 3,
    strategy: 'system_wide'
  },
  {
    id: 'mit',
    name: 'MIT',
    email: '<EMAIL>',
    ccEmail: '<EMAIL>',
    authorityScore: 89,
    backlinks: 3,
    strategy: 'research_tech'
  },
  {
    id: 'columbia',
    name: 'Columbia University',
    email: '<EMAIL>',
    authorityScore: 78,
    backlinks: 1,
    strategy: 'academic_partnership'
  },
  {
    id: 'nyu',
    name: 'NYU',
    email: '<EMAIL>',
    ccEmail: '<EMAIL>',
    authorityScore: 76,
    backlinks: 37,
    strategy: 'global_university'
  }
];

// Sample templates (you'll need to import the actual templates from your controller)
const EMAIL_TEMPLATES = {
  'oregon-state': {
    subject: 'Upgrade Your Pomodoro Technique Page - AI-Enhanced Version for OSU Students',
    text: `Dear Academic Success Center Team,

I discovered your excellent Pomodoro Technique page at https://success.oregonstate.edu/planning-time/pomodoro and was impressed by OSU's commitment to helping students develop effective study strategies.

As you're already promoting the Pomodoro Technique, I wanted to introduce you to AI-Pomo (ai-pomo.com), the next generation of this proven method.

Key Benefits for OSU Students:
• AI Project Breakdown: Students input "Prepare for Chemistry Final" and AI automatically generates a detailed study plan
• Smart Time Estimation: AI analyzes task complexity to recommend optimal Pomodoro sessions
• Progress Visualization: Interactive milestone timelines keep students motivated and on track
• Completely Free: Full access to all features for educational use

Proposal: Would you consider adding AI-Pomo as an "Advanced Tool" recommendation on your existing Pomodoro page?

Best regards,
AI-Pomo Support Team
<EMAIL>
https://ai-pomo.com`,
    html: `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI-Pomo Partnership Proposal - Oregon State University</title>
</head>
<body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; color: #2c3e50; background-color: #f8f9fa; line-height: 1.6;">
    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color: #f8f9fa;">
        <tr>
            <td align="center" style="padding: 40px 20px;">
                <table cellpadding="0" cellspacing="0" border="0" width="600" style="max-width: 600px; background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); overflow: hidden;">
                    <tr>
                        <td style="background: linear-gradient(135deg, #d95550 0%, #eb6b56 100%); padding: 40px 30px; text-align: center;">
                            <div style="font-size: 48px; margin-bottom: 15px;">🍅</div>
                            <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 600;">AI-Pomo</h1>
                            <p style="color: rgba(255,255,255,0.9); margin: 8px 0 0 0; font-size: 16px;">AI-Enhanced Productivity for Students</p>
                        </td>
                    </tr>
                    <tr>
                        <td style="padding: 40px 30px;">
                            <h2 style="color: #2c3e50; margin-top: 0; margin-bottom: 20px; font-size: 22px;">Partnership Proposal for Oregon State University</h2>
                            <p style="font-size: 16px; margin-bottom: 20px; color: #555;">Dear Academic Success Center Team,</p>
                            <p style="font-size: 16px; margin-bottom: 25px; color: #555;">I discovered your excellent Pomodoro Technique page and was impressed by OSU's commitment to helping students develop effective study strategies.</p>
                            <p style="font-size: 16px; margin-bottom: 25px; color: #555;">I'd be happy to schedule a brief demo to show how AI-Pomo can enhance your existing study strategy resources.</p>
                            <p style="font-size: 16px; margin-bottom: 0; color: #555;">
                                Best regards,<br>
                                <strong>AI-Pomo Support Team</strong><br>
                                <a href="mailto:<EMAIL>" style="color: #d95550;"><EMAIL></a>
                            </p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>`
  }
  // Add other templates here as needed
};

async function initializeTemplates() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    let created = 0;
    let updated = 0;

    for (const university of UNIVERSITY_CONTACTS) {
      const template = EMAIL_TEMPLATES[university.id];
      if (!template) {
        console.log(`No template found for ${university.name}, skipping...`);
        continue;
      }

      const existingTemplate = await EmailTemplate.findOne({ universityId: university.id });
      
      if (existingTemplate) {
        console.log(`Updating template for ${university.name}...`);
        existingTemplate.subject = template.subject;
        existingTemplate.textContent = template.text;
        existingTemplate.htmlContent = template.html;
        await existingTemplate.save();
        updated++;
      } else {
        console.log(`Creating template for ${university.name}...`);
        const newTemplate = new EmailTemplate({
          universityId: university.id,
          universityName: university.name,
          subject: template.subject,
          textContent: template.text,
          htmlContent: template.html
        });
        await newTemplate.save();
        created++;
      }
    }

    console.log(`\nInitialization complete:`);
    console.log(`- Created: ${created} templates`);
    console.log(`- Updated: ${updated} templates`);
    console.log(`- Total: ${created + updated} templates processed`);

  } catch (error) {
    console.error('Error initializing templates:', error);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
}

// Run the script
if (require.main === module) {
  initializeTemplates();
}

module.exports = { initializeTemplates };