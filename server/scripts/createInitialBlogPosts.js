/**
 * <PERSON><PERSON><PERSON> to create initial blog posts for AI Pomo
 * Populates the blog with high-quality content about productivity, Pomodoro technique, and AI
 */

const mongoose = require('mongoose');
const path = require('path');
const fs = require('fs');
const BlogPost = require('../src/models/BlogPost');
const BlogCategory = require('../src/models/BlogCategory');
const BlogImage = require('../src/models/BlogImage');
const User = require('../src/models/User');
const slugify = require('../src/utils/slugify');

// Load production environment variables
const loadProductionEnv = () => {
  const envPaths = [
    path.join(__dirname, '../.env.production'),
    path.join(__dirname, '../.env'),
    path.join(__dirname, '../../.env.production'),
    path.join(__dirname, '../../.env')
  ];

  for (const envPath of envPaths) {
    if (fs.existsSync(envPath)) {
      console.log(`📁 Loading environment from: ${envPath}`);
      require('dotenv').config({ path: envPath });
      return envPath;
    }
  }

  console.log('⚠️  No .env file found, using system environment variables');
  return null;
};

// Initialize environment
loadProductionEnv();

// Database connection
const connectDB = async () => {
  try {
    // Try to get MongoDB URI from environment variables
    let mongoUri = process.env.MONGODB_URI;

    if (!mongoUri) {
      // Fallback options for different environments
      const mongoHost = process.env.MONGO_HOST || 'localhost';
      const mongoPort = process.env.MONGO_PORT || '27017';
      const mongoDb = process.env.MONGO_DB || 'pomodoro-timer';
      const mongoUser = process.env.MONGO_USER;
      const mongoPass = process.env.MONGO_PASS;

      if (mongoUser && mongoPass) {
        mongoUri = `mongodb://${mongoUser}:${mongoPass}@${mongoHost}:${mongoPort}/${mongoDb}?authSource=admin`;
      } else {
        mongoUri = `mongodb://${mongoHost}:${mongoPort}/${mongoDb}`;
      }
    }

    console.log(`🔗 Connecting to MongoDB...`);

    await mongoose.connect(mongoUri, {
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    });

    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    console.log('\n💡 Set environment variables: MONGODB_URI or MONGO_USER/MONGO_PASS');
    process.exit(1);
  }
};

// Initial blog posts data
const initialPosts = [
  {
    title: "The Ultimate Guide to the Pomodoro Technique: Boost Your Productivity in 2024",
    excerpt: "Discover how the Pomodoro Technique can transform your productivity. Learn the science behind 25-minute focus sessions and how AI can enhance this time-tested method.",
    content: `# The Ultimate Guide to the Pomodoro Technique: Boost Your Productivity in 2024

The Pomodoro Technique, developed by Francesco Cirillo in the late 1980s, has become one of the most popular time management methods worldwide. But what makes it so effective, and how can AI enhance this time-tested approach?

## What is the Pomodoro Technique?

The Pomodoro Technique is a time management method that breaks work into 25-minute focused intervals, called "pomodoros," followed by short 5-minute breaks. After completing four pomodoros, you take a longer 15-30 minute break.

### The Basic Steps:
1. **Choose a task** you want to work on
2. **Set a timer for 25 minutes** (one pomodoro)
3. **Work on the task** until the timer rings
4. **Take a 5-minute break**
5. **Repeat** the process
6. After 4 pomodoros, take a **longer break** (15-30 minutes)

## The Science Behind Pomodoro

Research shows that our brains can maintain peak focus for approximately 25 minutes before attention begins to wane. The Pomodoro Technique leverages this natural rhythm, maximizing productivity while preventing mental fatigue.

### Key Benefits:
- **Improved Focus**: Short bursts eliminate distractions
- **Better Time Awareness**: Track how long tasks actually take
- **Reduced Procrastination**: 25 minutes feels manageable
- **Enhanced Motivation**: Regular breaks maintain energy
- **Stress Reduction**: Structured approach reduces overwhelm

## How AI Enhances the Pomodoro Technique

Modern AI-powered tools like AI Pomo take the traditional Pomodoro Technique to the next level:

### Smart Task Planning
AI can analyze your work patterns and suggest optimal task sequences, ensuring you tackle high-priority items when your energy is highest.

### Adaptive Timing
While 25 minutes works for most people, AI can learn your personal productivity patterns and suggest customized session lengths.

### Intelligent Break Suggestions
AI can recommend break activities based on your current stress level, time of day, and upcoming tasks.

### Progress Tracking
Advanced analytics help you understand your productivity patterns and identify areas for improvement.

## Getting Started with AI-Enhanced Pomodoro

1. **Start with the basics**: Use traditional 25-minute sessions
2. **Track your progress**: Monitor which tasks take longer than expected
3. **Analyze patterns**: Look for your peak productivity hours
4. **Customize gradually**: Adjust timing based on your data
5. **Integrate with other tools**: Connect with your calendar and task management systems

## Common Mistakes to Avoid

- **Skipping breaks**: Breaks are essential for maintaining focus
- **Multitasking during pomodoros**: Focus on one task only
- **Not tracking interruptions**: Log distractions to improve future sessions
- **Being too rigid**: Adapt the technique to your needs

## Conclusion

The Pomodoro Technique remains one of the most effective productivity methods available. When enhanced with AI capabilities, it becomes even more powerful, adapting to your unique work style and helping you achieve peak performance.

Ready to transform your productivity? Try AI Pomo today and experience the future of focus management.`,
    category: 'pomodoro-technique',
    tags: ['pomodoro', 'productivity', 'time management', 'focus', 'ai'],
    seoTitle: "Pomodoro Technique Guide 2024: AI-Enhanced Productivity Method",
    seoDescription: "Master the Pomodoro Technique with AI. Learn how 25-minute focus sessions can boost productivity, reduce stress, and improve time management in 2024.",
    seoKeywords: "pomodoro technique, productivity method, time management, focus sessions, AI productivity, work efficiency",
    readTime: 8,
    featured: true,
    published: true
  },

  {
    title: "10 AI-Powered Productivity Tips That Will Transform Your Workday",
    excerpt: "Discover how artificial intelligence can revolutionize your daily productivity. From smart scheduling to automated task prioritization, learn the AI strategies top performers use.",
    content: `# 10 AI-Powered Productivity Tips That Will Transform Your Workday

Artificial Intelligence isn't just changing industries—it's revolutionizing how we work. Here are 10 AI-powered strategies that can dramatically improve your daily productivity.

## 1. Smart Task Prioritization

AI can analyze your task list, deadlines, and energy patterns to suggest the optimal order for completing work. Instead of guessing what to do next, let AI guide your decisions based on data.

**How to implement:**
- Use AI-powered task managers that learn from your behavior
- Set up automated priority scoring based on deadlines and importance
- Let AI suggest when to tackle different types of tasks

## 2. Intelligent Calendar Management

AI can optimize your schedule by analyzing meeting patterns, energy levels, and task requirements to suggest the best times for different activities.

## 3. Automated Email Processing

Train AI to categorize, prioritize, and even draft responses to routine emails, freeing up hours of your day for high-value work.

## 4. Predictive Time Blocking

AI can predict how long tasks will actually take based on historical data, helping you create more realistic schedules.

## 5. Smart Break Timing

AI can monitor your productivity patterns and suggest optimal break times to maintain peak performance throughout the day.

## 6. Contextual Focus Sessions

Instead of generic 25-minute pomodoros, AI can suggest session lengths based on task complexity, your current energy level, and time of day.

## 7. Automated Progress Tracking

Let AI track your progress across projects and goals, providing insights without manual data entry.

## 8. Intelligent Distraction Management

AI can learn your distraction patterns and proactively block interruptions during your most productive hours.

## 9. Personalized Productivity Insights

AI analyzes your work patterns to provide personalized recommendations for improving efficiency and effectiveness.

## 10. Adaptive Workflow Optimization

AI continuously learns from your behavior to suggest workflow improvements and automation opportunities.

## Getting Started with AI Productivity

1. **Start small**: Choose one AI tool and master it before adding others
2. **Provide good data**: The more information you give AI, the better it performs
3. **Review and adjust**: Regularly check AI suggestions and provide feedback
4. **Stay human-centered**: Use AI to enhance, not replace, human judgment

## The Future of AI-Powered Productivity

As AI continues to evolve, we can expect even more sophisticated productivity enhancements:
- Real-time stress monitoring and adjustment
- Predictive burnout prevention
- Seamless integration across all work tools
- Personalized productivity coaching

## Conclusion

AI-powered productivity isn't about replacing human creativity and judgment—it's about eliminating routine decisions and optimizing workflows so you can focus on what matters most.

Start implementing these AI strategies today and experience the transformation in your workday efficiency.`,
    category: 'ai-technology',
    tags: ['ai', 'productivity', 'automation', 'efficiency', 'technology'],
    seoTitle: "10 AI Productivity Tips to Transform Your Workday | AI Pomo",
    seoDescription: "Discover 10 AI-powered productivity strategies that top performers use. Learn how artificial intelligence can optimize your schedule, tasks, and focus for maximum efficiency.",
    seoKeywords: "ai productivity tips, artificial intelligence productivity, smart task management, ai scheduling, automated productivity",
    readTime: 6,
    featured: true,
    published: true
  },

  {
    title: "Time Management for ADHD: How the Pomodoro Technique Can Help",
    excerpt: "Struggling with focus and time management due to ADHD? Learn how the Pomodoro Technique, combined with AI assistance, can provide structure and improve concentration.",
    content: `# Time Management for ADHD: How the Pomodoro Technique Can Help

Living with ADHD presents unique challenges when it comes to time management and sustained focus. The Pomodoro Technique offers a structured approach that can be particularly beneficial for individuals with ADHD.

## Understanding ADHD and Time Management Challenges

ADHD affects executive function, making it difficult to:
- Estimate how long tasks will take
- Maintain focus for extended periods
- Resist distractions and interruptions
- Transition between tasks smoothly
- Prioritize effectively

## Why the Pomodoro Technique Works for ADHD

### 1. Manageable Time Chunks
25-minute sessions feel achievable, reducing the overwhelm that longer time commitments can create.

### 2. Built-in Breaks
Regular breaks prevent hyperfocus burnout and provide opportunities to reset attention.

### 3. External Structure
The timer provides external accountability, compensating for internal time awareness challenges.

### 4. Dopamine Rewards
Completing pomodoros provides regular dopamine hits, maintaining motivation.

## ADHD-Specific Pomodoro Modifications

### Flexible Timing
- Start with 15-minute sessions if 25 minutes feels too long
- Gradually increase duration as focus improves
- Use longer sessions (45-50 minutes) during hyperfocus periods

### Enhanced Break Activities
- Physical movement (jumping jacks, stretching)
- Mindfulness exercises
- Fidget toys or stress balls
- Quick organizational tasks

### Visual and Auditory Cues
- Use colorful timers or apps
- Set up visual progress tracking
- Choose engaging timer sounds
- Create visual task lists

## AI Enhancements for ADHD

Modern AI tools can provide additional support:

### Adaptive Reminders
AI can learn your patterns and send reminders at optimal times.

### Distraction Monitoring
AI can detect when you're getting distracted and gently redirect focus.

### Energy Level Tracking
AI can help identify your peak focus times and schedule accordingly.

### Personalized Strategies
AI can suggest modifications based on your specific ADHD symptoms and responses.

## Implementation Tips for ADHD

1. **Start Small**: Begin with just 2-3 pomodoros per day
2. **Be Flexible**: Adjust timing based on your needs
3. **Track Patterns**: Notice what works best for you
4. **Celebrate Wins**: Acknowledge every completed session
5. **Prepare Your Environment**: Minimize distractions before starting

## Common ADHD Challenges and Solutions

### Hyperfocus
- Set multiple alarms for breaks
- Use AI to monitor session length
- Have a trusted person check on you

### Task Switching Difficulty
- Use transition rituals between pomodoros
- Keep a "parking lot" for intrusive thoughts
- Plan the next task during breaks

### Rejection Sensitivity
- Focus on progress, not perfection
- Adjust expectations realistically
- Celebrate small wins consistently

## Building Long-term Success

### Week 1-2: Foundation
- Focus on completing any length of focused work
- Establish the habit of taking breaks
- Experiment with different timer apps

### Week 3-4: Optimization
- Find your optimal session length
- Identify best times of day for focused work
- Develop effective break routines

### Month 2+: Integration
- Combine with other ADHD management strategies
- Use AI insights to refine your approach
- Share successes with your support network

## Conclusion

The Pomodoro Technique, especially when enhanced with AI capabilities, can be a game-changer for individuals with ADHD. It provides the structure and flexibility needed to work with, rather than against, ADHD symptoms.

Remember: the goal isn't to "fix" ADHD, but to create systems that help you thrive with your unique brain. Start small, be patient with yourself, and celebrate every step forward.`,
    category: 'study-tips',
    tags: ['adhd', 'time management', 'pomodoro', 'focus', 'neurodiversity'],
    seoTitle: "ADHD Time Management: Pomodoro Technique Guide | AI Pomo",
    seoDescription: "Learn how the Pomodoro Technique can help with ADHD time management and focus challenges. Discover AI-enhanced strategies for better productivity with ADHD.",
    seoKeywords: "adhd time management, pomodoro technique adhd, focus strategies adhd, adhd productivity tips, neurodiversity productivity",
    readTime: 7,
    featured: false,
    published: true
  },

  {
    title: "5 Essential Study Techniques Every Student Should Master",
    excerpt: "Transform your study sessions with these proven techniques. From active recall to spaced repetition, discover the methods that top students use to excel academically.",
    content: `# 5 Essential Study Techniques Every Student Should Master

Effective studying isn't about spending more hours with your books—it's about using proven techniques that maximize learning and retention. Here are five evidence-based study methods every student should master.

## 1. Active Recall

Active recall involves testing yourself on material rather than simply re-reading notes. This technique forces your brain to retrieve information, strengthening memory pathways.

**How to implement:**
- Close your textbook and write down everything you remember
- Use flashcards to test key concepts
- Explain topics out loud without looking at notes
- Take practice tests regularly

## 2. Spaced Repetition

Instead of cramming, spaced repetition involves reviewing material at increasing intervals. This technique leverages the psychological spacing effect to improve long-term retention.

**Implementation strategy:**
- Review new material after 1 day, then 3 days, then 1 week, then 2 weeks
- Use apps like Anki or Quizlet for automated spacing
- Schedule review sessions in your calendar
- Focus more time on difficult concepts

## 3. The Feynman Technique

Named after physicist Richard Feynman, this technique involves explaining complex concepts in simple terms, as if teaching a child.

**Steps:**
1. Choose a concept to learn
2. Explain it in simple language
3. Identify gaps in your understanding
4. Review and simplify further

## 4. Pomodoro Study Sessions

Break study time into focused 25-minute sessions with 5-minute breaks. This prevents mental fatigue and maintains concentration.

**Study-specific tips:**
- Use different subjects for different pomodoros
- Take longer breaks between subjects
- Track which subjects require more pomodoros
- Adjust timing based on material difficulty

## 5. Interleaving

Instead of studying one subject for hours, mix different topics or subjects within a study session. This improves problem-solving skills and retention.

**Example schedule:**
- 25 min: Math problems
- 5 min: Break
- 25 min: History reading
- 5 min: Break
- 25 min: Science concepts
- 15 min: Longer break

## Combining Techniques for Maximum Effect

The most effective students combine multiple techniques:
- Use Pomodoro sessions for active recall practice
- Apply spaced repetition to Feynman technique explanations
- Interleave different types of active recall exercises

## Common Study Mistakes to Avoid

- **Highlighting without purpose**: Passive highlighting doesn't improve retention
- **Re-reading notes repeatedly**: This creates false confidence
- **Studying in the same location**: Vary your environment for better recall
- **Ignoring sleep**: Memory consolidation happens during sleep

## Creating Your Personal Study System

1. **Assess your current methods**: What's working and what isn't?
2. **Start with one technique**: Master active recall first
3. **Gradually add others**: Introduce spaced repetition next
4. **Track your progress**: Monitor grades and retention
5. **Adjust as needed**: Customize techniques to your learning style

## Conclusion

Mastering these five study techniques will transform your academic performance. Start implementing them gradually, and you'll see improvements in both understanding and grades.

Remember: effective studying is a skill that improves with practice. Be patient with yourself as you develop these new habits.`,
    category: 'study-tips',
    tags: ['study techniques', 'learning', 'education', 'memory', 'academic success'],
    seoTitle: "5 Essential Study Techniques for Academic Success | Student Guide",
    seoDescription: "Master these 5 proven study techniques: active recall, spaced repetition, Feynman technique, Pomodoro sessions, and interleaving. Boost your grades and retention.",
    seoKeywords: "study techniques, active recall, spaced repetition, feynman technique, study methods, academic success",
    readTime: 5,
    featured: false,
    published: true
  },

  {
    title: "Work-Life Balance in the Digital Age: Setting Boundaries That Stick",
    excerpt: "Struggling to disconnect from work? Learn practical strategies for creating healthy boundaries between work and personal life in our always-connected world.",
    content: `# Work-Life Balance in the Digital Age: Setting Boundaries That Stick

In our hyperconnected world, the line between work and personal life has become increasingly blurred. Here's how to establish and maintain healthy boundaries that protect your well-being and actually improve your productivity.

## The Digital Dilemma

Modern technology has created unprecedented flexibility in how and where we work. However, this same technology has made it difficult to truly "leave" work, leading to:

- Constant email checking
- After-hours work calls
- Weekend project stress
- Difficulty being present with family and friends
- Chronic stress and burnout

## The Foundation: Clear Boundaries

### 1. Define Your Work Hours

Establish specific start and end times for your workday, and communicate these clearly to colleagues and clients.

**Implementation tips:**
- Set automatic email responses outside work hours
- Use separate devices or accounts for work and personal use
- Create a physical "end of workday" ritual
- Block personal time in your calendar

### 2. Create Physical Boundaries

If working from home, establish a dedicated workspace that you can "leave" at the end of the day.

**Strategies:**
- Use a specific room or corner for work only
- Pack away work materials at day's end
- Change clothes to signal the transition
- Take a short walk to simulate a "commute" home

## Digital Boundary Strategies

### Email Management
- Check email at designated times only
- Use filters to prioritize urgent messages
- Set up auto-responses explaining your communication schedule
- Turn off non-essential notifications

### Phone and App Boundaries
- Remove work apps from your personal phone
- Use "Do Not Disturb" modes during personal time
- Create separate user profiles for work and personal use
- Establish phone-free zones (bedroom, dining table)

### Social Media and News
- Limit news consumption to specific times
- Unfollow accounts that increase work stress
- Use app timers to limit social media use
- Create "information fasts" during personal time

## The Pomodoro Approach to Work-Life Balance

Apply Pomodoro principles to your entire day:

**Work Blocks (25-50 minutes):**
- Focused work with no personal interruptions
- Phone on silent, personal apps closed
- Complete presence in work tasks

**Personal Blocks (25-50 minutes):**
- Complete disconnection from work
- Focus on family, hobbies, or self-care
- No work emails or calls

**Transition Breaks (5-15 minutes):**
- Brief periods to switch between work and personal modes
- Time to close work applications and open personal ones
- Mental reset between different life areas

## Building Sustainable Habits

### Week 1-2: Foundation
- Establish clear work hours
- Create physical workspace boundaries
- Set up basic digital boundaries

### Week 3-4: Refinement
- Fine-tune notification settings
- Develop end-of-workday rituals
- Practice saying no to after-hours requests

### Month 2+: Mastery
- Maintain boundaries consistently
- Help others respect your boundaries
- Regularly assess and adjust as needed

## Handling Pushback

When establishing boundaries, you may face resistance:

**From colleagues:**
- Explain how boundaries improve your work quality
- Suggest alternative communication methods for urgent issues
- Be consistent in maintaining your boundaries

**From yourself:**
- Recognize that constant availability doesn't equal productivity
- Remember that rest improves performance
- Focus on output quality, not hours worked

## The Benefits of Strong Boundaries

- **Improved focus** during work hours
- **Better relationships** with family and friends
- **Reduced stress** and anxiety
- **Increased creativity** and problem-solving ability
- **Better physical health** and sleep quality
- **Higher job satisfaction** and career longevity

## Emergency Protocols

Create clear criteria for when boundaries can be broken:
- Define what constitutes a true emergency
- Establish alternative contact methods for urgent issues
- Set expectations with your team about response times
- Have backup plans for when you're unavailable

## Conclusion

Setting boundaries in the digital age isn't about being less committed to your work—it's about being more strategic with your energy and attention. Strong boundaries lead to better performance, improved relationships, and greater life satisfaction.

Start small, be consistent, and remember that protecting your personal time ultimately makes you more effective during work hours.`,
    category: 'work-life-balance',
    tags: ['work-life balance', 'boundaries', 'digital wellness', 'productivity', 'stress management'],
    seoTitle: "Work-Life Balance: Digital Boundaries That Actually Work | AI Pomo",
    seoDescription: "Learn to set healthy work-life boundaries in the digital age. Practical strategies for disconnecting from work, managing technology, and protecting personal time.",
    seoKeywords: "work life balance, digital boundaries, remote work boundaries, work from home balance, technology boundaries",
    readTime: 6,
    featured: false,
    published: true
  }
];

// Function to get a random image for a category
const getRandomImageForCategory = async (category) => {
  try {
    const images = await BlogImage.find({
      $or: [
        { category: category },
        { tags: { $in: [category, 'productivity', 'pomodoro'] } }
      ],
      isActive: true
    });

    if (images.length > 0) {
      const randomIndex = Math.floor(Math.random() * images.length);
      return images[randomIndex].url;
    }

    // Fallback to any available image
    const fallbackImages = await BlogImage.find({ isActive: true }).limit(5);
    if (fallbackImages.length > 0) {
      const randomIndex = Math.floor(Math.random() * fallbackImages.length);
      return fallbackImages[randomIndex].url;
    }

    // Ultimate fallback
    return 'https://images.unsplash.com/photo-1484480974693-6ca0a78fb36b?w=1200&h=800&fit=crop&q=80';

  } catch (error) {
    console.error('Error getting image:', error);
    return 'https://images.unsplash.com/photo-1484480974693-6ca0a78fb36b?w=1200&h=800&fit=crop&q=80';
  }
};

// Function to create initial blog posts
const createInitialPosts = async () => {
  try {
    console.log('📝 Creating initial blog posts...');

    // Find admin user for author
    const adminUser = await User.findOne({ isAdmin: true });
    if (!adminUser) {
      throw new Error('No admin user found. Please create an admin user first.');
    }

    let created = 0;
    let skipped = 0;

    for (const postData of initialPosts) {
      try {
        // Check if post already exists
        const existingPost = await BlogPost.findOne({
          $or: [
            { title: postData.title },
            { slug: slugify(postData.title) }
          ]
        });

        if (existingPost) {
          console.log(`⏭️  Skipped: "${postData.title}" (already exists)`);
          skipped++;
          continue;
        }

        // Get category
        const category = await BlogCategory.findOne({ slug: postData.category });
        if (!category) {
          console.log(`⚠️  Category "${postData.category}" not found for post "${postData.title}"`);
          continue;
        }

        // Get random image for this category
        const coverImage = await getRandomImageForCategory(postData.category);

        // Create blog post
        const blogPost = new BlogPost({
          title: postData.title,
          slug: slugify(postData.title),
          excerpt: postData.excerpt,
          content: postData.content,
          coverImage: coverImage,
          category: category.name,
          tags: postData.tags,
          author: adminUser._id,  // Just the ObjectId, not an object
          seoTitle: postData.seoTitle,
          seoDescription: postData.seoDescription,
          seoKeywords: postData.seoKeywords,
          readTime: postData.readTime,
          featured: postData.featured,
          published: postData.published
        });

        await blogPost.save();
        console.log(`✅ Created: "${postData.title}"`);
        created++;

      } catch (error) {
        console.error(`❌ Error creating post "${postData.title}":`, error.message);
      }
    }

    console.log('\n📊 Blog Posts Creation Summary:');
    console.log(`✅ Created: ${created} new posts`);
    console.log(`⏭️  Skipped: ${skipped} existing posts`);
    console.log(`📝 Total posts: ${await BlogPost.countDocuments()}`);

    // Show featured posts
    const featuredPosts = await BlogPost.find({ featured: true, published: true });
    console.log(`⭐ Featured posts: ${featuredPosts.length}`);

    return { created, skipped };

  } catch (error) {
    console.error('❌ Failed to create initial posts:', error);
    throw error;
  }
};

// Function to list current posts
const listPosts = async () => {
  try {
    const posts = await BlogPost.find().sort({ createdAt: -1 });

    console.log(`\n📝 Current Blog Posts (${posts.length} total):`);
    console.log('='.repeat(80));

    posts.forEach((post, idx) => {
      const status = post.published ? '✅ Published' : '📝 Draft';
      const featured = post.featured ? '⭐ Featured' : '';

      console.log(`${idx + 1}. ${post.title} ${featured}`);
      console.log(`   Status: ${status} | Category: ${post.category} | Read Time: ${post.readTime}min`);
      console.log(`   Slug: ${post.slug}`);
      console.log(`   Tags: ${post.tags.join(', ')}`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ Failed to list posts:', error);
    throw error;
  }
};

// Main execution
const main = async () => {
  await connectDB();

  const command = process.argv[2];

  try {
    switch (command) {
      case 'create':
        await createInitialPosts();
        break;

      case 'list':
        await listPosts();
        break;

      case 'both':
        await createInitialPosts();
        await listPosts();
        break;

      default:
        console.log('📝 Initial Blog Posts Script:');
        console.log('');
        console.log('  node createInitialBlogPosts.js create  - Create initial blog posts');
        console.log('  node createInitialBlogPosts.js list    - List current blog posts');
        console.log('  node createInitialBlogPosts.js both    - Create and list posts');
        console.log('');
        console.log('💡 Note: Requires admin user and blog categories to be set up first');
        break;
    }
  } catch (error) {
    console.error('💥 Command failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
  }
};

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Script error:', error);
    process.exit(1);
  });
}

module.exports = { createInitialPosts, listPosts };
