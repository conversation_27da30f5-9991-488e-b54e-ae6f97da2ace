module.exports = {
  apps: [
    {
      name: "pomodoro-backend",
      cwd: "./server",
      script: "src/index.js",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "500M",
      env: {
        NODE_ENV: "production",
        PORT: 5000,
        MONGODB_URI: "**************************************************************************",
        JWT_SECRET: "YY1sui@sz",
        CLIENT_URL: "https://www.ai-pomo.com",
        // Paddle Configuration
        PADDLE_API_KEY: "pdl_live_apikey_01jxk437vp8js7emrf1jvdg2t5_nzBc8f9A1FXFQQBsBXFRDb_AIS",
        PADDLE_WEBHOOK_SECRET: "your_webhook_secret_from_paddle_dashboard",
        PADDLE_VENDOR_ID: "pro_01jxjzx642xnz9k48cwt9hv4r2",
        PADDLE_ENVIRONMENT: "production",
        PADDLE_MONTHLY_PRICE_ID: "pri_01jxk01813b3zfs36kcz66x0xg",
        PADDLE_YEARLY_PRICE_ID: "pri_01jxk02vycmv0zjfck99vcxqmt",
        PADDLE_LIFETIME_PRICE_ID: "pri_01jxk0448wa25ssmshke9c7r1m"
      },
      env_development: {
        NODE_ENV: "development",
        PORT: 5000,
        MONGODB_URI: "**************************************************************************",
        JWT_SECRET: "YY1sui@sz",
        CLIENT_URL: "http://localhost:3000"
      }
    },
    {
      name: "pomodoro-frontend",
      script: "serve",
      env: {
        PM2_SERVE_PATH: "./pomodoro-timer/build",
        PM2_SERVE_PORT: 3000,
        PM2_SERVE_SPA: "true",
        PM2_SERVE_HOMEPAGE: "/index.html",
        NODE_ENV: "production"
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "300M"
    }
  ]
};
