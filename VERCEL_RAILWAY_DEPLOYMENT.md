# AI Pomo - Vercel & Railway Deployment Guide

This guide walks you through deploying AI Pomo to Vercel (frontend) and Railway (backend + database).

## Prerequisites

- GitHub account
- Vercel account
- Railway account
- MongoDB Atlas account (recommended) or Railway MongoDB plugin

## Part 1: Database Setup

### Option A: MongoDB Atlas (Recommended)
1. Create a free MongoDB Atlas account at https://cloud.mongodb.com
2. Create a new cluster
3. Create a database user with read/write permissions
4. Get your connection string (format: `mongodb+srv://username:<EMAIL>/database_name`)
5. Whitelist Railway's IP addresses or use `0.0.0.0/0` for all IPs

### Option B: Railway MongoDB Plugin
1. In your Railway project, go to "Add Plugin"
2. Select MongoDB
3. Railway will provide the connection string automatically

## Part 2: Backend Deployment (Railway)

### 1. Create Railway Project
1. Go to https://railway.app
2. Sign up/login with GitHub
3. Click "Start a New Project"
4. Select "Deploy from GitHub repo"
5. Choose your AI Pomo repository
6. Set the root directory to `server`

### 2. Configure Environment Variables
In your Railway project settings, add these environment variables:

```
NODE_ENV=production
PORT=5000
MONGODB_URI=your_mongodb_connection_string_here
JWT_SECRET=your_super_secret_jwt_key_here
CLIENT_URL=https://your-vercel-app.vercel.app
PADDLE_API_KEY=your_paddle_api_key_here
PADDLE_WEBHOOK_SECRET=your_paddle_webhook_secret_here
PADDLE_VENDOR_ID=pro_01jxjzx642xnz9k48cwt9hv4r2
PADDLE_ENVIRONMENT=production
PADDLE_MONTHLY_PRICE_ID=pri_01jxk01813b3zfs36kcz66x0xg
PADDLE_YEARLY_PRICE_ID=pri_01jxk02vycmv0zjfck99vcxqmt
PADDLE_LIFETIME_PRICE_ID=pri_01jxk0448wa25ssmshke9c7r1m
```

### 3. Deploy Backend
1. Railway will automatically deploy when you push to your repository
2. Note your Railway app URL (e.g., `https://your-app.railway.app`)
3. Test the health endpoint: `https://your-app.railway.app/api/health`

## Part 3: Frontend Deployment (Vercel)

### 1. Create Vercel Project
1. Go to https://vercel.com
2. Sign up/login with GitHub
3. Click "New Project"
4. Import your AI Pomo repository
5. Set the root directory to `pomodoro-timer`

### 2. Configure Environment Variables
In your Vercel project settings, add these environment variables:

```
REACT_APP_API_URL=https://ai-pomo-production.up.railway.app
REACT_APP_PADDLE_VENDOR_ID=pro_01jxjzx642xnz9k48cwt9hv4r2
REACT_APP_PADDLE_CLIENT_TOKEN=your_paddle_client_token_here
REACT_APP_PADDLE_ENVIRONMENT=production
```

### 3. Deploy Frontend
1. Vercel will automatically deploy when you push to your repository
2. Note your Vercel app URL (e.g., `https://your-app.vercel.app`)

## Part 4: Update Cross-Origin Configuration

### 1. Update Backend CLIENT_URL
Go back to your Railway project and update the `CLIENT_URL` environment variable to your actual Vercel URL:
```
CLIENT_URL=https://your-app.vercel.app
```

### 2. Redeploy Backend
Railway will automatically redeploy with the new environment variable.

## Part 5: Database Migration (If Coming from Existing Server)

### 1. Export Data from Current Server
```bash
# From your current server
mongodump --uri="$MONGODB_URI" --out=./backup
```

### 2. Import Data to New Database
```bash
# To MongoDB Atlas or Railway MongoDB
mongorestore --uri="your_new_mongodb_connection_string" ./backup/pomodoro-timer
```

## Part 6: Domain Configuration (Optional)

### 1. Custom Domain for Frontend (Vercel)
1. In Vercel project settings, go to "Domains"
2. Add your custom domain (e.g., `ai-pomo.com`)
3. Configure DNS records as instructed by Vercel

### 2. Custom Domain for Backend (Railway)
1. In Railway project settings, go to "Settings" > "Domains"
2. Add your custom domain (e.g., `api.ai-pomo.com`)
3. Configure DNS records as instructed by Railway

## Part 7: Environment Variable Summary

### Frontend (Vercel)
- `REACT_APP_API_URL`: Your Railway backend URL
- `REACT_APP_PADDLE_VENDOR_ID`: Paddle vendor ID
- `REACT_APP_PADDLE_CLIENT_TOKEN`: Paddle client token
- `REACT_APP_PADDLE_ENVIRONMENT`: production

### Backend (Railway)
- `NODE_ENV`: production
- `PORT`: 5000
- `MONGODB_URI`: MongoDB connection string
- `JWT_SECRET`: Your JWT secret key
- `CLIENT_URL`: Your Vercel frontend URL
- `PADDLE_*`: All Paddle configuration variables

## Part 8: Testing Deployment

1. Visit your Vercel frontend URL
2. Try to register/login
3. Create a project and add tasks
4. Test the Pomodoro timer
5. Check payment functionality (if configured)

## Part 9: Monitoring and Logs

### Vercel Logs
- Go to your Vercel project dashboard
- Click on "Functions" tab to view serverless function logs

### Railway Logs
- Go to your Railway project dashboard
- Click on "Deployments" to view build and runtime logs

## Troubleshooting

### Common Issues

1. **CORS Errors**: Make sure CLIENT_URL in Railway matches your Vercel URL
2. **API Connection Failed**: Verify REACT_APP_API_URL in Vercel settings
3. **Database Connection**: Check MONGODB_URI format and network access
4. **Build Failures**: Check build logs in respective platforms

### Support

- Vercel: https://vercel.com/docs
- Railway: https://railway.app/help
- MongoDB Atlas: https://docs.atlas.mongodb.com

## Cost Estimation

### Free Tier Limits
- **Vercel**: 100GB bandwidth, unlimited requests
- **Railway**: $5/month credit, ~550 hours runtime
- **MongoDB Atlas**: 512MB storage, shared cluster

### Scaling Considerations
- Monitor usage in all platforms
- Consider upgrading to paid plans as user base grows
- Implement proper error handling and logging