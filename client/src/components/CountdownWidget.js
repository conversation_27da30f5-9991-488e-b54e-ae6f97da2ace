import React, { useState, useEffect, useRef } from 'react';

const handleInputChange = (value, type) => {
  // 移除非数字字符
  const numericValue = value.replace(/[^0-9]/g, '');
  
  if (type === 'min') {
    // 限制分钟输入在0-99之间
    const minValue = numericValue === '' ? '' : Math.min(parseInt(numericValue), 99).toString();
    setInputMin(minValue);
    // 立即保存
    if (minValue !== '' || inputSec !== '') {
      saveTimerCountdown(minValue, inputSec);
    }
  } else {
    // 限制秒数输入在0-59之间
    const secValue = numericValue === '' ? '' : Math.min(parseInt(numericValue), 59).toString();
    setInputSec(secValue);
    // 立即保存
    if (secValue !== '' || inputMin !== '') {
      saveTimerCountdown(inputMin, secValue);
    }
  }
};

const saveTimerCountdown = async (min, sec) => {
  try {
    if (isAuthenticated()) {
      const duration = parseInt(min || '0', 10) * 60 + parseInt(sec || '0', 10);
      console.log('Saving timer countdown:', { duration, timerCountdownId, min, sec });
      
      if (!duration) {
        if (timerCountdownId) {
          await countdownsApi.deleteCountdown(timerCountdownId);
          setTimerCountdownId(null);
        }
        return;
      }
      
      const countdownData = {
        type: 'timer',
        duration,
        min: min,
        sec: sec,
        title: 'Timer Countdown'
      };
      
      if (timerCountdownId) {
        const updated = await countdownsApi.updateCountdown(timerCountdownId, countdownData);
        console.log('Updated timer countdown:', updated);
        setTimerCountdownId(updated._id);
      } else {
        const created = await countdownsApi.createCountdown(countdownData);
        console.log('Created timer countdown:', created);
        setTimerCountdownId(created._id);
      }
    } else {
      // 未登录用户使用localStorage
      const obj = localStorage.getItem(LOCAL_KEY);
      let data = obj ? JSON.parse(obj) : {};
      if (!min && !sec) {
        if (data.timer) delete data.timer;
      } else {
        data.timer = { min, sec };
      }
      localStorage.setItem(LOCAL_KEY, JSON.stringify(data));
    }
  } catch (err) {
    console.error('Failed to save timer countdown:', err);
    setError('保存倒计时失败');
  }
};

function CountdownWidget() {
  const [inputMin, setInputMin] = useState('');
  const [inputSec, setInputSec] = useState('');
  const [activeTab, setActiveTab] = useState('timer');
  const [isRunning, setIsRunning] = useState(false);
  const [remainingTime, setRemainingTime] = useState(0);
  const timerRef = useRef(null);

  const RUNNING_KEY = 'countdown_running';

  const handleStart = () => {
    const totalSeconds = parseInt(inputMin || '0', 10) * 60 + parseInt(inputSec || '0', 10);
    if (!totalSeconds) return;
    setIsRunning(true);
    setRemainingTime(totalSeconds);
    const startTimestamp = Date.now();
    persistRunningState(true, totalSeconds, startTimestamp);
    startTimerInterval(totalSeconds, startTimestamp);
  };

  const startTimerInterval = (duration, startTimestamp) => {
    clearInterval(timerRef.current);
    timerRef.current = setInterval(() => {
      const elapsed = Math.floor((Date.now() - startTimestamp) / 1000);
      const left = Math.max(duration - elapsed, 0);
      setRemainingTime(left);
      if (left <= 0) {
        clearInterval(timerRef.current);
        setIsRunning(false);
        persistRunningState(false, 0, null);
      }
    }, 1000);
  };

  const handleReset = () => {
    clearInterval(timerRef.current);
    setIsRunning(false);
    setRemainingTime(0);
    persistRunningState(false, 0, null);
  };

  const persistRunningState = (running, time, startTimestamp) => {
    const data = { running, time, startTimestamp };
    if (isAuthenticated()) {
      // TODO: 可扩展为后端存储
      localStorage.setItem(RUNNING_KEY, JSON.stringify(data));
    } else {
      localStorage.setItem(RUNNING_KEY, JSON.stringify(data));
    }
  };

  useEffect(() => {
    const obj = localStorage.getItem(RUNNING_KEY);
    if (obj) {
      const { running, time, startTimestamp } = JSON.parse(obj);
      if (running && time && startTimestamp) {
        const elapsed = Math.floor((Date.now() - startTimestamp) / 1000);
        const left = Math.max(time - elapsed, 0);
        if (left > 0) {
          setIsRunning(true);
          setRemainingTime(left);
          startTimerInterval(time, startTimestamp);
        } else {
          setIsRunning(false);
          setRemainingTime(0);
          persistRunningState(false, 0, null);
        }
      }
    }
    return () => clearInterval(timerRef.current);
  }, []);

  return (
    <div>
      {/* Timer Mode Inputs */}
      {activeTab === 'timer' && (
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
          <input
            type="text"
            inputMode="numeric"
            pattern="[0-9]*"
            placeholder="MM"
            value={inputMin}
            onChange={e => handleInputChange(e.target.value, 'min')}
            disabled={isRunning}
            style={{ width: 40, textAlign: 'center', fontSize: '1.2rem' }}
          />
          <span style={{ fontSize: '1.2rem', margin: '0 4px' }}>:</span>
          <input
            type="text"
            inputMode="numeric"
            pattern="[0-9]*"
            placeholder="SS"
            value={inputSec}
            onChange={e => handleInputChange(e.target.value, 'sec')}
            disabled={isRunning}
            style={{ width: 40, textAlign: 'center', fontSize: '1.2rem' }}
          />
        </div>
      )}
      {/* Persistent Countdown Display */}
      <div style={{ fontSize: '2.5rem', fontWeight: 'bold', margin: '10px 0' }}>
        {isRunning
          ? `${String(Math.floor(remainingTime / 60)).padStart(2, '0')}:${String(remainingTime % 60).padStart(2, '0')}`
          : `${String(inputMin || '0').padStart(2, '0')}:${String(inputSec || '0').padStart(2, '0')}`}
      </div>
      <button onClick={handleStart} disabled={isRunning}>Start</button>
      <button onClick={handleReset}>Reset</button>
    </div>
  );
}

export default CountdownWidget; 