# MongoDB Database Tools Installation Guide

This guide will help you install MongoDB Database Tools on Windows and configure them properly for use with the backup functionality.

## Windows Installation

### Step 1: Download MongoDB Database Tools

1. Go to the [MongoDB Database Tools Download Page](https://www.mongodb.com/try/download/database-tools)
2. Select the following options:
   - Version: Latest version (100.9.4 or newer)
   - Platform: Windows
   - Package: ZIP
3. Click the "Download" button

### Step 2: Extract the ZIP File

1. Locate the downloaded ZIP file (e.g., `mongodb-database-tools-windows-x86_64-100.9.4.zip`)
2. Right-click on the ZIP file and select "Extract All..."
3. Choose a location to extract the files (e.g., `C:\Program Files\MongoDB\Tools\`)
4. Click "Extract"

### Step 3: Add MongoDB Tools to PATH

1. Press the Windows key and search for "Environment Variables"
2. Click on "Edit the system environment variables"
3. In the System Properties window, click on the "Environment Variables" button
4. In the "System variables" section, find the "Path" variable, select it, and click "Edit"
5. Click "New" and add the path to the bin directory of the extracted MongoDB Tools
   - Example: `C:\Program Files\MongoDB\Tools\mongodb-database-tools-windows-x86_64-100.9.4\bin`
6. Click "OK" on all dialogs to save the changes

### Step 4: Verify Installation

1. Open a new Command Prompt (important: must be a new window after changing PATH)
2. Type `mongodump --version` and press Enter
3. You should see version information for mongodump

## Docker Configuration (Alternative)

If you prefer to use Docker for backups instead of installing MongoDB Tools:

1. Make sure Docker is installed and running
2. Set the following environment variables in your server configuration:

```
MONGODB_CONTAINER_NAME=your-mongodb-container-name
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DATABASE=pomodoro
```

3. Restart the server application

## Troubleshooting

### MongoDB Tools Not Found After Installation

If you've installed MongoDB Tools but they're still not found:

1. Make sure you've added the correct path to your PATH environment variable
2. Make sure you're using a new Command Prompt window after changing PATH
3. Try restarting your computer

### Path Format Issues

Make sure the path you add to the PATH environment variable points directly to the bin directory containing the MongoDB Tools executables.

Example of correct path:
```
C:\Program Files\MongoDB\Tools\mongodb-database-tools-windows-x86_64-100.9.4\bin
```

### Docker Issues

If you're using Docker and experiencing issues:

1. Make sure Docker is running
2. Make sure your MongoDB container is running
3. Make sure you've set the correct container name in the environment variables

## Testing Installation

You can run the provided test script to check if MongoDB Tools are properly installed:

```
node test-mongodb-tools.js
```

This script will check if MongoDB Tools are in your PATH and if Docker is available as a fallback option.
