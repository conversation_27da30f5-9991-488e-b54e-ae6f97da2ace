# Paddle Integration - Final Deployment Checklist

## ✅ Configuration Complete
Your Paddle configuration is now complete with all the correct IDs:

### Your Paddle Information:
- **Vendor ID**: `pro_01jxjzx642xnz9k48cwt9hv4r2`
- **Client Token**: `live_9c5e1bb16afca4f9f2969c86119`
- **Monthly Price ID**: `pri_01jxk01813b3zfs36kcz66x0xg` ($8/month, 7-day trial)
- **Yearly Price ID**: `pri_01jxk02vycmv0zjfck99vcxqmt` ($60/year, 7-day trial)
- **Lifetime Price ID**: `pri_01jxk0448wa25ssmshke9c7r1m` ($100 one-time)

## 🚨 CRITICAL SECURITY STEP (MUST DO FIRST)
- [ ] **REVOKE the exposed API key** in Paddle Dashboard immediately
- [ ] **Generate a NEW API key** and keep it secure
- [ ] **Never share API keys** in chat or public places again

## 📋 Pre-Deployment Checklist

### 1. Paddle Dashboard Configuration
- [ ] Monthly subscription created ($8/month, 7-day trial)
- [ ] Yearly subscription created ($60/year, 7-day trial)  
- [ ] Lifetime product created ($100 one-time)
- [ ] **Notifications** configured with URL: `https://ai-pomo.com/api/paddle/webhook`
- [ ] **Notification events** enabled:
  - [ ] `transaction.completed`
  - [ ] `subscription.created`
  - [ ] `subscription.updated`
  - [ ] `subscription.cancelled`
  - [ ] `subscription.paused`
  - [ ] `subscription.resumed`
- [ ] New API key generated (replacing the exposed one)
- [ ] **Notification ID** recorded: `ntfset_01jxk2ty9a4x0p7hm6sjepxqwn`
- [ ] Note: If no secret key is provided, webhook verification will be skipped

### 2. Environment Variables Setup

#### Backend (.env in server directory):
```bash
PADDLE_API_KEY=your_NEW_api_key_here
PADDLE_WEBHOOK_SECRET=# Leave empty if not provided by Paddle
PADDLE_NOTIFICATION_SET_ID=ntfset_01jxk2ty9a4x0p7hm6sjepxqwn
PADDLE_VENDOR_ID=pro_01jxjzx642xnz9k48cwt9hv4r2
PADDLE_ENVIRONMENT=production
PADDLE_MONTHLY_PRICE_ID=pri_01jxk01813b3zfs36kcz66x0xg
PADDLE_YEARLY_PRICE_ID=pri_01jxk02vycmv0zjfck99vcxqmt
PADDLE_LIFETIME_PRICE_ID=pri_01jxk0448wa25ssmshke9c7r1m
PADDLE_WEBHOOK_URL=https://ai-pomo.com/api/paddle/webhook
```

#### Frontend (.env in pomodoro-timer directory):
```bash
REACT_APP_PADDLE_VENDOR_ID=pro_01jxjzx642xnz9k48cwt9hv4r2
REACT_APP_PADDLE_CLIENT_TOKEN=live_9c5e1bb16afca4f9f2969c86119
REACT_APP_PADDLE_ENVIRONMENT=production
```

#### PM2 Configuration (ecosystem.config.js):
- [ ] Add all Paddle environment variables to the `env` section

### 3. Code Deployment
- [ ] Push code to GitHub: `git push origin master`
- [ ] Deploy to production: `./deploy-pm2.sh`
- [ ] Verify all services are running: `pm2 status`
- [ ] Check logs for errors: `pm2 logs`

### 4. Testing & Verification

#### API Endpoints Testing:
- [ ] Test checkout creation: `POST /api/paddle/checkout`
- [ ] Test subscription status: `GET /api/paddle/subscription/status`
- [ ] Test webhook endpoint: `POST /api/paddle/webhook`

#### Frontend Testing:
- [ ] Visit `/premium` page
- [ ] Select monthly plan
- [ ] Complete test transaction
- [ ] Verify subscription activation
- [ ] Test yearly plan
- [ ] Test lifetime plan

#### Webhook Testing:
- [ ] Test webhook connectivity: `curl https://ai-pomo.com/api/paddle/webhook/test`
- [ ] Use Paddle's "Test Notification" feature
- [ ] Verify webhook events are received in logs
- [ ] Check database updates after webhook events

### 5. Production Verification
- [ ] Complete a real $1 test transaction
- [ ] Verify subscription shows as active
- [ ] Check user gets premium features
- [ ] Test subscription cancellation
- [ ] Verify webhook logs show proper processing

## 🎯 Success Criteria
- [ ] Users can see 3 subscription plans (Monthly, Yearly, Lifetime)
- [ ] Checkout opens correctly with Paddle interface
- [ ] Payments are processed automatically
- [ ] Subscriptions are activated immediately
- [ ] Webhooks update database correctly
- [ ] Premium features are unlocked after payment
- [ ] Email notifications are sent (if configured)

## 📞 Support Information
- **Paddle Documentation**: https://developer.paddle.com/
- **Webhook Testing**: Use Paddle Dashboard webhook testing tool
- **Payment Testing**: Use Paddle's test card numbers

## 🔄 Post-Launch Monitoring
- Monitor webhook events in Paddle Dashboard
- Check PM2 logs regularly: `pm2 logs`
- Monitor subscription activation rates
- Watch for payment failures or webhook issues

---

**You're almost ready to go live! Just complete the security step first, then follow this checklist step by step.**