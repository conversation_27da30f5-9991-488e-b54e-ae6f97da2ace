#!/bin/bash

# MongoDB Local Backup Script
# This script creates a backup of MongoDB and stores it locally
# Author: AI Pomo Team

# Exit on any error
set -e

# Load environment variables if .env file exists
if [ -f /path/to/your/app/.env.production ]; then
    source /path/to/your/app/.env.production
fi

# Configuration
TIMESTAMP=$(date +"%Y-%m-%d_%H-%M-%S")
BACKUP_DIR="/var/backups/mongodb"
BACKUP_FILENAME="mongodb_backup_${TIMESTAMP}.gz"
BACKUP_PATH="${BACKUP_DIR}/${BACKUP_FILENAME}"
LOG_FILE="/var/log/mongodb-backup.log"

# MongoDB connection details
MONGODB_HOST=${MONGODB_HOST:-"localhost"}
MONGODB_PORT=${MONGODB_PORT:-"27017"}
MONGODB_DATABASE=${MONGODB_DATABASE:-"pomodoro-timer"}
MONGODB_USERNAME=${MONGODB_USERNAME:-"hiruben"}
MONGODB_PASSWORD=${MONGODB_PASSWORD:-"changeme"}
MONGODB_AUTH_DB=${MONGODB_AUTH_DB:-"admin"}

# Retention configuration
RETENTION_DAYS=5  # Keep backups for 5 days

# Create backup directory if it doesn't exist
mkdir -p ${BACKUP_DIR}

# Function to log messages
log_message() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" | tee -a ${LOG_FILE}
}

# Start backup process
log_message "Starting MongoDB backup process..."

# Check if running in Docker
if command -v docker &> /dev/null && docker ps | grep -q 'pomodoro-mongodb'; then
    log_message "Docker detected, using Docker for backup..."
    
    # Get container name
    CONTAINER_NAME=$(docker ps | grep pomodoro-mongodb | awk '{print $NF}')
    log_message "Using MongoDB container: ${CONTAINER_NAME}"
    
    # Create backup using Docker
    log_message "Creating backup using Docker mongodump..."
    docker exec ${CONTAINER_NAME} mongodump \
        --host=${MONGODB_HOST} \
        --port=${MONGODB_PORT} \
        --username=${MONGODB_USERNAME} \
        --password=${MONGODB_PASSWORD} \
        --authenticationDatabase=${MONGODB_AUTH_DB} \
        --db=${MONGODB_DATABASE} \
        --archive \
        --gzip > ${BACKUP_PATH}
else
    # Create backup using mongodump directly
    log_message "Creating backup using mongodump..."
    mongodump \
        --host=${MONGODB_HOST} \
        --port=${MONGODB_PORT} \
        --username=${MONGODB_USERNAME} \
        --password=${MONGODB_PASSWORD} \
        --authenticationDatabase=${MONGODB_AUTH_DB} \
        --db=${MONGODB_DATABASE} \
        --archive=${BACKUP_PATH} \
        --gzip
fi

# Check if backup was created successfully
if [ -f "${BACKUP_PATH}" ]; then
    BACKUP_SIZE=$(du -h "${BACKUP_PATH}" | cut -f1)
    log_message "Backup created successfully: ${BACKUP_PATH} (${BACKUP_SIZE})"
    
    # Create a symlink to the latest backup
    LATEST_LINK="${BACKUP_DIR}/latest_backup.gz"
    ln -sf "${BACKUP_PATH}" "${LATEST_LINK}"
    log_message "Created symlink to latest backup: ${LATEST_LINK}"
    
    # Add entry to backup log file
    echo "${TIMESTAMP},${BACKUP_FILENAME},${BACKUP_SIZE}" >> "${BACKUP_DIR}/backup_history.csv"
else
    log_message "Error: Backup file was not created"
    exit 1
fi

# Clean up old backups
log_message "Cleaning up old backups (older than ${RETENTION_DAYS} days)..."
find ${BACKUP_DIR} -name "mongodb_backup_*.gz" -type f -mtime +${RETENTION_DAYS} -delete

# Count remaining backups
BACKUP_COUNT=$(find ${BACKUP_DIR} -name "mongodb_backup_*.gz" | wc -l)
log_message "Current backup count: ${BACKUP_COUNT} (keeping backups for ${RETENTION_DAYS} days)"

# Calculate total backup size
TOTAL_SIZE=$(du -sh ${BACKUP_DIR} | cut -f1)
log_message "Total backup size: ${TOTAL_SIZE}"

log_message "Backup process completed successfully!"
exit 0
