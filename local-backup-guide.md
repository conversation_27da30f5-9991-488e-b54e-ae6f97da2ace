# MongoDB Local Backup Guide

This guide will help you set up local backups for your MongoDB database on a Debian server.

## Prerequisites

- A Debian-based server with root or sudo access
- MongoDB running in Docker or installed directly on the server
- Sufficient disk space for backups

## Step 1: Install MongoDB Database Tools

If you're using MongoDB in Docker, you might still need the MongoDB Database Tools on the host for backup operations:

```bash
# Import MongoDB public key
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -

# Add MongoDB repository
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/debian $(lsb_release -cs)/mongodb-org/6.0 main" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list

# Update package database
sudo apt-get update

# Install MongoDB Database Tools
sudo apt-get install -y mongodb-database-tools
```

## Step 2: Configure the Backup Script

Edit the backup script to match your MongoDB configuration:

```bash
# MongoDB connection details
MONGODB_HOST="localhost"
MONGODB_PORT="27017"
MONGODB_DATABASE="pomodoro-timer"
MONGODB_USERNAME="hiruben"
MONGODB_PASSWORD="changeme"
MONGODB_AUTH_DB="admin"

# Retention configuration
RETENTION_DAYS=5  # Keep backups for 5 days
```

## Step 3: Set Up the Backup Directory

The default backup directory is `/var/backups/mongodb`. Make sure it exists and has the correct permissions:

```bash
sudo mkdir -p /var/backups/mongodb
sudo chmod 750 /var/backups/mongodb
```

## Step 4: Test the Backup Script

Run the backup script manually to ensure it works correctly:

```bash
sudo bash /path/to/mongodb-backup-local.sh
```

Check the log file for any errors:

```bash
cat /var/log/mongodb-backup.log
```

## Step 5: Set Up the Cron Job

Add a cron job to run the backup script daily:

```bash
sudo crontab -e
```

Add the following line to run the backup at 2:00 AM every day:

```
0 2 * * * /opt/mongodb-backup/mongodb-backup-local.sh >> /var/log/mongodb-backup.log 2>&1
```

Save and exit the editor.

## Backup Retention

By default, backups older than 5 days are automatically deleted. You can change this by editing the `RETENTION_DAYS` variable in the backup script.

## Restoring from Backup

To restore from a backup:

```bash
# For a direct MongoDB installation
mongorestore --host localhost --port 27017 -u your_username -p your_password --authenticationDatabase admin --gzip --archive=/var/backups/mongodb/mongodb_backup_YYYY-MM-DD_HH-MM-SS.gz
```

For Docker-based MongoDB:

```bash
# Restore using Docker
cat /var/backups/mongodb/mongodb_backup_YYYY-MM-DD_HH-MM-SS.gz | docker exec -i your-mongodb-container mongorestore --host localhost --port 27017 -u your_username -p your_password --authenticationDatabase admin --gzip --archive
```

You can also use the latest backup symlink:

```bash
cat /var/backups/mongodb/latest_backup.gz | docker exec -i your-mongodb-container mongorestore --host localhost --port 27017 -u your_username -p your_password --authenticationDatabase admin --gzip --archive
```

## Monitoring Backup Size

To check the size of your backups:

```bash
# Check total backup size
du -sh /var/backups/mongodb

# List all backups with their sizes
du -h /var/backups/mongodb/*.gz | sort -h
```

## Troubleshooting

### MongoDB Connection Issues

If you have trouble connecting to MongoDB:

```bash
# Test MongoDB connection
mongosh --host localhost --port 27017 -u your_username -p your_password --authenticationDatabase admin
```

### Docker Container Issues

If you're using Docker and encounter issues:

```bash
# Check if the MongoDB container is running
docker ps | grep mongodb

# Get the exact container name
docker ps --format "{{.Names}}" | grep mongo
```

### Disk Space Issues

If you're running out of disk space:

```bash
# Check available disk space
df -h

# Reduce the retention period in the backup script
# Edit RETENTION_DAYS=5 to a smaller number
```

## Security Considerations

- The backup script contains your MongoDB credentials. Make sure it's only readable by root:
  ```bash
  sudo chmod 700 /opt/mongodb-backup/mongodb-backup-local.sh
  ```

- Consider encrypting sensitive backups:
  ```bash
  # Add encryption to the backup script
  openssl enc -aes-256-cbc -salt -in "${BACKUP_PATH}" -out "${BACKUP_PATH}.enc" -k "your-secure-password"
  ```
