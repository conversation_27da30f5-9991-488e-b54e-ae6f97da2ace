#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 需要处理的文件和对应的修复
const fixes = [
  {
    file: 'pomodoro-timer/src/AppWithAuth.js',
    changes: [
      // 移除未使用的导入
      { find: "import React, { useState, useEffect, useRef } from 'react';", replace: "import React, { useState, useEffect } from 'react';" },
      { find: "import ProjectDetail from './components/ProjectDetail';", replace: "// import ProjectDetail from './components/ProjectDetail';" },
      { find: "import ProjectDescriptionCard from './components/ProjectDescriptionCard';", replace: "// import ProjectDescriptionCard from './components/ProjectDescriptionCard';" },
      { find: "import AdminSubscriptionPage from './components/AdminSubscriptionPage';", replace: "// import AdminSubscriptionPage from './components/AdminSubscriptionPage';" },
      { find: "import CategoryPillarPage from './components/resources/CategoryPillarPage';", replace: "// import CategoryPillarPage from './components/resources/CategoryPillarPage';" },
      { find: "import { getCurrentUser, isAuthenticated, logout } from './services/authService';", replace: "import { getCurrentUser, isAuthenticated } from './services/authService';" },
      { find: "import { taskApi, pomodoroApi, statsApi, userApi, projectApi, milestoneApi } from './services/apiService';", replace: "import { taskApi, pomodoroApi, statsApi, userApi, projectApi } from './services/apiService';" }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/AIProjectGenerator.js',
    changes: [
      { find: "import React, { useState, useEffect, useRef } from 'react';", replace: "import React, { useState, useEffect } from 'react';" },
      { find: "import AILoadingScreen from './AILoadingScreen';", replace: "// import AILoadingScreen from './AILoadingScreen';" }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/AppFlowyStyleLandingPage.js',
    changes: [
      { find: "import { FaArrowRight, FaBars, FaTimes, FaCheck, FaExpand, FaCompress, FaPlus, FaMinus, FaGithub } from 'react-icons/fa';", replace: "import { FaTimes, FaExpand, FaPlus, FaMinus } from 'react-icons/fa';" },
      { find: "import { isAuthenticated } from '../services/authService';", replace: "// import { isAuthenticated } from '../services/authService';" }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/Calendar.js',
    changes: [
      { find: "import eventBus from '../utils/eventBus';", replace: "// import eventBus from '../utils/eventBus';" }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/GmailStyleLandingPage.js',
    changes: [
      { find: "import { FaCheck, FaGoogle, FaTimes, FaArrowRight } from 'react-icons/fa';", replace: "import { FaCheck, FaGoogle, FaTimes } from 'react-icons/fa';" }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/MilestoneTimeline.js',
    changes: [
      { find: "import { FaCalendarAlt, FaFlag, FaCheck, FaPlus, FaTimes } from 'react-icons/fa';", replace: "import { FaCalendarAlt, FaFlag, FaPlus, FaTimes } from 'react-icons/fa';" },
      { find: "import { format, parseISO } from 'date-fns';", replace: "import { parseISO } from 'date-fns';" }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/PremiumPage.js',
    changes: [
      { find: "import { FaArrowRight, FaCheck, FaCrown } from 'react-icons/fa';", replace: "import { FaArrowRight, FaCrown } from 'react-icons/fa';" }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/ProjectCard.js',
    changes: [
      { find: "import React, { useState } from 'react';", replace: "import React from 'react';" }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/TaskList.js',
    changes: [
      { find: "import { taskApi, milestoneApi } from '../services/apiService';", replace: "import { taskApi } from '../services/apiService';" },
      { find: "import { FaRegCircle, FaCircle, FaTasks } from 'react-icons/fa';", replace: "import { FaTasks } from 'react-icons/fa';" },
      { find: "import PomodoroIcon, { PomodoroIconWrapper } from './PomodoroIcon';", replace: "import PomodoroIcon from './PomodoroIcon';" }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/admin/AdminDashboard.js',
    changes: [
      { find: "import { FaUsers, FaChartLine, FaTasks, FaPlay, FaExclamationTriangle } from 'react-icons/fa';", replace: "import { FaUsers, FaChartLine, FaTasks, FaPlay } from 'react-icons/fa';" },
      { find: "import { FaProjectDiagram, FaCalendarWeek, FaHistory, FaClock, FaCalendarPlus } from 'react-icons/fa';", replace: "import { FaProjectDiagram, FaClock } from 'react-icons/fa';" }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/admin/AdminEnhancedSubscriptionPage.js',
    changes: [
      { find: "import { FaSearch, FaFilter, FaSort, FaEdit, FaTrash, FaDownload, FaSpinner, FaCheckCircle } from 'react-icons/fa';", replace: "import { FaSearch, FaSort, FaEdit, FaTrash, FaDownload, FaSpinner, FaCheckCircle } from 'react-icons/fa';" },
      { find: "} from 'react-icons/fa';", replace: "} from 'react-icons/fa';\n// eslint-disable-next-line no-unused-vars" }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/admin/AdminUserManagement.js',
    changes: [
      { find: "import { FaEdit, FaSave, FaTrash, FaCancel } from 'react-icons/fa';", replace: "import { FaEdit, FaSave, FaCancel } from 'react-icons/fa';" },
      { find: "import { FaBan, FaCrown, FaCheck, FaTimes } from 'react-icons/fa';", replace: "import { FaBan, FaCrown } from 'react-icons/fa';" }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/admin/EnhancedTextEditor.js',
    changes: [
      { find: "import { FaBold, FaItalic, FaUnderline, FaAlignLeft, FaAlignCenter, FaAlignRight, FaListUl, FaListOl, FaHeading, FaQuoteRight, FaCode, FaLink, FaImage, FaVideo } from 'react-icons/fa';", replace: "import { FaBold, FaItalic, FaUnderline, FaListUl, FaListOl, FaHeading, FaQuoteRight, FaCode, FaLink, FaImage, FaVideo } from 'react-icons/fa';" }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/admin/PillarPageEditor.js',
    changes: [
      { find: "import { FaEdit, FaTrash, FaPlus, FaArrowUp, FaArrowDown, FaGripVertical, FaSave, FaTimes, FaBold, FaItalic, FaLink, FaListUl, FaListOl, FaQuoteRight, FaHeading, FaCode, FaImage, FaVideo } from 'react-icons/fa';", replace: "import { FaEdit, FaTrash, FaPlus, FaArrowUp, FaArrowDown, FaSave, FaTimes, FaBold, FaItalic, FaLink, FaListUl, FaListOl, FaQuoteRight, FaHeading, FaCode, FaImage, FaVideo } from 'react-icons/fa';" }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/blog/BlogPage.js',
    changes: [
      { find: "import { Link } from 'react-router-dom';", replace: "// import { Link } from 'react-router-dom';" },
      { find: "import { FaCalendarAlt, FaClock, FaUser, FaSearch } from 'react-icons/fa';", replace: "import { FaUser, FaSearch } from 'react-icons/fa';" }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/blog/BlogPostList.js',
    changes: [
      { find: "import { Link } from 'react-router-dom';", replace: "// import { Link } from 'react-router-dom';" }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/resources/AllArticlesPage.js',
    changes: [
      { find: "import { FaSearch, FaFilter, FaSort } from 'react-icons/fa';", replace: "import { FaSearch, FaSort } from 'react-icons/fa';" },
      { find: "import BlogPostCard from '../blog/BlogPostCard';", replace: "// import BlogPostCard from '../blog/BlogPostCard';" }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/resources/CategoryPillarPage.js',
    changes: [
      { find: "import PillarPage from './PillarPage';", replace: "// import PillarPage from './PillarPage';" }
    ]
  },
  {
    file: 'pomodoro-timer/src/contexts/GlobalTimerContext.js',
    changes: [
      { find: "import { pomodoroApi, settingsApi } from '../services/apiService';", replace: "import { pomodoroApi } from '../services/apiService';" }
    ]
  },
  {
    file: 'pomodoro-timer/src/pages/ContactPage.js',
    changes: [
      { find: "import { FaEnvelope, FaPhone, FaMapMarkerAlt } from 'react-icons/fa';", replace: "import { FaEnvelope } from 'react-icons/fa';" }
    ]
  },
  {
    file: 'pomodoro-timer/src/pages/PricingPage.js',
    changes: [
      { find: "import { FaCheck, FaRocket, FaCrown, FaInfinity } from 'react-icons/fa';", replace: "import { FaCheck, FaCrown, FaInfinity } from 'react-icons/fa';" }
    ]
  },
  {
    file: 'pomodoro-timer/src/services/aiService.js',
    changes: [
      { find: "Example:\\nWrite a 2000-word essay on climate change\\Z\\n", replace: "Example:\\nWrite a 2000-word essay on climate change\\n" },
      { find: "Example:\\nDevelop a fitness app for seniors\\Z\\n", replace: "Example:\\nDevelop a fitness app for seniors\\n" },
      { find: "Example: Create a mobile app for food delivery\\Z\\n", replace: "Example: Create a mobile app for food delivery\\n" }
    ]
  }
];

// 执行修复
fixes.forEach(({ file, changes }) => {
  const filePath = path.join(__dirname, '..', file);
  
  if (!fs.existsSync(filePath)) {
    console.error(`File not found: ${filePath}`);
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  changes.forEach(({ find, replace }) => {
    if (content.includes(find)) {
      content = content.replace(find, replace);
      modified = true;
    }
  });
  
  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`✓ Fixed ${file}`);
  }
});

console.log('\nDone! All ESLint errors should be fixed.');