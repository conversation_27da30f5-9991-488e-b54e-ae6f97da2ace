#!/usr/bin/env node

/**
 * AI Pomo Deployment Configuration Checker
 * This script verifies that all necessary configuration is in place for Vercel/Railway deployment
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function printStep(message) {
  console.log(colorize(`[CHECK] ${message}`, 'blue'));
}

function printSuccess(message) {
  console.log(colorize(`✓ ${message}`, 'green'));
}

function printWarning(message) {
  console.log(colorize(`⚠ ${message}`, 'yellow'));
}

function printError(message) {
  console.log(colorize(`✗ ${message}`, 'red'));
}

function checkFileExists(filePath, description) {
  const fullPath = path.resolve(filePath);
  if (fs.existsSync(fullPath)) {
    printSuccess(`${description} exists: ${filePath}`);
    return true;
  } else {
    printError(`${description} missing: ${filePath}`);
    return false;
  }
}

function checkFrontendConfig() {
  printStep('Checking frontend configuration...');
  
  let allGood = true;
  
  // Check vercel.json
  if (!checkFileExists('./pomodoro-timer/vercel.json', 'Vercel config')) {
    allGood = false;
  }
  
  // Check package.json has vercel-build script
  try {
    const packageJson = JSON.parse(fs.readFileSync('./pomodoro-timer/package.json', 'utf8'));
    if (packageJson.scripts && packageJson.scripts['vercel-build']) {
      printSuccess('Frontend has vercel-build script');
    } else {
      printError('Frontend missing vercel-build script');
      allGood = false;
    }
    
    // Check if proxy is removed
    if (packageJson.proxy) {
      printWarning('Frontend still has proxy configuration - should be removed for Vercel');
    } else {
      printSuccess('Frontend proxy configuration removed');
    }
  } catch (error) {
    printError('Could not read frontend package.json');
    allGood = false;
  }
  
  // Check environment variable example
  if (!checkFileExists('./pomodoro-timer/.env.vercel.example', 'Frontend env example')) {
    allGood = false;
  }
  
  return allGood;
}

function checkBackendConfig() {
  printStep('Checking backend configuration...');
  
  let allGood = true;
  
  // Check railway.json
  if (!checkFileExists('./server/railway.json', 'Railway config')) {
    allGood = false;
  }
  
  // Check environment variable example
  if (!checkFileExists('./server/.env.railway.example', 'Backend env example')) {
    allGood = false;
  }
  
  // Check if health endpoint exists
  try {
    const indexJs = fs.readFileSync('./server/src/index.js', 'utf8');
    if (indexJs.includes('/api/health')) {
      printSuccess('Backend has health check endpoint');
    } else {
      printError('Backend missing health check endpoint');
      allGood = false;
    }
    
    // Check CORS configuration
    if (indexJs.includes('process.env.CLIENT_URL')) {
      printSuccess('Backend CORS uses CLIENT_URL environment variable');
    } else {
      printWarning('Backend CORS might not be using CLIENT_URL environment variable');
    }
  } catch (error) {
    printError('Could not read backend index.js');
    allGood = false;
  }
  
  return allGood;
}

function checkMigrationTools() {
  printStep('Checking migration tools...');
  
  let allGood = true;
  
  // Check migration script
  if (!checkFileExists('./scripts/migrate-to-cloud.sh', 'Database migration script')) {
    allGood = false;
  }
  
  // Check deployment guide
  if (!checkFileExists('./VERCEL_RAILWAY_DEPLOYMENT.md', 'Deployment guide')) {
    allGood = false;
  }
  
  return allGood;
}

function checkRequiredEnvVars() {
  printStep('Checking required environment variables documentation...');
  
  const requiredFrontendVars = [
    'REACT_APP_API_URL',
    'REACT_APP_PADDLE_VENDOR_ID',
    'REACT_APP_PADDLE_CLIENT_TOKEN',
    'REACT_APP_PADDLE_ENVIRONMENT'
  ];
  
  const requiredBackendVars = [
    'NODE_ENV',
    'PORT',
    'MONGODB_URI',
    'JWT_SECRET',
    'CLIENT_URL',
    'PADDLE_API_KEY',
    'PADDLE_WEBHOOK_SECRET',
    'PADDLE_VENDOR_ID',
    'PADDLE_ENVIRONMENT'
  ];
  
  // Check if these are documented in the env examples
  try {
    const frontendEnvExample = fs.readFileSync('./pomodoro-timer/.env.vercel.example', 'utf8');
    const backendEnvExample = fs.readFileSync('./server/.env.railway.example', 'utf8');
    
    let allDocumented = true;
    
    requiredFrontendVars.forEach(varname => {
      if (frontendEnvExample.includes(varname)) {
        printSuccess(`Frontend env variable documented: ${varname}`);
      } else {
        printError(`Frontend env variable not documented: ${varname}`);
        allDocumented = false;
      }
    });
    
    requiredBackendVars.forEach(varname => {
      if (backendEnvExample.includes(varname)) {
        printSuccess(`Backend env variable documented: ${varname}`);
      } else {
        printError(`Backend env variable not documented: ${varname}`);
        allDocumented = false;
      }
    });
    
    return allDocumented;
  } catch (error) {
    printError('Could not read environment variable examples');
    return false;
  }
}

function generateDeploymentSummary() {
  console.log('\n' + colorize('='.repeat(60), 'cyan'));
  console.log(colorize('DEPLOYMENT SUMMARY', 'cyan'));
  console.log(colorize('='.repeat(60), 'cyan'));
  
  console.log('\n' + colorize('Next steps:', 'yellow'));
  console.log('1. Push your code to GitHub');
  console.log('2. Create a Railway project and connect to your GitHub repo');
  console.log('3. Set Railway root directory to "server"');
  console.log('4. Configure Railway environment variables from .env.railway.example');
  console.log('5. Create a Vercel project and connect to your GitHub repo');
  console.log('6. Set Vercel root directory to "pomodoro-timer"');
  console.log('7. Configure Vercel environment variables from .env.vercel.example');
  console.log('8. Update CLIENT_URL in Railway to your Vercel domain');
  console.log('9. Run database migration script if needed');
  console.log('10. Test your deployment!');
  
  console.log('\n' + colorize('Documentation:', 'yellow'));
  console.log('- Read VERCEL_RAILWAY_DEPLOYMENT.md for detailed instructions');
  console.log('- Use scripts/migrate-to-cloud.sh for database migration');
  
  console.log('\n' + colorize('Monitoring:', 'yellow'));
  console.log('- Check Railway logs for backend issues');
  console.log('- Check Vercel function logs for frontend issues');
  console.log('- Test health endpoint: https://your-railway-app.railway.app/api/health');
}

function main() {
  console.log(colorize('AI Pomo Deployment Configuration Checker', 'cyan'));
  console.log(colorize('=====================================', 'cyan'));
  console.log();
  
  const frontendOk = checkFrontendConfig();
  console.log();
  
  const backendOk = checkBackendConfig();
  console.log();
  
  const migrationOk = checkMigrationTools();
  console.log();
  
  const envVarsOk = checkRequiredEnvVars();
  console.log();
  
  const allGood = frontendOk && backendOk && migrationOk && envVarsOk;
  
  if (allGood) {
    printSuccess('All deployment configuration checks passed!');
  } else {
    printError('Some configuration issues found. Please fix them before deploying.');
  }
  
  generateDeploymentSummary();
  
  process.exit(allGood ? 0 : 1);
}

main();