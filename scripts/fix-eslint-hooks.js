#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 修复 React Hooks 依赖和未使用变量
const hookFixes = [
  {
    file: 'pomodoro-timer/src/AppWithAuth.js',
    fixes: [
      // 注释掉未使用的 projectsLoading
      { 
        find: 'const { projects, projectsLoading, fetchProjects } = projectApi.useProjects();',
        replace: 'const { projects, fetchProjects } = projectApi.useProjects();'
      },
      // 修复 useEffect 依赖
      {
        find: `useEffect(() => {
    if (!isAuthenticated()) {
      setActiveTab('login');
    }
  }, []);`,
        replace: `useEffect(() => {
    if (!isAuthenticated()) {
      setActiveTab('login');
    }
  }, [setActiveTab]);`
      }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/AdminSubscriptionPage.js',
    fixes: [
      {
        find: `useEffect(() => {
    fetchSubscriptions();
  }, []);`,
        replace: `useEffect(() => {
    fetchSubscriptions();
  }, [fetchSubscriptions]);`
      }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/AppFlowyStyleLandingPage.js',
    fixes: [
      // 移除未使用的状态
      {
        find: 'const [isMenuOpen, setIsMenuOpen] = useState(false);',
        replace: '// const [isMenuOpen, setIsMenuOpen] = useState(false);'
      },
      {
        find: 'const [isSliderHovered, setIsSliderHovered] = useState(false);',
        replace: 'const [isSliderHovered] = useState(false);'
      }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/Calendar.js',
    fixes: [
      {
        find: `useEffect(() => {
    fetchCalendarData();
  }, [selectedProject, milestones]);`,
        replace: `useEffect(() => {
    fetchCalendarData();
  }, [selectedProject, milestones, fetchCalendarData]);`
      }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/MilestoneTimeline.js',
    fixes: [
      // 移除未使用的变量
      {
        find: 'const todayPosition = getPositionOnTimeline(today);',
        replace: '// const todayPosition = getPositionOnTimeline(today);'
      }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/ProjectList.js',
    fixes: [
      {
        find: 'const { notes, tasks, detailsLoading } = projectDetails;',
        replace: '// const { notes, tasks, detailsLoading } = projectDetails;'
      },
      {
        find: `useEffect(() => {
    fetchProjects();
  }, []);`,
        replace: `useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);`
      }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/admin/AdminDashboard.js',
    fixes: [
      {
        find: 'const [timeRange, setTimeRange] = useState(\'week\');',
        replace: '// const [timeRange, setTimeRange] = useState(\'week\');'
      },
      {
        find: `useEffect(() => {
    fetchStats();
  }, []);`,
        replace: `useEffect(() => {
    fetchStats();
  }, [fetchStats]);`
      }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/admin/AdminEnhancedSubscriptionPage.js',
    fixes: [
      {
        find: 'const [rejectModalOpen, setRejectModalOpen] = useState(false);',
        replace: '// const [rejectModalOpen, setRejectModalOpen] = useState(false);'
      },
      {
        find: 'const [userDetailsModalOpen, setUserDetailsModalOpen] = useState(false);',
        replace: '// const [userDetailsModalOpen, setUserDetailsModalOpen] = useState(false);'
      },
      {
        find: 'const [batchActionModalOpen, setBatchActionModalOpen] = useState(false);',
        replace: '// const [batchActionModalOpen, setBatchActionModalOpen] = useState(false);'
      },
      {
        find: 'const [paymentHistory, setPaymentHistory] = useState([]);',
        replace: '// const [paymentHistory, setPaymentHistory] = useState([]);'
      },
      {
        find: 'const [paymentHistoryModalOpen, setPaymentHistoryModalOpen] = useState(false);',
        replace: '// const [paymentHistoryModalOpen, setPaymentHistoryModalOpen] = useState(false);'
      },
      {
        find: 'const [userStats, setUserStats] = useState(null);',
        replace: '// const [userStats, setUserStats] = useState(null);'
      }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/admin/AdminUserManagement.js',
    fixes: [
      {
        find: `useEffect(() => {
    fetchUsers();
  }, []);`,
        replace: `useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);`
      }
    ]
  },
  {
    file: 'pomodoro-timer/src/components/admin/AnalyticsDashboard.js',
    fixes: [
      {
        find: `useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);`,
        replace: `useEffect(() => {
    fetchAnalytics();
  }, [timeRange, fetchAnalytics]);`
      }
    ]
  },
  {
    file: 'pomodoro-timer/src/contexts/GlobalTimerContext.js',
    fixes: [
      {
        find: 'const [settingsInitialized, setSettingsInitialized] = useState(false);',
        replace: '// const [settingsInitialized, setSettingsInitialized] = useState(false);'
      },
      {
        find: 'const [lastUpdatedTime, setLastUpdatedTime] = useState(Date.now());',
        replace: '// const [lastUpdatedTime, setLastUpdatedTime] = useState(Date.now());'
      }
    ]
  },
  {
    file: 'pomodoro-timer/src/pages/StandalonePomodoroPage.js',
    fixes: [
      {
        find: `const {
    timerState,
    isRunning,
    isPaused,
    timeRemaining,
    pomodoroCount,
    startTimer,
    pauseTimer,
    resumeTimer,
    resetTimer,
    skipTimer
  } = useGlobalTimer();`,
        replace: `const {
    timerState,
    timeRemaining
  } = useGlobalTimer();`
      }
    ]
  },
  {
    file: 'pomodoro-timer/src/services/apiService.js',
    fixes: [
      {
        find: 'const apiCallsInProgress = new Set();',
        replace: '// const apiCallsInProgress = new Set();'
      }
    ]
  }
];

// 应用修复
hookFixes.forEach(({ file, fixes }) => {
  const filePath = path.join(__dirname, '..', file);
  
  if (!fs.existsSync(filePath)) {
    console.error(`File not found: ${filePath}`);
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  fixes.forEach(({ find, replace }) => {
    if (content.includes(find)) {
      content = content.replace(find, replace);
      modified = true;
    }
  });
  
  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`✓ Fixed hooks/deps in ${file}`);
  }
});

console.log('\nDone! React Hooks dependencies should be fixed.');