#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Find all JS files with API_URL paths
const srcDir = path.join(__dirname, '..', 'pomodoro-timer', 'src');

// API endpoints that need to be updated
const apiEndpoints = [
  '/auth/',
  '/users/',
  '/tasks/',
  '/pomodoros/',
  '/stats/',
  '/projects/',
  '/milestones/',
  '/reminders/',
  '/countdowns/',
  '/aggregate/',
  '/standalone-tasks/',
  '/fast-tasks/',
  '/active-timer/',
  '/contact/',
  '/subscriptions/',
  '/paddle/',
  '/admin/',
  '/blog/',
  '/blog-images/',
  '/pillar-pages/',
  '/api-keys/',
  '/analytics/',
  '/content-export/'
];

function updateFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  apiEndpoints.forEach(endpoint => {
    // Replace patterns like ${API_URL}/auth/ with ${API_URL}/api/auth/
    const oldPattern = `\${API_URL}${endpoint}`;
    const newPattern = `\${API_URL}/api${endpoint}`;
    
    if (content.includes(oldPattern)) {
      content = content.replace(new RegExp(escapeRegExp(oldPattern), 'g'), newPattern);
      modified = true;
      console.log(`Updated ${endpoint} in ${filePath}`);
    }
  });

  if (modified) {
    fs.writeFileSync(filePath, content);
    return true;
  }
  return false;
}

function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function walkDir(dir) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      walkDir(filePath);
    } else if (file.endsWith('.js')) {
      updateFile(filePath);
    }
  });
}

console.log('Updating API paths to include /api prefix...');
walkDir(srcDir);
console.log('Done!');