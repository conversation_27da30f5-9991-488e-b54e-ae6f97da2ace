# Pomodoro Timer Application

A feature-rich Pomodoro timer application built with <PERSON><PERSON> to help you boost productivity and manage your work sessions effectively.

## Features

### Authentication and Data Persistence
- User registration and login
- Google OAuth integration
- MongoDB database for data storage
- Cross-device synchronization

### Core Timer Functionality
- Precise countdown timer for work and break sessions
- User-customizable session durations
- Clear visual progress feedback
- Manual controls (start, pause, stop/reset, skip)
- Session persistence across page reloads

### Notifications and Sound
- Sound alerts for session completion
- Multiple sound options
- Volume control
- Optional ticking sound during work sessions

### Task Management
- Task list with add, edit, and delete functionality
- Task association with Pomodoro sessions
- Task completion tracking
- Pomodoro estimation for tasks

### UI/UX
- Minimalist design
- Clear typography and visual hierarchy
- Light/dark theme support
- Responsive design for all devices

### Productivity Statistics
- Track completed Pomodoros
- Streak tracking
- Statistics display

### Rest Quality Enhancement
- Guided rest activities library
- Optional participation in guided activities

### Gamification
- Points/experience system
- Achievements/badges
- Visual streak tracking

## Getting Started

### Prerequisites
- Node.js and npm installed on your machine
- Docker and Docker Compose (for MongoDB)

### Installation

#### 1. Start MongoDB with Docker Compose

```bash
# From the project root directory
docker-compose up -d
```

This will start:
- MongoDB on port 27017
- Mongo Express (web-based MongoDB admin interface) on port 8081

You can access Mongo Express at http://localhost:8081 with:
- Username: admin
- Password: password

The MongoDB connection uses environment variables:
- Username: Set via MONGODB_USERNAME
- Password: Set via MONGODB_PASSWORD  
- Authentication Database: admin

#### 2. Start the Backend Server

```bash
# Navigate to the server directory
cd server

# Install dependencies
npm install

# Initialize the database (first time only)
npm run init-db

# Start the development server
npm run dev
```

The server will run on http://localhost:5000.

#### 3. Start the Frontend Development Server

```bash
# Navigate to the frontend directory
cd pomodoro-timer

# Install dependencies
npm install

# Start the development server
npm start
```

The application will open in your browser at http://localhost:3000.

## Usage

1. Set your preferred timer durations in the settings
2. Add tasks you want to work on
3. Select a task and start the timer
4. Work until the timer ends
5. Take a break when prompted
6. Repeat the process to boost your productivity

## Docker Commands

```bash
# Start containers
docker-compose up -d

# Stop containers
docker-compose down

# View logs
docker-compose logs

# Stop and remove containers, networks, and volumes
docker-compose down -v
```

## Environment Variables

### Backend (.env file in server directory)

```
PORT=5000
MONGODB_URI=mongodb://localhost:27017/pomodoro-timer?authSource=admin
JWT_SECRET=your_jwt_secret_key_change_this_in_production
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
CLIENT_URL=http://localhost:3000
```

### Frontend (.env file in pomodoro-timer directory)

```
REACT_APP_API_URL=http://localhost:5000
```

## Technologies Used

### Frontend
- React
- Styled Components
- React Icons
- Web Audio API
- Axios for API requests
- React Router for navigation

### Backend
- Node.js with Express
- MongoDB for data storage
- JWT for authentication
- Passport.js for Google OAuth
- Bcrypt for password hashing

### Infrastructure
- Docker and Docker Compose for containerization

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- The Pomodoro Technique was developed by Francesco Cirillo
- Inspired by various Pomodoro timer applications and productivity tools
