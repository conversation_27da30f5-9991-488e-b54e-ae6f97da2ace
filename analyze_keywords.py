#!/usr/bin/env python3
import csv
import re
from pathlib import Path
import sys

# 定义现有页面关键词
existing_pages = {
    'countdown-timer', 'online-timer', 'tomato-timer', 'cute-timer', 
    'homework-timer', 'classroom-timer', 'study-timer', 'work-timer', 
    'break-timer', 'custom-timer', 'visual-timer', 'pomodoro-timer'
}

# 工具类关键词模式和对应的页面建议
tool_categories = {
    'stopwatch': {
        'patterns': [r'stopwatch', r'stop.*watch'],
        'suggested_pages': ['online-stopwatch', 'digital-stopwatch', 'web-stopwatch']
    },
    'alarm': {
        'patterns': [r'alarm.*clock', r'alarm.*timer', r'online.*alarm', r'digital.*alarm'],
        'suggested_pages': ['online-alarm', 'alarm-clock', 'digital-alarm-clock']
    },
    'interval': {
        'patterns': [r'interval.*timer', r'timer.*interval', r'custom.*interval'],
        'suggested_pages': ['interval-timer', 'custom-interval-timer', 'workout-interval-timer']
    },
    'focus': {
        'patterns': [r'focus.*timer', r'timer.*focus'],
        'suggested_pages': ['focus-timer', 'concentration-timer']
    },
    'productivity': {
        'patterns': [r'productivity.*timer', r'timer.*productivity'],
        'suggested_pages': ['productivity-timer', 'work-productivity-timer']
    },
    'kitchen': {
        'patterns': [r'kitchen.*timer', r'cooking.*timer', r'baking.*timer', r'tea.*timer', r'egg.*timer'],
        'suggested_pages': ['kitchen-timer', 'cooking-timer', 'tea-timer', 'egg-timer']
    },
    'exercise': {
        'patterns': [r'exercise.*timer', r'workout.*timer', r'fitness.*timer', r'boxing.*timer', r'tabata.*timer', r'hiit.*timer'],
        'suggested_pages': ['exercise-timer', 'workout-timer', 'fitness-timer', 'tabata-timer', 'hiit-timer']
    },
    'meditation': {
        'patterns': [r'meditation.*timer', r'mindfulness.*timer', r'breathing.*timer', r'relax.*timer'],
        'suggested_pages': ['meditation-timer', 'breathing-timer', 'mindfulness-timer']
    },
    'creative': {
        'patterns': [r'writing.*timer', r'reading.*timer', r'creative.*timer'],
        'suggested_pages': ['writing-timer', 'reading-timer', 'creative-timer']
    },
    'sleep': {
        'patterns': [r'sleep.*timer', r'nap.*timer', r'rest.*timer'],
        'suggested_pages': ['sleep-timer', 'nap-timer', 'rest-timer']
    },
    'digital': {
        'patterns': [r'digital.*timer', r'online.*clock', r'web.*timer', r'browser.*timer'],
        'suggested_pages': ['digital-timer', 'web-clock', 'browser-timer']
    },
    'ambient': {
        'patterns': [r'ambient.*timer', r'nature.*timer', r'rain.*timer', r'forest.*timer', r'ocean.*timer', r'white.*noise.*timer'],
        'suggested_pages': ['ambient-timer', 'nature-sounds-timer', 'rain-timer', 'forest-timer']
    }
}

def main():
    csv_files = [
        '/home/<USER>/文档/ai-pomo/ai-pomo/keywords/pomofocus.keywords_2025-06-17T17_00_40.525Z.csv',
        '/home/<USER>/文档/ai-pomo/ai-pomo/keywords/studywithme.io.keywords_2025-06-17T17_09_38.635Z.csv',
        '/home/<USER>/文档/ai-pomo/ai-pomo/keywords/todoist.keywords_2025-06-17T16_41_01.421Z.csv',
        '/home/<USER>/文档/ai-pomo/ai-pomo/keywords/tomatotimers.com-organic.Positions-us-20250616-2025-06-17T17_15_10Z.csv'
    ]
    
    all_keywords = []
    
    for file_path in csv_files:
        try:
            # 尝试不同的编码
            encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        reader = csv.DictReader(f)
                        headers = reader.fieldnames
                        
                        # 找到关键词列
                        keyword_col = None
                        for col in headers:
                            if 'keyword' in col.lower():
                                keyword_col = col
                                break
                        
                        if keyword_col is None:
                            print(f"未找到关键词列在文件: {Path(file_path).name}")
                            print(f"可用列: {headers}")
                            break
                        
                        # 找到搜索量列
                        volume_col = None
                        for col in headers:
                            if any(word in col.lower() for word in ['volume', 'search volume', 'traffic']):
                                volume_col = col
                                break
                        
                        print(f"处理文件: {Path(file_path).name}")
                        print(f"关键词列: {keyword_col}")
                        print(f"搜索量列: {volume_col}")
                        
                        # 读取数据
                        row_count = 0
                        for row in reader:
                            row_count += 1
                            keyword = str(row[keyword_col]).lower().strip()
                            if keyword and keyword != 'nan' and keyword:
                                volume = 0
                                if volume_col and row[volume_col]:
                                    try:
                                        volume_str = str(row[volume_col]).replace(',', '')
                                        volume = int(float(volume_str))
                                    except:
                                        volume = 0
                                
                                all_keywords.append({
                                    'keyword': keyword,
                                    'volume': volume,
                                    'source': Path(file_path).stem
                                })
                        
                        print(f"处理了 {row_count} 行数据")
                        break
                        
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    print(f"使用编码 {encoding} 处理文件时出错: {e}")
                    continue
        
        except Exception as e:
            print(f"处理文件时出错 {file_path}: {e}")
    
    # 分析关键词
    print(f"总共分析了 {len(all_keywords)} 个关键词")
    print("\n=== 关键词分析结果 ===")
    
    # 按类别分析
    category_matches = {}
    high_value_keywords = []
    
    for kw_data in all_keywords:
        keyword = kw_data['keyword']
        volume = kw_data['volume']
        
        # 如果搜索量大于1000，记录为高价值关键词
        if volume > 1000:
            high_value_keywords.append(kw_data)
        
        # 检查是否匹配工具类别
        for category, config in tool_categories.items():
            for pattern in config['patterns']:
                if re.search(pattern, keyword, re.IGNORECASE):
                    if category not in category_matches:
                        category_matches[category] = []
                    category_matches[category].append(kw_data)
                    break
    
    # 输出结果
    print("\n=== 按类别分析的工具关键词 ===")
    for category, matches in category_matches.items():
        print(f"\n{category.upper()} 相关关键词 ({len(matches)} 个):")
        
        # 按搜索量排序
        sorted_matches = sorted(matches, key=lambda x: x['volume'], reverse=True)
        
        for match in sorted_matches[:10]:  # 显示前10个
            print(f"  - {match['keyword']} (搜索量: {match['volume']:,})")
        
        print(f"  建议页面: {', '.join(tool_categories[category]['suggested_pages'])}")
    
    # 高价值关键词分析
    print(f"\n=== 高价值关键词 (搜索量 > 1000) ===")
    high_value_sorted = sorted(high_value_keywords, key=lambda x: x['volume'], reverse=True)
    
    for kw_data in high_value_sorted[:30]:  # 显示前30个
        print(f"  - {kw_data['keyword']} (搜索量: {kw_data['volume']:,})")
    
    # 生成页面建议
    print(f"\n=== 建议创建的页面 (基于关键词分析) ===")
    
    priority_pages = []
    
    # 基于搜索量和类别重要性评分
    for category, matches in category_matches.items():
        total_volume = sum(match['volume'] for match in matches)
        avg_volume = total_volume / len(matches) if matches else 0
        
        for suggested_page in tool_categories[category]['suggested_pages']:
            # 检查是否已存在
            if suggested_page not in existing_pages:
                priority_pages.append({
                    'page': suggested_page,
                    'category': category,
                    'total_volume': total_volume,
                    'avg_volume': avg_volume,
                    'keyword_count': len(matches)
                })
    
    # 排序并输出
    priority_pages.sort(key=lambda x: x['total_volume'], reverse=True)
    
    print("\n高优先级页面:")
    for page in priority_pages[:10]:
        print(f"  1. {page['page']}")
        print(f"     类别: {page['category']}")
        print(f"     总搜索量: {page['total_volume']:,}")
        print(f"     平均搜索量: {page['avg_volume']:.0f}")
        print(f"     相关关键词数: {page['keyword_count']}")
        print()

if __name__ == "__main__":
    main()