name: pomodoro-timer

services:
  # MongoDB service
  mongodb:
    image: mongo:latest
    container_name: pomodoro-mongodb
    restart: always
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: hiruben
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD:-changeme}
      MONGO_INITDB_DATABASE: pomodoro-timer
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - pomodoro-network

  # MongoDB Express web-based admin interface (optional)
  mongo-express:
    image: mongo-express:latest
    container_name: pomodoro-mongo-express
    restart: always
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: hiruben
      ME_CONFIG_MONGODB_ADMINPASSWORD: ${MONGO_PASSWORD:-changeme}
      ME_CONFIG_MONGODB_SERVER: mongodb
    depends_on:
      - mongodb
    networks:
      - pomodoro-network

volumes:
  mongodb_data:
    name: pomodoro-mongodb-data

networks:
  pomodoro-network:
    driver: bridge
