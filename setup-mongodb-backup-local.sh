#!/bin/bash

# Setup script for MongoDB local backup cron job
# This script installs necessary dependencies and sets up a cron job for MongoDB backups
# Author: AI Pomo Team

# Exit on any error
set -e

# Check if running as root
if [ "$(id -u)" -ne 0 ]; then
    echo "This script must be run as root. Please use sudo."
    exit 1
fi

# Configuration
BACKUP_SCRIPT_PATH="/opt/mongodb-backup/mongodb-backup-local.sh"
BACKUP_SCRIPT_DIR=$(dirname "$BACKUP_SCRIPT_PATH")
LOG_DIR="/var/log"
BACKUP_LOG="$LOG_DIR/mongodb-backup.log"
CRON_SCHEDULE="0 2 * * *"  # 2:00 AM every day

# Create directories
echo "Creating directories..."
mkdir -p "$BACKUP_SCRIPT_DIR"
touch "$BACKUP_LOG"
chmod 640 "$BACKUP_LOG"

# Copy backup script
echo "Installing backup script..."
cp "$(dirname "$0")/mongodb-backup-local.sh" "$BACKUP_SCRIPT_PATH"
chmod +x "$BACKUP_SCRIPT_PATH"

# Install MongoDB Database Tools if not already installed
if ! command -v mongodump &> /dev/null; then
    echo "MongoDB Database Tools not found. Installing..."
    
    # Import MongoDB public key
    wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | apt-key add -
    
    # Add MongoDB repository
    echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/debian $(lsb_release -cs)/mongodb-org/6.0 main" | tee /etc/apt/sources.list.d/mongodb-org-6.0.list
    
    # Update package database
    apt-get update
    
    # Install MongoDB Database Tools
    apt-get install -y mongodb-database-tools
    
    echo "MongoDB Database Tools installed successfully."
else
    echo "MongoDB Database Tools already installed."
fi

# Set up cron job
echo "Setting up cron job..."
(crontab -l 2>/dev/null || echo "") | grep -v "$BACKUP_SCRIPT_PATH" | { cat; echo "$CRON_SCHEDULE $BACKUP_SCRIPT_PATH >> $BACKUP_LOG 2>&1"; } | crontab -

echo "Cron job installed successfully."
echo "Backup will run daily at 2:00 AM."
echo "You can edit the schedule by running: crontab -e"
echo ""
echo "IMPORTANT: Before running the backup, please:"
echo "1. Edit $BACKUP_SCRIPT_PATH to update your MongoDB connection settings"
echo "2. Make sure your MongoDB server is running and accessible"
echo "3. Ensure you have enough disk space for backups"
echo ""
echo "Setup completed successfully!"
