# MongoDB Local Backup

This package contains scripts to set up automated daily backups of your MongoDB database to local storage.

## Files Included

1. `mongodb-backup-local.sh` - The main backup script that creates MongoDB backups and stores them locally
2. `setup-mongodb-backup-local.sh` - A setup script that installs dependencies and configures the cron job
3. `local-backup-guide.md` - A detailed guide for setting up local backups
4. `README-local-backup.md` - This file

## Quick Start Guide

### Step 1: Copy Files to Your Server

Copy all files to your Debian server:

```bash
scp mongodb-backup-local.sh setup-mongodb-backup-local.sh local-backup-guide.md README-local-backup.md user@your-server:/tmp/
```

### Step 2: Configure the Backup Script

Edit the backup script to match your MongoDB configuration:

```bash
nano mongodb-backup-local.sh
```

Update the following variables:

```bash
# MongoDB connection details
MONGODB_HOST="localhost"
MONGODB_PORT="27017"
MONGODB_DATABASE="pomodoro-timer"
MONGODB_USERNAME="hiruben"
MONGODB_PASSWORD="changeme"
MONGODB_AUTH_DB="admin"

# Retention configuration
RETENTION_DAYS=5  # Keep backups for 5 days
```

### Step 3: Run the Setup Script

Run the setup script with sudo to install dependencies and set up the cron job:

```bash
sudo bash setup-mongodb-backup-local.sh
```

This will:
1. Install MongoDB Database Tools (if not already installed)
2. Copy the backup script to `/opt/mongodb-backup/`
3. Set up a cron job to run the backup daily at 2:00 AM

### Step 4: Test the Backup

Run the backup script manually to test it:

```bash
sudo /opt/mongodb-backup/mongodb-backup-local.sh
```

Check the log file for any errors:

```bash
cat /var/log/mongodb-backup.log
```

## Customizing the Backup Schedule

The default schedule is to run the backup daily at 2:00 AM. To change this:

```bash
sudo crontab -e
```

Edit the cron expression. For example, to run at 3:30 AM:

```
30 3 * * * /opt/mongodb-backup/mongodb-backup-local.sh >> /var/log/mongodb-backup.log 2>&1
```

## Backup Retention

By default, backups older than 5 days are automatically deleted. You can change this by editing the `RETENTION_DAYS` variable in the backup script.

## Restoring from Backup

To restore from a backup, see the "Restoring from Backup" section in `local-backup-guide.md`.

## Troubleshooting

If you encounter issues:

1. Check the log file: `/var/log/mongodb-backup.log`
2. Ensure MongoDB is running and accessible
3. Make sure the backup script has the correct MongoDB connection details

For more detailed troubleshooting, refer to `local-backup-guide.md`.

## Security Considerations

- The backup script contains your MongoDB credentials. Make sure it's only readable by root:
  ```bash
  sudo chmod 700 /opt/mongodb-backup/mongodb-backup-local.sh
  ```

- Consider encrypting sensitive backups:
  ```bash
  # Add encryption to the backup script
  openssl enc -aes-256-cbc -salt -in "${BACKUP_PATH}" -out "${BACKUP_PATH}.enc" -k "your-secure-password"
  ```

## Future Enhancements

When you're ready to set up Google Drive uploads, you can use the `mongodb-backup-to-gdrive.sh` script and follow the instructions in `gdrive-setup-guide.md`.

## Support

If you need assistance with these scripts, please contact the AI Pomo Team.
