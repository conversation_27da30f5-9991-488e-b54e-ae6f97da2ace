#!/bin/bash

# Make script exit on any error
set -e

echo "Downgrading Express from version 5 to version 4.18.2..."

# Navigate to the server directory
cd /var/www/pomodoro-timer/server

# Uninstall Express version 5
echo "Uninstalling Express version 5..."
npm uninstall express

# Install Express version 4.18.2
echo "Installing Express version 4.18.2..."
npm install express@4.18.2 --save

# Restart the backend
echo "Restarting the backend service..."
cd ..
pm2 restart pomodoro-backend

# Check the backend logs
echo "Checking backend logs for errors..."
sleep 5
pm2 logs pomodoro-backend --lines 20

echo "Express downgrade completed. Check the logs above for any errors."
echo "If there are no path-to-regexp errors, the downgrade was successful."
