# Vercel 部署调试步骤

## 错误分析
错误 `404: NOT_FOUND` 通常表示 Vercel 无法找到构建产物或路由配置有问题。

## 修复步骤

### 1. 检查 Vercel 项目设置
确保在 Vercel 项目设置中：
- **Root Directory**: 设置为 `pomodoro-timer`
- **Build Command**: 默认 `npm run build` 或留空
- **Output Directory**: 默认 `build` 或留空
- **Install Command**: 默认 `npm install` 或使用 `npm install --legacy-peer-deps`

### 2. 环境变量配置
在 Vercel 项目设置 > Environment Variables 中添加：
```
REACT_APP_API_URL=https://your-railway-app.railway.app
REACT_APP_PADDLE_VENDOR_ID=pro_01jxjzx642xnz9k48cwt9hv4r2
REACT_APP_PADDLE_CLIENT_TOKEN=your_paddle_client_token_here
REACT_APP_PADDLE_ENVIRONMENT=production
```

### 3. 构建命令问题排查
如果构建失败，尝试在 Vercel 项目设置中：
- **Install Command**: `npm install --legacy-peer-deps`
- **Build Command**: `npm run build`

### 4. 检查构建日志
在 Vercel 部署页面：
1. 点击失败的部署
2. 查看 "Build Logs" 
3. 查找任何错误信息

### 5. 常见问题和解决方案

#### 问题 1: 依赖安装失败
如果看到 peer dependency 错误：
```bash
# 在 Vercel 项目设置中设置 Install Command 为：
npm install --legacy-peer-deps
```

#### 问题 2: 构建超时或内存不足
在 Vercel 项目设置中：
- **Node.js Version**: 设置为 18.x 或 20.x
- 考虑升级到 Pro 计划以获得更多构建资源

#### 问题 3: 路由问题
确保 `vercel.json` 配置正确（已修复）

### 6. 手动测试构建
本地测试构建是否成功：
```bash
cd pomodoro-timer
npm install --legacy-peer-deps
npm run build
```

如果本地构建成功，问题可能在 Vercel 配置。

### 7. 临时解决方案
如果问题持续，可以尝试：
1. 删除 `vercel.json` 文件，让 Vercel 使用默认配置
2. 重新部署

### 8. 联系支持
如果问题仍然存在：
1. 截图构建日志错误
2. 提供项目设置截图
3. 检查 Vercel 状态页面: https://vercel-status.com/