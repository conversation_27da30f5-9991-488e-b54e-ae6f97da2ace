# Railway 部署快速修复指南

## 问题原因
Railway 正在尝试构建整个项目（包括前端），而不是只构建后端。

## 解决方案

### 在 Railway 项目设置中：

1. **General Settings** → **Root Directory**
   - 设置为: `server`
   - 不要留空，也不要设置为 `/server`

2. **确保环境变量已设置**：
   ```
   MONGODB_URI=${{ MongoDB.MONGO_URL }}
   NODE_ENV=production
   PORT=5000
   JWT_SECRET=your_secret_key_here
   CLIENT_URL=https://your-vercel-app.vercel.app
   ```

3. **重新部署**
   - 点击 "Redeploy" 按钮

## 为什么这样能解决问题？

- **之前**: Railway 尝试构建整个仓库（44MB+），包括前端代码
- **现在**: 只构建 server 目录（20MB），只有后端代码
- **结果**: 部署时间从 20 分钟减少到 1-2 分钟

## 验证
部署完成后，访问：
```
https://your-app.railway.app/api/health
```

应该看到健康检查响应。