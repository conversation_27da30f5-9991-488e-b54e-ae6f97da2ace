# Railway 环境变量设置指南

## 必需的环境变量

在 Railway 项目的 Variables 页面设置以下环境变量：

### 1. 数据库连接（使用 Railway MongoDB 插件）
```
MONGODB_URI=${{ MongoDB.MONGO_URL }}
```
⚠️ 注意：直接使用 Railway 的引用变量语法，不要复制实际的 URL

### 2. 基础配置
```
NODE_ENV=production
PORT=5000
JWT_SECRET=your_super_secret_jwt_key_here_change_this
CLIENT_URL=https://your-vercel-app.vercel.app
```

### 3. Paddle 支付系统（如果启用）
```
PADDLE_API_KEY=your_paddle_api_key
PADDLE_WEBHOOK_SECRET=your_paddle_webhook_secret
PADDLE_VENDOR_ID=pro_01jxjzx642xnz9k48cwt9hv4r2
PADDLE_ENVIRONMENT=production
PADDLE_MONTHLY_PRICE_ID=pri_01jxk01813b3zfs36kcz66x0xg
PADDLE_YEARLY_PRICE_ID=pri_01jxk02vycmv0zjfck99vcxqmt
PADDLE_LIFETIME_PRICE_ID=pri_01jxk0448wa25ssmshke9c7r1m
```

### 4. 邮件配置（可选）
```
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_email_password
```

## 重要提示

1. **MongoDB 连接**：使用 `${{ MongoDB.MONGO_URL }}` 而不是硬编码的 URL
2. **JWT_SECRET**：必须修改为一个强密码
3. **CLIENT_URL**：设置为你的 Vercel 前端 URL
4. **Paddle 配置**：如果不使用付费功能，可以跳过这些变量

## 验证部署

部署后访问健康检查端点：
```
https://your-railway-app.railway.app/api/health
```

应该返回：
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T12:00:00.000Z",
  "service": "ai-pomo-backend"
}
```