# AI Pomo Application - Functionality Specification

## 1. Overview

The AI Pomo Application is a comprehensive productivity tool that implements the Pomodoro Technique with advanced task management, project organization, and progress tracking features. The application helps users manage their time effectively by breaking work into focused intervals (pomodoros) separated by short breaks.

### 1.1 Core Functionality

The application provides the following core features:
- Customizable Pomodoro timer with work sessions, short breaks, and long breaks
- Task management with estimated and actual pomodoro counts (using 🍅 emoji)
- Project organization with milestones, notes, and deadlines
- AI-powered project generation from natural language descriptions
- Statistics and progress tracking with calendar visualization
- Reminders and countdown timers with browser notifications
- Dark/Light mode theme support
- MongoDB database storage for data persistence
- Responsive design for different screen sizes
- Sound settings for timer ticks and completion alerts
- User authentication and authorization (email and Google login)
- Timezone handling with standardized storage (UTC) and localized display
- Subscription model with different tiers of features

### 1.2 Key Principles

The application follows these key principles:
- **Focus on One Task**: Only one task can be active at a time
- **Project Limitations**: Maximum of 3 open projects in free plan, with only one in working state
- **Data Persistence**: All data is stored in MongoDB database
- **User Authentication**: Unauthenticated users cannot use the application
- **Consistent Timer**: Timer state is consistent across standalone timer and project detail pages
- **Completed Pomodoros Only**: Only completed pomodoros are counted in statistics
- **Interrupted Sessions**: If a timer is interrupted by switching tasks or closing the browser, it's abandoned
- **User Experience**: Smooth transitions and responsive design
- **Security**: JWT-based authentication and authorization

## 2. Core Components

### 2.1 AI Project Generator

- **Core Functionality**:
  - Accessible via the "AI" navigation item (first item in the navigation menu)
  - Allows users to generate complete project structures from natural language descriptions
  - Uses DeepSeek API to process descriptions and generate structured project data
  - Displays generated project structure for user review before creation
  - Creates projects with tasks, subtasks, milestones, and notes
  - Navigates to the projects tab with the newly created project selected after creation

- **User Interface**:
  - Clean, intuitive interface with clear instructions
  - Text input area for project description with sample text
  - Option to toggle between simple and detailed examples
  - Generate button to process the description
  - Preview section showing the generated project structure
  - Create Project button to finalize the project creation
  - Cancel button to discard the generated project

- **Project Generation Process**:
  - User enters a natural language description of their project
  - System sends the description to DeepSeek API with a structured prompt template
  - API returns a JSON structure with project details, milestones, tasks, subtasks, and notes
  - System displays the generated structure for user review and adjustment
  - User can create the project or cancel and try again
  - On creation, the system creates all related database records

- **Limitations and Validations**:
  - Respects the 3 open projects limit for free users
  - Validates user authentication before project creation
  - Provides clear error messages for any issues during generation or creation
  - Handles API errors gracefully with user-friendly messages

### 2.2 Pomodoro Timer

- **Timer Types**:
  - Work Session (25 minutes default, customizable, saved in database)
  - Short Break (5 minutes default, customizable, saved in database)
  - Long Break (15 minutes default, customizable, saved in database)
- **Timer Controls**:
  - Start/Pause/Resume/Reset functionality
  - Skip session functionality (doesn't increase pomodoro count)
  - Sound settings with volume control
  - Browser notifications for session completion
- **Timer Behavior**:
  - Work sessions require an assigned project and task
  - Break sessions are not associated with any project or task
  - Timer state is consistent across all pages
  - Session completion records data in the database (for work sessions)
  - Timer is reset when switching active tasks (with warning prompt)
  - Timer conflict warning when switching between projects with running timers

### 2.3 Task Management

- **Task Properties**:
  - Title and completion status
  - Estimated pomodoros (1-10)
  - Actual pomodoros completed (represented by 🍅 emoji)
  - Due date and creation timestamp
  - Project association
  - Subtasks with their own estimated pomodoro counts
- **Task Operations**:
  - Create tasks with title and estimated pomodoros
  - Add subtasks (manually or AI-generated)
  - Delete tasks and mark as complete/incomplete
  - Set task as active for pomodoro focus
  - Track pomodoro progress (actual vs estimated)
- **Task Organization**:
  - Tasks organized under specific projects
  - Split into ongoing and completed sections (using "Ongoing Tasks" terminology)
  - Tasks with due dates can be added to milestone timelines
- **AI-generated Subtasks**:
  - Break down tasks into subtasks with estimated pomodoro counts
  - Displayed in a collapsible section below the task
  - Editable and counted towards the task's estimated pomodoro count

### 2.4 Project Management

- **Project Properties**:
  - Title and description
  - Status (open, working, finished)
  - Deadline and completion date
  - Tasks, milestones, and notes
  - Completed pomodoros count (calculated from records)
- **Project Operations**:
  - Create new projects (manually or via AI generator)
  - Limited to 3 open projects in free plan
  - Only one project can be in working state at a time
  - Mark projects as finished
  - Reactivate finished projects via dedicated button
- **Project Organization**:
  - Projects displayed on the left side in a full-width layout
  - Project cards with light blue background (darker when selected)
  - Projects contain tasks, milestones, and notes

### 2.5 Calendar Feature

- **Calendar Views**: Month, Week, and Year views
- **Calendar Content**:
  - Shows milestones, task/project deadlines
  - Displays completed pomodoros per day
  - Weeks start with Monday
- **Calendar Controls**:
  - Today button to jump to current date
  - Navigation between months/weeks/years
  - View selector and refresh button
- **Calendar Behavior**:
  - Data automatically aggregated when opening the calendar
  - Pomodoro records displayed on user's local date
  - Clicking on a day opens a detailed view of that day's events

### 2.6 Statistics and Analytics

- **Data Visualization**:
  - Daily, weekly, and monthly pomodoro counts
  - Project and task completion rates
  - Productivity trends over time
- **Metrics**:
  - Total focus time (calculated from actual durations)
  - Completed pomodoros by project and task
  - Completion rate of estimated vs. actual pomodoros
- **Reports**:
  - Productivity summaries
  - Project progress reports
  - Time allocation by project

### 2.7 User Interface

- **Theme Support**:
  - Light and dark mode themes
  - Theme preference saved in database
  - Custom color schemes for different timer states
- **Layout**:
  - Clean, full-width layout with projects on the left
  - Responsive design for different screen sizes
  - Consistent spacing and typography
- **Navigation**:
  - Simple navigation between AI, projects, timer, calendar, and stats
  - Clear visual indicators for active sections
  - Contact button for premium subscription and support

## 3. Data Architecture

### 3.1 Data Models

- **User Model**:
  - User account information (name, email, password/googleId)
  - User settings (timer durations, sound preferences, theme)
  - Active task reference
  - Timezone settings

- **Project Model**:
  - Title, description, status (open, working, finished)
  - Deadline and completion date
  - User reference and timestamps

- **Task Model**:
  - Title, completion status, estimated pomodoros
  - Due date and project reference
  - Subtasks with their own estimated pomodoro counts
  - User reference and timestamps

- **Pomodoro Model**:
  - Start and end times (ISO format with timezone)
  - Duration and completion status
  - Task and project references
  - Interrupted status flag

- **Milestone Model**:
  - Title, due date, completion status
  - Project reference and timestamps

- **Note Model**:
  - Content, color, position
  - Project reference and timestamps

- **Stats Model**:
  - Pomodoro counts by date
  - Achievements and metrics
  - User reference and timestamps

### 3.2 Data Storage

- **MongoDB Database** for persistent storage
- **Data Isolation** between user accounts
- **Timezone Handling** for pomodoro records
- **Efficient Queries** for performance

## 4. Technical Implementation

### 4.1 Frontend Architecture

- **React Components**:
  - Modular component structure for reusability
  - AI Project Generator component for project creation
  - Timer components for pomodoro functionality
  - Project and task management components
  - Calendar and statistics visualization components
- **State Management**:
  - React Context API for global state
  - Local component state for UI elements
  - Event bus for cross-component communication
- **Styling**:
  - Styled-components with theme support
  - Responsive design for all screen sizes
  - Consistent visual language across the application

### 4.2 Backend Architecture

- **Server**: Node.js with Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT-based auth with Google OAuth integration
- **API Routes**: RESTful endpoints for all data operations
- **External Integrations**: DeepSeek API for AI project generation

### 4.3 Security Considerations

- **Authentication**: Secure login and session management
- **Authorization**: Resource ownership validation
- **Data Protection**: Input sanitization and HTTPS
- **Error Handling**: Graceful error recovery with user feedback

## 5. Future Enhancements

### 5.1 Planned Features

- **Premium Subscription**:
  - Unlock more open projects
  - Advanced analytics features
  - Priority support
- **Enhanced AI Features**:
  - Smart task scheduling
  - Productivity recommendations
  - Advanced project generation with templates
- **Collaboration**:
  - Team projects and shared tasks
  - Real-time collaboration
  - Team statistics and progress tracking
- **Integrations**:
  - Calendar integration (Google Calendar)
  - Task management tool integration
  - Note-taking app integration

### 5.2 Technical Improvements

- **Performance**: Service worker, PWA capabilities, optimized queries
- **Accessibility**: ARIA labels, keyboard shortcuts, screen reader support
- **Mobile Application**: Native iOS and Android apps
