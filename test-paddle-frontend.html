<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle Integration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .plan-card {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
        }
        .plan-card.selected {
            border-color: #1a73e8;
            background-color: #f8f9ff;
        }
        button {
            background-color: #1a73e8;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #1565C0;
        }
        .test-result {
            background-color: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Paddle Integration Test</h1>
    
    <div>
        <h2>1. Test Backend Connection</h2>
        <button onclick="testBackend()">Test Paddle Endpoint</button>
        <div id="backend-result" class="test-result"></div>
    </div>

    <div>
        <h2>2. Test Checkout Creation</h2>
        <div class="plan-card">
            <h3>Monthly Plan - $8/month</h3>
            <button onclick="testCheckout('monthly')">Test Monthly Checkout</button>
        </div>
        <div class="plan-card">
            <h3>Yearly Plan - $60/year</h3>
            <button onclick="testCheckout('yearly')">Test Yearly Checkout</button>
        </div>
        <div class="plan-card">
            <h3>Lifetime Plan - $100</h3>
            <button onclick="testCheckout('lifetime')">Test Lifetime Checkout</button>
        </div>
        <div id="checkout-result" class="test-result"></div>
    </div>

    <div>
        <h2>3. Paddle.js Integration Test</h2>
        <p>This will test loading Paddle.js from CDN:</p>
        <button onclick="testPaddleJS()">Load Paddle.js</button>
        <div id="paddlejs-result" class="test-result"></div>
    </div>

    <script>
        // Backend API base URL
        const API_BASE = 'http://localhost:5000/api';

        async function testBackend() {
            const resultDiv = document.getElementById('backend-result');
            resultDiv.textContent = 'Testing backend connection...';
            
            try {
                const response = await fetch(`${API_BASE}/paddle/test`);
                const data = await response.json();
                resultDiv.textContent = `✅ Backend test successful:\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.textContent = `❌ Backend test failed:\n${error.message}`;
            }
        }

        async function testCheckout(planType) {
            const resultDiv = document.getElementById('checkout-result');
            resultDiv.textContent = `Testing ${planType} checkout...`;
            
            try {
                const response = await fetch(`${API_BASE}/paddle/checkout`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        // Note: In real implementation, we'd need authentication token
                    },
                    body: JSON.stringify({ planType }),
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.textContent = `✅ ${planType} checkout test successful:\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.textContent = `⚠️ ${planType} checkout test (expected auth error):\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.textContent = `❌ ${planType} checkout test failed:\n${error.message}`;
            }
        }

        async function testPaddleJS() {
            const resultDiv = document.getElementById('paddlejs-result');
            resultDiv.textContent = 'Loading Paddle.js...';
            
            try {
                // Remove any existing Paddle script
                const existingScript = document.querySelector('script[src*="paddle"]');
                if (existingScript) {
                    existingScript.remove();
                }

                const script = document.createElement('script');
                script.src = 'https://cdn.paddle.com/paddle/v2/paddle.js';
                script.async = true;
                
                script.onload = () => {
                    try {
                        if (window.Paddle) {
                            // Initialize Paddle in sandbox mode
                            window.Paddle.Environment.set('sandbox');
                            window.Paddle.Setup({ 
                                vendor: 'pro_01jxjzx642xnz9k48cwt9hv4r2' 
                            });
                            
                            resultDiv.textContent = `✅ Paddle.js loaded successfully!\nPaddle object available: ${!!window.Paddle}\nVendor ID configured: pro_01jxjzx642xnz9k48cwt9hv4r2`;
                        } else {
                            resultDiv.textContent = '❌ Paddle.js loaded but Paddle object not available';
                        }
                    } catch (error) {
                        resultDiv.textContent = `❌ Paddle.js initialization error:\n${error.message}`;
                    }
                };
                
                script.onerror = () => {
                    resultDiv.textContent = '❌ Failed to load Paddle.js from CDN';
                };
                
                document.head.appendChild(script);
            } catch (error) {
                resultDiv.textContent = `❌ Paddle.js test failed:\n${error.message}`;
            }
        }

        // Auto-run backend test on page load
        window.addEventListener('load', () => {
            setTimeout(testBackend, 500);
        });
    </script>
</body>
</html>