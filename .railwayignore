# Frontend files (not needed for backend deployment)
pomodoro-timer/
client/
src/

# Documentation
*.md
docs/
PADDLE_*.md
PM2-*.md
VERCEL_*.md
README*.md

# Scripts not needed for deployment
scripts/
deploy-pm2.sh
restart-services.sh
setup-*.sh
mongodb-*.sh

# Development files
.git/
.gitignore
.env*
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# OS files
.DS_Store
Thumbs.db

# IDE files
.vscode/
.cursor/
*.swp
*.swo

# Docker files (using Railway's build system)
Dockerfile*
docker-compose*

# PM2 and local deployment configs
ecosystem.config.js
Caddyfile*

# Test files
test/
*.test.js
*.spec.js

# Build artifacts
build/
dist/
node_modules/

# Backup files
*.bak
backup/
backups/

# Large media files
*.mp4
*.mov
*.avi
landing-pics/
landing-videos/
sounds/

# Temporary files
tmp/
temp/
*.tmp