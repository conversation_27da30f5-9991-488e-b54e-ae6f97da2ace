// Standalone test server without external dependencies
const http = require('http');
const url = require('url');

// Import our Paddle configuration and controller logic
const paddleConfig = {
  environment: 'sandbox',
  vendorId: 'pro_01jxjzx642xnz9k48cwt9hv4r2',
  priceIds: {
    monthly: 'pri_01jxk01813b3zfs36kcz66x0xg',
    yearly: 'pri_01jxk02vycmv0zjfck99vcxqmt',
    lifetime: 'pri_01jxk0448wa25ssmshke9c7r1m',
  },
  successUrl: 'http://localhost:3000/premium?success=true',
  cancelUrl: 'http://localhost:3000/premium?cancelled=true',
};

const paddleController = {
  testPaddle: () => {
    return {
      success: true,
      message: 'Paddle integration is working',
      config: {
        environment: paddleConfig.environment,
        vendorId: paddleConfig.vendorId,
        priceIds: paddleConfig.priceIds,
      },
      timestamp: new Date().toISOString(),
    };
  },

  createCheckout: (planType) => {
    if (!['monthly', 'yearly', 'lifetime'].includes(planType)) {
      return {
        success: false,
        error: 'Invalid plan type',
      };
    }

    const priceId = paddleConfig.priceIds[planType];
    
    return {
      success: true,
      planType,
      priceId,
      message: 'Checkout configuration ready',
      checkoutUrl: `https://checkout.paddle.com/checkout?price_id=${priceId}`,
      config: paddleConfig,
    };
  }
};

// Simple HTTP server
const server = http.createServer((req, res) => {
  // Enable CORS for testing
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Content-Type', 'application/json');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`${new Date().toISOString()} - ${method} ${path}`);

  try {
    if (path === '/api/paddle/test' && method === 'GET') {
      // Test endpoint
      const result = paddleController.testPaddle();
      res.writeHead(200);
      res.end(JSON.stringify(result, null, 2));
      
    } else if (path === '/api/paddle/checkout' && method === 'POST') {
      // Checkout endpoint
      let body = '';
      req.on('data', chunk => {
        body += chunk.toString();
      });
      
      req.on('end', () => {
        try {
          const data = JSON.parse(body);
          const result = paddleController.createCheckout(data.planType);
          res.writeHead(200);
          res.end(JSON.stringify(result, null, 2));
        } catch (error) {
          res.writeHead(400);
          res.end(JSON.stringify({ 
            success: false, 
            error: 'Invalid JSON in request body' 
          }));
        }
      });
      
    } else if (path === '/' && method === 'GET') {
      // Root endpoint
      res.writeHead(200);
      res.end(JSON.stringify({
        message: 'Paddle Test Server Running',
        endpoints: [
          'GET /api/paddle/test',
          'POST /api/paddle/checkout'
        ],
        timestamp: new Date().toISOString()
      }, null, 2));
      
    } else {
      // 404 for other routes
      res.writeHead(404);
      res.end(JSON.stringify({
        error: 'Not Found',
        path: path,
        method: method
      }));
    }
  } catch (error) {
    console.error('Server error:', error);
    res.writeHead(500);
    res.end(JSON.stringify({
      error: 'Internal Server Error',
      message: error.message
    }));
  }
});

const PORT = 5000;
server.listen(PORT, 'localhost', () => {
  console.log(`🚀 Paddle Test Server running on http://localhost:${PORT}`);
  console.log('Available endpoints:');
  console.log('  GET  http://localhost:5000/api/paddle/test');
  console.log('  POST http://localhost:5000/api/paddle/checkout');
  console.log('\nPress Ctrl+C to stop the server');
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down server...');
  server.close(() => {
    console.log('Server stopped');
    process.exit(0);
  });
});